name: reps-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        type: string
        required: true

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    name: pre-deploy
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: reps-engine
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: reps
      part_name: engine
      sonar_project_key: ${{ vars.SONAR_REPS_ENGINE_PROJECT_KEY }}
      deploy_cqrs: true
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
      use_workspace_focus: true

  # deploy cqrs
  deploy-cqrs:
    needs:
      - pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: reps
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      sonar_project_key: ${{ vars.SONAR_REPS_CQRS_PROJECT_KEY }}
      work_dir: apps/transactionalAccount/reps/cqrs/cqrs-common
      use_eventbus_projection: false
      use_sqs_projection: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}

  deploy-first-stage:
    name: deploy first stage
    runs-on: depot-ubuntu-24.04-8
    environment: ${{ inputs.environment }}-reps-engine
    strategy:
      matrix:
        sls_config_file:
          - serverlessCommon.yml
          - serverlessTransactionsSqs.yml
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-cqrs
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.sls_config_file }}
          app_name: reps-engine
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/transactionalAccount/reps/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-second-stage:
    name: deploy second stage
    runs-on: depot-ubuntu-24.04-8
    environment: ${{ inputs.environment }}-reps-engine
    strategy:
      matrix:
        sls_config_file:
          - serverlessApi.yml
          - serverlessProjection.yml
          - serverlessProjectionTransactions.yml
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-first-stage
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.sls_config_file }}
          app_name: reps-engine
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/transactionalAccount/reps/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-bff-reps-api:
    name: deploy BFF API
    runs-on: depot-ubuntu-24.04-8
    needs:
      - pre-deploy
      - deploy-first-stage
      - deploy-second-stage
    strategy:
      fail-fast: false
      matrix:
        component:
          - name: mp
            app_name: mp-api
            sls_config: serverlessMp.yml
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/library
        with:
          app_name: reps-api
          environment: ${{ inputs.environment }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          sonar_token: ${{ secrets.SONAR_TOKEN }}
          sonar_org: ${{ vars.SONAR_ORGANIZATION }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          sonar_project_key: ${{ vars.SONAR_REPS_API_PROJECT_KEY }}
          use_workspace_focus: true

      - name: Deploy app
        uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: reps-api
          pre_deploy_script: sh bin/loadenv.sh ${{ inputs.stage }} ${{ matrix.component.name }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.component.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          work_dir: 'apps/transactionalAccount/reps/api/'
          use_workspace_focus: true
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}

  deploy-bff-reps-api-resolvers:
    name: deploy BFF API resolvers
    runs-on: depot-ubuntu-24.04-8
    needs:
      - pre-deploy
      - deploy-bff-reps-api
    strategy:
      fail-fast: false
      matrix:
        component:
          - name: mp
            app_name: mp-api
            sls_config: serverlessMpResolvers.yml
    environment: ${{ inputs.environment }}-${{ matrix.component.app_name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - name: Deploy app
        uses: ./.github/workflows/common/sls-deploy
        if: inputs.environment != 'st'
        with:
          environment: ${{ inputs.environment }}
          pre_deploy_script: sh bin/loadenv.sh ${{ inputs.stage }} ${{ matrix.component.name }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: reps-api
          sls_config: ${{ matrix.component.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)[matrix.component.app_name] }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/transactionalAccount/reps/api/'
          use_workspace_focus: true
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}

  db-migration:
    name: DB migration
    runs-on: depot-ubuntu-24.04-8
    environment: ${{ inputs.environment }}-reps-engine
    timeout-minutes: 60
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}
      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          DockerHubUser: ${{ secrets.DOCKERHUB_USERNAME }}
          DockerHubPassword: ${{ secrets.DOCKERHUB_PASSWORD }}
          AuroraEnvironment: ${{ inputs.environment == 'st' && 'dev' ||  inputs.environment }}
          EnvironmentName: ${{ inputs.stage }}
          STAGE: ${{ inputs.stage }}
          ComponentName: reps
          PartName: engine
          AwsRegion: ${{ inputs.region }}
        with:
          project-name: ${{inputs.environment}}-reps-engine
          buildspec-override: |
            version: 0.2

            env:
              shell: bash

            phases:
              pre_build:
                commands:
                  - docker login -u $DockerHubUser -p $DockerHubPassword
                  - docker pull postgres:16
              build:
                commands:
                  - echo Migration started on `date`
                  - echo Running migration on apps
                  - echo component name $ComponentName
                  - echo aurora environment $AuroraEnvironment
                  - export DB_URL=`aws ssm get-parameter --name /$AuroraEnvironment-$ComponentName-auroradb/DatabaseEndpointURL --query Parameter.Value --output text`
                  - echo $DB_URL
                  -
                  - DB_NAME=REPSEngine
                  - echo $DB_NAME
                  -
                  - npcoDbSuperUser=`aws secretsmanager get-secret-value --secret-id  $AuroraEnvironment-reps-auroradb/DbSuperUserSecret --query "SecretString" --output text|docker run --rm -i imega/jq --raw-output -c '.username'`
                  - npcoDbSuperPassword=`aws secretsmanager get-secret-value --secret-id  $AuroraEnvironment-reps-auroradb/DbSuperUserSecret --query "SecretString" --output text|docker run --rm -i imega/jq --raw-output -c '.password'`
                  - npcoDbUser=`aws secretsmanager get-secret-value --secret-id  $AuroraEnvironment-reps-auroradb/DbUserSecret --query "SecretString" --output text|docker run --rm -i imega/jq --raw-output -c '.username'`
                  -
                  - cd apps/transactionalAccount/reps/engine
                  - aws --version
                  - schema=$EnvironmentName
                  - export domicile='AUS'
                  - export domicileCamel='Aus'
                  - dbschemaLocation='filesystem:/workdir/dbschema'
                  - |
                    if [[ "$AwsRegion" == 'eu-west-2' ]]; then
                      export domicile='GBR'
                      export domicileCamel='Gbr'
                    fi
                  - echo "domicile=$domicile, dbschemaLocation=$dbschemaLocation"
                  - echo "docker run -v `pwd`:/workdir flyway/flyway migrate -url=********************************** -locations=$dbschemaLocation -user=$npcoDbSuperUser -password=$npcoDbSuperPassword -schemas=$schema -placeholders.domicile=$domicile -placeholders.domicileCamel=$domicileCamel"
                  - docker run -v `pwd`:/workdir flyway/flyway migrate -url=********************************** -locations=$dbschemaLocation -user=$npcoDbSuperUser -password=$npcoDbSuperPassword -schemas=$schema -placeholders.domicile=$domicile -placeholders.domicileCamel=$domicileCamel
                  - docker run -e PGPASSWORD=$npcoDbSuperPassword -v `pwd`:/tmp postgres:16 psql "host=$DB_URL sslmode=verify-ca sslrootcert=/tmp/global-bundle.pem dbname=$DB_NAME user=$npcoDbSuperUser" -c "GRANT CONNECT ON DATABASE \"$DB_NAME\" TO $npcoDbUser"
                  - docker run -e PGPASSWORD=$npcoDbSuperPassword -v `pwd`:/tmp postgres:16 psql "host=$DB_URL sslmode=verify-ca sslrootcert=/tmp/global-bundle.pem dbname=$DB_NAME user=$npcoDbSuperUser" -c "GRANT USAGE ON SCHEMA $schema to $npcoDbUser"
                  - docker run -e PGPASSWORD=$npcoDbSuperPassword -v `pwd`:/tmp postgres:16 psql "host=$DB_URL sslmode=verify-ca sslrootcert=/tmp/global-bundle.pem dbname=$DB_NAME user=$npcoDbSuperUser" -c "GRANT ALL ON ALL TABLES IN SCHEMA $schema to $npcoDbUser"
                  - docker run -e PGPASSWORD=$npcoDbSuperPassword -v `pwd`:/tmp postgres:16 psql "host=$DB_URL sslmode=verify-ca sslrootcert=/tmp/global-bundle.pem dbname=$DB_NAME user=$npcoDbSuperUser" -c "GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA $schema TO $npcoDbUser"
                  -
          env-vars-for-codebuild: |
            DockerHubUser,
            DockerHubPassword,
            AuroraEnvironment,
            STAGE,
            EnvironmentName,
            ComponentName,
            PartName,
            AwsRegion

  list-system-test-spec-files-matrix:
    runs-on: depot-ubuntu-24.04-8
    if: inputs.environment == 'st' || inputs.environment == 'dev'
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-first-stage
      - deploy-second-stage
      - deploy-bff-reps-api
      - db-migration
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - uses: actions/checkout@v4
      - id: set-matrix
        run: |
          cd apps/transactionalAccount/reps/system-test/tests
          echo "matrix=$(ls *.spec.ts | jq -R -s -c 'split("\n")[:-1]')" >> $GITHUB_OUTPUT

  engine-system-test:
    name: engine system test
    runs-on: depot-ubuntu-24.04-8
    environment: ${{ inputs.environment }}-reps-engine
    if: (inputs.environment == 'st' && inputs.region == vars.SYDNEY_REGION ) || inputs.environment == 'dev'
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-first-stage
      - deploy-second-stage
      - deploy-bff-reps-api
      - deploy-bff-reps-api-resolvers
      - db-migration
      - list-system-test-spec-files-matrix
    strategy:
      fail-fast: false
      matrix:
        specFile: ${{fromJson(needs.list-system-test-spec-files-matrix.outputs.matrix)}}
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}
      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JfrogRegistry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          JfrogToken: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          STAGE: ${{ inputs.stage}}
          AWS_REGION: ${{ inputs.region }}
          SYDNEY_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          LONDON_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
        with:
          project-name: ${{inputs.environment}}-reps-engine
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - env
                  - cat .yarnrc.yml
                  - yarn config set npmPublishRegistry https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmAuthToken "$JfrogToken"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - corepack enable

              build:
                commands:
                  - set +e
                  - yarn workspaces focus component-bff reps-system-test @npco/component-dto-richdata @npco/component-dto-issuing-transaction @npco/component-bff-core
                  - yarn nx build reps-system-test
                  - export STAGE=${{ inputs.stage }}
                  - export AWS_REGION=${{ inputs.region }}
                  - yarn nx run reps-system-test:system:test tests/${{ matrix.specFile}}
          env-vars-for-codebuild: |
            JfrogRegistry,
            JfrogToken,
            STAGE
            AWS_REGION
            SYDNEY_ACCOUNT_ID
            LONDON_ACCOUNT_ID
  tag:
    runs-on: depot-ubuntu-24.04-8
    needs:
      - deploy-cqrs
      - deploy-first-stage
      - deploy-second-stage
      - deploy-bff-reps-api
      - deploy-bff-reps-api-resolvers
      - db-migration
    environment: ${{ inputs.environment }}-reps-engine
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-reps-engine
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: depot-ubuntu-24.04-8
    if: always()
    needs:
      - pre-deploy
      - tag
      - deploy-cqrs
      - db-migration
      - deploy-first-stage
      - deploy-second-stage
      - deploy-bff-reps-api
      - deploy-bff-reps-api-resolvers
      - list-system-test-spec-files-matrix
      - engine-system-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: reps-engine
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
