name: ais-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        type: string
        required: true
      role_to_assume:
        type: string
        required: true

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-nx-packaged-pre-deploy.yml
    secrets: inherit
    permissions:
      id-token: write
      contents: read
    with:
      environment: ${{ inputs.environment }}
      app_name: ais-engine
      component_name: ais
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      part_name: engine
      sonar_project_key: ${{ vars.SONAR_AIS_ENGINE_PROJECT_KEY }}
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      package_command: 'ais-engine-serverless:package:all'
      build_cqrs: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      deploy_cqrs: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      build_image: ubuntu-8core
      role_to_assume: ${{ from<PERSON><PERSON>(inputs.role_to_assume)['engine_api'] }}

  # deploy cqrs
  deploy-cqrs:
    needs: pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: ais
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}
      work_dir: apps/accounting/ais/cqrs/cqrs-common
      use_eventbus_projection: false
      use_sqs_projection: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}

  deploy-first-stage:
    name: deploy first stage
    runs-on: ubuntu-latest
    needs: pre-deploy
    environment: ${{ inputs.environment }}-ais-engine
    strategy:
      matrix:
        task_name:
          - deploy:common
          - deploy:api
          - deploy:dynamodb
          - deploy:sqs
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          component_name: ais
          app_name: ais-engine-serverless
          region: ${{ inputs.region }}
          task_name: ${{ matrix.task_name }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-second-stage:
    name: deploy second stage
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-first-stage
    strategy:
      matrix:
        task_name:
          - deploy:projection
          - deploy:warmup
          - deploy:statement
          - deploy:transfer
    environment: ${{ inputs.environment }}-ais-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        with:
          environment: ${{ inputs.environment }}
          app_name: ais-engine-serverless
          stage: ${{ inputs.stage }}
          component_name: ais
          region: ${{ inputs.region }}
          task_name: ${{ matrix.task_name }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  system-test-first-stage:
    name: system test first stage
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-first-stage
      - deploy-second-stage
      - deploy-cqrs
    environment: ${{ inputs.environment }}-ais-engine
    if: (inputs.environment == 'dev' || inputs.environment == 'st')
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix:
        test_name:
          - stacks
          - projection
          - statements
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - name: Setup JFrog
        shell: bash
        run: |
          corepack enable
          yarn config set npmPublishRegistry https://${{ secrets.NPCO_JFROG_REGISTRY }}
          yarn config set npmScopes.npco.npmRegistryServer https://${{ secrets.NPCO_JFROG_REGISTRY }}
          yarn config set npmScopes.npco.npmAuthToken "${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}"
          yarn config set npmScopes.npco.npmAlwaysAuth true
          cat .yarnrc.yml

      - name: Execute tests
        shell: bash
        env:
          SYDNEY_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          LONDON_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          AWS_REGION: ${{ inputs.region }}
          region: ${{ inputs.region }}
        run: |
          yarn workspaces focus component-bff @npco/component-dto-test-utils @npco/component-dto-xero @npco/component-dto-sim @npco/component-dto-notifications @npco/component-dto-ticket @npco/component-dto-subscription @npco/component-dto-promotion @npco/component-dto-digital-wallet-token @npco/component-dto-core @npco/component-dto-pos-interface @npco/component-dto-cardholder @npco/component-dto-issuing-card @npco/component-dto-issuing-account @npco/component-dto-payment-instrument @npco/component-dto-deposit @npco/component-dto-connection @npco/component-dto-addressbook @npco/component-dto-catalog @npco/component-dto-transaction @npco/component-dto-entity @npco/component-dto-order @npco/component-dto-cpoc @npco/component-dto-richdata @npco/component-dto-customer @npco/component-events-core bff-ecommerce-graphql @npco/component-dto-merchant @npco/component-bff-domain-events @npco/component-dto-cnp @npco/component-dto-issuing-transaction @npco/component-dto-site @npco/component-dto-device @npco/component-dto-invoice @npco/component-bff-core @npco/bff-systemtest-utils @npco/bff-common @npco/component-dbs-mp-common ais-engine
          yarn nx run-many -t build -p @npco/component-bff-core @npco/bff-systemtest-utils bff-permission-interface @npco/component-dto-richdata @npco/component-dto-issuing-transaction @npco/component-dto-entity @npco/component-dto-connection @npco/component-dto-core @npco/component-dto-customer @npco/component-dto-device @npco/component-dto-site @npco/component-dto-xero @npco/component-events-core
          cd apps/accounting/ais/system-test
          yarn config set enableImmutableInstalls false
          yarn install
          export STATIC_ENV_NAME=dev
          if [ "${{ inputs.environment }}" == "dev" ]; then
            export STATIC_ENV_NAME=dev
          elif [ "${{ inputs.environment }}" == "staging" ]; then
            export STATIC_ENV_NAME=staging
          elif [ "${{ inputs.environment }}" == "prod" ]; then
            export STATIC_ENV_NAME=prod
          fi
          export APPSYNC_STACK_NAME=${{ inputs.stage}}-ais-engine-appsync

          echo "Run ${{matrix.test_name}}.spec.ts"
          STAGE=${{ inputs.stage }} yarn system:test ${{matrix.test_name}}.spec.ts

  system-test-second-stage:
    name: system test second stage
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-first-stage
      - deploy-second-stage
      - system-test-first-stage
      - deploy-cqrs
    environment: ${{ inputs.environment }}-ais-engine
    if: (inputs.environment == 'dev' || inputs.environment == 'st')
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix:
        test_name:
          - transfers
          - transferStream
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - name: Setup JFrog
        shell: bash
        run: |
          corepack enable
          yarn config set npmPublishRegistry https://${{ secrets.NPCO_JFROG_REGISTRY }}
          yarn config set npmScopes.npco.npmRegistryServer https://${{ secrets.NPCO_JFROG_REGISTRY }}
          yarn config set npmScopes.npco.npmAuthToken "${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}"
          yarn config set npmScopes.npco.npmAlwaysAuth true
          cat .yarnrc.yml

      - name: Execute tests
        shell: bash
        env:
          SYDNEY_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          LONDON_ACCOUNT_ID: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          AWS_REGION: ${{ inputs.region }}
          region: ${{ inputs.region }}
        run: |
          yarn workspaces focus component-bff @npco/component-dto-test-utils @npco/component-dto-xero @npco/component-dto-sim @npco/component-dto-notifications @npco/component-dto-ticket @npco/component-dto-subscription @npco/component-dto-promotion @npco/component-dto-digital-wallet-token @npco/component-dto-core @npco/component-dto-pos-interface @npco/component-dto-cardholder @npco/component-dto-issuing-card @npco/component-dto-issuing-account @npco/component-dto-payment-instrument @npco/component-dto-deposit @npco/component-dto-connection @npco/component-dto-addressbook @npco/component-dto-catalog @npco/component-dto-transaction @npco/component-dto-entity @npco/component-dto-order @npco/component-dto-cpoc @npco/component-dto-richdata @npco/component-dto-customer @npco/component-events-core bff-ecommerce-graphql @npco/component-dto-merchant @npco/component-bff-domain-events @npco/component-dto-cnp @npco/component-dto-issuing-transaction @npco/component-dto-site @npco/component-dto-device @npco/component-dto-invoice @npco/component-bff-core @npco/bff-systemtest-utils @npco/bff-common @npco/component-dbs-mp-common ais-engine
          yarn nx run-many -t build -p @npco/component-bff-core @npco/bff-systemtest-utils bff-permission-interface @npco/component-dto-richdata @npco/component-dto-issuing-transaction @npco/component-dto-entity @npco/component-dto-connection @npco/component-dto-core @npco/component-dto-customer @npco/component-dto-device @npco/component-dto-site @npco/component-dto-xero @npco/component-events-core
          cd apps/accounting/ais/system-test
          yarn config set enableImmutableInstalls false
          yarn install
          export STATIC_ENV_NAME=dev
          if [ "${{ inputs.environment }}" == "dev" ]; then
            export STATIC_ENV_NAME=dev
          elif [ "${{ inputs.environment }}" == "staging" ]; then
            export STATIC_ENV_NAME=staging
          elif [ "${{ inputs.environment }}" == "prod" ]; then
            export STATIC_ENV_NAME=prod
          fi
          export APPSYNC_STACK_NAME=${{ inputs.stage}}-ais-engine-appsync

          echo "Run ${{matrix.test_name}}.spec.ts"
          STAGE=${{ inputs.stage }} yarn system:test ${{matrix.test_name}}.spec.ts

  tag:
    runs-on: ubuntu-latest
    needs:
      - deploy-cqrs
      - deploy-first-stage
      - deploy-second-stage
    environment: ${{ inputs.environment }}-ais-engine
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-ais-engine
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - tag
      - pre-deploy
      - deploy-cqrs
      - deploy-first-stage
      - deploy-second-stage
      - system-test-first-stage
      - system-test-second-stage
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: ais-engine
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
          region: ${{ inputs.region }}
