name: dbs-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: dbs-api
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: dbs
      part_name: api
      sonar_project_key: ${{ vars.SONAR_DBS_API_PROJECT_KEY }}
      deploy_cqrs: true
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      use_workspace_focus: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}

  # deploy cqrs
  deploy-cqrs:
    needs:
      - pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: dbs
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/bff-api/dbs/cqrs/cqrs-common
      use_eventbus_projection: true
      use_sqs_projection: true
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}

  # API
  deploy-common-layer:
    name: Deploy common layer
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-dbs-api
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: dbs-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessCommonLayers.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/bff-api/dbs/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-infra:
    name: Deploy infra
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-dbs-api
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-common-layer
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: Dynamodb
            sls_config: serverlessDynamodb.ts
          - name: Appsync
            sls_config: serverlessAppsync.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: dbs-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.stack.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/bff-api/dbs/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-apps:
    name: Deploy apps
    timeout-minutes: 60
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}-dbs-api
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-infra
      - deploy-cqrs
    strategy:
      fail-fast: false
      matrix:
        sls_config:
          - serverlessProjectionSqs.ts
          - serverlessSim.ts
          - serverlessBillingaccount.ts
          - serverlessWarmup.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: dbs-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/bff-api/dbs/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  list-system-test-spec-files-matrix:
    runs-on: ubuntu-latest
    if: inputs.environment == 'st' || inputs.environment == 'dev'
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - uses: actions/checkout@v4
      - id: set-matrix
        run: |
          cd apps/bff-api/dbs/api/tests/system/
          echo "matrix=$(ls *.spec.ts | jq -R -s -c 'split("\n")[:-1]')" >> $GITHUB_OUTPUT

  system-test:
    name: System test
    runs-on: ubuntu-latest
    timeout-minutes: 30
    environment: ${{ inputs.environment }}-dbs-api
    if: inputs.environment == 'st' || inputs.environment == 'dev'
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-infra
      - deploy-apps
      - list-system-test-spec-files-matrix
    strategy:
      fail-fast: false
      matrix:
        specFile: ${{fromJson(needs.list-system-test-spec-files-matrix.outputs.matrix)}}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - uses: ./.github/workflows/common/restore-archive
        if: needs.pre-deploy.outputs.archived_name != '' && needs.pre-deploy.outputs.archived_file_name != ''
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - run: |
          yarn workspaces focus component-bff dbs-api
          yarn nx build dbs-api
          cd apps/bff-api/dbs/api
          export AWS_REGION=${{ inputs.region }}
          STAGE=${{ inputs.stage }} yarn system:test tests/system/${{ matrix.specFile }}
  tag:
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
    environment: ${{ inputs.environment }}-dbs-api
    permissions: write-all
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-dbs-api
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - tag
      - deploy-cqrs
      - deploy-infra
      - deploy-apps
      - system-test
      - pre-deploy
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: dbs-api
          region: ${{ inputs.region }}
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
