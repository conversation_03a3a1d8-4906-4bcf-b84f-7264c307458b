name: ers-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/ers/**
      - .github/workflows/*ers*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        type: string
        required: true

jobs:
  destroy-ers-cqrs:
    runs-on: depot-ubuntu-22.04
    environment: st-ers-cqrs
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: cqrs-common
          app_path: 'apps/core/cqrs-common'
          stage: ${{ inputs.stage }}
          component_name: 'ers'
          destroy_cqrs: true
          region: ${{ vars.SYDNEY_REGION }}
  destroy-ers-engine:
    runs-on: depot-ubuntu-22.04
    environment: st-ers-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: ers-engine
          app_path: apps/payments/ers/engine
          stage: ${{ inputs.stage }}
          region: ${{ vars.SYDNEY_REGION }}
