name: os-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        type: string
        required: true

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: os-engine
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: os
      part_name: engine
      sonar_project_key: ${{ vars.SONAR_OS_ENGINE_PROJECT_KEY }}
      deploy_cqrs: true
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      build_image: 'ubuntu-8core'
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
      use_workspace_focus: true

  # deploy cqrs
  deploy-cqrs:
    needs:
      - pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: os
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      sonar_project_key: ${{ vars.SONAR_OS_CQRS_PROJECT_KEY }}
      work_dir: apps/onboarding/os/cqrs/cqrs-common
      use_eventbus_projection: true
      use_sqs_projection: false
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}

  # deploy os-engine
  deploy-dynamodb-sydney:
    name: deploy dynamodb (Sydney)
    runs-on: depot-ubuntu-22.04-4
    if: ${{ inputs.region == vars.SYDNEY_REGION }}
    needs:
      - pre-deploy
      - deploy-cqrs
    environment: ${{ inputs.environment }}-os-engine
    timeout-minutes: 60
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessDynamodbSydney.yml
          app_name: os-engine
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/onboarding/os/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true

  deploy-common:
    name: deploy common
    runs-on: depot-ubuntu-22.04-4
    needs:
      - pre-deploy
      - deploy-cqrs
    environment: ${{ inputs.environment }}-os-engine
    timeout-minutes: 60
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessCommon.yml
          app_name: os-engine
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/onboarding/os/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true


  deploy-command-handler:
    name: deploy os-engine lambda destination command handler lambda
    runs-on: depot-ubuntu-22.04-4
    needs:
      - pre-deploy
      - deploy-cqrs
    environment: ${{ inputs.environment }}-os-engine
    timeout-minutes: 60
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessCommandHandler.yml
          app_name: os-engine
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/onboarding/os/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true

  deploy-os-engine-apps-sydney:
    name: deploy Engine, PrivateApi, PublicApi (Sydney)
    runs-on: depot-ubuntu-22.04-4
    timeout-minutes: 60
    if: ${{ inputs.region == vars.SYDNEY_REGION }}
    needs:
      - pre-deploy
      - deploy-dynamodb-sydney
      - deploy-common
      - deploy-command-handler
    environment: ${{ inputs.environment }}-os-engine
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: Engine
            sls_config: serverlessEngine.yml
          - name: PrivateApi
            sls_config: serverlessPrivateApi.yml
          - name: PublicApi
            sls_config: serverlessPublicApi.yml
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: os-engine
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.stack.sls_config }}
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/onboarding/os/engine'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          pre_deploy_script: mkdir -p src/fonts && cp ../../../libs/dbs-mp-common/src/fonts/* src/fonts
          use_workspace_focus: true

  # deploy bff-onboarding-api
  deploy-bff-onboarding-api-sydney:
    name: deploy component
    runs-on: depot-ubuntu-22.04-4
    if: ${{ inputs.region == vars.SYDNEY_REGION }}
    needs:
      - pre-deploy
      # should only deploy when the os engine is deployed
      - deploy-os-engine-apps-sydney
    strategy:
      matrix:
        component:
          [
            {
              name: mp-api,
              sls_config: serverlessMp,
              sls_appsync: serverlessAppsync,
              component_dir: apps/bff-api/mp/api,
            },
          ]
    environment: ${{ inputs.environment }}-${{ matrix.component.name }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - uses: ./.github/workflows/common/sls-library
        if: inputs.environment == 'st'
        with:
          app_name: bff-onboarding-api
          environment: ${{ inputs.environment }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          run_test: false
          use_workspace_focus: true

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          aws_region: ${{ inputs.region }}
      - name: Deploy mock appsync
        if: inputs.environment == 'st'

        run: |
          echo "Deploying appsync"
          cd ${{ matrix.component.component_dir}}
          yarn build:schemas
          yarn sls deploy --stage ${{ inputs.stage }} --region ${{ inputs.region }} --config ${{ matrix.component.sls_appsync }}.ts
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: bff-onboarding-api
          sls_config: ${{ matrix.component.sls_config }}.ts
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/onboarding/bff-onboarding/api'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true

  #os-engine system test
  os-engine-system-test-sydney:
    name: os engine system test
    runs-on: depot-ubuntu-22.04-4
    timeout-minutes: 30
    if: (inputs.environment == 'st' || inputs.environment == 'dev') && (inputs.region == vars.SYDNEY_REGION)
    needs:
      - pre-deploy
      - deploy-os-engine-apps-sydney
    environment: ${{ inputs.environment }}-os-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - run: |
          yarn workspaces focus component-bff os-engine-system-test
          yarn nx build os-engine-system-test
          cd apps/onboarding/os/system-test
          STAGE=${{ inputs.stage }} yarn test

  tag-sydney:
    runs-on: depot-ubuntu-22.04-4
    needs:
      - deploy-cqrs
      - deploy-bff-onboarding-api-sydney
      - deploy-os-engine-apps-sydney
    environment: ${{ inputs.environment }}-os-engine
    if: (inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod') && (inputs.region == vars.SYDNEY_REGION)
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-os-engine
          ghPat: ${{ secrets.GH_PAT }}

  tag-london:
    runs-on: depot-ubuntu-22.04-4
    needs:
      - deploy-cqrs
    environment: ${{ inputs.environment }}-os-engine
    if: (inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod') && (inputs.region == vars.LONDON_REGION)
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-os-engine
          ghPat: ${{ secrets.GH_PAT }}

  slack-sydney:
    name: Slack (Sydney)
    runs-on: depot-ubuntu-22.04-4
    if: inputs.region == vars.SYDNEY_REGION
    needs:
      - pre-deploy
      - tag-sydney
      - deploy-cqrs
      - deploy-dynamodb-sydney
      - deploy-common
      - deploy-command-handler
      - deploy-os-engine-apps-sydney
      - deploy-bff-onboarding-api-sydney
      - os-engine-system-test-sydney
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: os-engine
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}

  slack-london:
    name: Slack (London)
    runs-on: depot-ubuntu-22.04-4
    if: inputs.region == vars.LONDON_REGION
    needs:
      - pre-deploy
      - tag-london
      - deploy-cqrs
      - deploy-common
      - deploy-command-handler
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: os-engine
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
