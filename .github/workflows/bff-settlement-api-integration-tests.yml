name: bff-settlement-api-integration-tests

on:
  schedule:
    - cron: '0 13 * * *'
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string
      archived_name:
        required: true
        type: string
      archived_file_name:
        required: true
        type: string

env:
  ENVIRONMENT: ${{ inputs.environment || 'dev' }}
  STAGE: ${{ inputs.stage || 'dev' }}
  REGION: ${{ inputs.region || vars.SYDNEY_REGION }}

jobs:
  integration-test:
    name: integration test
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'dev' }}-bff-integration-tests

    permissions:
      id-token: write
      contents: read

    if: >
      ${{ github.event_name == 'schedule' || 
      (github.event_name == 'workflow_call' && (
      inputs.environment || 'dev') == 'dev') }}

    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ inputs.role_to_assume }}
          aws_region: ${{ env.REGION }}

      - uses: ./.github/workflows/common/restore-archive
        if: inputs.archived_name != '' && inputs.archived_file_name != ''
        with:
          archived_name: ${{ inputs.archived_name }}
          archived_file_name: ${{ inputs.archived_file_name }}

      - name: Run Integration Tests
        run: |
          export STAGE=dev
          yarn workspaces focus '@npco/component-bff-settlement-api' component-bff crms-engine-system-test
          yarn nx build bff-settlement-api
          cd apps/payments/settlement/api
          STAGE=$STAGE yarn integration:test

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    needs:
      - integration-test
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-settlement-api
          environment: ${{ env.ENVIRONMENT }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
