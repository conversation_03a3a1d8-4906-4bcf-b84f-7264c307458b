name: sdk-integration-test

on:
  schedule:
    - cron: '0 13 * * *'

  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      archived_name:
        required: true
        type: string
      archived_file_name:
        required: true
        type: string

  workflow_dispatch:
    inputs:
      environment:
        default: dev
        type: string
        description: 'Environment to test against'
      region:
        default: ap-southeast-2
        type: string
        description: 'Region to test against'

# Kept the values strictly to only "dev" stage for now
jobs:
  integration-test:
    name: integration test
    runs-on: depot-ubuntu-22.04
    environment: dev-bff-integration-tests
    permissions:
      id-token: write
      contents: read
    env:
      COLUMNS: 120
    if: github.event_name == 'schedule' || inputs.environment == 'dev'
    steps:
      - uses: actions/checkout@v4

      - name: set AWS_REGION
        run: echo "AWS_REGION=${{ github.event_name == 'schedule' && vars.SYDNEY_REGION || inputs.region }}" >> $GITHUB_ENV

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          aws_region: ${{ env.AWS_REGION }}

      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/restore-archive
        if: inputs.archived_name != '' && inputs.archived_file_name != ''
        with:
          archived_name: ${{ inputs.archived_name }}
          archived_file_name: ${{ inputs.archived_file_name }}

      - name: Run Integration Tests
        run: |
          export STAGE=dev
          export AUTH0_DBS_CLIENT_ID=$(aws ssm get-parameter --with-decryption --name /dev-dbs-api/AUTH0_CLIENT_ID | jq -r '.Parameter.Value')
          export AUTH0_DBS_CLIENT_SECRET=$(aws ssm get-parameter --with-decryption --name /dev-dbs-api/AUTH0_CLIENT_SECRET | jq -r '.Parameter.Value')
          echo $AUTH0_DBS_CLIENT_ID $AUTH0_DBS_CLIENT_SECRET

          yarn workspaces focus component-bff sdk-integration-test
          yarn nx run sdk-integration-test:build
          yarn nx run sdk-integration-test:integration:test

  slack:
    name: Slack
    runs-on: depot-ubuntu-22.04
    if: github.event_name == 'schedule' # Runs only when triggered by cron
    needs:
      - integration-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: sdk-api
          environment: dev
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
