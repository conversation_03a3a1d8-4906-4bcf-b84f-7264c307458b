name: reps-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/transactionalAccount/reps/**
      - apps/transactionalAccount/reps/cqrs/cqrs/**
      - .github/workflows/reps*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        type: string
        required: true

jobs:
  destroy-reps-cqrs:
    runs-on: depot-ubuntu-24.04-4
    environment: st-reps-cqrs
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: cqrs-common
          app_path: 'apps/core/cqrs-common'
          stage: ${{ inputs.stage }}
          component_name: 'reps'
          destroy_cqrs: true
          region: ${{ vars.SYDNEY_REGION }}

  destroy-reps-engine:
    runs-on: depot-ubuntu-24.04-4
    environment: st-reps-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: reps-engine
          app_path: 'apps/transactionalAccount/reps/engine'
          stage: ${{ inputs.stage }}
          region: ${{ vars.SYDNEY_REGION }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          aws_region: ${{ vars.SYDNEY_REGION }}
      - id: find-stage
        run: |
          if [[ "${{github.event_name }}" == 'workflow_dispatch' ]]; then
            stage=${{ inputs.stage }}
          else
            stage=st${{github.event.pull_request.number}}
          fi
          echo "stage=$stage" >> $GITHUB_OUTPUT

      - name: Destroy db
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          STAGE: ${{ steps.find-stage.outputs.stage }}
          AuroraEnvironment: dev
        with:
          project-name: st-reps-engine
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              build:
                commands:
                  - set +e

                  - echo Destroy db schema
                  - dbUrl=`aws ssm get-parameter --name /$AuroraEnvironment-reps-auroradb/DatabaseEndpointURL --query Parameter.Value --output text`
                  - echo dbUrl $dbUrl
                  - pwd
                  - cd apps/transactionalAccount/reps/engine
                  - ls -la
                  - npcoDbSuperUser=`aws secretsmanager get-secret-value --secret-id  $AuroraEnvironment-reps-auroradb/DbSuperUserSecret --query "SecretString" --output text|docker run --rm -i imega/jq --raw-output -c '.username'`
                  - npcoDbSuperPassword=`aws secretsmanager get-secret-value --secret-id  $AuroraEnvironment-reps-auroradb/DbSuperUserSecret --query "SecretString" --output text|docker run --rm -i imega/jq --raw-output -c '.password'`
                  - echo npcoDbSuperUser $npcoDbSuperUser
                  - echo npcoDbSuperPassword $npcoDbSuperPassword
                  - export PGPASSWORD=$npcoDbSuperPassword
                  - docker run -e PGPASSWORD=$PGPASSWORD -v `pwd`:/tmp postgres:16 psql "host=$dbUrl sslmode=verify-ca sslrootcert=/tmp/global-bundle.pem dbname=REPSEngine user=$npcoDbSuperUser" -c "drop schema $STAGE cascade"

          env-vars-for-codebuild: |
            AuroraEnvironment,
            STAGE

  destroy-reps-api:
    runs-on: depot-ubuntu-24.04-4
    strategy:
      matrix:
        component:
          - name: mp
            app: mp-api
    environment: st-${{ matrix.component.app }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: reps-api
          app_path: 'apps/transactionalAccount/reps/api/'
          stage: ${{ inputs.stage }}
          skip_build: true
          region: ${{ vars.SYDNEY_REGION }}
