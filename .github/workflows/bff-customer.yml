name: bff-customer

on:
  push:
    paths:
      - apps/bff-api/customer/**
      - .github/workflows/bff-customer*
      - apps/libs/dbs-mp-common/**
      - apps/libs/bff-core/**
    branches:
      - develop
      - master

  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string

concurrency:
  # only one workflow run at a time per branch
  group: '${{ github.workflow }} @ ${{ github.head_ref || github.ref }}'
  cancel-in-progress: false

jobs:
  detect-env:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}

  assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: ${{ needs.detect-env.outputs.environment }}
      component_name: bff
      part_name: customer-api

  bff-customer-sydney:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/bff-customer-stacks.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.SYDNEY_REGION]) }}

  bff-customer-london:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    if: ${{ needs.detect-env.outputs.environment == 'dev' }}
    uses: ./.github/workflows/bff-customer-stacks.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.LONDON_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.LONDON_REGION]) }}

  prod-assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    if: needs.detect-env.outputs.environment == 'staging'
    with:
      environment: prod
      component_name: bff
      part_name: customer-api

  # production deployment should always be after staging deployment
  bff-customer-prod-sydney:
    secrets: inherit
    needs:
      - bff-customer-sydney
      - detect-env
      - prod-assume-roles
    if: needs.detect-env.outputs.environment == 'staging'
    uses: ./.github/workflows/bff-customer-stacks.yml
    with:
      stage: prod
      environment: prod
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.prod-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}
