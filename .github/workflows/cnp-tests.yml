name: cnp-tests

on:
  push:
    paths:
      - apps/payments/cnp/**
      - .github/workflows/cnp-tests*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string

  schedule:
    - cron: '12 0 * * *' # 1am AEST Daily

jobs:
  detect-env:
    runs-on: depot-ubuntu-24.04
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}

  integration-test:
    name: integration test
    runs-on: depot-ubuntu-24.04
    needs: detect-env
    environment: ${{ needs.detect-env.outputs.environment }}-cnp-tests
    if: needs.detect-env.outputs.environment == 'dev'
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/node-env
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          aws_region: ${{ vars.SYDNEY_REGION }}

      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JFROG_EMAIL: ${{ secrets.NPCO_JFROG_EMAIL }}
          JFROG_ACCESS_TOKEN: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          JFROG_REGISTRY: ${{ secrets.NPCO_JFROG_REGISTRY }}
          STAGE: ${{ needs.detect-env.outputs.stage }}
        with:
          project-name: ${{needs.detect-env.outputs.environment}}-cnp-tests
          compute-type-override: BUILD_GENERAL1_MEDIUM
          buildspec-override: |
            version: 0.2
            phases:
              install:
                runtime-versions:
                  nodejs: 18
              pre_build:
                commands:
                  - env
                  - corepack enable
                  - yarn config set npmPublishRegistry https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JFROG_REGISTRY
                  - yarn config set npmScopes.npco.npmAuthToken "$JFROG_ACCESS_TOKEN"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - cat .yarnrc.yml
              build:
                commands:
                  - yarn install --immutable --mode=skip-build
                  - echo Build started on `date`
                  - yarn nx build cnp-tests
                  - STAGE=$STAGE yarn nx run cnp-tests:integration:test
            reports:
              report:
                  files:
                    - "apps/payments/cnp/tests/dist/report.xml"
                  file-format: "JUNITXML"
          env-vars-for-codebuild: |
            JFROG_EMAIL,
            JFROG_ACCESS_TOKEN,
            JFROG_REGISTRY,
            STAGE

  slack:
    name: Slack
    runs-on: depot-ubuntu-24.04
    if: always()
    needs:
      - detect-env
      - integration-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: cnp-tests
          environment: ${{ needs.detect-env.outputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }} 
