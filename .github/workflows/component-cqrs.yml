name: component cqrs deployment

on:
  workflow_call:
    inputs:
      environment:
        type: string
        description: 'environment'
        required: true
      stage:
        type: string
        description: 'stage'
        required: true
      region:
        type: string
        description: 'region'
        required: true
      component_name:
        type: string
        description: 'component name'
        required: true
      use_sqs_projection:
        type: string
        default: 'false'
      use_eventbus_projection:
        type: string
        default: 'false'
      work_dir:
        type: string
        description: 'work dir'
        required: true
      archived_name:
        type: string
        description: 'archived name'
        required: true
      archived_file_name:
        type: string
        description: 'archived file name'
        required: true
      mock_cqrs:
        type: string
        default: 'false'
      skip_in_st:
        type: string
        default: 'false'
      role_to_assume:
        type: string
        description: 'role to assume'
      sonar_project_key:
        type: string
        description: 'sonar project key'
        required: true

jobs:
  mock-event-bus:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    permissions:
      id-token: write
      contents: read
    environment: ${{ inputs.environment }}-${{ inputs.component_name }}-cqrs
    steps:
      - uses: actions/checkout@v4
        if: inputs.environment == 'st'
      - name: 'Setup JFrog Registry'
        shell: bash
        if: inputs.environment == 'st'
        run: |
          corepack enable
          yarn config set npmPublishRegistry https://${{ secrets.NPCO_JFROG_REGISTRY }}
          yarn config set npmScopes.npco.npmRegistryServer https://${{ secrets.NPCO_JFROG_REGISTRY }}
          yarn config set npmScopes.npco.npmAuthToken "${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}"
          yarn config set npmScopes.npco.npmAlwaysAuth true
          cat .yarnrc.yml
      - uses: ./.github/workflows/common/aws-setup
        if: inputs.environment == 'st'
        with:
          role_to_assume: ${{ inputs.role_to_assume || vars.ROLE_TO_ASSUME }}
          aws_region: ${{ inputs.region }}
      - name: mock event bridge
        if: inputs.environment == 'st'
        run: |
          yarn workspaces focus component-bff cqrs-common
          stage=${{ inputs.stage }} COMPONENT_NAME=${{ inputs.component_name }} yarn nx run cqrs-common:deploy:mock-event-bridge
          echo deploy to ${{ inputs.stage }}

  build:
    runs-on: depot-ubuntu-24.04-8
    name: build-cqrs
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/library
        # don't run unit test for PR
        if: inputs.environment != 'st' || inputs.mock_cqrs == 'true'
        with:
          app_name: cqrs-common
          environment: ${{ inputs.environment }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          sonar_token: ${{ secrets.SONAR_TOKEN }}
          sonar_org: ${{ vars.SONAR_ORGANIZATION }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          sonar_project_key: ${{ inputs.sonar_project_key }}

  # deploy cqrs
  cqrs-eventstore:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-${{ inputs.component_name }}-cqrs
    needs:
      - build
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
        if: inputs.environment != 'st' || inputs.mock_cqrs == 'true'
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        if: inputs.environment != 'st' || inputs.mock_cqrs == 'true'
        with:
          environment: ${{ inputs.environment }}
          app_name: cqrs-common
          component_name: ${{ inputs.component_name }}
          task_name: 'deploy:dynamodb'
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          role_to_assume: ${{ inputs.role_to_assume || vars.ROLE_TO_ASSUME }}
          archived_name: ${{ inputs.archived_name }}
          archived_file_name: ${{ inputs.archived_file_name }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

  # deploy cqrs
  cqrs-infra:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}-${{ inputs.component_name }}-cqrs
    needs:
      - build
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: sqs
            sls_config: serverlessSqs.ts
            task_name: 'deploy:sqs'
            enabled: ${{ inputs.use_sqs_projection }}
          - name: event-bus
            task_name: 'deploy:event-bridge'
            enabled: ${{ inputs.use_eventbus_projection }}
    steps:
      - uses: actions/checkout@v4
        if: matrix.stack.enabled == 'true' && inputs.skip_in_st != 'true'
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        if: matrix.stack.enabled == 'true' && inputs.skip_in_st != 'true'
        with:
          environment: ${{ inputs.environment }}
          app_name: cqrs-common
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          component_name: ${{ inputs.component_name }}
          task_name: ${{ matrix.stack.task_name }}
          role_to_assume: ${{ inputs.role_to_assume || vars.ROLE_TO_ASSUME }}
          archived_name: ${{ inputs.archived_name }}
          archived_file_name: ${{ inputs.archived_file_name }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

  cqrs-apps:
    name: cqrs-app
    runs-on: ubuntu-latest
    timeout-minutes: 60
    needs:
      - cqrs-infra
      - cqrs-eventstore
      - mock-event-bus
      - build
    environment: ${{ inputs.environment }}-${{ inputs.component_name }}-cqrs
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: dbstream
            task_name: 'deploy:dynamodb-stream-handler'
            enabled: 'true'
            st_deploy: 'false'
          - name: command-handler
            task_name: 'deploy:command-handlers'
            enabled: 'true'
            st_deploy: 'true'
          - name: saga-handler
            task_name: 'deploy:saga-event-handlers'
            enabled: 'true'
            st_deploy: 'false'
    steps:
      - uses: actions/checkout@v4
        if: matrix.stack.enabled == 'true' && inputs.skip_in_st != 'true' && (inputs.environment != 'st' || matrix.stack.st_deploy == 'true' || inputs.mock_cqrs == 'true')
      - uses: ./.github/workflows/common/sls-deploy-with-nx
        if: matrix.stack.enabled == 'true' && inputs.skip_in_st != 'true' && (inputs.environment != 'st' || matrix.stack.st_deploy == 'true' || inputs.mock_cqrs == 'true')
        with:
          environment: ${{ inputs.environment }}
          app_name: cqrs-common
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          component_name: ${{ inputs.component_name }}
          task_name: ${{ matrix.stack.task_name }}
          role_to_assume: ${{ inputs.role_to_assume || vars.ROLE_TO_ASSUME }}
          archived_name: ${{ inputs.archived_name }}
          archived_file_name: ${{ inputs.archived_file_name }}
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
