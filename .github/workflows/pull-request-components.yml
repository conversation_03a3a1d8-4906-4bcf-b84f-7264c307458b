name: system-test

on:
  workflow_call:
    inputs:
      app_name:
        required: true
        type: string
jobs:
  ams-assume-roles:
    secrets: inherit
    if: inputs.app_name == 'ams-engine'
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: st
      component_name: ams
      part_name: engine

  ams:
    needs:
      - ams-assume-roles
    secrets: inherit
    uses: ./.github/workflows/ams-stacks.yml
    with:
      stage: st${{github.event.pull_request.number}}
      environment: st
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.ams-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}

  entity-assume-roles:
    secrets: inherit
    if: inputs.app_name == 'bff-entity-api'
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: st
      component_name: bff
      part_name: entity-api

  bff-entity-api:
    needs:
      - entity-assume-roles
    secrets: inherit
    uses: ./.github/workflows/bff-entity-stacks.yml
    with:
      stage: st${{github.event.pull_request.number}}
      environment: st
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.entity-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}

  customer-assume-roles:
    secrets: inherit
    if: inputs.app_name == 'bff-customer-api'
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: st
      component_name: bff
      part_name: customer-api

  customer-entity-api:
    needs:
      - customer-assume-roles
    secrets: inherit
    uses: ./.github/workflows/bff-customer-stacks.yml
    with:
      stage: st${{github.event.pull_request.number}}
      environment: st
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.customer-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}

  crms-assume-roles:
    secrets: inherit
    if: inputs.app_name == 'crms-engine'
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: st
      component_name: crms
      part_name: engine

  crms-engine:
    needs:
      - crms-assume-roles
    secrets: inherit
    uses: ./.github/workflows/crms-stacks.yml
    with:
      stage: st${{github.event.pull_request.number}}
      environment: st
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.crms-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}

  addressbook-assume-roles:
    secrets: inherit
    if: inputs.app_name == 'bff-addressbook-api'
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: st
      component_name: bff
      part_name: addressbook-api

  addressbook-api:
    needs:
      - addressbook-assume-roles
    secrets: inherit
    uses: ./.github/workflows/bff-addressbook-api-stacks.yml
    with:
      stage: st${{github.event.pull_request.number}}
      environment: st
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.addressbook-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}

  mp-assume-roles:
    secrets: inherit
    if: inputs.app_name == 'mp-api'
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: st
      component_name: mp
      part_name: api

  mp-api:
    needs:
      - mp-assume-roles
    secrets: inherit
    uses: ./.github/workflows/mp-stacks.yml
    with:
      stage: st${{github.event.pull_request.number}}
      environment: st
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.mp-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}
