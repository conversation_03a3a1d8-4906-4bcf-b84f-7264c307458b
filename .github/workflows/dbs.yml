name: dbs

on:
  push:
    paths:
      - apps/bff-api/dbs/**
      - apps/libs/dbs-mp-common/**
      - .github/workflows/dbs*
      - apps/bff-api/dbs/cqrs/cqrs/**
    branches:
      - develop
      - master

  pull_request:
    types:
      - opened
      - synchronize
    branches:
      - develop
    paths:
      - apps/bff-api/dbs/api/serverless*
      - apps/bff-api/dbs/api/resources/**
      - .github/workflows/dbs*

  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string

  schedule:
    - cron: '0 15 * * *'

concurrency:
  # only one workflow run at a time per branch
  group: '${{ github.workflow }} @ ${{ github.head_ref || github.ref }}'
  cancel-in-progress: false

jobs:
  detect-env:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}

  assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: ${{ needs.detect-env.outputs.environment }}
      component_name: dbs
      part_name: api

  dbs-sydney:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/dbs-stacks.yml
    permissions: write-all
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.SYDNEY_REGION]) }}
      region: ${{ vars.SYDNEY_REGION }}

  dbs-london:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/dbs-stacks.yml
    permissions: write-all
    if: ${{ needs.detect-env.outputs.environment == 'dev' }}
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.LONDON_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.LONDON_REGION]) }}

  prod-assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    if: needs.detect-env.outputs.environment == 'staging'
    with:
      environment: prod
      component_name: dbs
      part_name: api

  # production deployment should always be after stging deployment
  dbs-prod-sydney:
    secrets: inherit
    needs:
      - dbs-sydney
      - detect-env
      - prod-assume-roles
    permissions: write-all
    if: needs.detect-env.outputs.environment == 'staging'
    uses: ./.github/workflows/dbs-stacks.yml
    with:
      stage: prod
      environment: prod
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.prod-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}
