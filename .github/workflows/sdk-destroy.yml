name: sdk-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/sdk/api/**
      - apps/payments/sdk/cqrs/**
      - .github/workflows/sdk*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        type: string
        required: true

jobs:
  destroy-sdk-api:
    runs-on: depot-ubuntu-22.04
    environment: st-sdk-api
    permissions:
      id-token: write
      contents: read
    env:
      COLUMNS: 120
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: sdk-api
          app_path: apps/payments/sdk/api
          stage: ${{ inputs.stage }}
          region: ${{ vars.SYDNEY_REGION }}

  destroy-sdk-cqrs:
    runs-on: depot-ubuntu-22.04
    environment: st-sdk-cqrs
    permissions:
      id-token: write
      contents: read
    env:
      COLUMNS: 120
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: cqrs-common
          app_path: 'apps/core/cqrs-common'
          stage: ${{ inputs.stage }}
          component_name: 'sdk'
          destroy_cqrs: true
          region: ${{ vars.SYDNEY_REGION }}
