name: dbs-mp-common

on:
  push:
    paths:
      - apps/libs/dbs-mp-common/**
    branches:
      - develop

jobs:
  libs:
    name: Build
    runs-on: depot-ubuntu-24.04-8
    environment: dev-@npco/dbs-mp-common
    steps:
      - uses: actions/checkout@v4

      - id: env-detect
        uses: ./.github/workflows/common/env-detect

      - uses: ./.github/workflows/common/library
        with:
          app_name: '@npco/component-dbs-mp-common'
          environment: ${{ steps.env-detect.outputs.environment }}
          jfrog_email: ${{ secrets.NPCO_JFROG_PUBLISHER_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          sonar_token: ${{ secrets.SONAR_TOKEN }}
          sonar_org: ${{ vars.SONAR_ORGANIZATION }}
          sonar_project_key: ${{ vars.SONAR_DBS_MP_COMMMON_PROJECT_KEY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
