name: cnp-api-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: cnp-api
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: cnp
      part_name: api
      sonar_project_key: ${{ vars.SONAR_CNP_API_PROJECT_KEY }}
      deploy_cqrs: true
      build_image: ubuntu-8core
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      use_workspace_focus: true
      default_workspace_name: component-cnp-api
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}

  deploy-common:
    name: deploy common
    runs-on: depot-ubuntu-24.04
    needs: pre-deploy
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: Debug
        run: |
          echo "Role to assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}"
          echo "Environment: ${{ inputs.environment }}"
          echo "Stage: ${{ inputs.stage }}"
          echo "Region: ${{ inputs.region }}"
          echo "Archived name: ${{ needs.pre-deploy.outputs.archived_name }}"
          echo "Archived file name: ${{ needs.pre-deploy.outputs.archived_file_name }}"
          echo "aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}"
          echo "aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}"
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessCommon.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-api:
    name: deploy api
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-common
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessApi.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-api-internal:
    name: deploy api internal
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-common
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessApiInternal.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-dynamodb:
    name: deploy dynamodb
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-common
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessDynamodb.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          additional_workspaces: '@npco/component-bff-serverless'
          build_project: false
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-projection:
    name: deploy projection
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-common
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessProjection.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-transaction:
    name: deploy transaction
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-api
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessTransaction.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-ecommerce:
    name: deploy ecommerce
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-api
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessEcommerce.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-ecommerce-internal:
    name: deploy ecommerce internal
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-api-internal
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessEcommerceInternal.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  deploy-warmup:
    name: deploy warmup
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-api
    environment: ${{ inputs.environment }}-cnp-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cnp-api
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessWarmup.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/cnp/api
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
          default_workspace_name: component-cnp-api
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}

  system-test:
    name: system test
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-dynamodb
      - deploy-projection
      - deploy-transaction
      - deploy-ecommerce
      - deploy-ecommerce-internal
      - deploy-warmup
    environment: ${{ inputs.environment }}-cnp-api
    if: inputs.environment != 'prod' && inputs.environment != 'staging'
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - run: |
          yarn workspaces focus component-bff component-cnp-api
          yarn nx reset
          yarn nx build cnp-api
          cd apps/payments/cnp/api/
          STAGE=${{ inputs.stage }} yarn system:test

  # deploy cqrs
  deploy-cqrs:
    needs:
      - pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: cnp
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/payments/cnp/cqrs/cqrs-common
      use_eventbus_projection: false
      use_sqs_projection: false
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}

  tag:
    runs-on: depot-ubuntu-24.04
    needs:
      - pre-deploy
      - deploy-dynamodb
      - deploy-projection
      - deploy-transaction
      - deploy-ecommerce
      - deploy-ecommerce-internal
      - deploy-warmup
      - deploy-cqrs
    environment: ${{ inputs.environment }}-cnp-api
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-cnp-api
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: depot-ubuntu-24.04
    if: always()
    needs:
      - pre-deploy
      - tag
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: cnp-api
          region: ${{ inputs.region }}
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
