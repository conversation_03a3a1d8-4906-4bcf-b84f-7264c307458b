name: sis-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      role_to_assume:
        type: string
        required: true

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: sis-engine
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: sis
      part_name: engine
      sonar_project_key: ${{ vars.SONAR_SIS_ENGINE_PROJECT_KEY }}
      deploy_cqrs: true
      deployment_s3_bucket: ${{ inputs.region != vars.SYDNEY_REGION || false }}
      build_image: 'ubuntu-8core'
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
      use_workspace_focus: true

  # deploy cqrs
  deploy-cqrs:
    needs: pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: sis
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/core/sis/cqrs/cqrs-common
      use_eventbus_projection: true
      use_sqs_projection: false
      sonar_project_key: ${{ vars.SONAR_SIS_CQRS_PROJECT_KEY }}
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['cqrs'] }}

  deploy-first-stage:
    name: deploy common sqs dynamodb
    runs-on: depot-ubuntu-22.04-4
    environment: ${{ inputs.environment }}-sis-engine
    strategy:
      matrix:
        sls_config_file:
          - serverlessCommon.yml
          - serverlessSqs.yml
          - serverlessDynamodb.yml
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-cqrs
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.sls_config_file }}
          app_name: sis-engine
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/core/sis/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          use_workspace_focus: true

  deploy-second-stage:
    name: deploy engine lambda
    runs-on: depot-ubuntu-22.04-4
    environment: ${{ inputs.environment }}-sis-engine
    strategy:
      matrix:
        sls_config_file:
          - serverlessApi.yml
          - serverlessDevices.yml
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-first-stage
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: ${{ matrix.sls_config_file }}
          app_name: sis-engine
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/core/sis/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          aws_london_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.LONDON_REGION] }}
          aws_sydney_account_id: ${{ fromJson(vars.AWS_ACCOUNTS)[inputs.environment][vars.SYDNEY_REGION] }}
          use_workspace_focus: true

  engine-system-test:
    name: engine system test
    runs-on: depot-ubuntu-22.04-4
    environment: ${{ inputs.environment }}-sis-engine
    if: (inputs.environment == 'st' || inputs.environment == 'dev')
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-first-stage
      - deploy-second-stage
    strategy:
      fail-fast: false
      matrix:
        component: [{ name: device, fileName: device.spec.ts }, { name: device_api, fileName: deviceApi.spec.ts }]
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          aws_region: ${{ inputs.region }}

      - uses: ./.github/workflows/common/restore-archive
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JfrogRegistry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          JfrogToken: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          STAGE: ${{ inputs.stage}}
        with:
          project-name: ${{inputs.environment}}-sis-engine
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - env
                  - cat .yarnrc.yml
                  - yarn config set npmPublishRegistry https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmAuthToken "$JfrogToken"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - corepack enable

              build:
                commands:
                  - pwd
                  - ls -la
                  - set +e
                  - yarn workspaces focus component-bff sis-system-test
                  - yarn nx build sis-system-test
                  - export STAGE=${{ inputs.stage }}
                  - cd apps/core/sis/system-test
                  - yarn run system:test src/${{ matrix.component.fileName }}
          env-vars-for-codebuild: |
            JfrogRegistry,
            JfrogToken,
            STAGE

  tag:
    runs-on: depot-ubuntu-22.04-4
    needs:
      - deploy-cqrs
      - deploy-first-stage
      - deploy-second-stage
    environment: ${{ inputs.environment }}-sis-engine
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-sis-engine
          ghPat: ${{ secrets.GH_PAT }}

  # slack notification
  slack:
    name: Slack
    runs-on: depot-ubuntu-22.04-4
    if: always()
    needs:
      - pre-deploy
      - tag
      - deploy-cqrs
      - deploy-first-stage
      - deploy-second-stage
      - engine-system-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: sis-engine
          region: ${{ inputs.region }}
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
