name: ers

on:
  push:
    paths:
      - apps/payments/ers/**
      - .github/workflows/*ers*
    branches:
      - develop
      - master

  pull_request:
    types:
      - opened
      - synchronize
    branches:
      - develop
    paths:
      - apps/payments/ers/**
      - .github/workflows/*ers*

  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string

concurrency:
  # only one workflow run at a time per branch
  group: '${{ github.workflow }} @ ${{ github.head_ref || github.ref }}'
  cancel-in-progress: false

jobs:
  detect-env:
    runs-on: depot-ubuntu-22.04
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}

  ers-sydney:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/ers-stacks.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.SYDNEY_REGION }}

  # production deployment should always be after stging deployment
  ers-prod-sydney:
    secrets: inherit
    needs:
      - ers-sydney
      - detect-env
    if: needs.detect-env.outputs.environment == 'staging'
    uses: ./.github/workflows/ers-stacks.yml
    with:
      stage: prod
      environment: prod
      region: ${{ vars.SYDNEY_REGION }}
