name: crms

on:
  push:
    paths:
      - apps/bff-api/crms/**
      - apps/libs/dbs-mp-common/**
      - .github/workflows/crms*
      - apps/bff-api/crms/cqrs/cqrs/**
      - apps/payments/cnp/bff-ecommerce/graphql/**
    branches:
      - develop
      - master

  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string

  schedule:
    - cron: '0 14 * * *'

concurrency:
  # only one workflow run at a time per branch
  group: '${{ github.workflow }} @ ${{ github.head_ref || github.ref }}'
  cancel-in-progress: false

jobs:
  detect-env:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}

  assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: ${{ needs.detect-env.outputs.environment }}
      component_name: crms
      part_name: engine

  crms-sydney:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/crms-stacks.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.SYDNEY_REGION]) }}

  crms-london:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/crms-stacks.yml
    if: needs.detect-env.outputs.environment == 'dev'
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.LONDON_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.LONDON_REGION]) }}

  prod-assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    if: needs.detect-env.outputs.environment == 'staging'
    with:
      environment: prod
      component_name: crms
      part_name: engine

  # production deployment should always be after stging deployment
  crms-prod-sydney:
    secrets: inherit
    needs:
      - crms-sydney
      - detect-env
      - prod-assume-roles
    if: needs.detect-env.outputs.environment == 'staging'
    uses: ./.github/workflows/crms-stacks.yml
    with:
      stage: prod
      environment: prod
      role_to_assume: ${{ toJson(fromJson(needs.prod-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}
      region: ${{ vars.SYDNEY_REGION }}
