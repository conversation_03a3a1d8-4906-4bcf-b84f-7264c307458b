name: cnp-sdk

on:
  push:
    paths:
      - apps/payments/cnp/sdk/**
    branches:
      - develop

jobs:
  libs:
    name: Build
    runs-on: depot-ubuntu-24.04
    environment: dev-@npco/component-cnp-sdk
    steps:
      - uses: actions/checkout@v4

      - id: env-detect
        uses: ./.github/workflows/common/env-detect

      - uses: ./.github/workflows/common/library
        with:
          app_name: '@npco/component-cnp-sdk'
          environment: ${{ steps.env-detect.outputs.environment }}
          jfrog_email: ${{ secrets.NPCO_JFROG_PUBLISHER_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          sonar_token: ${{ secrets.SONAR_TOKEN }}
          sonar_org: ${{ vars.SONAR_ORGANIZATION }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          sonar_project_key: ${{ vars.SONAR_DTO_COMMON_PROJECT_KEY}}
