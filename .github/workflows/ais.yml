name: ais

on:
  push:
    paths:
      - apps/accounting/ais/**
      - .github/workflows/ais*
    branches:
      - develop
      - master

  pull_request:
    types:
      - opened
      - synchronize
    branches:
      - develop
    paths:
      - apps/accounting/ais/engine/serverless*
      - apps/accounting/ais/engine/project.json
      - apps/accounting/ais/cqrs/cqrs/**
      - .github/workflows/ais*

  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string

concurrency:
  # only one workflow run at a time per branch
  group: '${{ github.workflow }} @ ${{ github.head_ref || github.ref }}'
  cancel-in-progress: false

jobs:
  detect-env:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}
  assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: ${{ needs.detect-env.outputs.environment }}
      component_name: ais
      part_name: engine
  ais-sydney:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/ais-stacks.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.SYDNEY_REGION]) }}
  ais-london:
    secrets: inherit
    if: ${{ needs.detect-env.outputs.environment == 'dev' }}
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/ais-stacks.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.LONDON_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.LONDON_REGION]) }}
  prod-assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    if: needs.detect-env.outputs.environment == 'staging'
    with:
      environment: prod
      component_name: ais
      part_name: engine
  # production deployment should always be after stging deployment
  ais-prod-sydney:
    secrets: inherit
    needs:
      - ais-sydney
      - detect-env
      - prod-assume-roles
    if: needs.detect-env.outputs.environment == 'staging'
    uses: ./.github/workflows/ais-stacks.yml
    with:
      stage: prod
      environment: prod
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.prod-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}
