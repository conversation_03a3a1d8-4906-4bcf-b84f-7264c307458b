name: Affected
run-name: affected by ${{ github.event_name }}

on:
  workflow_call:
    inputs:
      node_version:
        type: string
        default: '18'
      last_commit:
        description: the last commit to compare
        type: string

    outputs:
      affected_apps_array: # a json array including affected apps
        value: ${{ jobs.affected-apps.outputs.affectedAppsArray }}
      environment:
        value: ${{ jobs.affected-apps.outputs.environment }}
      stage:
        value: ${{ jobs.affected-apps.outputs.stage }}
      apps_build_images:
        value: ${{ jobs.affected-apps.outputs.apps_build_images }}

jobs:
  pr-check:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - uses: actions/checkout@v4
      - name: pr-check
        uses: ./.github/workflows/common/pr-check
        with:
          github_token: ${{ secrets.GITHUB_TOKEN}}

  generate-pr-title:
    runs-on: ubuntu-latest
    needs: pr-check
    outputs:
      new_title: ${{ steps.update-title.outputs.new_title }}
    if: always() && needs.pr-check.result == 'failure'
    steps:
      - uses: actions/checkout@v4
      - id: update-title
        uses: ./.github/workflows/common/generate-pr-title

  # find affected apps based on the target branch of a PR
  affected-apps:
    runs-on: depot-ubuntu-24.04-8
    needs:
      - pr-check
      - generate-pr-title
    if: ${{ !cancelled() && github.event_name == 'pull_request' && needs.pr-check.result == 'success' || needs.generate-pr-title.result == 'success' }}
    timeout-minutes: 30
    outputs:
      affectedAppsArray: ${{ steps.nx-affected.outputs.affected_apps_array }}
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
      apps_build_images: ${{ steps.build-image-config.outputs.apps_build_images }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 2
      - uses: ./.github/workflows/common/node-env
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          node_version: ${{ inputs.node_version }}
      - id: fetch-commits
        uses: ./.github/workflows/common/fetch-commit-messages
        with:
          github_token: ${{ secrets.GITHUB_TOKEN}}
      - id: find-git-diff
        uses: ./.github/workflows/common/find-diff

      - id: update-pr-detail
        uses: ./.github/workflows/common/pr-update
        env:
          PR_BODY: '${{ github.event.pull_request.body }}'
          PR_TITLE: '${{ github.event.pull_request.title }}'
        with:
          title-check-result: ${{ needs.pr-check.result }}
          generate-title-result: ${{ needs.generate-pr-title.result }}
          github_token: ${{ secrets.GITHUB_TOKEN}}
          title: ${{ needs.generate-pr-title.outputs.new_title }}
          authors: ${{ steps.fetch-commits.outputs.authors }}
          jids: ${{ steps.fetch-commits.outputs.jids }}
          apps: ${{ steps.find-git-diff.outputs.change_apps }}
      - name: Fetch base branch
        run: |
          git fetch origin ${{ github.base_ref }} --depth=1
      - name: Install
        run: |
          yarn install --immutable
          yarn nx affected --base=origin/${{ github.base_ref }} --target=yarninstall --parallel=1 --exclude='*,!tag:separate-yarn-install'
      - name: Compare change
        run: |
          # should no change
          echo $(git diff)
          diff=$(git diff --name-only -- ':!.yarnrc.yml')
          if [ -n "$diff" ]; then
            echo "There are changes after yarn install"
            echo "Changed files: $diff"
            exit 1
          fi

      - id: nx-affected
        name: Find affected apps
        run: |
          env
          pwd
          ls
          cat .yarnrc.yml
          echo event name: ${{ github.event_name }}
          baseRef=${GITHUB_BASE_REF:-develop}
          baseRef=origin/$baseRef
          echo baseRef: $baseRef

          # find affected apps
          rawOutput=$(yarn nx show projects --affected --base=$baseRef -t ci:build)
          jsonOutput=$(yarn nx show projects --affected --base=$baseRef -t ci:build --json)
          affectedApps="${rawOutput//$'\n'/ }"
          echo "affected_apps=$affectedApps" >> $GITHUB_OUTPUT

          affectedAppsArray="${jsonOutput//$'\n'/}"
          echo "affected_apps_array=$affectedAppsArray" >> $GITHUB_OUTPUT

          echo $GITHUB_OUTPUT
          cat $GITHUB_OUTPUT

      - name: Batch build affected
        run: |
          yarn nx affected --target=build --base=origin/${{ github.base_ref }} --batch --exclude='*,!tag:batch-build-capable'

      - name: Test dto
        run: |
          yarn nx affected --base=origin/${{ github.base_ref }} --target=test --parallel=8 --exclude='*,!tag:dto-library'

      - name: Fast build affected
        run: |
          yarn nx affected --target=build --base=origin/${{ github.base_ref }} --parallel=8

      - name: Fast lint affected
        run: |
          yarn nx affected --target=lint --base=origin/${{ github.base_ref }} --parallel=10 --exclude=posconnector-api,hlpos-engine,oraclepos-engine,bff-core-go,impos-interface,cpi-tevalis-engine

      - name: Get build image config
        id: build-image-config
        shell: bash
        run: |
          runs_on_value=$(jq '."build-image"' .github/workflows/zeller-config.json -r)
          echo run on value $runs_on_value
          affectedApps=(${{ steps.nx-affected.outputs.affected_apps }})
          echo affectedApps $affectedApps
          appsBuildImages='{'
          for app in "${affectedApps[@]}"; do
            echo check app $app
            runs_on=`echo $runs_on_value | jq -r ".\"$app\""`
            echo runs on $runs_on
            if [ "$runs_on" == "null" ]; then
              echo "app $app not found in build-image config, use default"
              runs_on="ubuntu-latest"
            fi
            appsBuildImages="$appsBuildImages \"$app\":\"$runs_on\","
          done
          appsBuildImages=${appsBuildImages%?}
          appsBuildImages="$appsBuildImages }"
          echo appsBuildImages: $appsBuildImages
          echo "apps_build_images=$appsBuildImages" >> $GITHUB_OUTPUT
          cat $GITHUB_OUTPUT

      - id: env-detect
        uses: './.github/workflows/common/env-detect'

      - name: Print affected
        run: |
          echo affectedAppsArray: ${{ steps.nx-affected.outputs.affected_apps_array }}
          echo environment: ${{ steps.env-detect.outputs.environment }}
          echo stage: ${{ steps.env-detect.outputs.stage }}
          echo apps_build_images: ${{ steps.build-image-config.outputs.apps_build_images }}

      - id: archive
        uses: ./.github/workflows/common/archive
        with:
          archived_name: ${{ steps.env-detect.outputs.stage }}-pull-request
          environment: st
