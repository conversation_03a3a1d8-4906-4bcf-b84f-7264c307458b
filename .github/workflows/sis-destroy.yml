name: sis-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/core/sis/engine/**
      - apps/core/sis/engine/cqrs/cqrs/**
      - .github/workflows/sis*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        type: string
        required: true

jobs:
  destroy-sis-engine:
    runs-on: depot-ubuntu-22.04-4
    environment: st-sis-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: sis-engine
          app_path: 'apps/core/sis/engine'
          stage: ${{ inputs.stage }}
          skip_build: true
          region: ${{ vars.SYDNEY_REGION }}

  destroy-sis-cqrs:
    runs-on: depot-ubuntu-22.04-4
    environment: st-sis-cqrs
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: cqrs-common
          app_path: 'apps/core/cqrs-common'
          stage: ${{ inputs.stage }}
          component_name: 'sis'
          destroy_cqrs: true
          region: ${{ vars.SYDNEY_REGION }}
