name: bff-device-integration-test

on:
  schedule:
    - cron: '0 14 * * *'

  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string
      archived_name:
        required: true
        type: string
      archived_file_name:
        required: true
        type: string
      role_to_assume:
        required: false
        type: string

  workflow_dispatch:
    inputs:
      environment:
        default: dev
        type: string
        description: 'Environment to test against'
      region:
        default: ap-southeast-2
        type: string
        description: 'Region to test against'

jobs:
  integration-test:
    name: integration test
    runs-on: depot-ubuntu-22.04-4
    environment: dev-bff-integration-tests
    permissions:
      id-token: write
      contents: read
    if: github.event_name == 'schedule' || inputs.environment == 'dev'
    steps:
      - uses: actions/checkout@v4

      - name: set AWS_REGION
        run: echo "AWS_REGION=${{ github.event_name == 'schedule' && vars.SYDNEY_REGION || inputs.region }}" >> $GITHUB_ENV

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ inputs.role_to_assume || vars.ROLE_TO_ASSUME }}
          aws_region: ${{ env.AWS_REGION }}

      - uses: ./.github/workflows/common/jfrog-credentials
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/restore-archive
        if: inputs.archived_name != '' && inputs.archived_file_name != ''
        with:
          archived_name: ${{ inputs.archived_name }}
          archived_file_name: ${{ inputs.archived_file_name }}

      - name: Run Integration Tests
        run: |
          export STAGE=dev
          yarn workspaces focus component-bff bff-device-integration-test
          yarn nx run bff-device-integration-test:build
          yarn nx run bff-device-integration-test:integration:test

  slack:
    name: Slack
    runs-on: depot-ubuntu-22.04-4
    if: github.event_name == 'schedule' # Runs only when triggered by cron
    needs:
      - integration-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: bff-device
          environment: dev
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
