{"build-image": {"default": "depot-ubuntu-24.04", "@npco/component-dbs-mp-common": "depot-ubuntu-24.04-8", "ams-engine": "depot-ubuntu-24.04-8", "bff-migration": "depot-ubuntu-24.04-8", "crms-engine": "depot-ubuntu-24.04-8", "bff-banking-api": "depot-ubuntu-24.04-8", "reps-engine": "depot-ubuntu-24.04-8"}, "sonar-key-name": {"@npco/bff-fe-common": "zeller-engineering_component-bff-fe-common", "@npco/end-to-end-encryption": "zeller-engineering_component-end-to-end-encryption", "@npco/component-dto-core": "zeller-engineering_component-dto-common-core", "@npco/component-dto-addressbook": "zeller-engineering_component-dto-addressbook", "@npco/component-dto-cardholder": "zeller-engineering_component-dto-cardholder", "@npco/component-dto-cnp": "zeller-engineering_component-dto-cnp", "@npco/component-dto-configuration": "zeller-engineering_component-dto-configuration", "@npco/component-dto-connection": "zeller-engineering_component-dto-connection", "@npco/component-dto-cpoc": "zeller-engineering_component-dto-cpoc", "@npco/component-dto-customer": "zeller-engineering_component-dto-customer", "@npco/component-dto-debit-card": "zeller-engineering_component-dto-debit-card", "@npco/component-dto-deposit": "zeller-engineering_component-dto-deposit", "@npco/component-dto-device": "zeller-engineering_component-dto-device", "@npco/component-dto-digital-wallet-token": "zeller-engineering_component-dto-digital-wallet-token", "@npco/component-dto-entity": "zeller-engineering_component-dto-entity", "@npco/component-dto-fee": "zeller-engineering_component-dto-fee", "@npco/component-dto-invoice": "zeller-engineering_component-dto-invoice", "@npco/component-dto-issuing-card": "zeller-engineering_component-dto-issuing-card", "@npco/component-dto-issuing-account": "zeller-engineering_component-dto-issuing-account", "@npco/component-dto-issuing-transaction-statement": "zeller-engineering_component-dto-issuing-transaction-statement", "@npco/component-dto-issuing-transaction": "zeller-engineering_component-dto-issuing-transaction", "@npco/component-dto-issuing-statement-storage": "zeller-engineering_component-dto-issuing-statement-storage", "@npco/component-dto-merchant": "zeller-engineering_component-dto-merchant", "@npco/component-dto-notifications": "zeller-engineering_component-dto-notifications", "@npco/component-dto-payment-instrument": "zeller-engineering_component-dto-payment-instrument", "@npco/component-dto-pos-interface": "zeller-engineering_component-dto-pos-interface", "@npco/component-dto-promotion": "zeller-engineering_component-dto-promotion", "@npco/component-dto-purchase-advice-transaction-group": "zeller-engineering_component-dto-purchase-advice-transaction-group", "@npco/component-dto-richdata": "zeller-engineering_component-dto-richdata", "@npco/component-dto-sim": "zeller-engineering_component-dto-sim", "@npco/component-dto-site": "zeller-engineering_component-dto-site", "@npco/component-dto-subscription": "zeller-engineering_component-dto-subscription", "@npco/component-dto-test-utils": "zeller-engineering_component-dto-test-utils", "@npco/component-dto-third-party-bank-account": "zeller-engineering_component-dto-third-party-bank-account", "@npco/component-dto-ticket": "zeller-engineering_component-dto-ticket", "@npco/component-dto-transaction": "zeller-engineering_component-dto-transaction", "@npco/component-dto-transaction-account": "zeller-engineering_component-dto-transaction-account", "@npco/component-dto-xero": "zeller-engineering_component-dto-xero", "@npco/component-dto-catalog": "zeller-engineering_component-dto-catalog", "@npco/component-dto-order": "zeller-engineering_component-dto-order", "@npco/component-dto-scheduled-transfer": "zeller-engineering_component-dto-scheduled-transfer", "@npco/component-events-ais": "zeller-engineering_component-events-ais", "@npco/component-events-attribution": "zeller-engineering_component-events-attribution", "@npco/component-events-ces": "zeller-engineering_component-events-ces", "@npco/component-events-cims": "zeller-engineering_component-events-cims", "@npco/component-events-cnp": "zeller-engineering_component-events-cnp", "@npco/component-events-core": "zeller-engineering_component-events-core", "@npco/component-events-crms": "zeller-engineering_component-events-crms", "@npco/component-events-dbs": "zeller-engineering_component-events-dbs", "@npco/component-events-ers": "zeller-engineering_component-events-ers", "@npco/component-events-hlpos": "zeller-engineering_component-events-hlpos-cis", "@npco/component-events-ims": "zeller-engineering_component-events-ims", "@npco/component-events-mp": "zeller-engineering_component-events-mp", "@npco/component-events-nms": "zeller-engineering_component-events-nms", "@npco/component-events-oraclepos": "zeller-engineering_component-events-oraclepos-cis", "@npco/component-events-os": "zeller-engineering_component-events-os", "@npco/component-events-posconnector": "zeller-engineering_component-events-pos-cs", "@npco/component-events-proxy": "zeller-engineering_component-events-proxy", "@npco/component-events-ps": "zeller-engineering_component-events-ps", "@npco/component-events-reps": "zeller-engineering_component-events-reps", "@npco/component-events-sms": "zeller-engineering_component-events-sms", "@npco/component-events-ss": "zeller-engineering_component-events-ss", "@npco/component-events-zpos": "zeller-engineering_component-events-zposs", "@npco/component-bff-domain-events": "zeller-engineering_component-bff-domain-events", "@npco/component-bff-core": "SONAR_BFF_CORE_PROJECT_KEY", "@npco/component-bff-serverless": "SONAR_BFF_SERVERLESS_PROJECT_KEY", "@npco/component-dbs-mp-common": "SONAR_DBS_MP_COMMMON_PROJECT_KEY", "@npco/component-bff-cicd": "SONAR_BFF_CICD_PROJECT_KEY", "bff-rbac-engine": "SONAR_BFF_RBAC_ENGINE_PROJECT_KEY", "bff-permission-interface": "SONAR_BFF_PERMISSION_INTERFACE_PROJECT_KEY", "bff-addressbook-api": "SONAR_BFF_ADDRESSBOOK_API_PROJECT_KEY", "bff-pos-systems": "SONAR_BFF_POS_SYSTEMS_PROJECT_KEY", "bff-3rdaccount-api": "SONAR_BFF_3RDACCOUNT_API_PROJECT_KEY", "bff-core-go": "SONAR_BFF_CORE_GO_PROJECT_KEY", "crms-engine": "SONAR_CRMS_ENGINE_PROJECT_KEY", "posconnector-api": "SONAR_POSCONNECTOR_PROJECT_KEY", "bff-cims-api": "SONAR_BFF_CIMS_API_PROJECT_KEY", "bff-ims-api": "SONAR_BFF_IMS_API_PROJECT_KEY", "bff-zpos-api": "SONAR_BFF_ZPOS_API_PROJECT_KEY", "hlpos-engine": "SONAR_HLPOS_ENGINE_PROJECT_KEY", "oraclepos-engine": "SONAR_ORACLEPOS_ENGINE_PROJECT_KEY", "@npco/component-cnp-sdk": "SONAR_CNP_SDK_PROJECT_KEY", "cqrs-common": "SONAR_CQRS_COMMON_PROJECT_KEY", "bff-migration": "SONAR_BFF_MIGRATION_PROJECT_KEY", "att-engine": "SONAR_COMPONENT_ATT_ENGINE_PROJECT_KEY", "cnp-api": "SONAR_CNP_API_PROJECT_KEY", "dbs-api": "SONAR_DBS_API_PROJECT_KEY", "mp-api": "SONAR_MP_API_PROJECT_KEY", "bff-connections-service": "SONAR_BFF_CONNECTIONS_SERVICE_PROJECT_KEY", "ais-engine": "SONAR_AIS_ENGINE_PROJECT_KEY", "bff-banking-api": "SONAR_BFF_BANKING_API_PROJECT_KEY", "bff-cms-api": "SONAR_BFF_CMS_API_PROJECT_KEY", "bff-nms-api": "SONAR_BFF_NMS_API_PROJECT_KEY", "nms-engine": "SONAR_NMS_ENGINE_PROJECT_KEY", "ams-engine": "SONAR_AMS_ENGINE_PROJECT_KEY", "cims-engine": "SONAR_CIMS_ENGINE_PROJECT_KEY", "ims-engine": "SONAR_IMS_ENGINE_PROJECT_KEY", "ers-engine": "SONAR_ERS_ENGINE_PROJECT_KEY", "bff-customer-api": "SONAR_BFF_CUSTOMER_API_PROJECT_KEY", "@npco/component-bff-settlement-api": "SONAR_BFF_SETTLEMENT_API_PROJECT_KEY", "bff-device-api": "SONAR_BFF_DEVICE_API_PROJECT_KEY", "reps-engine": "SONAR_REPS_ENGINE_PROJECT_KEY", "reps-api": "SONAR_REPS_API_PROJECT_KEY", "bff-site-api": "SONAR_BFF_SITE_API_PROJECT_KEY", "bff-transaction-api": "SONAR_BFF_TRANSACTION_API_PROJECT_KEY", "bff-entity-api": "SONAR_BFF_ENTITY_API_PROJECT_KEY", "os-engine": "SONAR_OS_ENGINE_PROJECT_KEY", "sis-engine": "SONAR_SIS_ENGINE_PROJECT_KEY", "sms-engine": "SONAR_SMS_ENGINE_PROJECT_KEY", "bff-sms-api": "SONAR_BFF_SMS_API_PROJECT_KEY", "impos-interface": "SONAR_IMPOS_INTERFACE_PROJECT_KEY", "impos-ui": "SONAR_IMPOS_ENGINE_PROJECT_KEY", "cpi-tevalis-engine": "SONAR_CPI_TEVALIS_ENGINE_PROJECT_KEY"}, "slack-notification": {"alexwitting": "U017WE8K7DY", "zhaoyi0113": "U013HBP0763", "ejgargalicano": "U032W1HEPV1", "danielh016": "U03ECC3V3HU", "justin8953": "U05MZ966HFB", "ilievskizoran": "U0355GUEZ9V", "joseph-so": "U019MPZH456", "mon0051": "U02UAKH9KAQ", "Jack083": "U03NU4AJWEP", "LuBuss": "U02CWT1PJS0", "hutomo-zeller": "U05V1PECPU1", "Sujith1799": "U059VBNJ8BC", "AaronSuZeller": "U03THTMA1C5", "kathy-kk": "U02BW4JQ3HB", "Calvinsd": "U06K53UL6SH", "ShubhajitZ": "U05FAABEABW", "hemxnt-saini": "U07KLN81TU1", "zeller-ananya-nag": "U07EFMDTEU8", "yyang-zeller": "U08758V5QTG", "kamranzeller": "U082HR68JS3", "pradeepkshzeller": "U07ERUZ5Z8D", "niraj-patel-zr": "U08668MA4TH", "Imran109": "U087Q1F0WBA", "rishabh-ahuja007": "U08BXM8T8TF", "rittesh-pv": "U08DETH92CQ", "shabaan-qureshi": "U08J0CPBA1G", "roland-zeller": "U0152G07NR0", "SamSike": "U0820FNAPH8", "BFF": "S03SXUXT7KN"}}