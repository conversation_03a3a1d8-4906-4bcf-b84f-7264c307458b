name: bff-device-api-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/payments/device/api/**
      - .github/workflows/bff-device*
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        type: string
        required: true

jobs:
  destroy-bff-device-api:
    runs-on: depot-ubuntu-22.04-4
    strategy:
      matrix:
        component:
          - name: mp
            part: api
          - name: dbs
            part: api
          - name: crms
            part: engine
          - name: sdk
            part: api
    environment: st-${{ matrix.component.name }}-${{ matrix.component.part }}
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/teardown
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          stage: ${{ inputs.stage }}
          component_name: ${{ matrix.component.name }}
          part_name: ${{ matrix.component.part }}
          region: ${{ vars.SYDNEY_REGION }}
