name: ers-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      app_name: ers-engine
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: ers
      part_name: engine
      deploy_cqrs: true
      deployment_s3_bucket: false
      sonar_project_key: ${{ vars.SONAR_ERS_ENGINE_PROJECT_KEY }}
      use_workspace_focus: true

  deploy-cqrs:
    needs: pre-deploy
    uses: ./.github/workflows/component-cqrs.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: ers
      archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
      archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
      work_dir: apps/payments/ers/cqrs/cqrs-common
      use_sqs_projection: true
      sonar_project_key: ${{ vars.SONAR_CQRS_COMMON_PROJECT_KEY }}
      use_eventbus_projection: true

  cqrs-system-test:
    name: cqrs system test
    runs-on: depot-ubuntu-22.04
    if: false
    needs:
      - deploy-cqrs
    environment: ${{ inputs.environment }}-ers-cqrs
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/node-env
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          aws_region: ${{ inputs.region }}

      - run: |
          yarn install --immutable
          yarn nx build ers-cqrs-system-test
          cd apps/payments/ers/cqrs/system-test
          STAGE=${{ inputs.stage }} yarn system:test

  visual-testing:
    name: visual testing
    runs-on: depot-ubuntu-22.04
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/restore-archive
        if: needs.pre-deploy.outputs.archived_name != '' && needs.pre-deploy.outputs.archived_file_name != ''
        with:
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}

      - uses: ./.github/workflows/common/node-env
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
      - run: |
          yarn config set enableImmutableInstalls false
          yarn install
          yarn nx build ers-engine

        # cd apps/payments/ers/visual-testing/docker-tests
        # sh bin/run.sh -v true

  deploy-common:
    name: deploy common resources
    runs-on: depot-ubuntu-22.04
    needs:
      - visual-testing
      - pre-deploy
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: common resources
            sls_config: serverlessCommon.ts
    environment: ${{ inputs.environment }}-ers-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: ers-engine
          sls_config: ${{ matrix.stack.sls_config}}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/ers/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
  deploy-api:
    name: deploy api, jobs, monitoring
    runs-on: depot-ubuntu-22.04
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-common
    environment: ${{ inputs.environment }}-ers-engine
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: api
            sls_config: serverlessApi.ts
          - name: warmup
            sls_config: serverlessWarmup.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: ers-engine
          sls_config: ${{ matrix.stack.sls_config}}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/ers/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true
  deploy-appsync:
    name: deploy appsync
    runs-on: depot-ubuntu-22.04
    needs:
      - pre-deploy
      - deploy-cqrs
      - deploy-common
    environment: ${{ inputs.environment }}-ers-engine
    permissions:
      id-token: write
      contents: read
    strategy:
      fail-fast: false
      matrix:
        stack:
          - name: appsync
            sls_config: serverlessAppsync.ts
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          app_name: ers-engine
          sls_config: ${{ matrix.stack.sls_config}}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: apps/payments/ers/engine
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          use_workspace_focus: true

  engine-system-test:
    name: ers engine system test
    runs-on: depot-ubuntu-22.04
    environment: ${{ inputs.environment }}-ers-engine
    if: inputs.environment == 'st' || inputs.environment == 'dev'
    permissions:
      id-token: write
      contents: read
    needs:
      - pre-deploy
      - deploy-api
      - deploy-appsync
    strategy:
      fail-fast: false
      matrix:
        component: [{ name: api, fileName: receipt.spec.ts }]
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/aws-setup
        with:
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          aws_region: ${{ inputs.region }}

      - name: Run Codebuild
        uses: aws-actions/aws-codebuild-run-build@v1
        env:
          JfrogRegistry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          JfrogToken: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          STAGE: ${{ inputs.stage}}
        with:
          project-name: ${{inputs.environment}}-ers-engine
          buildspec-override: |
            version: 0.2
            env:
              shell: bash
            phases:
              pre_build:
                commands:
                  - env
                  - cat .yarnrc.yml
                  - yarn config set npmPublishRegistry https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmRegistryServer https://$JfrogRegistry
                  - yarn config set npmScopes.npco.npmAuthToken "$JfrogToken"
                  - yarn config set npmScopes.npco.npmAlwaysAuth true
                  - corepack enable

              build:
                commands:
                  - yarn workspaces focus component-bff ers-system-tests
                  - yarn nx build ers-system-tests
                  - cd apps/payments/ers/system-test
                  - export STAGE=${{ inputs.stage }}
                  - yarn run system-test src/${{ matrix.component.fileName }}
          env-vars-for-codebuild: |
            JfrogRegistry,
            JfrogToken,
            STAGE
  tag:
    runs-on: depot-ubuntu-22.04
    needs:
      - deploy-cqrs
    environment: ${{ inputs.environment }}-ers-engine
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-ers-engine
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: depot-ubuntu-22.04
    if: always()
    needs:
      - pre-deploy
      - tag
      - deploy-cqrs
      - cqrs-system-test
      - visual-testing
      - deploy-common
      - deploy-api
      - engine-system-test
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: ers-engine
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}
