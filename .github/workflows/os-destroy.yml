name: os-destroy

on:
  pull_request:
    types: [closed]
    paths:
      - apps/onboarding/os/cqrs/cqrs/**
      - apps/onboarding/os/engine/**
      - apps/onboarding/bff-onboarding/api/**
    branches:
      - develop

  workflow_dispatch:
    inputs:
      stage:
        type: string
        required: true

jobs:
  destroy-bff-onboarding-api:
    runs-on: depot-ubuntu-22.04-4
    environment: st-bff-onboarding-api
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4
      - name: setup env
        run: |
          echo STATIC_COMPONENT_NAME="mp-api" >> $GITHUB_ENV
          echo "delete mp-api resources"
      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: mp-api
          app_path: "apps/onboarding/bff-onboarding/api"
          stage: ${{ inputs.stage }}
          region: ${{ vars.SYDNEY_REGION }}

  destroy-os-engine:
    runs-on: depot-ubuntu-22.04-4
    environment: st-os-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: os-engine
          app_path: "apps/onboarding/os/engine"
          stage: ${{ inputs.stage }}
          region: ${{ vars.SYDNEY_REGION }}

  destroy-os-cqrs:
    runs-on: depot-ubuntu-22.04-4
    environment: st-os-cqrs
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/destroy
        with:
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
          role_to_assume: ${{ vars.ROLE_TO_ASSUME }}
          app_name: cqrs-common
          app_path: "apps/core/cqrs-common"
          stage: ${{ inputs.stage }}
          component_name: "os"
          destroy_cqrs: true
          region: ${{ vars.SYDNEY_REGION }}
