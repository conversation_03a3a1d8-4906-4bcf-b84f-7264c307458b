name: reps

on:
  push:
    paths:
      - apps/transactionalAccount/reps/**
      - .github/workflows/reps*
    branches:
      - develop
      - master

  pull_request:
    types:
      - opened
      - synchronize
    branches:
      - develop
    paths:
      - apps/transactionalAccount/reps/**
      - .github/workflows/reps*

  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string

concurrency:
  # only one workflow run at a time per branch
  group: '${{ github.workflow }} @ ${{ github.head_ref || github.ref }}'
  cancel-in-progress: false

jobs:
  detect-env:
    runs-on: depot-ubuntu-24.04-8
    outputs:
      environment: ${{ steps.env-detect.outputs.environment }}
      stage: ${{ steps.env-detect.outputs.stage }}
    steps:
      - uses: actions/checkout@v4
      - id: env-detect
        uses: ./.github/workflows/common/env-detect
        with:
          environment: ${{ inputs.environment }}

  assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    with:
      environment: ${{ needs.detect-env.outputs.environment }}
      component_name: reps
      part_name: engine

  reps-sydney:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/reps-stacks.yml
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume )[vars.SYDNEY_REGION]) }}

  reps-london:
    secrets: inherit
    needs:
      - detect-env
      - assume-roles
    uses: ./.github/workflows/reps-stacks.yml
    if: ${{ needs.detect-env.outputs.environment == 'dev' }}
    with:
      stage: ${{ needs.detect-env.outputs.stage }}
      environment: ${{ needs.detect-env.outputs.environment }}
      region: ${{ vars.LONDON_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.assume-roles.outputs.role_to_assume)[vars.LONDON_REGION]) }}

  prod-assume-roles:
    secrets: inherit
    needs: detect-env
    uses: ./.github/workflows/assume-roles.yml
    if: needs.detect-env.outputs.environment == 'staging'
    with:
      environment: prod
      component_name: reps
      part_name: engine

  print-prod-role:
    runs-on: depot-ubuntu-24.04-8
    needs: prod-assume-roles
    steps:
      - run: echo ${{ needs.prod-assume-roles.outputs.role_to_assume }}
      - run: echo ${{ fromJson(needs.prod-assume-roles.outputs.role_to_assume) }}

  reps-prod-sydney:
    secrets: inherit
    needs:
      - reps-sydney
      - detect-env
      - prod-assume-roles
    if: needs.detect-env.outputs.environment == 'staging'
    uses: ./.github/workflows/reps-stacks.yml
    with:
      stage: prod
      environment: prod
      region: ${{ vars.SYDNEY_REGION }}
      role_to_assume: ${{ toJson(fromJson(needs.prod-assume-roles.outputs.role_to_assume || '{}')[vars.SYDNEY_REGION]) }}
