name: cpi-tevalis-engine-stacks

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      stage:
        required: true
        type: string
      region:
        type: string
        default: ap-southeast-2
      role_to_assume:
        required: true
        type: string

jobs:
  pre-deploy:
    uses: ./.github/workflows/common-pre-deploy.yml
    secrets: inherit
    with:
      environment: ${{ inputs.environment }}
      stage: ${{ inputs.stage }}
      region: ${{ inputs.region }}
      component_name: cpi
      part_name: tevalis-engine
      sonar_project_key: ${{ vars.SONAR_CPI_TEVALIS_ENGINE_PROJECT_KEY }}
      app_name: cpi-tevalis-engine
      role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
      deployment_s3_bucket: true
      language: go
      deploy_cqrs: false

  deploy-api:
    name: deploy api
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
    environment: ${{ inputs.environment }}-cpi-tevalis-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cpi-tevalis-engine
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessApi.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/payments/cpi/tevalis/engine'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
  
  deploy-warmup:
    name: deploy warmup
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
    environment: ${{ inputs.environment }}-cpi-tevalis-engine
    permissions:
      id-token: write
      contents: read
    steps:
      - uses: actions/checkout@v4

      - uses: ./.github/workflows/common/sls-deploy
        with:
          environment: ${{ inputs.environment }}
          app_name: cpi-tevalis-engine
          stage: ${{ inputs.stage }}
          region: ${{ inputs.region }}
          sls_config: serverlessWarmup.ts
          role_to_assume: ${{ fromJson(inputs.role_to_assume)['engine_api'] }}
          archived_name: ${{ needs.pre-deploy.outputs.archived_name }}
          archived_file_name: ${{ needs.pre-deploy.outputs.archived_file_name }}
          work_dir: 'apps/payments/cpi/tevalis/engine'
          jfrog_email: ${{ secrets.NPCO_JFROG_EMAIL }}
          jfrog_token: ${{ secrets.NPCO_JFROG_ACCESS_TOKEN }}
          jfrog_registry: ${{ secrets.NPCO_JFROG_REGISTRY }}
  
  tag:
    runs-on: ubuntu-latest
    needs:
      - pre-deploy
      - deploy-api
      - deploy-warmup
    environment: ${{ inputs.environment }}-cpi-tevalis-engine
    if: inputs.environment == 'dev' || inputs.environment == 'staging' || inputs.environment == 'prod'
    steps:
      - uses: actions/checkout@v4
      - id: tag
        uses: ./.github/workflows/common/git-tag
        with:
          environment: ${{ inputs.environment }}-cpi-tevalis-engine
          ghPat: ${{ secrets.GH_PAT }}

  slack:
    name: Slack
    runs-on: ubuntu-latest
    if: always()
    needs:
      - pre-deploy
      - tag
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/common/component-slack
        with:
          app_name: cpi-tevalis-engine
          environment: ${{ inputs.environment }}
          jobs: ${{ toJson(needs) }}
          dev_channel: ${{ vars.SLACK_DEV_BFF_CHANNEL }}
          staging_channel: ${{ vars.SLACK_STAGING_BFF_CHANNEL }}
          prod_channel: ${{ vars.SLACK_PROD_BFF_CHANNEL }}