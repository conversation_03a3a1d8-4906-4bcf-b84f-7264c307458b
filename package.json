{"name": "component-bff", "version": "0.0.0", "scripts": {"affected": "yarn nx affected --target=build --base=master", "remove-cache-source": "cd .nx && find . -path '*/source' -delete", "affected:plain": "yarn nx show projects --affected --base=origin/master", "force-cancel": "sh .github/utils/gh-force-cancel.sh", "bulk-cancel": "sh .github/utils/gh-bulk-cancel.sh", "destroy-st": "sh .github/utils/destroy-st.sh", "destroy-st-all": "sh .github/utils/destroy-st-all.sh", "check-outstanding-stacks": "sh .github/utils/check-outstanding-stacks-on-closed-prs.sh", "check-outstanding-tables": "sh .github/utils/check-outstanding-dynamodb-on-closed-prs.sh", "postinstall": "husky install"}, "main": "", "private": true, "packageManager": "yarn@4.9.1", "dependencies": {"@firebase/logger": "^0.4.0", "@smithy/node-config-provider": "^2.1.6", "@smithy/smithy-client": "^2.3.1", "axios": "^1.8.2", "eslint-plugin-sonarjs": "^0.23.0", "glob": "^10.4.1", "jsonwebtoken": "^9.0.2", "tslib": "^2.3.0"}, "devDependencies": {"2-thenable": "^1.0.0", "@aws-sdk/types": "3.413.0", "@npco/eslint-config-backend": "^1.0.12", "@nx/devkit": "20.7.0", "@nx/eslint": "20.7.0", "@nx/eslint-plugin": "20.7.0", "@nx/jest": "20.7.0", "@nx/js": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@shelf/jest-dynamodb": "^3.4.2", "@smithy/types": "^2.6.0", "@swc-node/register": "^1.8.0", "@swc/core": "^1.3.85", "@swc/helpers": "~0.5.2", "@swc/jest": "^0.2.29", "@types/glob": "^8.1.0", "@types/heic-convert": "^1.2.0", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/mocha": "^10.0.1", "@types/node": "18.19.14", "@types/serverless": "^3.12.22", "@typescript-eslint/parser": "^5.38.0", "cache-manager": "*", "class-transformer": "*", "class-validator": "*", "esbuild": "0.19.12", "eslint": "^8.56.0", "husky": "8.0.1", "improved-yarn-audit": "^3.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.4.1", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "nx": "20.7.0", "prettier": "^2.6.2", "serverless": "^3.39.0", "serverless-dependson-plugin": "^1.1.2", "serverless-domain-manager": "^7.3.6", "serverless-dotenv-plugin": "^6.0.0", "serverless-esbuild": "^1.52.1", "serverless-plugin-resource-tagging": "^1.2.0", "serverless-plugin-tracing": "^2.0.0", "serverless-prune-plugin": "^2.0.2", "ts-jest": "^29.1.1", "ts-mockito": "^2.6.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "resolutions": {"debug": "4.3.1", "@babel/traverse": "7.23.2", "axios": "^1.8.2", "node-fetch": "2.6.7", "apollo-client": "2.6.10", "graphql": "^15.7.0", "apollo-link": "1.2.14", "apollo-utilities": "1.3.4", "apollo-link-http-common": "0.2.7", "aws-appsync-auth-link": "2.0.8", "apollo-link-retry": "2.2.7", "aws-sdk": "2.1502.0", "jose@>3.0.0 <5.0.0": "^4.15.5", "jose@<3.0.0": "^2.0.7", "@swc/core": "1.4.16", "aws-xray-sdk": "3.5.1", "aws-xray-sdk-core": "3.5.1"}, "@comment resolutions": {"aws-xray-sdk": "pin aws-xray-sdk & aws-xray-sdk-core to 3.5.1 to match aws sdk version and avoid the error: _X_AMZN_TRACE_ID contains invalid trace ID"}, "dependenciesMeta": {"snappy": {"built": false}}, "workspaces": ["!apps/bff-api/connections-service/system-testing", "!apps/accounting/ais/system-test", "!apps/payments/ims/visual-testing/**", "!apps/payments/ers/visual-testing/**", "!apps/payments/cnp/simulator/**", "!apps/payments/sms/layers/nodejs", "!apps/payments/pay-at-table/impos/simulator", "!apps/payments/pay-at-table/impos/ui", "apps/libs/**", "apps/core/**", "apps/bff-api/**", "apps/payments/**", "apps/accounting/**", "apps/transactionalAccount/**", "apps/onboarding/**", "!**/dist/**"]}