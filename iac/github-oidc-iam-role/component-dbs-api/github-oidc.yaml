Parameters:
  GitHubOrg:
    Description: Name of GitHub organization/user (case sensitive)
    Type: String
  GitHubBranch:
    Description: Name of GitHub branch (case sensitive)
    Type: String
  RepositoryName:
    Description: Name of GitHub repository (case sensitive)
    Type: String
  OIDCProviderArn:
    Description: Arn for the GitHub OIDC Provider.
    Type: String
  OIDCAudience:
    Description: Audience supplied to configure-aws-credentials.
    Type: String
  Environment:
    Type: String
  AccountID:
    Type: String
  Componentname:
    Type: String
  Partname:
    Type: String
  LambdaAccountID:
    Type: AWS::SSM::Parameter::Value<String>
    Default: 'Parameters-and-Secrets-Lambda-Extension'

Conditions:
  CreateOIDCProvider: !Equals [!Ref OIDCProviderArn, '']
Resources:
  GithubOidc:
    Type: AWS::IAM::OIDCProvider
    Condition: CreateOIDCProvider
    Properties:
      Url: https://token.actions.githubusercontent.com
      ClientIdList:
        - sts.amazonaws.com
      ThumbprintList:
        - 6938fd4d98bab03faadb97b34396831e3780aea1
  Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-github-actions-role']]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Action: sts:AssumeRoleWithWebIdentity
            Principal:
              Federated: !If
                - CreateOIDCProvider
                - !Ref GithubOidc
                - !Ref OIDCProviderArn
            Condition:
              StringLike:
                token.actions.githubusercontent.com:aud: !Ref OIDCAudience
                token.actions.githubusercontent.com:sub: !Sub repo:${GitHubOrg}/${RepositoryName}:${GitHubBranch}
      Policies:
        - PolicyName:
            !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-github-actions-inline-policy']]
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - 'events:Describe*'
                  - 'events:PutRule'
                  - 'events:RemoveTargets'
                  - 'events:DeleteRule'
                  - 'events:PutTargets'
                  - 'events:CreateEventBus'
                  - 'events:DeleteEventBus'
                  - 'events:DeleteRule'
                  - 'events:List*'
                  - 'events:*Tag*'
                  - 'events:PutEvents'
                Resource:
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:rule/*dbs*'
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/*dbs*'
              - Effect: Allow
                Action:
                  - 'dynamodb:Get*'
                  - 'dynamodb:List*'
                  - 'dynamodb:Describe*'
                  - 'dynamodb:CreateTable'
                  - 'dynamodb:CreateGlobalTable'
                  - 'dynamodb:DeleteTable'
                  - 'dynamodb:DeleteItem'
                  - 'dynamodb:PutItem'
                  - 'dynamodb:Query'
                  - 'dynamodb:TagResource'
                  - 'dynamodb:UpdateItem'
                  - 'dynamodb:UpdateContributorInsights'
                  - 'dynamodb:UpdateGlobalTable'
                  - 'dynamodb:UpdateGlobalTableSettings'
                  - 'dynamodb:UpdateTimeToLive'
                  - 'dynamodb:UpdateTableReplicaAutoScaling'
                  - 'dynamodb:UpdateTable'
                  - 'dynamodb:UpdateContinuousBackups'
                  - 'dynamodb:UntagResource'
                Resource:
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-dbs-*'
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-dbs-*/*'
                  - !Sub 'arn:aws:dynamodb:*:*:table/*ams-engine-DomicileLookup*'
                  - !Sub 'arn:aws:dynamodb:*:*:table/*ams-engine-DomicileLookup*/*'
              - Effect: Allow
                Action:
                  - 'ec2:Describe*'
                  - 'backup:*'
                  - 'backup-storage:*'
                  - 'ssm:Get*'
                  - 'ssm:List*'
                  - 'ssm:*Tag*'
                  - 's3:ListAllMyBuckets'
                  - 'cloudformation:List*'
                  - 'events:List*'
                  - 'dynamodb:List*'
                  - 'codedeploy:*Get*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:GetEventSourceMapping'
                  - 'lambda:DeleteEventSourceMapping'
                  - 'lambda:GetLayer*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                Resource:
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${LambdaAccountID}:layer:AWS-Parameters-and-Secrets-Lambda-Extension*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                  - 'lambda:CreateFunction*'
                  - 'lambda:CreateAlias*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:AddPermission'
                  - 'lambda:DeleteAlias'
                  - 'lambda:UpdateAlias'
                  - 'lambda:DeleteFunction'
                  - 'lambda:DeleteEventSourceMapping'
                  - 'lambda:PublishVersion'
                  - 'lambda:RemovePermission'
                  - 'lambda:TagResource'
                  - 'lambda:UntagResource'
                  - 'lambda:UpdateEventSourceMapping'
                  - 'lambda:UpdateFunctionCode'
                  - 'lambda:UpdateFunctionConfiguration'
                  - 'lambda:InvokeFunction'
                  - 'lambda:PutFunctionConcurrency'
                Resource:
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*-dbs-api-*'
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:event-source-mapping:*'
              - Effect: Allow
                Action:
                  - 'ssm:PutParameter'
                  - 'ssm:GetParameter'
                  - 'ssm:DeleteParameter'
                  - 'ssm:AddTagsToResource'
                  - 'ssm:RemoveTagsFromResource'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/*'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/cdk-bootstrap/*'
              - Effect: Allow
                Action:
                  - 'codebuild:*Get*'
                  - 'codebuild:*List*'
                  - 'codebuild:*Describe*'
                  - 'codebuild:CreateProject'
                  - 'codebuild:DeleteProject'
                  - 'codebuild:UpdateProject'
                  - 'codebuild:StartBuild'
                  - 'codebuild:CreateReportGroup'
                Resource:
                  - !Sub 'arn:aws:codebuild:${AWS::Region}:${AWS::AccountId}:*dbs-api*'
              - Effect: Allow
                Action:
                  - 'iam:CreateRole'
                  - 'iam:CreatePolicy'
                  - 'iam:UpdateRole'
                  - 'iam:UpdateRoleDescription'
                  - 'iam:AttachRolePolicy'
                  - 'iam:UpdateAssumeRolePolicy'
                  - 'iam:CreatePolicyVersion'
                  - 'iam:DeleteRole'
                  - 'iam:DeleteRolePolicy'
                  - 'iam:DeletePolicy'
                  - 'iam:DeletePolicyVersion'
                  - 'iam:Tag*'
                  - 'iam:List*'
                  - 'iam:PutRolePolicy'
                  - 'iam:PassRole'
                  - 'iam:Get*'
                  - 'iam:CreateUser'
                  - 'iam:DeleteUser'
                  - 'iam:*UserPolicy'
                  - 'iam:*AccessKey'
                  - 'iam:DetachRolePolicy'
                  - 'iam:DetachUserPolicy'
                Resource:
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/*dbs*'
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:policy/*dbs*'
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/cdk*'
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:user/*dbs*'
              - Effect: Allow
                Action:
                  - iam:CreateServiceLinkedRole
                Resource: '*'

              - Effect: Allow
                Action:
                  - 'cloudformation:CreateStack'
                  - 'cloudformation:CreateChangeSet'
                  - 'cloudformation:DescribeChangeSet'
                  - 'cloudformation:DescribeStacks'
                  - 'cloudformation:ExecuteChangeSet'
                  - 'cloudformation:DescribeStackEvents'
                  - 'cloudformation:ListStackResources'
                  - 'cloudformation:DescribeStackResources'
                  - 'cloudformation:DeleteChangeSet'
                  - 'cloudformation:DeleteStack'
                  - 'cloudformation:Get*'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                Resource:
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*dbs*'
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*dbs*/*'
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/CDKToolkit*'

              - Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                  - 's3:ListAllMyBuckets'
                  - 's3:ListBucket'
                  - 's3:GetBucketLocation'
                  - 's3:GetObjectVersion'
                  - 's3:DeleteObject'
                Resource:
                  - !Sub 'arn:aws:s3:::*dbs-api*'
                  - !Sub 'arn:aws:s3:::*dbs-cqrs*'
                  - !Sub 'arn:aws:s3:::*cdk*'
              - Effect: Allow
                Action:
                  - 'cloudformation:ValidateTemplate'
                  - 'cloudformation:List*'
                  - 'cloudformation:GetTemplate'
                  - 'cloudformation:Describe*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'logs:CreateLogGroup'
                  - 'logs:CreateLogStream'
                  - 'logs:DeleteLogGroup'
                  - 'logs:DeleteLogStream'
                  - 'logs:TagResource'
                  - 'logs:UntagResource'
                  - 'logs:DeleteResourcePolicy'
                  - 'logs:Describe*'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:*'

              - Effect: Allow
                Action:
                  - 'codepipeline:StartPipelineExecution'
                Resource:
                  - !Sub 'arn:aws:codepipeline:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'sqs:CreateQueue'
                  - 'sqs:DeleteQueue'
                  - 'sqs:PurgeQueue'
                  - 'sqs:*Permission*'
                  - 'sqs:*Tag*'
                  - 'sqs:SetQueueAttributes'
                  - 'sqs:Get*'
                  - 'sqs:List*'
                  - 'sqs:*Message*'
                Resource:
                  - !Sub 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:*dbs*'
              - Effect: Allow
                Action:
                  - 'appsync:CreateGraphqlApi'
                  - 'appsync:CreateApiKey'
                  - 'appsync:CreateDomainName'
                  - 'appsync:Get*'
                  - 'appsync:DeleteGraphqlApi'
                  - 'appsync:CreateApiKey'
                  - 'appsync:DeleteApiKey'
                  - 'appsync:List*'
                  - 'appsync:*Tag*'
                  - 'appsync:StartSchemaCreation'
                  - 'appsync:*Resolver*'
                  - 'appsync:*DataSource'
                  - 'appsync:UpdateApiKey'
                Resource:
                  - !Sub 'arn:aws:appsync:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'secretsmanager:Get*'
                  - 'secretsmanager:CreateSecret'
                  - 'secretsmanager:DeleteSecret'
                  - 'secretsmanager:Describe*'
                  - 'secretsmanager:List*'
                  - 'secretsmanager:*Tag*'
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:*dbs-api*'
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:pact*'
              - Effect: Allow
                Action:
                  - 'kms:CreateGrant'
                  - 'kms:DescribeKey'
                  - 'kms:RetireGrant'
                  - 'kms:Decrypt'
                  - 'kms:GenerateDataKey'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'appconfig:CreateApplication'
                  - 'appconfig:CreateConfigurationProfile'
                  - 'appconfig:CreateDeploymentStrategy'
                  - 'appconfig:CreateEnvironment'
                  - 'appconfig:CreateHostedConfigurationVersion'
                  - 'appconfig:DeleteApplication'
                  - 'appconfig:DeleteConfigurationProfile'
                  - 'appconfig:DeleteDeploymentStrategy'
                  - 'appconfig:DeleteEnvironment'
                  - 'appconfig:DeleteHostedConfigurationVersion'
                  - 'appconfig:UpdateApplication'
                  - 'appconfig:UpdateConfigurationProfile'
                  - 'appconfig:UpdateDeploymentStrategy'
                  - 'appconfig:UpdateEnvironment'
                  - 'appconfig:UpdateExtensionAssociation'
                  - 'appconfig:TagResource'
                  - 'appconfig:UntagResource'
                  - 'appconfig:StartDeployment'
                  - 'appconfig:GetDeployment'
                  - 'appconfig:StopDeployment'
                Resource:
                  - !Sub 'arn:aws:appconfig:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'codedeploy:CreateApplication'
                  - 'codedeploy:*Get*'
                  - 'codedeploy:*List*'
                  - 'codedeploy:*Tag*'
                  - 'codedeploy:StopDeployment'
                  - 'codedeploy:ContinueDeployment'
                  - 'codedeploy:CreateDeployment'
                  - 'codedeploy:CreateDeploymentConfig'
                  - 'codedeploy:CreateDeploymentGroup'
                  - 'codedeploy:DeleteApplication'
                  - 'codedeploy:DeleteDeploymentConfig'
                  - 'codedeploy:DeleteDeploymentGroup'
                  - 'codedeploy:RegisterApplicationRevision'
                Resource:
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:application:*dbs-api*'
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:deploymentgroup:*dbs-api*'
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:deploymentconfig:*'

Outputs:
  Role:
    Value: !GetAtt Role.Arn
