Parameters:
  GitHubOrg:
    Description: Name of GitHub organization/user (case sensitive)
    Type: String
  GitHubBranch:
    Description: Name of GitHub branch (case sensitive)
    Type: String
  RepositoryName:
    Description: Name of GitHub repository (case sensitive)
    Type: String
  OIDCProviderArn:
    Description: Arn for the GitHub OIDC Provider.
    Type: String
  OIDCAudience:
    Description: Audience supplied to configure-aws-credentials.
    Type: String
  Environment:
    Type: String
  AccountID:
    Type: String
  Componentname:
    Type: String
  Partname:
    Type: String

Conditions:
  CreateOIDCProvider: !Equals [!Ref OIDCProviderArn, '']
Resources:
  GithubOidc:
    Type: AWS::IAM::OIDCProvider
    Condition: CreateOIDCProvider
    Properties:
      Url: https://token.actions.githubusercontent.com
      ClientIdList:
        - sts.amazonaws.com
      ThumbprintList:
        - 6938fd4d98bab03faadb97b34396831e3780aea1
  Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-github-actions-role']]
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Action: sts:AssumeRoleWithWebIdentity
            Principal:
              Federated: !If
                - CreateOIDCProvider
                - !Ref GithubOidc
                - !Ref OIDCProviderArn
            Condition:
              StringLike:
                token.actions.githubusercontent.com:aud: !Ref OIDCAudience
                token.actions.githubusercontent.com:sub: !Sub repo:${GitHubOrg}/${RepositoryName}:${GitHubBranch}
      Policies:
        - PolicyName:
            !Join ['', [!Ref Environment, '-', !Ref Componentname, '-', !Ref Partname, '-github-actions-inline-policy']]
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - 'ssm:PutParameter'
                  - 'ssm:GetParameter'
                  - 'ssm:DeleteParameter'
                  - 'ssm:AddTagsToResource'
                  - 'ssm:GetParameters'
                  - 'ssm:RemoveTagsFromResource'
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/*'
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/cdk-bootstrap/*'
              - Effect: Allow
                Action:
                  - 'iam:CreateRole'
                  - 'iam:CreatePolicy'
                  - 'iam:UpdateRole'
                  - 'iam:UpdateRoleDescription'
                  - 'iam:AttachRolePolicy'
                  - 'iam:CreatePolicyVersion'
                  - 'iam:DeleteRole'
                  - 'iam:DeleteRolePolicy'
                  - 'iam:DeletePolicy'
                  - 'iam:DeletePolicyVersion'
                  - 'iam:TagRole'
                  - 'iam:TagPolicy'
                  - 'iam:ListRolePolicies'
                  - 'iam:ListPolicies'
                  - 'iam:ListRoles'
                  - 'iam:PutRolePolicy'
                  - 'iam:GetRole'
                  - 'iam:GetPolicy'
                  - 'iam:GetRolePolicy'
                  - 'iam:PassRole'
                  - 'iam:DetachRolePolicy'
                  - 'iam:ListPolicyVersions'
                Resource:
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/*'
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:policy/*'
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/cdk*'
              - Effect: Allow
                Action:
                  - 'cloudformation:CreateStack'
                  - 'cloudformation:CreateChangeSet'
                  - 'cloudformation:Describe*'
                  - 'cloudformation:ExecuteChangeSet'
                  - 'cloudformation:ListStackResources'
                  - 'cloudformation:DescribeStackResources'
                  - 'cloudformation:DeleteChangeSet'
                  - 'cloudformation:DeleteStack'
                  - 'cloudformation:Get*'
                  - 'cloudformation:List*'
                  - 'cloudformation:DescribeChangeSet'
                  - 'cloudformation:DescribeStacks'
                  - 'cloudformation:DescribeStackEvents'
                Resource:
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*sdk*'
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*sdk*/*'
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/CDKToolkit*'
              - Effect: Allow
                Action:
                  - 's3:Put*'
                  - 's3:List*'
                  - 's3:Get*'
                  - 's3:Delete*'
                  - 's3:Create*'
                  - 's3:Update*'
                  - 's3:*Tag*'
                Resource:
                  - !Sub 'arn:aws:s3:::*'
                  - !Sub 'arn:aws:s3:::*cdk*'
              - Effect: Allow
                Action:
                  - 'ec2:Describe*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:GetEventSourceMapping'
                  - 'lambda:DeleteEventSourceMapping'
                  - 'lambda:UpdateEventSourceMapping'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'lambda:Get*'
                  - 'lambda:List*'
                  - 'lambda:CreateFunction*'
                  - 'lambda:CreateAlias*'
                  - 'lambda:UpdateAlias*'
                  - 'lambda:CreateEventSourceMapping'
                  - 'lambda:AddPermission'
                  - 'lambda:DeleteAlias'
                  - 'lambda:DeleteFunction'
                  - 'lambda:DeleteEventSourceMapping'
                  - 'lambda:RemovePermission'
                  - 'lambda:TagResource'
                  - 'lambda:UntagResource'
                  - 'lambda:UpdateAlias'
                  - 'lambda:UpdateEventSourceMapping'
                  - 'lambda:UpdateFunctionCode'
                  - 'lambda:UpdateFunctionConfiguration'
                  - 'lambda:PublishVersion'
                  - 'lambda:InvokeFunction'
                  - 'lambda:PutFunctionConcurrency'
                  - 'lambda:PutFunctionEventInvokeConfig'
                  - 'lambda:DeleteFunctionConcurrency'
                  - 'lambda:DeleteFunctionEventInvokeConfig'
                  - 'lambda:GetLayerVersion'
                  - 'lambda:PublishVersion'
                Resource:
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*-sdk-api-*'
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*-mp-api-*'
                  - !Sub "arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:*-ams-*"
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:event-source-mapping:*'
                  - !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:layer:AWS-Parameters-and-Secrets-Lambda-Extension:*'
              - Effect: Allow
                Action:
                  - 'logs:CreateLogGroup'
                  - 'logs:CreateLogStream'
                  - 'logs:DeleteLogGroup'
                  - 'logs:DeleteLogStream'
                  - 'logs:TagResource'
                  - 'logs:UntagResource'
                  - 'logs:GetLogEvents'
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:*'
              - Effect: Allow
                Action:
                  - 'events:PutEvents'
                  - 'events:Describe*'
                  - 'events:PutRule'
                  - 'events:RemoveTargets'
                  - 'events:DeleteRule'
                  - 'events:PutTargets'
                  - 'events:TagResource'
                Resource:
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:rule/*sdk-api*'
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/*sdk-*'
                  - !Sub 'arn:aws:events:${AWS::Region}:${AWS::AccountId}:event-bus/*mp-*'
              - Effect: Allow
                Action:
                  - 'cloudformation:ValidateTemplate'
                  - 'cloudformation:GetTemplate'
                  - 'cloudformation:List*'
                  - 'cloudformation:Describe*'
                  - 's3:ListAllMyBuckets'
                  - 'cloudformation:List*'
                  - 'events:List*'
                  - 'dynamodb:List*'
                  - 'codedeploy:*Get*'
                Resource: '*'
              - Effect: Allow
                Action:
                  - 'sqs:CreateQueue'
                  - 'sqs:DeleteQueue'
                  - 'sqs:TagQueue'
                  - 'sqs:UntagQueue'
                  - 'sqs:AddPermission'
                  - 'sqs:SetQueueAttributes'
                  - 'sqs:Get*'
                  - 'sqs:List*'
                  - 'sqs:SendMessage'
                Resource:
                  - !Sub 'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'secretsmanager:Get*'
                  - 'secretsmanager:Describe*'
                  - 'secretsmanager:List*'
                  - 'secretsmanager:CreateSecret'
                  - 'secretsmanager:DeleteSecret'
                  - 'secretsmanager:PutSecretValue'
                  - 'secretsmanager:UpdateSecret'
                  - 'secretsmanager:TagResource'
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:*'
              - Effect: Allow
                Action:
                  - 'codepipeline:StartPipelineExecution'
                Resource:
                  - !Sub 'arn:aws:codepipeline:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'apigateway:PUT'
                  - 'apigateway:GET'
                  - 'apigateway:POST'
                  - 'apigateway:DELETE'
                  - 'apigateway:PATCH'
                  - 'apigateway:UpdateRestApiPolicy'
                Resource:
                  - !Sub 'arn:aws:apigateway:${AWS::Region}::*'
                  - !Sub 'arn:aws:apigateway:${AWS::Region}::/tags/*'
              - Effect: Allow
                Action:
                  - 'appsync:CreateGraphqlApi'
                  - 'appsync:UpdateGraphqlApi'
                  - 'appsync:CreateApiKey'
                  - 'appsync:CreateDomainName'
                  - 'appsync:Get*'
                  - 'appsync:DeleteGraphqlApi'
                  - 'appsync:CreateApiKey'
                  - 'appsync:DeleteApiKey'
                  - 'appsync:List*'
                  - 'appsync:*Tag*'
                  - 'appsync:StartSchemaCreation'
                  - 'appsync:*Resolver*'
                  - 'appsync:*DataSource'
                  - 'appsync:UpdateApiKey'
                Resource:
                  - !Sub 'arn:aws:appsync:${AWS::Region}:${AWS::AccountId}:*'
              - Effect: Allow
                Action:
                  - 'codebuild:StartBuild'
                  - 'codebuild:BatchGetBuilds'
                Resource:
                  - !Sub 'arn:aws:codebuild:${AWS::Region}:${AWS::AccountId}:project/*sdk*'
              - Effect: Allow
                Action:
                  - 'dynamodb:Get*'
                  - 'dynamodb:List*'
                  - 'dynamodb:Describe*'
                  - 'dynamodb:CreateTable'
                  - 'dynamodb:CreateGlobalTable'
                  - 'dynamodb:DeleteTable'
                  - 'dynamodb:DeleteItem'
                  - 'dynamodb:PutItem'
                  - 'dynamodb:Query'
                  - 'dynamodb:TagResource'
                  - 'dynamodb:UpdateItem'
                  - 'dynamodb:UpdateContributorInsights'
                  - 'dynamodb:UpdateGlobalTable'
                  - 'dynamodb:UpdateGlobalTableSettings'
                  - 'dynamodb:UpdateTimeToLive'
                  - 'dynamodb:UpdateTableReplicaAutoScaling'
                  - 'dynamodb:UpdateTable'
                  - 'dynamodb:UpdateContinuousBackups'
                  - 'dynamodb:UntagResource'
                Resource:
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-sdk-*'
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-sdk-*/*'
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-mp-*'
                  - !Sub 'arn:aws:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-mp-*/*'
                  - !Sub 'arn:aws:dynamodb:*:*:table/*ams-engine-DomicileLookup*'
                  - !Sub 'arn:aws:dynamodb:*:*:table/*ams-engine-DomicileLookup*/*'
              - Effect: Allow
                Action:
                  - 'codedeploy:CreateApplication'
                  - 'codedeploy:*Get*'
                  - 'codedeploy:*List*'
                  - 'codedeploy:*Tag*'
                  - 'codedeploy:StopDeployment'
                  - 'codedeploy:ContinueDeployment'
                  - 'codedeploy:CreateDeployment'
                  - 'codedeploy:CreateDeploymentConfig'
                  - 'codedeploy:CreateDeploymentGroup'
                  - 'codedeploy:DeleteApplication'
                  - 'codedeploy:DeleteDeploymentConfig'
                  - 'codedeploy:DeleteDeploymentGroup'
                  - 'codedeploy:RegisterApplicationRevision'
                Resource:
                  - !Sub 'arn:aws:codedeploy:${AWS::Region}:${AWS::AccountId}:*'
Outputs:
  Role:
    Value: !GetAtt Role.Arn
