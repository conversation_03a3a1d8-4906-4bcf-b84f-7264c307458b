data "github_repository" "repo" {
  full_name = var.repo_name
}


resource "github_actions_environment_variable" "BFF_3RDACCOUNT_API_ROLE_TO_ASSUME" {
  environment     = var.bff_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_3RDACCOUNT_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_3RDACCOUNT_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_3RDACCOUNT_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_3RDACCOUNT_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_3RDACCOUNT_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_3RDACCOUNT_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_3RDACCOUNT_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_3RDACCOUNT_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_3RDACCOUNT_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_3RDACCOUNT_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_3RDACCOUNT_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_3RDACCOUNT_API_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_3RDACCOUNT_API_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_3RDACCOUNT_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_3RDACCOUNT_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_3RDACCOUNT_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_3RDACCOUNT_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_3RDACCOUNT_API_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_3RDACCOUNT_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_3RDACCOUNT_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_3RDACCOUNT_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_3RDACCOUNT_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_3rdaccount_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_3RDACCOUNT_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "ORACLEPOS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.oraclepos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.ORACLEPOS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "ORACLEPOS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.oraclepos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.ORACLEPOS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "ORACLEPOS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.oraclepos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.ORACLEPOS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "AIS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.ais_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.AIS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "AIS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ais_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.AIS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "AIS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ais_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.AIS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.dbs_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_CQRS_ROLE_TO_ASSUME" {
  environment     = var.mp_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "AMS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.ams_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.AMS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "AMS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ams_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.AMS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "AMS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ams_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.AMS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.crms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "POSCONNECTOR_API_ROLE_TO_ASSUME" {
  environment     = var.posconnector_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.POSCONNECTOR_API_ROLE_ARN
}

resource "github_actions_environment_variable" "POSCONNECTOR_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.posconnector_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.POSCONNECTOR_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "POSCONNECTOR_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.posconnector_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.POSCONNECTOR_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "HLPOS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.hlpos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.HLPOS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "HLPOS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.hlpos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.HLPOS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "HLPOS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.hlpos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.HLPOS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "POSCONNECTOR_CQRS_ROLE_TO_ASSUME" {
  environment     = var.posconnector_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.POSCONNECTOR_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "POSCONNECTOR_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.posconnector_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.POSCONNECTOR_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "POSCONNECTOR_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.posconnector_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.POSCONNECTOR_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "HLPOS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.hlpos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.HLPOS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "HLPOS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.hlpos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.HLPOS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "HLPOS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.hlpos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.HLPOS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_CONNECTIONS_SERVICE_ROLE_TO_ASSUME" {
  environment     = var.bff_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_CONNECTIONS_SERVICE_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_CONNECTIONS_SERVICE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME" {
  environment     = var.mp_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_CONNECTIONS_SERVICE_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_CONNECTIONS_SERVICE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "SDK_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME" {
  environment     = var.sdk_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.SDK_API_CONNECTIONS_SERVICE_ROLE_ARN
}

resource "github_actions_environment_variable" "SDK_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.sdk_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.SDK_API_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "SDK_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.sdk_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.SDK_API_CONNECTIONS_SERVICE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "OS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.os_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.OS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "OS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.os_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.OS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "OS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.os_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.OS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "REPS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.reps_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.REPS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "REPS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.reps_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.REPS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "REPS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.reps_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.REPS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_BANKING_API_ROLE_TO_ASSUME" {
  environment     = var.bff_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_BANKING_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_BANKING_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_BANKING_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_BANKING_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_BANKING_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_CIMS_API_ROLE_TO_ASSUME" {
  environment     = var.bff_cims_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_CIMS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_CIMS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_cims_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_CIMS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_CIMS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_cims_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_CIMS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_MIGRATION_ROLE_TO_ASSUME" {
  environment     = var.bff_migration_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_MIGRATION_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_MIGRATION_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_migration_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_MIGRATION_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_MIGRATION_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_migration_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_MIGRATION_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CNP_API_ROLE_TO_ASSUME" {
  environment     = var.cnp_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CNP_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CNP_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.cnp_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CNP_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CNP_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.cnp_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CNP_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CNP_CQRS_ROLE_TO_ASSUME" {
  environment     = var.cnp_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CNP_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "CNP_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.cnp_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CNP_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CNP_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.cnp_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CNP_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "AIS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.ais_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.AIS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "AIS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ais_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.AIS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "AIS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ais_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.AIS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "AMS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.ams_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.AMS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "AMS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ams_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.AMS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "AMS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ams_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.AMS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_CONNECTIONS_SERVICE_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_CONNECTIONS_SERVICE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_connections_service_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_CONNECTIONS_SERVICE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_CIMS_ROLE_TO_ASSUME" {
  environment     = var.mp_api_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_CIMS_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_CIMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_CIMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_CIMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_CIMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_CIMS_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_CIMS_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_CIMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_CIMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_CIMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_CIMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CIMS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.cims_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CIMS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "CIMS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.cims_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CIMS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CIMS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.cims_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CIMS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_BANKING_API_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_BANKING_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_BANKING_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_BANKING_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_BANKING_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_BANKING_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_BANKING_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_BANKING_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_BANKING_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_BANKING_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_BANKING_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_BANKING_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "ATT_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.att_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.ATT_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "ATT_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.att_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.ATT_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "ATT_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.att_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.ATT_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_HOLDS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.bff_holds_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_HOLDS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_HOLDS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_holds_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_HOLDS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_HOLDS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_holds_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_HOLDS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_NMS_API_ROLE_TO_ASSUME" {
  environment     = var.bff_nms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_NMS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_NMS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_nms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_NMS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_NMS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_nms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_NMS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CIMS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.cims_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CIMS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "CIMS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.cims_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CIMS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CIMS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.cims_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CIMS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CNP_TESTS_ROLE_TO_ASSUME" {
  environment     = var.cnp_tests_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CNP_TESTS_ROLE_ARN
}

resource "github_actions_environment_variable" "CNP_TESTS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.cnp_tests_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CNP_TESTS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CNP_TESTS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.cnp_tests_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CNP_TESTS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "IMS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.ims_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.IMS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "IMS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ims_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.IMS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "IMS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ims_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.IMS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "NMS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.nms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.NMS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "NMS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.nms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.NMS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "NMS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.nms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.NMS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "ORACLEPOS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.oraclepos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.ORACLEPOS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "ORACLEPOS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.oraclepos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.ORACLEPOS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "ORACLEPOS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.oraclepos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.ORACLEPOS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_ADDRESSBOOK_API_ROLE_TO_ASSUME" {
  environment     = var.bff_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_ADDRESSBOOK_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_ADDRESSBOOK_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_ADDRESSBOOK_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_ADDRESSBOOK_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_ADDRESSBOOK_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_ADDRESSBOOK_API_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_ADDRESSBOOK_API_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_ADDRESSBOOK_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_ADDRESSBOOK_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_ADDRESSBOOK_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_ADDRESSBOOK_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_ADDRESSBOOK_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_ADDRESSBOOK_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_ADDRESSBOOK_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_ADDRESSBOOK_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_ADDRESSBOOK_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_ADDRESSBOOK_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ADDRESSBOOK_API_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_ADDRESSBOOK_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ADDRESSBOOK_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_ADDRESSBOOK_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ADDRESSBOOK_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_addressbook_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_ADDRESSBOOK_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_IMS_ROLE_TO_ASSUME" {
  environment     = var.mp_api_ims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_IMS_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_IMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_ims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_IMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_IMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_ims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_IMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_IMS_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_ims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_IMS_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_IMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_ims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_IMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_IMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_ims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_IMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "IMS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.ims_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.IMS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "IMS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ims_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.IMS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "IMS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ims_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.IMS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_BANKING_API_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_BANKING_API_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_BANKING_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_BANKING_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_BANKING_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_banking_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_BANKING_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CE_CQRS_ROLE_TO_ASSUME" {
  environment     = var.ce_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CE_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "CE_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ce_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CE_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CE_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ce_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CE_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "ATT_CQRS_ROLE_TO_ASSUME" {
  environment     = var.att_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.ATT_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "ATT_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.att_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.ATT_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "ATT_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.att_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.ATT_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_POS_SYSTEMS_ROLE_TO_ASSUME" {
  environment     = var.bff_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_POS_SYSTEMS_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_POS_SYSTEMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_POS_SYSTEMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_POS_SYSTEMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_POS_SYSTEMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_POS_SYSTEMS_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_POS_SYSTEMS_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_POS_SYSTEMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_POS_SYSTEMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_POS_SYSTEMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_POS_SYSTEMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_POS_SYSTEMS_ROLE_TO_ASSUME" {
  environment     = var.mp_api_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_POS_SYSTEMS_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_POS_SYSTEMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_POS_SYSTEMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_POS_SYSTEMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_POS_SYSTEMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_POS_SYSTEMS_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_POS_SYSTEMS_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_POS_SYSTEMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_POS_SYSTEMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_POS_SYSTEMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_pos_systems_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_POS_SYSTEMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "ERS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.ers_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.ERS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "ERS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ers_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.ERS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "ERS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ers_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.ERS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "ERS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.ers_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.ERS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "ERS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ers_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.ERS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "ERS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ers_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.ERS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "SDK_API_ROLE_TO_ASSUME" {
  environment     = var.sdk_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.SDK_API_ROLE_ARN
}

resource "github_actions_environment_variable" "SDK_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.sdk_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.SDK_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "SDK_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.sdk_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.SDK_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "SDK_CQRS_ROLE_TO_ASSUME" {
  environment     = var.sdk_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.SDK_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "SDK_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.sdk_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.SDK_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "SDK_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.sdk_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.SDK_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_SI_ROLE_TO_ASSUME" {
  environment     = var.dbs_si_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_SI_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_SI_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_si_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_SI_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_SI_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_si_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_SI_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_SETTLEMENT_API_ROLE_TO_ASSUME" {
  environment     = var.bff_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_SETTLEMENT_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_SETTLEMENT_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_SETTLEMENT_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_SETTLEMENT_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_SETTLEMENT_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_CUSTOMER_API_ROLE_TO_ASSUME" {
  environment     = var.bff_customer_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_CUSTOMER_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_CUSTOMER_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_customer_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_CUSTOMER_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_CUSTOMER_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_customer_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_CUSTOMER_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_SETTLEMENT_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_SETTLEMENT_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_SETTLEMENT_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_SETTLEMENT_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_SETTLEMENT_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_SETTLEMENT_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_ONBOARDING_API_ROLE_TO_ASSUME" {
  environment     = var.bff_onboarding_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_ONBOARDING_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_ONBOARDING_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_onboarding_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_ONBOARDING_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_ONBOARDING_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_onboarding_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_ONBOARDING_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_SETTLEMENT_API_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_SETTLEMENT_API_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_SETTLEMENT_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_SETTLEMENT_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_SETTLEMENT_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_SETTLEMENT_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_SETTLEMENT_API_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_SETTLEMENT_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_SETTLEMENT_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_SETTLEMENT_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_SETTLEMENT_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_settlement_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_SETTLEMENT_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_ECOMMERCE_API_ROLE_TO_ASSUME" {
  environment     = var.bff_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_ECOMMERCE_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_ECOMMERCE_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_ECOMMERCE_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_ECOMMERCE_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_ECOMMERCE_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_ECOMMERCE_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_ECOMMERCE_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_ECOMMERCE_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_ECOMMERCE_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_ECOMMERCE_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_ECOMMERCE_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ECOMMERCE_API_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_ECOMMERCE_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ECOMMERCE_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_ECOMMERCE_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ECOMMERCE_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_ecommerce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_ECOMMERCE_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "REPS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.reps_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.REPS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "REPS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.reps_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.REPS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "REPS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.reps_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.REPS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "REPS_API_ROLE_TO_ASSUME" {
  environment     = var.reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.REPS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "REPS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.REPS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "REPS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.REPS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "OS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.os_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.OS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "OS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.os_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.OS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "OS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.os_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.OS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_CIMS_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_CIMS_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_CIMS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_CIMS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_CIMS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_cims_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_CIMS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "ZPOS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.zpos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.ZPOS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "ZPOS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.zpos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.ZPOS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "ZPOS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.zpos_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.ZPOS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "ZPOS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.zpos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.ZPOS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "ZPOS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.zpos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.ZPOS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "ZPOS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.zpos_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.ZPOS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_ZPOS_API_ROLE_TO_ASSUME" {
  environment     = var.bff_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_ZPOS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_ZPOS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_ZPOS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_ZPOS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_ZPOS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_ZPOS_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_ZPOS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_ZPOS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_ZPOS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_ZPOS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_ZPOS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_ZPOS_API_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_ZPOS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_ZPOS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_ZPOS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_ZPOS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_ZPOS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ZPOS_API_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_ZPOS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ZPOS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_ZPOS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_ZPOS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_zpos_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_ZPOS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "SIS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.sis_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.SIS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "SIS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.sis_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.SIS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "SIS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.sis_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.SIS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "SIS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.sis_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.SIS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "SIS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.sis_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.SIS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "SIS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.sis_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.SIS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_RBAC_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.bff_rbac_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_RBAC_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_RBAC_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_rbac_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_RBAC_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_RBAC_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_rbac_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_RBAC_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_INTEGRATION_TESTS_ROLE_TO_ASSUME" {
  environment     = var.bff_integration_tests_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_INTEGRATION_TESTS_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_INTEGRATION_TESTS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_integration_tests_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_INTEGRATION_TESTS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_INTEGRATION_TESTS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_integration_tests_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_INTEGRATION_TESTS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_REPS_API_ROLE_TO_ASSUME" {
  environment     = var.bff_reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_REPS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_REPS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_REPS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_REPS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_REPS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_REPS_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_REPS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_REPS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_REPS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_REPS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_reps_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_REPS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "NMS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.nms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.NMS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "NMS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.nms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.NMS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "NMS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.nms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.NMS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "SMS_CQRS_ROLE_TO_ASSUME" {
  environment     = var.sms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.SMS_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "SMS_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.sms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.SMS_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "SMS_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.sms_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.SMS_CQRS_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "SMS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.sms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.SMS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "SMS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.sms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.SMS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "SMS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.sms_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.SMS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "BFF_SMS_API_ROLE_TO_ASSUME" {
  environment     = var.bff_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_SMS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_SMS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_SMS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_SMS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_SMS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "DBS_API_SMS_API_ROLE_TO_ASSUME" {
  environment     = var.dbs_api_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.DBS_API_SMS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "DBS_API_SMS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.dbs_api_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.DBS_API_SMS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "DBS_API_SMS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.dbs_api_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.DBS_API_SMS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "MP_API_SMS_API_ROLE_TO_ASSUME" {
  environment     = var.mp_api_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.MP_API_SMS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "MP_API_SMS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.mp_api_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.MP_API_SMS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "MP_API_SMS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.mp_api_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.MP_API_SMS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CRMS_ENGINE_SMS_API_ROLE_TO_ASSUME" {
  environment     = var.crms_engine_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CRMS_ENGINE_SMS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CRMS_ENGINE_SMS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.crms_engine_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CRMS_ENGINE_SMS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CRMS_ENGINE_SMS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.crms_engine_sms_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CRMS_ENGINE_SMS_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CE_API_ROLE_TO_ASSUME" {
  environment     = var.ce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CE_API_ROLE_ARN
}

resource "github_actions_environment_variable" "CE_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.ce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CE_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CE_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.ce_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CE_API_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "IMPOS_UI_ROLE_TO_ASSUME" {
  environment     = var.impos_ui_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.IMPOS_UI_ROLE_ARN
}

resource "github_actions_environment_variable" "IMPOS_UI_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.impos_ui_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.IMPOS_UI_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "IMPOS_UI_ROLE_TO_ASSUME_LONDON" {
  environment     = var.impos_ui_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.IMPOS_UI_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CPI_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.cpi_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CPI_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "CPI_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.cpi_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CPI_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CPI_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.cpi_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CPI_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CPI_TEVALIS_ENGINE_ROLE_TO_ASSUME" {
  environment     = var.cpi_tevalis_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CPI_TEVALIS_ENGINE_ROLE_ARN
}

resource "github_actions_environment_variable" "CPI_TEVALIS_ENGINE_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.cpi_tevalis_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CPI_TEVALIS_ENGINE_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CPI_TEVALIS_ENGINE_ROLE_TO_ASSUME_LONDON" {
  environment     = var.cpi_tevalis_engine_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CPI_TEVALIS_ENGINE_ROLE_ARN_LONDON
}

resource "github_actions_environment_variable" "CPI_CQRS_ROLE_TO_ASSUME" {
  environment     = var.cpi_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.CPI_CQRS_ROLE_ARN
}

resource "github_actions_environment_variable" "CPI_CQRS_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.cpi_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.CPI_CQRS_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "CPI_CQRS_ROLE_TO_ASSUME_LONDON" {
  environment     = var.cpi_cqrs_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.CPI_CQRS_ROLE_ARN_LONDON
}


resource "github_actions_environment_variable" "BFF_IMS_API_ROLE_TO_ASSUME" {
  environment     = var.bff_ims_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME"
  value = var.BFF_IMS_API_ROLE_ARN
}

resource "github_actions_environment_variable" "BFF_IMS_API_ROLE_TO_ASSUME_SYDNEY" {
  environment     = var.bff_ims_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_SYDNEY"
  value = var.BFF_IMS_API_ROLE_ARN_SYDNEY
}

resource "github_actions_environment_variable" "BFF_IMS_API_ROLE_TO_ASSUME_LONDON" {
  environment     = var.bff_ims_api_environment
  repository      = data.github_repository.repo.name
  variable_name     = "ROLE_TO_ASSUME_LONDON"
  value = var.BFF_IMS_API_ROLE_ARN_LONDON
}
