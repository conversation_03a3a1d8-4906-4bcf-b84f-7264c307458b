variable "GITHUB_APP_ID" {
  type = string
}

variable "GITHUB_APP_INSTALLATION_ID" {
  type = string
}

variable "GITHUB_APP_PEM_FILE" {
  type = string
}


variable "repo_name" {
  type = string
}


variable "bff_3rdaccount_api_environment" {
  type = string
}

variable "BFF_3RDACCOUNT_API_ROLE_ARN" {
  type = string
}

variable "BFF_3RDACCOUNT_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_3RDACCOUNT_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_3rdaccount_api_environment" {
  type = string
}

variable "MP_API_3RDACCOUNT_API_ROLE_ARN" {
  type = string
}

variable "MP_API_3RDACCOUNT_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_3RDACCOUNT_API_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_3rdaccount_api_environment" {
  type = string
}

variable "DBS_API_3RDACCOUNT_API_ROLE_ARN" {
  type = string
}

variable "DBS_API_3RDACCOUNT_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_3RDACCOUNT_API_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_3rdaccount_api_environment" {
  type = string
}

variable "CRMS_ENGINE_3RDACCOUNT_API_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_3RDACCOUNT_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_3RDACCOUNT_API_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_environment" {
  type = string
}

variable "CRMS_ENGINE_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_connections_service_environment" {
  type = string
}

variable "BFF_CONNECTIONS_SERVICE_ROLE_ARN" {
  type = string
}

variable "BFF_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_CONNECTIONS_SERVICE_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_connections_service_environment" {
  type = string
}

variable "CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_CONNECTIONS_SERVICE_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_connections_service_environment" {
  type = string
}

variable "MP_API_CONNECTIONS_SERVICE_ROLE_ARN" {
  type = string
}

variable "MP_API_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_CONNECTIONS_SERVICE_ROLE_ARN_LONDON" {
  type = string
}

variable "sdk_api_connections_service_environment" {
  type = string
}

variable "SDK_API_CONNECTIONS_SERVICE_ROLE_ARN" {
  type = string
}

variable "SDK_API_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "SDK_API_CONNECTIONS_SERVICE_ROLE_ARN_LONDON" {
  type = string
}

variable "oraclepos_engine_environment" {
  type = string
}

variable "ORACLEPOS_ENGINE_ROLE_ARN" {
  type = string
}

variable "ORACLEPOS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "ORACLEPOS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "oraclepos_cqrs_environment" {
  type = string
}

variable "ORACLEPOS_CQRS_ROLE_ARN" {
  type = string
}

variable "ORACLEPOS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "ORACLEPOS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_cqrs_environment" {
  type = string
}

variable "DBS_CQRS_ROLE_ARN" {
  type = string
}

variable "DBS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_cqrs_environment" {
  type = string
}

variable "MP_CQRS_ROLE_ARN" {
  type = string
}

variable "MP_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "ams_cqrs_environment" {
  type = string
}

variable "AMS_CQRS_ROLE_ARN" {
  type = string
}

variable "AMS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "AMS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_cqrs_environment" {
  type = string
}

variable "CRMS_CQRS_ROLE_ARN" {
  type = string
}

variable "CRMS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "ais_cqrs_environment" {
  type = string
}

variable "AIS_CQRS_ROLE_ARN" {
  type = string
}

variable "AIS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "AIS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_environment" {
  type = string
}

variable "DBS_API_ROLE_ARN" {
  type = string
}

variable "DBS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "os_cqrs_environment" {
  type = string
}

variable "OS_CQRS_ROLE_ARN" {
  type = string
}

variable "OS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "OS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "MP_API_ROLE_ARN" {
  type = string
}

variable "MP_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_environment" {
  type = string
}

variable "CIMS_CQRS_ROLE_ARN" {
  type = string
}

variable "CIMS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CIMS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "cims_cqrs_environment" {
  type = string
}

variable "NMS_CQRS_ROLE_ARN" {
  type = string
}

variable "NMS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "NMS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "nms_cqrs_environment" {
  type = string
}

variable "CNP_CQRS_ROLE_ARN" {
  type = string
}

variable "CNP_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CNP_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "cnp_cqrs_environment" {
  type = string
}

variable "REPS_CQRS_ROLE_ARN" {
  type = string
}

variable "REPS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "REPS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "reps_cqrs_environment" {
  type = string
}

variable "bff_banking_api_environment" {
  type = string
}

variable "BFF_BANKING_API_ROLE_ARN" {
  type = string
}

variable "BFF_BANKING_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_BANKING_API_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_banking_api_environment" {
  type = string
}

variable "CRMS_ENGINE_BANKING_API_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_BANKING_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_BANKING_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_banking_api_environment" {
  type = string
}

variable "MP_API_BANKING_API_ROLE_ARN" {
  type = string
}

variable "MP_API_BANKING_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_BANKING_API_ROLE_ARN_LONDON" {
  type = string
}

variable "ams_engine_environment" {
  type = string
}

variable "AMS_ENGINE_ROLE_ARN" {
  type = string
}

variable "AMS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "AMS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_cims_api_environment" {
  type = string
}

variable "BFF_CIMS_API_ROLE_ARN" {
  type = string
}

variable "BFF_CIMS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_CIMS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "ais_engine_environment" {
  type = string
}

variable "AIS_ENGINE_ROLE_ARN" {
  type = string
}

variable "AIS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "AIS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "att_engine_environment" {
  type = string
}

variable "ATT_ENGINE_ROLE_ARN" {
  type = string
}

variable "ATT_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "ATT_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_holds_engine_environment" {
  type = string
}

variable "BFF_HOLDS_ENGINE_ROLE_ARN" {
  type = string
}

variable "BFF_HOLDS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_HOLDS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_migration_environment" {
  type = string
}

variable "BFF_MIGRATION_ROLE_ARN" {
  type = string
}

variable "BFF_MIGRATION_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_MIGRATION_ROLE_ARN_LONDON" {
  type = string
}

variable "cnp_api_environment" {
  type = string
}

variable "CNP_API_ROLE_ARN" {
  type = string
}

variable "CNP_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CNP_API_ROLE_ARN_LONDON" {
  type = string
}

variable "hlpos_cqrs_environment" {
  type = string
}

variable "HLPOS_CQRS_ROLE_ARN" {
  type = string
}

variable "HLPOS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "HLPOS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "hlpos_engine_environment" {
  type = string
}

variable "HLPOS_ENGINE_ROLE_ARN" {
  type = string
}

variable "HLPOS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "HLPOS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "posconnector_api_environment" {
  type = string
}

variable "POSCONNECTOR_API_ROLE_ARN" {
  type = string
}

variable "POSCONNECTOR_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "POSCONNECTOR_API_ROLE_ARN_LONDON" {
  type = string
}

variable "posconnector_cqrs_environment" {
  type = string
}

variable "POSCONNECTOR_CQRS_ROLE_ARN" {
  type = string
}

variable "POSCONNECTOR_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "POSCONNECTOR_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_nms_api_environment" {
  type = string
}

variable "BFF_NMS_API_ROLE_ARN" {
  type = string
}

variable "BFF_NMS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_NMS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "ims_cqrs_environment" {
  type = string
}

variable "IMS_CQRS_ROLE_ARN" {
  type = string
}

variable "IMS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "IMS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_cims_environment" {
  type = string
}

variable "MP_API_CIMS_ROLE_ARN" {
  type = string
}

variable "MP_API_CIMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_CIMS_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_cims_environment" {
  type = string
}

variable "CRMS_ENGINE_CIMS_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_CIMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_CIMS_ROLE_ARN_LONDON" {
  type = string
}

variable "cims_engine_environment" {
  type = string
}

variable "CIMS_ENGINE_ROLE_ARN" {
  type = string
}

variable "CIMS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CIMS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_connections_service_environment" {
  type = string
}

variable "DBS_API_CONNECTIONS_SERVICE_ROLE_ARN" {
  type = string
}

variable "DBS_API_CONNECTIONS_SERVICE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_CONNECTIONS_SERVICE_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_addressbook_api_environment" {
  type = string
}

variable "BFF_ADDRESSBOOK_API_ROLE_ARN" {
  type = string
}

variable "BFF_ADDRESSBOOK_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_ADDRESSBOOK_API_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_addressbook_api_environment" {
  type = string
}

variable "DBS_API_ADDRESSBOOK_API_ROLE_ARN" {
  type = string
}

variable "DBS_API_ADDRESSBOOK_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_ADDRESSBOOK_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_addressbook_api_environment" {
  type = string
}

variable "MP_API_ADDRESSBOOK_API_ROLE_ARN" {
  type = string
}

variable "MP_API_ADDRESSBOOK_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_ADDRESSBOOK_API_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_addressbook_api_environment" {
  type = string
}

variable "CRMS_ENGINE_ADDRESSBOOK_API_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_ADDRESSBOOK_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_ADDRESSBOOK_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_ims_environment" {
  type = string
}

variable "MP_API_IMS_ROLE_ARN" {
  type = string
}

variable "MP_API_IMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_IMS_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_ims_environment" {
  type = string
}

variable "CRMS_ENGINE_IMS_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_IMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_IMS_ROLE_ARN_LONDON" {
  type = string
}

variable "ims_engine_environment" {
  type = string
}

variable "IMS_ENGINE_ROLE_ARN" {
  type = string
}

variable "IMS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "IMS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "ers_cqrs_environment" {
  type = string
}

variable "ERS_CQRS_ROLE_ARN" {
  type = string
}

variable "ERS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "ERS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "sdk_cqrs_environment" {
  type = string
}

variable "SDK_CQRS_ROLE_ARN" {
  type = string
}

variable "SDK_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "SDK_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "os_engine_environment" {
  type = string
}

variable "OS_ENGINE_ROLE_ARN" {
  type = string
}

variable "OS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "OS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_banking_api_environment" {
  type = string
}

variable "DBS_API_BANKING_API_ROLE_ARN" {
  type = string
}

variable "DBS_API_BANKING_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_BANKING_API_ROLE_ARN_LONDON" {
  type = string
}

variable "ers_engine_environment" {
  type = string
}

variable "ERS_ENGINE_ROLE_ARN" {
  type = string
}

variable "ERS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "ERS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "sdk_api_environment" {
  type = string
}

variable "SDK_API_ROLE_ARN" {
  type = string
}

variable "SDK_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "SDK_API_ROLE_ARN_LONDON" {
  type = string
}

variable "ce_cqrs_environment" {
  type = string
}

variable "CE_CQRS_ROLE_ARN" {
  type = string
}

variable "CE_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CE_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_systemtest_infra_environment" {
  type = string
}

variable "BFF_SYSTEMTEST_INFRA_ROLE_ARN" {
  type = string
}

variable "BFF_SYSTEMTEST_INFRA_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_SYSTEMTEST_INFRA_ROLE_ARN_LONDON" {
  type = string
}

variable "att_cqrs_environment" {
  type = string
}

variable "ATT_CQRS_ROLE_ARN" {
  type = string
}

variable "ATT_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "ATT_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_pos_systems_environment" {
  type = string
}

variable "BFF_POS_SYSTEMS_ROLE_ARN" {
  type = string
}

variable "BFF_POS_SYSTEMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_POS_SYSTEMS_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_pos_systems_environment" {
  type = string
}

variable "DBS_API_POS_SYSTEMS_ROLE_ARN" {
  type = string
}

variable "DBS_API_POS_SYSTEMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_POS_SYSTEMS_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_pos_systems_environment" {
  type = string
}

variable "MP_API_POS_SYSTEMS_ROLE_ARN" {
  type = string
}

variable "MP_API_POS_SYSTEMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_POS_SYSTEMS_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_pos_systems_environment" {
  type = string
}

variable "CRMS_ENGINE_POS_SYSTEMS_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_POS_SYSTEMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_POS_SYSTEMS_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_ecommerce_api_environment" {
  type = string
}

variable "BFF_ECOMMERCE_API_ROLE_ARN" {
  type = string
}

variable "BFF_ECOMMERCE_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_ECOMMERCE_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_ecommerce_api_environment" {
  type = string
}

variable "MP_API_ECOMMERCE_API_ROLE_ARN" {
  type = string
}

variable "MP_API_ECOMMERCE_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_ECOMMERCE_API_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_ecommerce_api_environment" {
  type = string
}

variable "CRMS_ENGINE_ECOMMERCE_API_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_ECOMMERCE_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_ECOMMERCE_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_settlement_api_environment" {
  type = string
}

variable "MP_API_SETTLEMENT_API_ROLE_ARN" {
  type = string
}

variable "MP_API_SETTLEMENT_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_SETTLEMENT_API_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_settlement_api_environment" {
  type = string
}

variable "BFF_SETTLEMENT_API_ROLE_ARN" {
  type = string
}

variable "BFF_SETTLEMENT_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_SETTLEMENT_API_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_settlement_api_environment" {
  type = string
}

variable "DBS_API_SETTLEMENT_API_ROLE_ARN" {
  type = string
}

variable "DBS_API_SETTLEMENT_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_SETTLEMENT_API_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_settlement_api_environment" {
  type = string
}

variable "CRMS_ENGINE_SETTLEMENT_API_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_SETTLEMENT_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_SETTLEMENT_API_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_cims_environment" {
  type = string
}

variable "DBS_API_CIMS_ROLE_ARN" {
  type = string
}

variable "DBS_API_CIMS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_CIMS_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_customer_api_environment" {
  type = string
}

variable "BFF_CUSTOMER_API_ROLE_ARN" {
  type = string
}

variable "BFF_CUSTOMER_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_CUSTOMER_API_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_si_environment" {
  type = string
}

variable "DBS_SI_ROLE_ARN" {
  type = string
}

variable "DBS_SI_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_SI_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_onboarding_api_environment" {
  type = string
}

variable "BFF_ONBOARDING_API_ROLE_ARN" {
  type = string
}

variable "BFF_ONBOARDING_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_ONBOARDING_API_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_reps_api_environment" {
  type = string
}

variable "BFF_REPS_API_ROLE_ARN" {
  type = string
}

variable "BFF_REPS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_REPS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_reps_api_environment" {
  type = string
}

variable "MP_API_REPS_API_ROLE_ARN" {
  type = string
}

variable "MP_API_REPS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_REPS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "REPS_ENGINE_ROLE_ARN" {
  type = string
}

variable "REPS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "REPS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "reps_engine_environment" {
  type = string
}

variable "REPS_API_ROLE_ARN" {
  type = string
}

variable "REPS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "REPS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "reps_api_environment" {
  type = string
}

variable "ZPOS_ENGINE_ROLE_ARN" {
  type = string
}

variable "ZPOS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "ZPOS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "zpos_engine_environment" {
  type = string
}

variable "BFF_ZPOS_API_ROLE_ARN" {
  type = string
}

variable "BFF_ZPOS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_ZPOS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_zpos_api_environment" {
  type = string
}

variable "mp_api_zpos_api_environment" {
  type = string
}

variable "MP_API_ZPOS_API_ROLE_ARN" {
  type = string
}

variable "MP_API_ZPOS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_ZPOS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "dbs_api_zpos_api_environment" {
  type = string
}

variable "DBS_API_ZPOS_API_ROLE_ARN" {
  type = string
}

variable "DBS_API_ZPOS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_ZPOS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_zpos_api_environment" {
  type = string
}

variable "CRMS_ENGINE_ZPOS_API_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_ZPOS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_ZPOS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "ZPOS_CQRS_ROLE_ARN" {
  type = string
}

variable "ZPOS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "ZPOS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "zpos_cqrs_environment" {
  type = string
}

variable "SIS_ENGINE_ROLE_ARN" {
  type = string
}

variable "SIS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "SIS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "sis_engine_environment" {
  type = string
}

variable "SIS_CQRS_ROLE_ARN" {
  type = string
}

variable "SIS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "SIS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "sis_cqrs_environment" {
  type = string
}

variable "BFF_RBAC_ENGINE_ROLE_ARN" {
  type = string
}

variable "BFF_RBAC_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_RBAC_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_rbac_engine_environment" {
  type = string
}

variable "NMS_ENGINE_ROLE_ARN" {
  type = string
}

variable "NMS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "NMS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "nms_engine_environment" {
  type = string
}

variable "SMS_CQRS_ROLE_ARN" {
  type = string
}

variable "SMS_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "SMS_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "sms_cqrs_environment" {
  type = string
}

variable "SMS_ENGINE_ROLE_ARN" {
  type = string
}

variable "SMS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "SMS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "sms_engine_environment" {
  type = string
}

variable "BFF_SMS_API_ROLE_ARN" {
  type = string
}

variable "BFF_SMS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_SMS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_sms_api_environment" {
  type = string
}

variable "dbs_api_sms_api_environment" {
  type = string
}

variable "DBS_API_SMS_API_ROLE_ARN" {
  type = string
}

variable "DBS_API_SMS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "DBS_API_SMS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "mp_api_sms_api_environment" {
  type = string
}

variable "MP_API_SMS_API_ROLE_ARN" {
  type = string
}

variable "MP_API_SMS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "MP_API_SMS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "crms_engine_sms_api_environment" {
  type = string
}

variable "CRMS_ENGINE_SMS_API_ROLE_ARN" {
  type = string
}

variable "CRMS_ENGINE_SMS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CRMS_ENGINE_SMS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "CE_API_ROLE_ARN" {
  type = string
}

variable "CE_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CE_API_ROLE_ARN_LONDON" {
  type = string
}

variable "ce_api_environment" {
  type = string
}

variable "impos_ui_environment" {
  type = string
}

variable "IMPOS_UI_ROLE_ARN" {
  type = string
}

variable "IMPOS_UI_ROLE_ARN_SYDNEY" {
  type = string
}

variable "IMPOS_UI_ROLE_ARN_LONDON" {
  type = string
}

variable "cpi_engine_environment" {
  type = string
}

variable "CPI_ENGINE_ROLE_ARN" {
  type = string
}

variable "CPI_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CPI_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "cpi_tevalis_engine_environment" {
  type = string
}

variable "CPI_TEVALIS_ENGINE_ROLE_ARN" {
  type = string
}

variable "CPI_TEVALIS_ENGINE_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CPI_TEVALIS_ENGINE_ROLE_ARN_LONDON" {
  type = string
}

variable "cpi_cqrs_environment" {
  type = string
}

variable "CPI_CQRS_ROLE_ARN" {
  type = string
}

variable "CPI_CQRS_ROLE_ARN_SYDNEY" {
  type = string
}

variable "CPI_CQRS_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_ims_api_environment" {
  type = string
}

variable "BFF_IMS_API_ROLE_ARN" {
  type = string
}

variable "BFF_IMS_API_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_IMS_API_ROLE_ARN_LONDON" {
  type = string
}

variable "bff_teardown_environment" {
  type = string
}

variable "BFF_TEARDOWN_ROLE_ARN" {
  type = string
}

variable "BFF_TEARDOWN_ROLE_ARN_SYDNEY" {
  type = string
}

variable "BFF_TEARDOWN_ROLE_ARN_LONDON" {
  type = string
}
