import {ZPOSAPI} from './zposApi';
import {provider} from './provider';
import {Matchers} from '@pact-foundation/pact';
import {OrderItemType} from '@npco/component-dto-order/dist/types';
import {CatalogDiscountConfig, CatalogUnit} from '@npco/component-dto-catalog/dist/types';

describe('zpos API', () => {
    beforeAll(() => provider.setup());
    afterEach(() => provider.verify());
    afterAll(() => provider.finalize());

    describe('create zpos order api', () => {
        const createOrderDto = {
            id: '16cd7adb-6ffd-4a35-b746-345e57fb5cc9',
            entityUuid: '96cd7adb-6ffd-4a35-b746-345e57fb5cc6',
            siteUuid: '56cd7adb-6ffd-4a35-b746-345e57fb5cc9',
            catalogSettings: {itemsTaxInclusive: true},
            discounts: [{config: CatalogDiscountConfig.AMOUNT, value: '1000', name: 'order level', ordinal: 1}],
            items: [
                {
                    name: 'test item',
                    price: '10000',
                    ordinal: 1,
                    type: OrderItemType.SINGLE,
                    unit: CatalogUnit.HOUR,
                    discounts: [{
                        config: CatalogDiscountConfig.PERCENTAGE,
                        value: '10',
                        name: 'item level',
                        ordinal: 1
                    }],
                    quantity: 1,
                    modifiers: [
                        {
                            name: 'test modifier',
                            price: '1000',
                            ordinal: 1,
                            unit: CatalogUnit.QUANTITY,
                            quantity: 1,
                        },
                    ],
                    taxes: [{name: 'GST', enabled: true, percent: 10}],
                },
            ],
        };

        it('should be able to create an empty order', async () => {
            await provider.addInteraction({
                state: 'create new order from consumer',
                uponReceiving: 'order was created',
                withRequest: {
                    method: 'POST',
                    path: '/v1/createOrder',
                    headers: {'Content-Type': 'application/json'},
                    body: {
                        input: {...createOrderDto, items: [], discounts: []},
                    }
                },
                willRespondWith: {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: {
                        id: "16cd7adb-6ffd-4a35-b746-345e57fb5cc9",
                        entityUuid: "96cd7adb-6ffd-4a35-b746-345e57fb5cc6",
                        status: "OPEN",
                        referenceNumber: Matchers.string(),
                        siteUuid: "56cd7adb-6ffd-4a35-b746-345e57fb5cc9",
                        items: [],
                        discounts: [],
                        paidAmount: {
                            currency: "AUD",
                            value: "0"
                        },
                        dueAmount: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalAmount: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalSurcharge: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalGst: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalDiscount: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalTips: {
                            currency: "AUD",
                            value: "0"
                        },
                        createdTime: Matchers.integer(),
                    }
                }
            });
            const api = new ZPOSAPI(provider.mockService.baseUrl);
            const order = await api.createOrder({input: {...createOrderDto, items: [], discounts: []}});
            console.log('create zpos order', order);
            expect(order.id).toEqual(createOrderDto.id);
            expect(order.entityUuid).toEqual(createOrderDto.entityUuid);
            expect(order.siteUuid).toEqual(createOrderDto.siteUuid);
            expect(order.status).toEqual('OPEN');
        });

        it('should be able to create an order with items and item modifiers', async () => {
            const dto = createOrderDto;
            dto.items[0] = {
                ...dto.items[0],
                discounts: [],
            };
            dto.discounts = [];
            await provider.addInteraction({
                state: 'create new order from consumer with items and modifiers',
                uponReceiving: 'order was created',
                withRequest: {
                    method: 'POST',
                    path: '/v1/createOrder',
                    headers: {'Content-Type': 'application/json'},
                    body: {
                        input: dto,
                    }
                },
                willRespondWith: {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: {
                        id: "16cd7adb-6ffd-4a35-b746-345e57fb5cc9",
                        entityUuid: "96cd7adb-6ffd-4a35-b746-345e57fb5cc6",
                        status: "OPEN",
                        referenceNumber: Matchers.string(),
                        siteUuid: "56cd7adb-6ffd-4a35-b746-345e57fb5cc9",
                        items: [
                            {
                                name: 'test item',
                                price: {
                                    currency: "AUD",
                                    value: "10000"
                                },
                                ordinal: 1,
                                type: OrderItemType.SINGLE,
                                unit: CatalogUnit.HOUR,
                                discounts: [],
                                quantity: 1,
                                modifiers: [
                                    {
                                        name: 'test modifier',
                                        price: {
                                            currency: "AUD",
                                            value: "1000"
                                        },
                                        ordinal: 1,
                                        unit: CatalogUnit.QUANTITY,
                                        quantity: 1,
                                    },
                                ],
                                taxes: [{name: 'GST', enabled: true, percent: 10}],
                            },
                        ],
                        discounts: [],
                        paidAmount: {
                            currency: "AUD",
                            value: "0"
                        },
                        dueAmount: {
                            currency: "AUD",
                            value: "12000"
                        },
                        totalAmount: {
                            currency: "AUD",
                            value: "12000"
                        },
                        totalSurcharge: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalGst: {
                            currency: "AUD",
                            value: "1000"
                        },
                        totalDiscount: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalTips: {
                            currency: "AUD",
                            value: "0"
                        },
                        createdTime: Matchers.integer(),
                    }
                }
            });
            const api = new ZPOSAPI(provider.mockService.baseUrl);
            const order = await api.createOrder({input: dto});
            console.log('create zpos order', order);
            expect(order.id).toEqual(createOrderDto.id);
            expect(order.entityUuid).toEqual(createOrderDto.entityUuid);
            expect(order.siteUuid).toEqual(createOrderDto.siteUuid);
            expect(order.status).toEqual('OPEN');
            expect(order.items.length).toEqual(1);
            expect(order.items[0].name).toEqual('test item');
            expect(order.items[0].modifiers.length).toEqual(1);
            expect(order.items[0].modifiers[0].name).toEqual('test modifier');
        });

        xit('should be able to create an order with item level discounts', async () => {
            const dto = createOrderDto;
            dto.discounts = [];
            dto.items[0] = {
                ...dto.items[0],
                discounts: [
                    {config: CatalogDiscountConfig.AMOUNT, value: '100', name: 'item level 1', ordinal: 1},
                    {config: CatalogDiscountConfig.PERCENTAGE, value: '10', name: 'item level 2', ordinal: 2},
                ],
            };
            await provider.addInteraction({
                state: 'create new order from consumer with items level discounts',
                uponReceiving: 'order was created',
                withRequest: {
                    method: 'POST',
                    path: '/v1/createOrder',
                    headers: {'Content-Type': 'application/json'},
                    body: {
                        input: dto,
                    }
                },
                willRespondWith: {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: {
                        id: "16cd7adb-6ffd-4a35-b746-345e57fb5cc9",
                        entityUuid: "96cd7adb-6ffd-4a35-b746-345e57fb5cc6",
                        status: "OPEN",
                        referenceNumber: Matchers.string(),
                        siteUuid: "56cd7adb-6ffd-4a35-b746-345e57fb5cc9",
                        items: [
                            {
                                name: 'test item',
                                price: {
                                    currency: "AUD",
                                    value: "10000"
                                },
                                ordinal: 1,
                                type: OrderItemType.SINGLE,
                                unit: CatalogUnit.HOUR,
                                discounts: [
                                    {
                                        config: CatalogDiscountConfig.AMOUNT,
                                        value: "100",
                                        name: 'item level 1',
                                        ordinal: 1
                                    },
                                    {
                                        config: CatalogDiscountConfig.PERCENTAGE,
                                        value: "10",
                                        name: 'item level 2',
                                        ordinal: 2
                                    },
                                ],
                                quantity: 1,
                                modifiers: [
                                    {
                                        name: 'test modifier',
                                        price: {
                                            currency: "AUD",
                                            value: "1000"
                                        },
                                        ordinal: 1,
                                        unit: CatalogUnit.QUANTITY,
                                        quantity: 1,
                                    },
                                ],
                                taxes: [{name: 'GST', enabled: true, percent: 10}],
                            },
                        ],
                        discounts: [],
                        paidAmount: {
                            currency: "AUD",
                            value: "0"
                        },
                        dueAmount: {
                            currency: "AUD",
                            value: "10700"
                        },
                        totalAmount: {
                            currency: "AUD",
                            value: "10700"
                        },
                        totalSurcharge: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalGst: {
                            currency: "AUD",
                            value: "900"
                        },
                        totalDiscount: {
                            currency: "AUD",
                            value: "1300"
                        },
                        totalTips: {
                            currency: "AUD",
                            value: "0"
                        },
                        createdTime: Matchers.integer(),
                    }
                }
            });
            const api = new ZPOSAPI(provider.mockService.baseUrl);
            const order = await api.createOrder({input: dto});
            console.log('create zpos order', order);
            expect(order.id).toEqual(createOrderDto.id);
            expect(order.entityUuid).toEqual(createOrderDto.entityUuid);
            expect(order.siteUuid).toEqual(createOrderDto.siteUuid);
            expect(order.status).toEqual('OPEN');
            expect(order.items.length).toEqual(1);
            expect(order.items[0].name).toEqual('test item');
            expect(order.items[0].discounts.length).toEqual(2);
        });

        xit('should be able to create an order with order level discounts', async () => {
            const dto = createOrderDto;
            dto.discounts = [
                {config: CatalogDiscountConfig.AMOUNT, value: '100', name: 'order level 1', ordinal: 1},
                {config: CatalogDiscountConfig.PERCENTAGE, value: '10', name: 'order level 2', ordinal: 2},
            ];
            await provider.addInteraction({
                state: 'create new order from consumer with order level discounts',
                uponReceiving: 'order was created',
                withRequest: {
                    method: 'POST',
                    path: '/v1/createOrder',
                    headers: {'Content-Type': 'application/json'},
                    body: {
                        input: dto,
                    }
                },
                willRespondWith: {
                    status: 200,
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: {
                        id: "16cd7adb-6ffd-4a35-b746-345e57fb5cc9",
                        entityUuid: "96cd7adb-6ffd-4a35-b746-345e57fb5cc6",
                        status: "OPEN",
                        referenceNumber: Matchers.string(),
                        siteUuid: "56cd7adb-6ffd-4a35-b746-345e57fb5cc9",
                        items: [
                            {
                                name: 'test item',
                                price: {
                                    currency: "AUD",
                                    value: "10000"
                                },
                                ordinal: 1,
                                type: OrderItemType.SINGLE,
                                unit: CatalogUnit.HOUR,
                                discounts: [
                                    {
                                        config: CatalogDiscountConfig.AMOUNT,
                                        value: "100",
                                        name: 'item level 1',
                                        ordinal: 1
                                    },
                                    {
                                        config: CatalogDiscountConfig.PERCENTAGE,
                                        value: "10",
                                        name: 'item level 2',
                                        ordinal: 2
                                    },
                                ],
                                quantity: 1,
                                modifiers: [
                                    {
                                        name: 'test modifier',
                                        price: {
                                            currency: "AUD",
                                            value: "1000"
                                        },
                                        ordinal: 1,
                                        unit: CatalogUnit.QUANTITY,
                                        quantity: 1,
                                    },
                                ],
                                taxes: [{name: 'GST', enabled: true, percent: 10}],
                            },
                        ],
                        discounts: [
                            {config: CatalogDiscountConfig.AMOUNT, value: "100", name: 'order level 1', ordinal: 1},
                            {config: CatalogDiscountConfig.PERCENTAGE, value: "10", name: 'order level 2', ordinal: 2},
                        ],
                        paidAmount: {
                            currency: "AUD",
                            value: "0"
                        },
                        dueAmount: {
                            currency: "AUD",
                            value: "9500"
                        },
                        totalAmount: {
                            currency: "AUD",
                            value: "9500"
                        },
                        totalSurcharge: {
                            currency: "AUD",
                            value: "0"
                        },
                        totalGst: {
                            currency: "AUD",
                            value: "800"
                        },
                        totalDiscount: {
                            currency: "AUD",
                            value: "2500"
                        },
                        totalTips: {
                            currency: "AUD",
                            value: "0"
                        },
                        createdTime: Matchers.integer(),
                    }
                }
            });
            const api = new ZPOSAPI(provider.mockService.baseUrl);
            const order = await api.createOrder({input: dto});
            console.log('create zpos order', order);
            expect(order.id).toEqual(createOrderDto.id);
            expect(order.entityUuid).toEqual(createOrderDto.entityUuid);
            expect(order.siteUuid).toEqual(createOrderDto.siteUuid);
            expect(order.status).toEqual('OPEN');
            expect(order.discounts.length).toEqual(2);
        });
    });
});
