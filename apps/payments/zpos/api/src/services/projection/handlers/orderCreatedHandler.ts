import { info } from '@npco/component-bff-core/dist/utils/logger';
import { OrderCreatedDto } from '@npco/component-dto-order';
import { CrmsOrderCreatedEvent, CrmsOrderUpdatedEvent } from '@npco/component-events-crms';
import { DbsOrderCreatedEvent, DbsOrderUpdatedEvent } from '@npco/component-events-dbs';
import { MpOrderCreatedEvent, MpOrderUpdatedEvent } from '@npco/component-events-mp';

import type { IHookAction } from '@nestpack/hooks';
import { HookAction } from '@nestpack/hooks';

import { ProjectionService } from '../projectionService';

@HookAction(
  MpOrderCreatedEvent,
  DbsOrderCreatedEvent,
  CrmsOrderCreatedEvent,
  MpOrderUpdatedEvent,
  DbsOrderUpdatedEvent,
  CrmsOrderUpdatedEvent,
)
export class OrderCreated<PERSON><PERSON><PERSON> implements IHookAction {
  constructor(private readonly service: ProjectionService) {}

  async handle(
    event:
      | MpOrderCreatedEvent
      | DbsOrderCreatedEvent
      | CrmsOrderCreatedEvent
      | MpOrderUpdatedEvent
      | DbsOrderUpdatedEvent
      | CrmsOrderUpdatedEvent,
  ) {
    info(`hook handle ${JSON.stringify(event)}`);
    // prettier-ignore
    const dto = new OrderCreatedDto({ // NOSONAR
            id: event.aggregateId,
            ...event.payload,
            serviceCharges: event.payload.serviceCharges as any, // NOSONAR
            discounts: event.payload.discounts as any, // NOSONAR
            items: event.payload.items as any // NOSONAR,
        }); // NOSONAR
    await this.service.saveOrder(dto, true);
  }
}
