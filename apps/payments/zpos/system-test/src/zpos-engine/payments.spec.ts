import { retry, sendSqsMessage } from '@npco/bff-systemtest-utils';

import axios from 'axios';
import { Client } from 'pg';
import { v4 } from 'uuid';

import { getApiEndpoint, getDbClientOptions, region } from './utils';

const getCreateOrderRequestInput = () => {
  return {
    id: v4(),
    entityUuid: v4(),
    siteUuid: v4(),
    catalogSettings: { itemsTaxInclusive: true },
    items: [
      {
        name: 'test item',
        price: '1000',
        ordinal: 1,
        type: 'SINGLE',
        unit: 'HOUR',
        quantity: 1,
        taxes: [{ name: 'GST', enabled: true, percent: 10 }],
      } as any,
    ],
  };
};
let accountId = '************';
if (process.env.STAGE === 'dev') {
  accountId = region === 'ap-southeast-2' ? '************' : '************';
}
describe('ZPOS engine transaction projections system tests', () => {
  const paymentSqsEndpoint = `https://sqs.ap-southeast-2.amazonaws.com/${accountId}/${process.env.STAGE}-zpos-engine-payment-sqs`;
  const order = getCreateOrderRequestInput();
  const txn = {
    id: v4(),
    amount: 11,
    saleAmount: 10,
    status: 'APPROVED',
    tipAmount: 1,
    externalReference: order.id,
    type: 'CNP',
    source: 'ZELLER_POS',
    surchargeAmount: 0,
    taxAmounts: [{ amount: 1 } as any],
    timestamp: new Date().toISOString(),
  };

  let client: Client;
  let baseEndpoint: string;
  let endpointV1: string;

  beforeAll(async () => {
    const options = await getDbClientOptions();
    baseEndpoint = await getApiEndpoint();
    endpointV1 = `${baseEndpoint}/v1/`;

    console.log('endpoint:', endpointV1);
    console.log('options:', options);
    client = new Client(options);
    await client.connect();
    try {
      await client.query(`SET search_path TO ${options.search_path}`);
    } catch (err) {
      console.error(err);
    }
    // create order
    const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: order }), {
      headers: { 'Content-Type': 'application/json' },
    });
    expect(apiResult.status).toBe(200);
  });

  afterAll(async () => {
    await client.end();
  });

  const queryOrder = async (id: string) => {
    const res = await client.query({
      name: `find-order-${v4()}`,
      text: `SELECT * FROM "Orders" WHERE id = $1`,
      values: [id],
    });
    return res.rows[0];
  };

  const queryOrderPayment = async (orderId: string) => {
    const res = await client.query({
      name: `find-order-${v4()}`,
      text: `SELECT * FROM "OrderPayments" WHERE "orderId" = $1`,
      values: [orderId],
    });
    return res.rows[0];
  };

  describe('Payments test suite', () => {
    it('should update order status on APPROVED transaction', async () => {
      // send payment event
      await sendSqsMessage(paymentSqsEndpoint, txn);
      // verify
      await retry(async () => {
        const paidOrder = await queryOrder(order.id);
        expect(paidOrder).not.toBeUndefined();
        expect(paidOrder.status).not.toBe('OPEN');
        const payment = await queryOrderPayment(order.id);
        expect(payment).toBeDefined();
        expect(payment.status).toBe('APPROVED');
      });
    });

    it('should NOT update order status on DECLINED transaction', async () => {
      // send payment event
      const newOrder = getCreateOrderRequestInput();
      const declinedTxn = { ...txn, status: 'DECLINED', externalReference: newOrder.id };

      // create order
      const apiResult = await axios.post(`${endpointV1}createOrder`, JSON.stringify({ input: newOrder }), {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(apiResult.status).toBe(200);
      await sendSqsMessage(paymentSqsEndpoint, declinedTxn);
      // verify
      await retry(async () => {
        const payment = await queryOrderPayment(newOrder.id);
        expect(payment).toBeDefined();
        expect(payment.status).toBe('DECLINED');
        const existing = await queryOrder(newOrder.id);
        expect(existing).not.toBeUndefined();
        expect(existing.status).toBe('OPEN');
        expect(existing.orderAmount).toEqual('11');
      });
    });
  });
});
