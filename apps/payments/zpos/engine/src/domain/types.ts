import type {
  CatalogDiscountConfig,
  CatalogItem,
  CatalogModifier,
  CatalogServiceChargeConfig,
  CatalogUnit,
} from '@npco/component-dto-catalog/dist';
import type { ISO3166 } from '@npco/component-dto-core/dist/types';
import type { OrderItemType, OrderStatus, TenderType } from '@npco/component-dto-order/dist';
import type { TransactionStatus } from '@npco/component-dto-transaction/dist/types';

export type CurrencyRateConfig = {
  [key: string]: {
    rate: number;
    fractionDigits: number;
  };
};

export enum GetOrdersFilterInputStatus {
  OPEN = 'OPEN',
  PAID = 'PAID',
}

export type GetOrdersFilterInput = {
  siteUuid?: string;
  status?: OrderStatus;
};

export type GetOrdersInput = {
  limit: number;
  filter: GetOrdersFilterInput;
};

export type CreateOrderDiscountInput = {
  id: string;
  catalogDiscountUuid?: string;
  catalogDiscount?: any;
  name: string;
  config: CatalogDiscountConfig;
  value: string;
  ordinal: number;
};

export type CreateOrderServiceChargeInput = {
  id: string;
  catalogServiceChargeUuid?: string;
  catalogServiceCharge?: any;
  name: string;
  config: CatalogServiceChargeConfig;
  value: string;
  ordinal: number;
};

export type CreateOrderItemModifierInput = {
  id: string;
  catalogModifierUuid?: string;
  catalogModifier?: any;
  name: string;
  price: string;
  ordinal: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
};

export type CreateOrderItemInput = {
  id: string;
  catalogItemUuid?: string;
  type: OrderItemType;
  name: string;
  price: string;
  ordinal: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
  discounts?: CreateOrderDiscountInput[];
  serviceCharges?: CreateOrderServiceChargeInput[];
  taxes?: CatalogTaxInput[];
  modifiers?: CreateOrderItemModifierInput[];
  variantName?: string;
};

export type CreateOrderInput = {
  id: string;
  entityUuid: string;
  siteUuid: string;
  createdFromDeviceUuid?: string;
  updatedTime: string;
  catalogSettings: CatalogSettingsSnapshot;
  createdTimestampLocal?: string;
  items: CreateOrderItemInput[];
  discounts?: CreateOrderDiscountInput[];
  serviceCharges?: CreateOrderServiceChargeInput[];
  currency?: string;
};

export type OrderDiscount = {
  catalogDiscountUuid?: string;
  name?: string;
  config: CatalogDiscountConfig;
  value: number;
  discountedAmount?: number;
  ordinal: number;
};

export type OrderServiceCharge = {
  catalogServiceChargeUuid?: string;
  name?: string;
  config: CatalogServiceChargeConfig;
  value: number;
  serviceChargeAmount?: number;
  ordinal: number;
};

export type OrderItemModifier = {
  catalogModifier?: CatalogModifier;
  name: string;
  price: number;
  ordinal?: number;
  unit: CatalogUnit;
  quantity: number;
};

export type OrderItemAttribute = {
  attributeName: string;
  attributeValue: string;
};

export type OrderItem = {
  id: string;
  entityUuid: string;
  parentId?: string;
  catalogItem?: CatalogItem;
  name: string;
  type: OrderItemType;
  price: number;
  sku?: string;
  ordinal?: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
  discounts?: OrderDiscount[];
  serviceCharges?: OrderServiceCharge[];
  taxes?: CatalogTax[];
  modifiers?: OrderItemModifier[];
  variantName?: string;
  subtotalAmount?: number;
};

export type OrderPayment = {
  id: string;
  entityUuid: string;
  transactionUuid: string;
  status: string;
  shortId?: string;
  currency?: string;
  amount: number;
  surchargeAmount?: number;
  type: string;
  tenderSubType: string;
  tenderType: TenderType;
  tips?: number;
  amountTendered?: number;
  change?: number;
  cashRoundingAdjustment?: number;
  timestamp: string;
  timestampLocal?: string;
  note?: string;
  taxAmounts: any;
  orderId: string;
};

export type Order = {
  id: string;
  entityUuid: string;
  status: OrderStatus;
  referenceNumber: string;
  catalogSettings: CatalogSettingsSnapshot;
  siteUuid: string;
  createdFromDeviceUuid?: string;
  paidAmount?: number;
  dueAmount?: number;
  currency: string;
  currencyMinorUnit: number;
  /**
   * totalAmount - deprecated, use orderAmount instead
   */
  totalAmount: number;
  orderAmount: number;
  subtotalAmount: number;
  totalSurcharge?: number;
  totalSurchargedGst?: number;
  totalGst?: number;
  orderGst?: number;
  cashRoundingAdjustment?: number;
  totalTips?: number;
  totalDiscount?: number;
  totalServiceCharge?: number;
  orderDisplayAmount?: number;
  totalChange?: number;
  totalAmountTendered?: number;
  createdTime: number;
  updatedTime?: number;
  updatedTimeInMilliseconds?: number;
  paidTime?: number;
  createdTimestampLocal?: string;
  payments?: OrderPayment[];
  items?: OrderItem[];
  discounts?: OrderDiscount[];
  serviceCharges?: OrderServiceCharge[];
};

export type CatalogTaxInput = {
  enabled: boolean;
  name: string;
  percent?: number;
};

export type CatalogTax = {
  enabled: boolean;
  name: string;
  percent?: number;
};

export type OrderTableConnection = {
  orders: Order[];
  nextToken?: string;
};

export type CatalogSettingsSnapshot = {
  itemsTaxInclusive: boolean;
  itemsApplyTax: boolean;
  autoSkuEnabled: boolean;
};

export type MultiTenderTransaction = {
  id: string;
  amount: number;
  saleAmount: number;
  surchargeAmount?: number;
  tipAmount?: number;
  amountTendered?: number;
  change?: number;
  taxAmounts: { name: string; amount: number }[];
  status: TransactionStatus;
  timestamp: string;
  timestampLocal?: string;
  tenderType: TenderType;
  cashRoundingAdjustment?: number;
  tenderSubType?: string;
  currency?: ISO3166;
  note?: string;
};
