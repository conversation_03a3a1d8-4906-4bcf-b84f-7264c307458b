import { TenderType } from '@npco/component-dto-order/dist';

import { Column, Entity, ManyToOne, PrimaryColumn } from 'typeorm';

import { Order } from '../types';

import { ColumnBigIntToNumberTransformer } from './transformers';

@Entity('OrderPayments')
export class OrderPayment {
  @PrimaryColumn({ type: 'uuid' })
  id!: string;

  @Column({ type: 'uuid' })
  entityUuid!: string;

  @Column({ type: 'uuid' })
  transactionUuid!: string;

  @Column({ type: 'varchar' })
  status!: string;

  @Column({ type: 'varchar' })
  shortId?: string;

  @Column({ type: 'varchar' })
  currency?: string;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  amount!: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  surchargeAmount?: number;

  @Column({ type: 'varchar' })
  type!: string;

  @Column({ type: 'varchar' })
  tenderSubType!: string;

  @Column({
    type: 'enum',
    enum: TenderType,
  })
  tenderType!: TenderType;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  tips?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  amountTendered?: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  change?: number;

  @Column({ type: 'int' })
  cashRoundingAdjustment?: number;

  @Column({ type: 'timestamp' })
  timestamp!: string;

  @Column({ type: 'varchar' })
  timestampLocal?: string;

  @Column({ type: 'varchar' })
  note?: string;

  @Column({ type: 'jsonb' })
  taxAmounts: any;

  @Column({ type: 'uuid' })
  orderId!: string;

  @ManyToOne('Order', 'payments', {
    orphanedRowAction: 'delete',
  })
  public order!: Order;
}
