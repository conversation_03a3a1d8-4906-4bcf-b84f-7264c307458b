import { CatalogDiscountConfig, CatalogDiscountType } from '@npco/component-dto-catalog/dist';

import { Column, Entity, ManyToOne, PrimaryColumn, Relation } from 'typeorm';

import { Order } from '../types';

import type { OrderItem } from './orderItem';
import { ColumnBigIntToNumberTransformer } from './transformers';

@Entity('OrderDiscounts')
export class OrderDiscount {
  @PrimaryColumn({ type: 'uuid' })
  id!: string;

  @Column({ type: 'uuid' })
  entityUuid!: string;

  @Column({ type: 'uuid' })
  orderId?: string;

  @Column({ type: 'uuid' })
  orderItemId?: string;

  @Column({ type: 'uuid' })
  catalogDiscountUuid?: string;

  @Column({ type: 'jsonb' })
  catalogDiscount?: any;

  @Column({ type: 'text' })
  name?: string;

  @Column({ type: 'enum', enum: CatalogDiscountConfig })
  config!: CatalogDiscountConfig;

  @Column({ type: 'enum', enum: CatalogDiscountType })
  type!: CatalogDiscountType;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  value!: number;

  @Column({ type: 'integer' })
  createdTime!: number;

  @Column({ type: 'integer' })
  updatedTime?: number;

  @Column({ type: 'integer' })
  ordinal!: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  discountedAmount?: number;

  @ManyToOne('OrderItem', 'discounts', {
    orphanedRowAction: 'delete',
  })
  public orderItem?: Relation<OrderItem>;

  @ManyToOne('Order', 'discounts', {
    orphanedRowAction: 'delete',
  })
  public order?: Order;
}
