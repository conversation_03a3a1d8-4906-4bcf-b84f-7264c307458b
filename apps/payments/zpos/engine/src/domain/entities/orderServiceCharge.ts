import { CatalogServiceChargeConfig, CatalogServiceChargeType } from '@npco/component-events-core/dist/catalog/types';

import { Column, Entity, ManyToOne, PrimaryColumn, Relation } from 'typeorm';

import { Order } from '../types';

import type { OrderItem } from './orderItem';
import { ColumnBigIntToNumberTransformer } from './transformers';

@Entity('OrderServiceCharges')
export class OrderServiceCharge {
  @PrimaryColumn({ type: 'uuid' })
  id!: string;

  @Column({ type: 'uuid' })
  entityUuid!: string;

  @Column({ type: 'uuid' })
  orderId?: string;

  @Column({ type: 'uuid' })
  orderItemId?: string;

  @Column({ type: 'uuid' })
  catalogServiceChargeUuid?: string;

  @Column({ type: 'jsonb' })
  catalogServiceCharge?: any;

  @Column({ type: 'text' })
  name?: string;

  @Column({ type: 'enum', enum: CatalogServiceChargeConfig })
  config!: CatalogServiceChargeConfig;

  @Column({ type: 'enum', enum: CatalogServiceChargeType })
  type!: CatalogServiceChargeType;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  value!: number;

  @Column({ type: 'integer' })
  createdTime!: number;

  @Column({ type: 'integer' })
  updatedTime?: number;

  @Column({ type: 'integer' })
  ordinal!: number;

  @Column({ type: 'bigint', transformer: new ColumnBigIntToNumberTransformer() })
  serviceChargeAmount?: number;

  @ManyToOne('OrderItem', 'serviceCharges', {
    orphanedRowAction: 'delete',
  })
  public orderItem?: Relation<OrderItem>;

  @ManyToOne('Order', 'serviceCharges', {
    orphanedRowAction: 'delete',
  })
  public order?: Order;
}
