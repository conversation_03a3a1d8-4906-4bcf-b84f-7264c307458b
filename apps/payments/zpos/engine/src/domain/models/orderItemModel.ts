import { debug } from '@npco/component-bff-core/dist/utils/logger';
import { OrderItemType } from '@npco/component-dto-order/dist';

import { Injectable } from '@nestjs/common';
import lodash from 'lodash';

import type { Order, OrderItemModifier } from '../entities';
import { OrderItem } from '../entities/orderItem';

import { BaseModel } from './baseModel';
import { OrderDiscountModel } from './orderDiscountModel';
import { OrderServiceChargeModel } from './orderServiceChargeModel';

/* eslint-disable no-param-reassign */
@Injectable()
export class OrderItemModel extends BaseModel {
  constructor(
    private readonly discountModel: OrderDiscountModel,
    private readonly serviceChargeModel: OrderServiceChargeModel,
  ) {
    super();
  }

  isEqual = (original: OrderItem, updated: OrderItem) => {
    const originalItem = JSON.parse(JSON.stringify(original));
    const omitItemFields = ['createdTime', 'updatedTime', 'catalogItem', 'subTotalAmount', 'totalAmount'];
    return lodash.isEqual(lodash.omit(originalItem, omitItemFields), lodash.omit(updated, omitItemFields));
  };

  createCore = (entityUuid: string, orderUuid: string, itm: OrderItem, parentId?: string) => {
    const item = new OrderItem();
    item.id = this.getIdFromFE(itm);
    item.orderId = orderUuid;
    item.entityUuid = entityUuid;
    item.catalogItem = itm.catalogItem;
    item.catalogItemUuid = itm.catalogItemUuid;
    item.type = itm.type;
    item.name = itm.name;
    item.unit = itm.unit;
    item.price = Number(itm.price);
    item.quantity = itm.quantity;
    item.taxes = itm.taxes;
    item.sku = itm.sku;
    item.ordinal = itm.ordinal;
    item.createdTime = this.getTimeUnix();
    item.variantName = itm.variantName;
    item.discounts = itm.discounts;
    item.serviceCharges = itm.serviceCharges;
    item.modifiers = itm.modifiers;
    if (item.type === OrderItemType.MODIFIER) {
      (item as OrderItemModifier).parentId = parentId as string;
      item.catalogModifierUuid = itm.catalogModifierUuid;
      item.catalogModifier = itm.catalogModifier;
    }
    return item;
  };

  create = async (order: {
    id: string;
    entityUuid: string;
    catalogSettings: { itemsTaxInclusive: boolean };
    items?: OrderItem[];
  }): Promise<OrderItem[] | undefined> => {
    if (order.items?.length) {
      const { entityUuid } = order;
      const orderItems = order.items.map((itm) => this.createCore(entityUuid, order.id, itm));
      const orderItemsWithRelations = orderItems.map((item) => {
        const itemWithRelations = { ...item };
        itemWithRelations.discounts = this.discountModel.createItemLevelDiscounts(item, item.discounts as any);
        itemWithRelations.serviceCharges = this.serviceChargeModel.createItemLevelServiceCharges(
          item,
          item.serviceCharges as any,
        );
        itemWithRelations.modifiers = item.modifiers?.map(
          (modifier) =>
            this.createCore(
              entityUuid,
              order.id,
              {
                ...modifier,
                type: OrderItemType.MODIFIER,
              },
              item.id,
            ) as OrderItemModifier,
        );
        this.setAmounts(itemWithRelations, order.catalogSettings);
        return itemWithRelations;
      });
      debug(orderItemsWithRelations);
      return orderItemsWithRelations;
    }
    return undefined;
  };

  update = async (original: Order, updateInput: Order) => {
    const { items } = updateInput;
    let updated;
    let deleted;
    let created;
    let unchangedItems;

    if (items && items.length > 0) {
      unchangedItems = this.getUnchangedItems(original, updateInput);
      created = await this.getCreatedItems(original, updateInput);
      updated = this.getUpdatedItems(original, updateInput);
      deleted = this.getDeletedItems(original, updateInput);
    } else if (original.items && original.items.length > 0) {
      deleted = original.items;
    }
    const updatedItems = [unchangedItems, created, updated]
      .filter((d) => d?.length)
      .reduce((acc = [], val = []) => acc.concat(val), []);
    return { items: updatedItems, delta: { updated, deleted, created } };
  };

  setAmounts = (item: OrderItem, catalogSettings: { itemsTaxInclusive: boolean }) => {
    const { lineItems } = this.calculateLineItems([item] as any[], [], [], {
      itemsTaxInclusive: catalogSettings.itemsTaxInclusive,
    });
    const lineItem = lineItems[item.id];
    item.discounts = item.discounts?.map((discount) => {
      const discountAmount = lineItem.discounts[discount.ordinal] ?? 0;
      return {
        ...discount,
        discountedAmount: discountAmount,
      };
    });
    item.serviceCharges = item.serviceCharges?.map((serviceCharge) => {
      const serviceChargeAmount = lineItem.serviceCharges[serviceCharge.ordinal] ?? 0;
      return {
        ...serviceCharge,
        serviceChargeAmount,
      };
    });
    item.totalAmount = lineItem.total;
    item.subtotalAmount = lineItem.priceWithTax * item.quantity;
    item.modifiers = item.modifiers?.map((modifier) => {
      return {
        ...modifier,
        subtotalAmount: lineItem.modifiers[modifier.ordinal!] ?? 0,
      };
    });
  };

  private readonly getUpdatedItems = (original: Order, updateInput: Order) =>
    updateInput
      .items!.filter((updatedItem) =>
        original.items!.find(
          (originalItem) => originalItem.id === updatedItem.id && !this.isEqual(originalItem, updatedItem),
        ),
      )
      .map((updatedItem) => {
        const existing = original.items!.find((origItem) => origItem.id === updatedItem.id);
        const newItem = this.mergedObject(updatedItem, existing) as OrderItem;
        if (newItem.modifiers && newItem.modifiers.length > 0) {
          const copyOfModifiers = [...newItem.modifiers];
          // should create a new 'OrderItem' instance of type 'MODIFIER' for those who are missing 'id' before saving to the db
          newItem.modifiers = this.getUpdatedModifiers(
            copyOfModifiers,
            updateInput.entityUuid,
            updateInput.id,
            newItem.id,
            existing?.modifiers,
          );
        }
        newItem.discounts = this.discountModel.updateItemLevelDiscounts(existing!, updatedItem.discounts as any);
        newItem.serviceCharges = this.serviceChargeModel.updateItemLevelServiceCharges(
          existing as OrderItem,
          updatedItem.serviceCharges as any,
        );
        this.setAmounts(newItem, updateInput.catalogSettings);
        debug(newItem);
        return newItem;
      });

  private readonly getUpdatedModifiers = (
    modifiers: OrderItemModifier[],
    entityUuid: string,
    orderId: string,
    orderItemId: string,
    existingModifiers?: OrderItemModifier[],
  ) => {
    const existingModifiersSet = new Set<string>();
    existingModifiers?.forEach((m) => {
      existingModifiersSet.add(m.id);
    });
    return modifiers.reduce((acc: OrderItemModifier[], cur: OrderItemModifier) => {
      if (!existingModifiersSet.has(cur.id)) {
        const newMod = this.createCore(
          entityUuid,
          orderId,
          {
            ...cur,
            type: OrderItemType.MODIFIER,
          },
          orderItemId,
        ) as OrderItemModifier;
        return [...acc, newMod];
      }
      return [...acc, cur];
    }, []);
  };

  private readonly getUnchangedItems = (original: Order, updateInput: Order) =>
    original.items!.filter((originalItem) =>
      updateInput.items!.find(
        (updatedItem) => originalItem.id === updatedItem.id && this.isEqual(originalItem, updatedItem),
      ),
    );

  private readonly getDeletedItems = (original: Order, updateInput: Order) => {
    const existingItems = updateInput.items!.filter((item) => !!item.id);
    return original.items!.filter(
      (originalItem) => !existingItems.find((updatedItem) => originalItem.id === updatedItem.id),
    );
  };

  private readonly getCreatedItems = async (original: Order, updateInput: Order) => {
    const existingOrderItemsIdSet = new Set<string>();
    original.items?.forEach((item) => {
      existingOrderItemsIdSet.add(item.id);
    });
    const createItemsInput = updateInput.items!.filter((item) => !existingOrderItemsIdSet.has(item.id));
    return this.create({
      id: updateInput.id,
      entityUuid: updateInput.entityUuid,
      catalogSettings: updateInput.catalogSettings,
      items: createItemsInput,
    });
  };
}
