import { debug } from '@npco/component-bff-core/dist/utils/logger';
import { CatalogDiscountType } from '@npco/component-dto-catalog/dist';

import { Injectable } from '@nestjs/common';
import lodash from 'lodash';

import type { OrderItem } from '../entities';
import { OrderDiscount } from '../entities';
import type { Order } from '../entities/order';
import type { CreateOrderDiscountInput } from '../types';

import { BaseModel } from './baseModel';

/* eslint-disable no-param-reassign */

@Injectable()
export class OrderDiscountModel extends BaseModel {
  createCore = (entityUuid: string, discounts?: CreateOrderDiscountInput[]) => {
    return discounts?.map((d) => {
      const orderDiscount = new OrderDiscount();
      orderDiscount.id = this.getIdFromFE(d);
      orderDiscount.entityUuid = entityUuid;
      orderDiscount.catalogDiscountUuid = d.catalogDiscountUuid;
      orderDiscount.catalogDiscount = d.catalogDiscount;
      orderDiscount.name = d.name;
      orderDiscount.config = d.config;
      orderDiscount.type = CatalogDiscountType.BASIC;
      orderDiscount.value = Number(d.value);
      orderDiscount.ordinal = d.ordinal;
      this.setCreatedTime(orderDiscount);
      return orderDiscount;
    });
  };

  isEqual = (original: OrderDiscount, updated: CreateOrderDiscountInput) => {
    const originalDiscount = JSON.parse(JSON.stringify(original));
    const omitDiscountFields = ['createdTime', 'updatedTime', 'catalogDiscount'];
    return lodash.isEqual(lodash.omit(originalDiscount, omitDiscountFields), lodash.omit(updated, omitDiscountFields));
  };

  getUpdatedDiscounts = (original?: OrderDiscount[], updateInput?: CreateOrderDiscountInput[]) =>
    updateInput
      ?.filter((updatedDiscount) =>
        original?.find(
          (originalDiscount) =>
            originalDiscount.id === updatedDiscount.id && !this.isEqual(originalDiscount, updatedDiscount),
        ),
      )
      .map((updatedDiscount) => {
        const existing = original!.find((origDiscount) => origDiscount.id === updatedDiscount.id);
        const newDiscount = this.mergedObject(updatedDiscount, existing) as OrderDiscount;
        debug(newDiscount);
        return newDiscount;
      });

  getUnchangedDiscounts = (original?: OrderDiscount[], updateInput?: CreateOrderDiscountInput[]) =>
    original?.filter((originalDiscount) =>
      updateInput?.find(
        (updatedDiscount) =>
          originalDiscount.id === updatedDiscount.id && this.isEqual(originalDiscount, updatedDiscount),
      ),
    );

  getCreatedDiscounts = (
    entityUuid: string,
    originalDiscounts?: OrderDiscount[],
    updateInput?: CreateOrderDiscountInput[],
  ) => {
    const existingDiscounts = new Set<string>();
    originalDiscounts?.forEach((d) => existingDiscounts.add(d.id));
    const createDiscountsInput = updateInput?.filter((item) => !existingDiscounts.has(item.id));
    return this.createCore(entityUuid, createDiscountsInput as any);
  };

  updateCore = (entityUuid: string, original?: OrderDiscount[], updateInput?: CreateOrderDiscountInput[]) => {
    let updated;
    let created;
    let unchangedDiscounts;
    if (updateInput && updateInput.length > 0) {
      unchangedDiscounts = this.getUnchangedDiscounts(original, updateInput);
      created = this.getCreatedDiscounts(entityUuid, original, updateInput);
      updated = this.getUpdatedDiscounts(original, updateInput);
    }
    const updatedDiscounts = [unchangedDiscounts, created, updated]
      .filter((d) => d?.length)
      .reduce((acc, val) => acc!.concat(val!), []);
    return updatedDiscounts;
  };

  createItemLevelDiscounts = (item: OrderItem, discounts?: CreateOrderDiscountInput[]): OrderDiscount[] | undefined => {
    const coreDiscounts = this.createCore(item.entityUuid, discounts);
    return coreDiscounts?.map((d) => ({ ...d, orderItemId: item.id }));
  };

  createOrderLevelDiscounts = (order: Order, discounts?: CreateOrderDiscountInput[]) => {
    const coreDiscounts = this.createCore(order.entityUuid, discounts);
    return coreDiscounts?.map((d) => ({ ...d, orderId: order.id }));
  };

  updateItemLevelDiscounts = (original: OrderItem, updateInput?: CreateOrderDiscountInput[]) => {
    const coreDiscounts = this.updateCore(original.entityUuid, original.discounts, updateInput);
    return coreDiscounts?.map((d) => ({ ...d, orderItemId: original.id }));
  };

  updateOrderLevelDiscounts = (original: Order, updateInput?: CreateOrderDiscountInput[]) => {
    const coreDiscounts = this.updateCore(original.entityUuid, original.discounts, updateInput);
    return coreDiscounts?.map((d) => ({ ...d, orderId: original.id }));
  };
}
