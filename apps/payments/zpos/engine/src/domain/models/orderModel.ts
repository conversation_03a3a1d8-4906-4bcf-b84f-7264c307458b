import { convertAmountToAUD } from '@npco/bff-fe-common/dist/order/calculator/currencyConverter';
import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger';
import { ISO4217 } from '@npco/component-dto-core/dist';
import { OrderStatus } from '@npco/component-dto-order/dist';
import type { Transaction } from '@npco/component-dto-transaction/dist';
import { TransactionStatus } from '@npco/component-dto-transaction/dist';

import { Injectable } from '@nestjs/common';
import lodash from 'lodash';
import { v4 } from 'uuid';

import type { CreateOrderPaymentInput, TaxAmount, UpdateOrderPaymentInput } from '../../services/types';
import { PURCHASE, TenderType } from '../../services/types';
import { CURRENCY_COMMON_MINOR_UNIT, ZERO_CENT_SALE_TRESHOLD } from '../const';
import { OrderPayment } from '../entities';
import { Order } from '../entities/order';
import { BadRequestError, ServerError } from '../error';
import type { CreateOrderInput, MultiTenderTransaction, Order as OrderType } from '../types';

import { BaseModel } from './baseModel';
import { OrderDiscountModel } from './orderDiscountModel';
import { OrderItemModel } from './orderItemModel';
import { OrderServiceChargeModel } from './orderServiceChargeModel';

/* eslint-disable no-param-reassign */
@Injectable()
export class OrderModel extends BaseModel {
  constructor(
    private readonly itemModel: OrderItemModel,
    private readonly discountModel: OrderDiscountModel,
    private readonly serviceChargeModel: OrderServiceChargeModel,
  ) {
    super();
  }

  getZeroCentSaleThreshold = () => {
    return ZERO_CENT_SALE_TRESHOLD;
  };

  isOrderUpdatedTimeValid = (orderInput: { updatedTime?: string }, order: Order | null) => {
    orderInput.updatedTime ??= new Date().toISOString();
    const updatedTimestamp = new Date(orderInput.updatedTime);
    if (Math.abs(updatedTimestamp.getTime() - new Date().getTime()) > 300000) {
      warn('order updatedTime is more than +-5 minutes from the system time');
      throw new BadRequestError('order updatedTime is more than +-5 minutes from the system time');
    }
    if (
      order?.updatedTimeInMilliseconds &&
      new Date(orderInput.updatedTime).getTime() <= order.updatedTimeInMilliseconds
    ) {
      warn('order input updatedTime is less than current order updatedTime');
      throw new BadRequestError('order input updatedTime is less than current order updatedTime');
    }
  };

  setUpdatedTime(order: { updatedTime?: number; updatedTimeInMilliseconds?: number }, updatedTime?: string) {
    if (!updatedTime) {
      order.updatedTime = this.getTimeUnix();
      order.updatedTimeInMilliseconds = this.getTimeUnixInMs();
    } else {
      order.updatedTime = this.getTimeUnixFromInput(updatedTime);
      order.updatedTimeInMilliseconds = this.getTimeUnixInMsFromInput(updatedTime);
    }
  }

  create = async (orderInput: CreateOrderInput) => {
    const order = new Order();
    order.id = this.getIdFromFE(orderInput);
    order.entityUuid = orderInput.entityUuid;
    order.siteUuid = orderInput.siteUuid;
    order.status = OrderStatus.OPEN;
    order.currency = orderInput.currency ?? ISO4217.AUD;
    order.currencyMinorUnit = CURRENCY_COMMON_MINOR_UNIT;
    order.referenceNumber = this.generateReference();
    order.catalogSettings = orderInput.catalogSettings;
    order.createdFromDeviceUuid = orderInput.createdFromDeviceUuid;
    order.createdTimestampLocal = orderInput.createdTimestampLocal;
    order.discounts = this.discountModel.createOrderLevelDiscounts(order, orderInput.discounts);
    order.serviceCharges = this.serviceChargeModel.createOrderLevelServiceCharges(order, orderInput.serviceCharges);
    order.items = await this.itemModel.create({
      id: order.id,
      entityUuid: order.entityUuid,
      catalogSettings: order.catalogSettings,
      items: orderInput.items as any,
    });
    this.setCreatedTime(order);
    this.setUpdatedTime(order, orderInput.updatedTime);
    this.setAmountsToZero(order);
    this.updateAmountsInUnpaidOrder(order as OrderType);
    return order;
  };

  setAmountsToZero = (order: Order) => {
    order.orderAmount = 0;
    order.totalAmount = 0;
    order.totalGst = 0;
    order.orderGst = 0;
    order.totalSurcharge = 0;
    order.totalSurchargedGst = 0;
    order.totalTips = 0;
    order.paidAmount = 0;
    order.dueAmount = 0;
    order.orderDisplayAmount = 0;
    order.totalAmountTendered = 0;
    order.totalChange = 0;
  };

  updateCore = (original: Order, updateInput: Order) => {
    const omitFields = ['items', 'updatedTime', 'createdTime'];
    const partialOrder = lodash.omit(updateInput, omitFields);
    const originalOrder = lodash.omit(original, omitFields);
    if (!lodash.isEqual(originalOrder, partialOrder)) {
      const merged = this.mergedObject(partialOrder, originalOrder);
      merged.createdTime = original.createdTime;
      return merged;
    }
    return original;
  };

  update = async (original: Order, updateInput: Order, forceUpdate = false): Promise<Order> => {
    const itemsUpdateResult = await this.itemModel.update(original, updateInput);
    const discountsUpdateResult = this.discountModel.updateOrderLevelDiscounts(original, updateInput.discounts as any);
    const serviceChargesUpdateResult = this.serviceChargeModel.updateOrderLevelServiceCharges(
      original,
      updateInput.serviceCharges as any,
    );
    const coreUpdateResult = this.updateCore(original, updateInput);
    const order = {
      ...coreUpdateResult,
      currencyMinorUnit: CURRENCY_COMMON_MINOR_UNIT,
      items: itemsUpdateResult.items,
      discounts: discountsUpdateResult,
      serviceCharges: serviceChargesUpdateResult,
      id: original.id,
    } as Order;

    this.setUpdatedTime(order, updateInput.updatedTime as any);
    this.updateAmountsInUnpaidOrder(order as OrderType, forceUpdate);
    return order;
  };

  getOrderStatusForUpdatedDueAmount = (order: Order) => {
    const dueAmount = this.safeGetNumber(order.dueAmount);
    if (dueAmount < 0) {
      error('getOrderStatusForUpdatedDueAmount :: dueAmount is negative');
      throw new ServerError('dueAmount cannot be negative');
    }

    if (dueAmount === 0) {
      return OrderStatus.PAID;
    }

    if (dueAmount > 0) {
      // case 1: checking card sales with 0.00xx precision
      // e.g. total amount is 1.0049, paid amount will be 1.00, dueAmount in centi cents is 49, but we mark the order as PAID
      if (dueAmount < this.getZeroCentSaleThreshold()) {
        return OrderStatus.PAID;
      }
      // case 2: checking if order has any payment recorded
      if (order.paidAmount && order.paidAmount > 0) {
        return OrderStatus.PART_PAID;
      }
      // case 4: order is OPEN and without payments
      return OrderStatus.OPEN;
    }
    // default
    return OrderStatus.OPEN;
  };

  setPaymentEntityCommonFields = (
    payment: OrderPayment,
    input: { order: Order; transaction: Transaction | MultiTenderTransaction },
  ) => {
    const { order, transaction } = input;
    payment.entityUuid = order.entityUuid;
    payment.orderId = order.id;
    payment.amount = this.safeGetNumber(transaction.amount);
    payment.status =
      transaction.status === TransactionStatus.APPROVED ? TransactionStatus.APPROVED : TransactionStatus.DECLINED;
    payment.surchargeAmount = this.safeGetNumber(transaction.surchargeAmount);
    payment.timestamp = transaction.timestamp;
    payment.timestampLocal = transaction.timestampLocal;
    payment.tips = this.safeGetNumber(transaction.tipAmount);
    payment.taxAmounts = transaction.taxAmounts;
  };

  setMultiTenderPaymentEntityFields = (payment: OrderPayment, nonCardTransaction: MultiTenderTransaction) => {
    payment.id = nonCardTransaction.id;
    payment.transactionUuid = v4();
    payment.shortId = this.generateNonCardPaymentShortId();
    payment.tenderType = nonCardTransaction.tenderType;
    payment.type = PURCHASE;
    payment.note = nonCardTransaction.note;
    payment.currency = nonCardTransaction.currency ?? ISO4217.AUD;
    if (payment.tenderType === TenderType.OTHER) {
      payment.tenderSubType = nonCardTransaction.tenderSubType ?? '';
    } else if (payment.tenderType === TenderType.CASH) {
      payment.amountTendered = this.safeGetNumber(nonCardTransaction.amountTendered);
      payment.change = this.safeGetNumber(nonCardTransaction.change);
      payment.cashRoundingAdjustment = this.safeGetNumber(nonCardTransaction.cashRoundingAdjustment);
    }
  };

  setCardPaymentEntityFields = (payment: OrderPayment, cardTransaction: Transaction) => {
    payment.id = v4();
    payment.transactionUuid = cardTransaction.id;
    payment.type = cardTransaction.type;
    payment.tenderType = TenderType.CARD;
    payment.shortId = cardTransaction.reference;
    payment.currency = ISO4217.AUD;
  };

  getPaymentEntity = (input: { order: Order; transaction: Transaction | MultiTenderTransaction }) => {
    const { transaction } = input;
    const payment = new OrderPayment();
    this.setPaymentEntityCommonFields(payment, input);

    if ([TenderType.CASH, TenderType.OTHER].includes((transaction as MultiTenderTransaction).tenderType)) {
      this.setMultiTenderPaymentEntityFields(payment, transaction as MultiTenderTransaction);
    } else {
      this.setCardPaymentEntityFields(payment, transaction as Transaction);
    }
    return payment;
  };

  getTotalGstFromTransaction = (txn: { taxAmounts?: TaxAmount[] }) => {
    if (txn.taxAmounts?.length) {
      const gst = txn.taxAmounts.reduce((acc, tax) => acc + tax.amount, 0);
      return this.safeGetNumber(gst);
    }
    return 0;
  };

  updateOrderStatusToPaid = (order: Order) => {
    // if it is less than 50 centicents, $0.0049, it will be rounded as $0.00 so the user doesn't pay anything
    if (
      order.status === OrderStatus.PAID ||
      !order.items ||
      order.items.length === 0 ||
      (order.dueAmount && order.dueAmount >= ZERO_CENT_SALE_TRESHOLD)
    ) {
      error('markOrderAsPaid :: Invalid order');
      throw new BadRequestError('Invalid order status');
    }
    order.status = OrderStatus.PAID;
    order.dueAmount = 0;
    order.orderAmount = order.orderAmount >= ZERO_CENT_SALE_TRESHOLD ? order.orderAmount : 0;
    order.totalAmount = order.paidAmount!;
    order.paidTime = this.getTimeUnix();
    order.updatedTime = this.getTimeUnix();
    order.updatedTimeInMilliseconds = this.getTimeUnixInMs();
    return order;
  };

  getTransactionFromNonCardCheckoutInput = (nonCardCheckoutInput: CreateOrderPaymentInput) => {
    const transaction: MultiTenderTransaction = {
      id: nonCardCheckoutInput.id,
      amount: this.safeGetNumber(nonCardCheckoutInput.amount),
      saleAmount: nonCardCheckoutInput.amount - this.safeGetNumber(nonCardCheckoutInput.tipAmount),
      surchargeAmount: 0,
      tipAmount: this.safeGetNumber(nonCardCheckoutInput.tipAmount),
      taxAmounts: nonCardCheckoutInput.taxAmounts,
      status: TransactionStatus.APPROVED,
      timestamp: new Date().toISOString(),
      timestampLocal: nonCardCheckoutInput.timestampLocal,
      tenderType: nonCardCheckoutInput.tenderType,
      cashRoundingAdjustment: this.safeGetNumber(nonCardCheckoutInput.cashRoundingAdjustment),
      amountTendered: this.safeGetNumber(nonCardCheckoutInput.amountTendered),
      change: this.safeGetNumber(nonCardCheckoutInput.change),
      tenderSubType: nonCardCheckoutInput.tenderSubType,
      note: nonCardCheckoutInput.note,
    };
    return transaction;
  };

  setOrderStatusByShouldFinalise(order: Order, shouldFinalise: boolean): Order {
    if (!shouldFinalise && order.status === OrderStatus.PAID) {
      order.status = OrderStatus.PART_PAID;
    }
    return order;
  }

  throwIfCashAdjustmentOnNonLastPayment = (existingOrder: Order, transaction: MultiTenderTransaction) => {
    if (transaction.tenderType === TenderType.CASH) {
      const haveCashRoundingAdjustment = this.safeGetNumber(transaction.cashRoundingAdjustment) > 0;
      if (haveCashRoundingAdjustment) {
        let isLastPayment = false;
        try {
          const dueAmountAfterPayment = this.calculateDueAmountAfterPayment(
            existingOrder,
            transaction,
            TenderType.CASH,
          );
          isLastPayment = dueAmountAfterPayment === 0;
        } catch (e) {
          debug(e);
          info(
            `error caught from calculateDueAmountAfterPayment, dueAmount is negative and cash payment cannot be applied`,
          );
        }
        if (!isLastPayment) {
          throw new BadRequestError('Cash rounding adjustment is only allowed for the last payment');
        }
      }
    }
  };

  recordNonCardPayment = async (input: { order: Order; nonCardCheckoutInput: CreateOrderPaymentInput }) => {
    const transaction = this.getTransactionFromNonCardCheckoutInput(input.nonCardCheckoutInput);
    this.throwIfCashAdjustmentOnNonLastPayment(input.order, transaction);
    debug(`transaction:${JSON.stringify(transaction)}`);
    const { order, newPayment } = await this.recordApprovedPayment({
      order: input.order,
      transaction,
    });
    order.totalAmountTendered = newPayment.amountTendered;
    order.totalChange = newPayment.change;
    order.cashRoundingAdjustment = newPayment.cashRoundingAdjustment;
    order.orderAmount += this.safeGetNumber(newPayment.cashRoundingAdjustment);
    order.orderDisplayAmount = order.paidAmount! > order.orderAmount ? order.paidAmount! : order.orderAmount;
    return { order, newPayment };
  };

  getAmountsFromTransaction = (transaction: Transaction | MultiTenderTransaction) => {
    return {
      amount: this.safeGetNumber(transaction.amount),
      tipAmount: this.safeGetNumber(transaction.tipAmount),
      surchargeAmount: this.safeGetNumber(transaction.surchargeAmount),
      saleAmount: this.safeGetNumber(transaction.saleAmount),
      cashRoundingAdjustment: this.safeGetNumber((transaction as MultiTenderTransaction).cashRoundingAdjustment),
    };
  };

  getTotalGstForPaidOrder = (order: Order) => {
    if (order.status === OrderStatus.PAID) {
      return (
        order.payments?.reduce(
          (acc, current) =>
            current.status === TransactionStatus.APPROVED ? this.getTotalGstFromTransaction(current) + acc : acc,
          0,
        ) ?? 0
      );
    }
    return order.totalGst;
  };

  calculateDueAmountAfterPayment = (
    order: Order,
    transaction: Transaction | MultiTenderTransaction,
    tenderType: TenderType,
  ) => {
    const { saleAmount, cashRoundingAdjustment } = this.getAmountsFromTransaction(transaction);
    let dueAmount = this.safeGetNumber(order.dueAmount) - saleAmount;
    if (tenderType === TenderType.CASH) {
      dueAmount += cashRoundingAdjustment; // if cashRoundingAdjustment was rounded down, its a negative value
    }
    if (dueAmount > 0 && dueAmount < this.getZeroCentSaleThreshold()) {
      // if it is less or equal than $0.0049, it will be rounded as $0.00 so the order is marked as PAID
      dueAmount = 0;
    } else if (dueAmount < 0) {
      if (Math.abs(dueAmount) <= this.getZeroCentSaleThreshold()) {
        // if it is $0.005, the Merchant pay $0.01 on the T2, and the dueAmount is -$0.005
        info(`dueAmount is within the ZERO_CENT_SALE_TRESHOLD, setting it to 0`);
        dueAmount = 0;
      } else if (tenderType === TenderType.CASH) {
        throw new ServerError('dueAmount cannot be negative');
      }
      error(
        `calculateDueAmountAfterPayment :: dueAmount is -$${Math.abs(
          convertAmountToAUD(dueAmount),
        )} after applying card transaction ${transaction.id}, orderUuid ${order.id}`,
      );
      error(`dueAmount is set to 0 for orderUuid ${order.id}`);
      dueAmount = 0;
    }

    return this.safeGetNumber(dueAmount);
  };

  setOrderPaymentAmountsAndStatus = (
    order: Order,
    transaction: Transaction | MultiTenderTransaction,
    tenderType: TenderType,
  ) => {
    const { amount, surchargeAmount, tipAmount, saleAmount } = this.getAmountsFromTransaction(transaction);
    debug(`order.dueAmount = ${order.dueAmount}, saleAmount = ${saleAmount}, amount = ${amount}`);
    order.paidAmount = this.safeGetNumber(order.paidAmount) + amount;
    order.dueAmount = this.calculateDueAmountAfterPayment(order, transaction, tenderType);
    order.totalTips = this.safeGetNumber(order.totalTips) + tipAmount;
    order.totalSurcharge = this.safeGetNumber(order.totalSurcharge) + surchargeAmount;
    order.status = this.getOrderStatusForUpdatedDueAmount(order);
  };

  recordApprovedPayment = async (input: { order: Order; transaction: Transaction | MultiTenderTransaction }) => {
    const { order, transaction } = input;
    const payment = this.getPaymentEntity(input);
    order.payments = (order.payments ?? []).concat([payment]);
    this.setOrderPaymentAmountsAndStatus(order, transaction, payment.tenderType);
    order.totalGst = this.getTotalGstForPaidOrder(order);
    order.orderDisplayAmount = order.paidAmount;
    order.paidTime = this.getTimeUnix(); // only full payments supported at the moment, with split-payments we'll need to check the order status
    this.setUpdatedTime(order);
    /**
     * totalAmount is deprecated, use orderAmount instead
     */
    order.totalAmount = this.safeGetNumber(order.paidAmount);

    debug(`sale approved: ${JSON.stringify(order)}`);
    return { order, newPayment: payment };
  };

  recordFailedPayment = async (input: { order: Order; transaction: Transaction }) => {
    const { order } = input;
    info('record failed paymnt');
    try {
      const paymentToBeCancelled = order.payments?.find(
        (p) => p.transactionUuid === input.transaction.id && p.status === TransactionStatus.APPROVED,
      );
      const payment = paymentToBeCancelled ?? this.getPaymentEntity(input);
      payment.status = TransactionStatus.DECLINED;

      if (paymentToBeCancelled) {
        info(`cancelling payment ${paymentToBeCancelled.id}, transactionUuid: ${input.transaction.id}`);
        debug(`${JSON.stringify(paymentToBeCancelled)}`);
        order.totalTips = this.safeGetNumber(order.totalTips) - this.safeGetNumber(paymentToBeCancelled.tips);
        order.totalSurcharge =
          this.safeGetNumber(order.totalSurcharge) - this.safeGetNumber(paymentToBeCancelled.surchargeAmount);
        order.paidAmount =
          this.safeGetNumber(order.paidAmount) > 0
            ? this.safeGetNumber(order.paidAmount) - paymentToBeCancelled.amount
            : 0;
        order.dueAmount =
          order.paidAmount > 0
            ? this.safeGetNumber(order.dueAmount) + this.safeGetNumber(input.transaction.saleAmount)
            : order.orderAmount;
      }
      order.payments = (order.payments ?? [])
        .filter((p) => p.transactionUuid !== payment.transactionUuid)
        .concat([payment]);

      order.status = this.getOrderStatusForUpdatedDueAmount(order);
      const { totalGst } = this.calculator.calculateTaxes(order as any);
      order.totalGst = totalGst;
      order.orderDisplayAmount = order.orderAmount;
      order.totalAmount = order.orderAmount;

      this.setUpdatedTime(order);
      debug(`${JSON.stringify(order)}`);
      return { order, newPayment: payment };
    } catch (e) {
      error(JSON.stringify(e));
      throw e;
    }
  };

  recordPayment = async (input: { order: Order; transaction: Transaction }) => {
    const { transaction } = input;
    if (transaction.status === TransactionStatus.APPROVED) {
      return this.recordApprovedPayment(input);
    }
    if ([TransactionStatus.CANCELLED, TransactionStatus.DECLINED].includes(transaction.status)) {
      return this.recordFailedPayment(input);
    }
    return Promise.reject(new Error('Invalid transaction status'));
  };

  migrationSetPaidOrderAmount = async (order: Order, transaction: Transaction) => {
    order.paidAmount = this.safeGetNumber(transaction.amount);
    order.orderAmount = this.safeGetNumber(transaction.amount);
    order.totalTips = this.safeGetNumber(transaction.tipAmount);
    order.totalSurcharge = this.safeGetNumber(transaction.surchargeAmount);
    order.totalGst = this.getTotalGstFromTransaction(transaction);
    order.dueAmount = 0;
    return order;
  };

  safeGetNumber = (value?: number | string): number => {
    return value && !Number.isNaN(Number(value)) ? Number(value) : 0;
  };

  updateOrderPayment = async (order: Order, paymentUpdateInput: UpdateOrderPaymentInput) => {
    const updatedOrder = { ...order };
    const paymentIndex = updatedOrder.payments?.findIndex((p) => p.id === paymentUpdateInput.paymentUuid) ?? -1;
    const updatedPaymentRecord = { ...updatedOrder.payments![paymentIndex] };

    if (paymentIndex === -1) {
      throw new BadRequestError('Payment not found');
    }
    if (updatedPaymentRecord.tenderType !== TenderType.CASH) {
      throw new BadRequestError('Only cash payments can be updated');
    }

    // only tips can be updated at the moment
    if (paymentUpdateInput.tips) {
      updatedPaymentRecord.tips = this.safeGetNumber(paymentUpdateInput.tips);
      // change is updated when tips are updated
      updatedPaymentRecord.change = this.safeGetNumber(paymentUpdateInput.change);
      updatedPaymentRecord.amount += updatedPaymentRecord.tips;
      updatedOrder.totalTips! += updatedPaymentRecord.tips;
      updatedOrder.paidAmount! += updatedPaymentRecord.tips;
      updatedOrder.payments![paymentIndex] = updatedPaymentRecord;
      updatedOrder.totalChange = updatedPaymentRecord.change;
      updatedOrder.totalAmount = updatedOrder.paidAmount!;
      this.setUpdatedTime(updatedOrder, paymentUpdateInput.updatedTime);
    }
    return updatedOrder;
  };
}
