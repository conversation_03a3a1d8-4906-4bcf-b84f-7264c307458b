import type { Config as LineItemConfig } from '@npco/bff-fe-common/dist';
import { LineItemCalculator } from '@npco/bff-fe-common/dist';
import { bankersRound } from '@npco/bff-fe-common/dist/lineItems/utils';
import { OrderAmountCalculator } from '@npco/bff-fe-common/dist/order/calculator';
import { generateShortId } from '@npco/component-bff-core/dist/utils/shortId';
import { OrderStatus } from '@npco/component-dto-order/dist';

import lodash from 'lodash';
import { v4 } from 'uuid';

import { AUD_MINOR_UNIT_CENTICENTS, CURRENCY_COMMON_MINOR_UNIT } from '../const';
import { BadRequestError } from '../error';
import type { Order, OrderDiscount, OrderItem, OrderServiceCharge } from '../types';

export class BaseModel {
  calculator = new OrderAmountCalculator();

  private readonly shortIdLength = 8;

  private readonly shortIdPrefix = 'ZP';

  private readonly nonCardPaymentReferencePrefix = 'REF';

  public getTimeUnix = () => Math.floor(new Date().getTime() / 1000);

  public getTimeUnixInMs = () => new Date().getTime();

  public getTimeUnixFromInput = (time: string) => Math.floor(new Date(time).getTime() / 1000);

  public getTimeUnixInMsFromInput = (time: string) => new Date(time).getTime();

  public setUpdatedTime(entity: { updatedTime?: number }) {
    /* eslint-disable no-param-reassign */
    entity.updatedTime = this.getTimeUnix();
  }

  public setCreatedTime(entity: { updatedTime?: number; createdTime?: number }) {
    /* eslint-disable no-param-reassign */
    entity.createdTime = this.getTimeUnix();
    entity.updatedTime = entity.createdTime;
  }

  readonly generateReference = () => {
    return `${this.shortIdPrefix}${generateShortId(this.shortIdLength)}`;
  };

  readonly generateNonCardPaymentShortId = () => {
    return `${generateShortId(this.shortIdLength)}`;
  };

  readonly getCentsFromCentiCents = (amount: number) => {
    return bankersRound(amount / 100);
  };

  mergedObject = (entity: any, existing: any) =>
    lodash.mergeWith(lodash.omit(existing, Object.keys(entity)), entity, (obj, src) => {
      if (Array.isArray(obj)) {
        return src;
      }
      if (src === null || JSON.stringify(src) === '{}') {
        return src;
      }
      return undefined;
    });

  getIdFromFE = (obj: { id: string }): string => {
    if (process.env.ID_SENT_FROM_FE_ENABLED !== 'true') {
      return obj.id ?? v4();
    }
    if (!obj.id) {
      throw new BadRequestError('id must be provided');
    } else {
      return obj.id;
    }
  };

  public calculateLineItems = (
    items: OrderItem[],
    discounts: OrderDiscount[],
    serviceCharges: OrderServiceCharge[],
    config: Partial<LineItemConfig> = {},
  ) => {
    const normalizeQuantity = <T extends { [key: string]: any; quantity?: number }>(item: T): T => ({
      ...item,
      quantity: item.quantity ?? 1,
    });

    const formattedItems = items.map((item) => ({
      ...normalizeQuantity(item),
      price: Number(item.price),
      modifiers:
        item.modifiers?.map((modifier) => ({
          ...normalizeQuantity(modifier),
          price: Number(modifier.price),
        })) ?? [],
      discounts: item.discounts?.map(normalizeQuantity) ?? [],
      serviceCharges: item.serviceCharges?.map(normalizeQuantity) ?? [],
    }));

    const formattedDiscounts = discounts.map(normalizeQuantity);
    const formattedServiceCharges = serviceCharges.map(normalizeQuantity);

    return new LineItemCalculator(config).calculate({
      lineItems: formattedItems as any[],
      discounts: formattedDiscounts as any[],
      serviceCharges: formattedServiceCharges as any[],
    });
  };

  calculateOrderAmounts = (order: Order) => {
    const {
      total: orderAmount,
      tax: totalGst,
      lineItems,
      discount: orderLevelDiscount,
      discounts: orderDiscountsMap,
      serviceCharge: orderLevelServiceCharge,
      serviceCharges: orderServiceChargesMap,
    } = this.calculateLineItems(order.items || [], order.discounts || [], order.serviceCharges || [], {
      itemsTaxInclusive: order.catalogSettings.itemsTaxInclusive,
    });
    const subtotalAmount = Object.values(lineItems).reduce((acc, curr) => {
      return acc + curr.total;
    }, 0);

    const totalDiscount = orderLevelDiscount;
    const discounts = order.discounts?.map((discount) => {
      const discountedAmount = orderDiscountsMap[discount.ordinal] ?? 0;
      return {
        ...discount,
        discountedAmount,
      };
    });
    const totalServiceCharge = orderLevelServiceCharge;
    const serviceCharges = order.serviceCharges?.map((serviceCharge) => {
      const serviceChargeAmount = orderServiceChargesMap[serviceCharge.ordinal] ?? 0;
      return {
        ...serviceCharge,
        serviceChargeAmount,
      };
    });

    return {
      orderAmount: this.getCentsFromCentiCents(orderAmount),
      totalGst: this.getCentsFromCentiCents(totalGst),
      subtotalAmount: this.getCentsFromCentiCents(subtotalAmount),
      totalDiscount: this.getCentsFromCentiCents(totalDiscount),
      totalServiceCharge: this.getCentsFromCentiCents(totalServiceCharge),
      discounts,
      serviceCharges,
    };
  };

  safeGetNumber = (value?: number): number => {
    return value && !Number.isNaN(Number(value)) ? Number(value) : 0;
  };

  updateAmountsInUnpaidOrder = (order: Order, isMigration = false) => {
    if (order.status === OrderStatus.PAID && !isMigration) {
      throw new BadRequestError(`This method cannot be used on PAID orders.`);
    }
    const { orderAmount, subtotalAmount, totalGst, totalServiceCharge, totalDiscount, discounts, serviceCharges } =
      this.calculateOrderAmounts(order);
    const paidAmount = this.safeGetNumber(order.paidAmount);
    order.dueAmount =
      paidAmount > 0 ? orderAmount - (order.orderAmount - this.safeGetNumber(order.dueAmount)) : orderAmount;
    order.orderAmount = orderAmount;
    order.totalAmount = orderAmount;
    order.subtotalAmount = subtotalAmount;
    order.totalGst = totalGst;
    order.orderGst = totalGst;
    order.totalDiscount = totalDiscount;
    order.discounts = discounts;
    order.totalServiceCharge = totalServiceCharge;
    order.serviceCharges = serviceCharges;
    order.orderDisplayAmount = order.orderAmount > paidAmount ? order.orderAmount : paidAmount;
  };

  convertCentiCentsToCentsWithRounding = (amount?: number): number => {
    const value = this.safeGetNumber(amount);
    return bankersRound(value / 100);
  };

  convertPaidOrderAmountToCents = (order: Order) => {
    if (
      order.currencyMinorUnit === AUD_MINOR_UNIT_CENTICENTS &&
      order.currency === 'AUD' &&
      order.status === OrderStatus.PAID
    ) {
      order.currencyMinorUnit = CURRENCY_COMMON_MINOR_UNIT;
      order.paidAmount = this.convertCentiCentsToCentsWithRounding(order.paidAmount);
      order.dueAmount = this.convertCentiCentsToCentsWithRounding(order.dueAmount);
      order.orderAmount = this.convertCentiCentsToCentsWithRounding(order.orderAmount);
      order.totalAmount = this.convertCentiCentsToCentsWithRounding(order.totalAmount);
      order.subtotalAmount = this.convertCentiCentsToCentsWithRounding(order.subtotalAmount);
      order.orderDisplayAmount = this.convertCentiCentsToCentsWithRounding(order.orderDisplayAmount);
      order.totalGst = this.convertCentiCentsToCentsWithRounding(order.totalGst);
      order.orderGst = this.convertCentiCentsToCentsWithRounding(order.orderGst);
      order.totalDiscount = this.convertCentiCentsToCentsWithRounding(order.totalDiscount);
      order.totalServiceCharge = this.convertCentiCentsToCentsWithRounding(order.totalServiceCharge);
      order.totalTips = this.convertCentiCentsToCentsWithRounding(order.totalTips);
      order.totalSurcharge = this.convertCentiCentsToCentsWithRounding(order.totalSurcharge);
      order.totalSurchargedGst = this.convertCentiCentsToCentsWithRounding(order.totalSurchargedGst);
      order.cashRoundingAdjustment = this.convertCentiCentsToCentsWithRounding(order.cashRoundingAdjustment);
      order.totalAmountTendered = this.convertCentiCentsToCentsWithRounding(order.totalAmountTendered);
      order.totalChange = this.convertCentiCentsToCentsWithRounding(order.totalChange);
      order.payments = order.payments?.map((payment) => {
        return {
          ...payment,
          amount: this.convertCentiCentsToCentsWithRounding(payment.amount),
          tips: this.convertCentiCentsToCentsWithRounding(payment.tips),
          amountTendered: this.convertCentiCentsToCentsWithRounding(payment.amountTendered),
          change: this.convertCentiCentsToCentsWithRounding(payment.change),
          surchargeAmount: this.convertCentiCentsToCentsWithRounding(payment.surchargeAmount),
          cashRoundingAdjustment: this.convertCentiCentsToCentsWithRounding(payment.cashRoundingAdjustment),
        };
      });
    }
  };

  convertOrderAmountToCentsIfRequired = (order: Order) => {
    if (order.currencyMinorUnit === AUD_MINOR_UNIT_CENTICENTS && order.currency === 'AUD') {
      order.currencyMinorUnit = CURRENCY_COMMON_MINOR_UNIT;
      if (order.status === OrderStatus.PAID) {
        this.convertPaidOrderAmountToCents(order);
      }
      this.updateAmountsInUnpaidOrder(order);
    }
  };

  safeGetArray = <T>(arr: T[] | undefined | null): T[] => {
    if (arr && arr.length) {
      return arr;
    }
    return [];
  };
}
