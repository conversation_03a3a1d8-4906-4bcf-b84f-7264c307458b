import { CatalogDiscountConfig, CatalogServiceChargeConfig } from '@npco/component-dto-catalog/dist';
import type { OrderItem } from '@npco/component-dto-order/dist/types';
import { OrderItemType } from '@npco/component-dto-order/dist/types';

import type { OrderItemModifier } from 'domain/entities';
import { v4 } from 'uuid';

import { OrderDiscountModel } from './orderDiscountModel';
import { OrderItemModel } from './orderItemModel';
import { OrderServiceChargeModel } from './orderServiceChargeModel';

describe('ItemModel test suite', () => {
  const itemModel = new OrderItemModel(new OrderDiscountModel(), new OrderServiceChargeModel());

  const getItem = () => ({
    id: v4(),
    name: v4(),
    price: 10000,
    taxes: [],
    catalogItem: {
      id: v4(),
    },
    quantity: 1.1,
  });
  const getItemModelInput = () => {
    return {
      id: v4(), /// orderId
      entityUuid: v4(),
      items: [getItem(), getItem(), getItem(), getItem()],
      siteSettings: { order: {} },
    };
  };

  describe('create', () => {
    it('should not create items when its not set in order', async () => {
      await expect(itemModel.create({} as any)).resolves.toEqual(undefined);
    });

    it('should create items when its set in order', async () => {
      const order = {
        entityUuid: v4(),
        siteSettings: { order: {} },
        catalogSettings: { itemsTaxInclusive: true },
        items: [
          {
            name: v4(),
            taxes: [],
            price: 10000,
            catalogItem: {
              id: v4(),
            },
            discounts: [{ config: CatalogDiscountConfig.AMOUNT, value: 1234 }],
            serviceCharges: [{ config: CatalogServiceChargeConfig.AMOUNT, value: 1234 }],
          },
        ],
      };
      const items = await itemModel.create(order as any);
      expect(items).toHaveLength(1);
      expect(items![0].name).toEqual(order.items[0].name);
    });

    it('should create items with amounts', async () => {
      const itemModel2 = new OrderItemModel(new OrderDiscountModel(), new OrderServiceChargeModel());
      const order = {
        entityUuid: v4(),
        siteSettings: { order: {} },
        catalogSettings: { itemsTaxInclusive: true },
        items: [
          {
            name: v4(),
            taxes: [],
            price: 900000,
            quantity: 1,
            catalogItem: {
              id: v4(),
            },
            modifiers: [
              {
                name: v4(),
                price: 100000,
                quantity: 1,
              },
            ],
            discounts: [
              { config: CatalogDiscountConfig.AMOUNT, value: 200000 },
              { config: CatalogDiscountConfig.PERCENTAGE, value: 10 },
            ],
            serviceCharges: [
              { config: CatalogServiceChargeConfig.AMOUNT, value: 200000 },
              { config: CatalogServiceChargeConfig.PERCENTAGE, value: 10 },
            ],
          },
        ],
      };
      const items = await itemModel2.create(order as any);
      expect(items).toHaveLength(1);
      expect(items![0].name).toEqual(order.items[0].name);
      expect(items![0].subtotalAmount).toEqual(900000);
      expect(items![0].totalAmount).toEqual(1000000);
      expect(items![0].modifiers![0].subtotalAmount).toEqual(100000);
      expect(items![0].discounts![0].discountedAmount).toEqual(200000);
      expect(items![0].discounts![1].discountedAmount).toEqual(200000);
      expect(items![0].serviceCharges![0].serviceChargeAmount).toEqual(200000);
      expect(items![0].serviceCharges![1].serviceChargeAmount).toEqual(200000);
    });
  });

  describe('edit', () => {
    it('should update items', async () => {
      const itemModelInput = getItemModelInput();
      const newItems = {
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: JSON.parse(JSON.stringify(itemModelInput.items)) as any[],
      };
      const originalItems = {
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: JSON.parse(JSON.stringify(itemModelInput.items)) as any[],
      };
      // 1 updated sku
      newItems.items[0].sku = undefined as any;
      const updated1 = newItems.items[0];
      // 1 updated name
      newItems.items[1].name = 'updated';
      const updated2 = newItems.items[1];
      // 1 not updated
      const unchangedItem = newItems.items[2];

      // 1 deleted + 1 new created
      const createdItem = { ...getItem(), id: undefined };
      const deletedItem = originalItems.items[3];
      newItems.items[3] = createdItem;

      const { items, delta } = await itemModel.update(originalItems as any, newItems as any);

      expect(delta.updated).toHaveLength(2);
      expect(delta.deleted).toHaveLength(1);
      expect(delta.created).toHaveLength(1);
      // verify 1: item also added to updated array
      expect(delta.updated![0].id).toEqual(updated1.id);
      expect(delta.updated![0].sku).toEqual(undefined);
      expect(items?.find((item) => item.id === updated1.id)).not.toBeUndefined();
      // verify 2: item added to updated array
      expect(delta.updated![1].id).toEqual(updated2.id);
      expect(delta.updated![1].name).toEqual(updated2.name);
      expect(items?.find((item) => item.id === updated2.id)).not.toBeUndefined();
      // verify 3: new item added to created array
      expect(delta.created![0].name).toEqual(createdItem.name);
      expect(items?.find((item) => item.name === createdItem.name)).not.toBeUndefined();
      // verify 4: deleted item added to deleted array and missing from newdelta array
      expect(delta.deleted![0].id).toEqual(deletedItem.id);
      expect(items?.find((item) => item.id === deletedItem.id)).toBeUndefined();

      // verify 5: newItems array should include unchanged items
      expect(items?.find((item) => item.id === unchangedItem.id)).not.toBeUndefined();
    });

    it('should update items', async () => {
      const itemModel2 = new OrderItemModel(new OrderDiscountModel(), new OrderServiceChargeModel());
      const itemModelInput = getItemModelInput();
      const newItems = {
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: JSON.parse(JSON.stringify(itemModelInput.items)) as any[],
      };
      const originalItems = {
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: JSON.parse(JSON.stringify(itemModelInput.items)) as any[],
      };
      // 1 updated sku
      newItems.items[0].price = 2000;
      newItems.items[0].quantity = 2;
      newItems.items[0].modifiers = [{ name: 'box', price: 1000, quantity: 1 }];
      const updated1 = newItems.items[0];

      const { delta } = await itemModel2.update(originalItems as any, newItems as any);

      expect(delta.updated).toHaveLength(1);
      expect(delta.deleted).toHaveLength(0);
      // verify 1: item also added to updated array
      expect(delta.updated![0].id).toEqual(updated1.id);
      expect(delta.updated![0].price).toEqual(2000);
      expect(delta.updated![0].subtotalAmount).toEqual(4000);
      expect(delta.updated![0].modifiers![0].subtotalAmount).toEqual(1000);
      expect(delta.updated![0].totalAmount).toEqual(5000);
    });

    it('should remove all items', async () => {
      const itemModelInput = getItemModelInput();
      const newItems = {
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: [],
        siteSettings: { order: {} },
      };
      const originalItems = {
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: JSON.parse(JSON.stringify(itemModelInput.items)) as any[],
        siteSettings: { order: {} },
      };

      const items = await itemModel.update(originalItems as any, newItems as any);

      expect(items.delta.deleted).toHaveLength(originalItems.items.length);
    });

    it('should update item discounts', async () => {
      const itemModelInput = getItemModelInput();
      const originalItems = {
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: JSON.parse(JSON.stringify(itemModelInput.items)),
        siteSettings: { order: { discountsEnabled: true } },
      };
      const updatedItems = {
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: originalItems.items.map((item: any) => ({
          ...item,
          discounts: [{ config: CatalogDiscountConfig.AMOUNT, value: 1234 }],
          serviceCharges: [{ config: CatalogServiceChargeConfig.AMOUNT, value: 1234 }],
        })),
      };

      const items = await itemModel.update(originalItems as any, updatedItems as any);
      expect(items.items).toBeDefined();
    });

    it('should update item modifiers', async () => {
      const itemModelInput = getItemModelInput();
      const [firstOrderItem] = itemModelInput.items;
      const orginalOrder = {
        id: itemModelInput.id,
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: [firstOrderItem],
        siteSettings: { order: { discountsEnabled: true } },
      };

      const updatedOrder = {
        id: itemModelInput.id,
        entityUuid: itemModelInput.entityUuid,
        catalogSettings: { itemsTaxInclusive: true },
        items: orginalOrder.items.map((item: any) => ({
          ...item,
          // adding a new modifier without 'id'
          modifiers: [
            {
              name: 'Full Cream',
              price: '0',
              ordinal: 1,
              unit: 'QUANTITY',
              quantity: 1,
            },
          ],
        })),
      };

      const updateResult = await itemModel.update(orginalOrder as any, updatedOrder as any);
      const [item] = updateResult.items as unknown as OrderItem[];
      const [modifier] = item.modifiers as unknown as OrderItemModifier[];
      expect(modifier.id).toBeDefined();
      expect(modifier.type).toEqual(OrderItemType.MODIFIER);
      expect(modifier.parentId).toEqual(item.id);
    });
  });
});
