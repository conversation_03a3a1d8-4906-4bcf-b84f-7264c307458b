import { BaseModel } from './baseModel';

describe('base model tests', () => {
  const model = new BaseModel();

  it('should merge two objects', () => {
    const res = model.mergedObject({ images: [], name: null, settings: { tags: [] } }, { items: [] });
    expect(res).toEqual({ images: [], items: [], name: null, settings: { tags: [] } });
  });

  describe('getIdFromFE', () => {
    describe('ID_SENT_FROM_FE_ENABLED true', () => {
      beforeAll(() => {
        process.env.ID_SENT_FROM_FE_ENABLED = 'true';
      });
      it('should throw error when id not provided', () => {
        expect(() => model.getIdFromFE({} as any)).toThrow('id must be provided');
      });
      it('should return id when provided', () => {
        expect(model.getIdFromFE({ id: 'id' })).toEqual('id');
      });
    });

    describe('ID_SENT_FROM_FE_ENABLED false', () => {
      beforeAll(() => {
        process.env.ID_SENT_FROM_FE_ENABLED = 'false';
      });
      it('should throw error when id not provided', () => {
        expect(model.getIdFromFE({} as any)).toBeDefined();
      });
      it('should return id when provided', () => {
        expect(model.getIdFromFE({ id: 'id' })).toEqual('id');
      });
    });
  });

  describe('convert paid order amounts to cents', () => {
    it('should convert paid order amounts to cents', () => {
      const order = {
        status: 'PAID',
        currency: 'AUD',
        currencyMinorUnit: 4,
        paidAmount: 100000,
        dueAmount: 100000,
        totalAmount: 100000,
        orderAmount: 100000,
        subtotalAmount: 100000,
        totalSurcharge: 1112,
        totalSurchargedGst: 110,
        totalGst: 1120,
        orderGst: 1120,
        cashRoundingAdjustment: 1,
        totalTips: 2389,
        totalDiscount: 9999,
        totalServiceCharge: 9856,
        orderDisplayAmount: 100000,
        totalAmountTendered: 100000,
        totalChange: 1112,
        payments: [
          {
            amount: 100000,
            amountTendered: 100000,
            change: 1112,
            tips: 2389,
            surchargeAmount: 0,
            cashRoundingAdjustment: 0,
          },
        ],
      };
      model.convertPaidOrderAmountToCents(order as any);
      expect(order.currencyMinorUnit).toEqual(2);
      expect(order.paidAmount).toEqual(1000);
      expect(order.dueAmount).toEqual(1000);
      expect(order.totalAmount).toEqual(1000);
      expect(order.orderAmount).toEqual(1000);
      expect(order.subtotalAmount).toEqual(1000);
      expect(order.totalSurcharge).toEqual(11);
      expect(order.totalSurchargedGst).toEqual(1);
      expect(order.totalGst).toEqual(11);
      expect(order.orderGst).toEqual(11);
      expect(order.cashRoundingAdjustment).toEqual(0);
      expect(order.totalTips).toEqual(24);
      expect(order.totalDiscount).toEqual(100);
      expect(order.totalServiceCharge).toEqual(99);
      expect(order.orderDisplayAmount).toEqual(1000);
      expect(order.totalAmountTendered).toEqual(1000);
      expect(order.totalChange).toEqual(11);

      expect(order.payments[0].amount).toEqual(1000);
      expect(order.payments[0].amountTendered).toEqual(1000);
      expect(order.payments[0].change).toEqual(11);
      expect(order.payments[0].tips).toEqual(24);
      expect(order.payments[0].surchargeAmount).toEqual(0);
      expect(order.payments[0].cashRoundingAdjustment).toEqual(0);
    });
  });

  describe('safe get array', () => {
    it('should return empty array when value is undefined', () => {
      expect(model.safeGetArray(undefined)).toEqual([]);
    });
  });
});
