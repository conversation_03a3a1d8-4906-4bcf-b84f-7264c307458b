import { debug } from '@npco/component-bff-core/dist/utils/logger';
import { CatalogServiceChargeType } from '@npco/component-dto-catalog/dist';

import { Injectable } from '@nestjs/common';
import lodash from 'lodash';

import type { OrderItem } from '../entities';
import { OrderServiceCharge } from '../entities';
import type { Order } from '../entities/order';
import type { CreateOrderServiceChargeInput } from '../types';

import { BaseModel } from './baseModel';

@Injectable()
export class OrderServiceChargeModel extends BaseModel {
  createCore = (entityUuid: string, serviceCharges?: CreateOrderServiceChargeInput[]) => {
    return serviceCharges?.map((d) => {
      const orderServiceCharge = new OrderServiceCharge();
      orderServiceCharge.id = this.getIdFromFE(d);
      orderServiceCharge.entityUuid = entityUuid;
      orderServiceCharge.catalogServiceChargeUuid = d.catalogServiceChargeUuid;
      orderServiceCharge.catalogServiceCharge = d.catalogServiceCharge;
      orderServiceCharge.name = d.name;
      orderServiceCharge.config = d.config;
      orderServiceCharge.type = CatalogServiceChargeType.BASIC;
      orderServiceCharge.value = Number(d.value);
      orderServiceCharge.ordinal = d.ordinal;
      this.setCreatedTime(orderServiceCharge);
      return orderServiceCharge;
    });
  };

  isEqual = (original: OrderServiceCharge, updated: CreateOrderServiceChargeInput) => {
    const originalServiceCharge = JSON.parse(JSON.stringify(original));
    const omitServiceChargeFields = ['createdTime', 'updatedTime', 'catalogServiceCharge'];
    return lodash.isEqual(
      lodash.omit(originalServiceCharge, omitServiceChargeFields),
      lodash.omit(updated, omitServiceChargeFields),
    );
  };

  getUpdatedServiceCharges = (original?: OrderServiceCharge[], updateInput?: CreateOrderServiceChargeInput[]) =>
    updateInput
      ?.filter((updatedServiceCharge) =>
        original?.find(
          (originalServiceCharge) =>
            originalServiceCharge.id === updatedServiceCharge.id &&
            !this.isEqual(originalServiceCharge, updatedServiceCharge),
        ),
      )
      .map((updatedServiceCharge) => {
        const existing = original?.find((origServiceCharge) => origServiceCharge.id === updatedServiceCharge.id);
        const newServiceCharge = this.mergedObject(updatedServiceCharge, existing) as OrderServiceCharge;
        debug(newServiceCharge);
        return newServiceCharge;
      });

  getUnchangedServiceCharges = (original?: OrderServiceCharge[], updateInput?: CreateOrderServiceChargeInput[]) =>
    original?.filter((originalServiceCharge) =>
      updateInput?.find(
        (updatedServiceCharge) =>
          originalServiceCharge.id === updatedServiceCharge.id &&
          this.isEqual(originalServiceCharge, updatedServiceCharge),
      ),
    );

  getCreatedServiceCharges = (
    entityUuid: string,
    originalServiceCharges?: OrderServiceCharge[],
    updateInput?: CreateOrderServiceChargeInput[],
  ) => {
    const existingServiceCharges = new Set<string>();
    originalServiceCharges?.forEach((d) => existingServiceCharges.add(d.id));
    const createServiceChargesInput = updateInput?.filter((item) => !existingServiceCharges.has(item.id));
    return this.createCore(entityUuid, createServiceChargesInput as any);
  };

  updateCore = (entityUuid: string, original?: OrderServiceCharge[], updateInput?: CreateOrderServiceChargeInput[]) => {
    let updated;
    let created;
    let unchangedServiceCharges;
    if (updateInput && updateInput.length > 0) {
      unchangedServiceCharges = this.getUnchangedServiceCharges(original, updateInput);
      created = this.getCreatedServiceCharges(entityUuid, original, updateInput);
      updated = this.getUpdatedServiceCharges(original, updateInput);
    }
    const updatedServiceCharges = [unchangedServiceCharges, created, updated]
      .filter((d) => d?.length)
      .reduce((acc, val) => acc?.concat(val as OrderServiceCharge[]), []);
    return updatedServiceCharges;
  };

  createItemLevelServiceCharges = (
    item: OrderItem,
    serviceCharges?: CreateOrderServiceChargeInput[],
  ): OrderServiceCharge[] | undefined => {
    const coreServiceCharges = this.createCore(item.entityUuid, serviceCharges);
    return coreServiceCharges?.map((d) => ({ ...d, orderItemId: item.id }));
  };

  createOrderLevelServiceCharges = (order: Order, serviceCharges?: CreateOrderServiceChargeInput[]) => {
    const coreServiceCharges = this.createCore(order.entityUuid, serviceCharges);
    return coreServiceCharges?.map((d) => ({ ...d, orderId: order.id }));
  };

  updateItemLevelServiceCharges = (original: OrderItem, updateInput?: CreateOrderServiceChargeInput[]) => {
    const coreServiceCharges = this.updateCore(original.entityUuid, original.serviceCharges, updateInput);
    return coreServiceCharges?.map((d) => ({ ...d, orderItemId: original.id }));
  };

  updateOrderLevelServiceCharges = (original: Order, updateInput?: CreateOrderServiceChargeInput[]) => {
    const coreServiceCharges = this.updateCore(original.entityUuid, original.serviceCharges, updateInput);
    return coreServiceCharges?.map((d) => ({ ...d, orderId: original.id }));
  };
}
