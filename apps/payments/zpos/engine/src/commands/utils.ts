import type {
  OrderAmounts,
  OrderDiscount,
  OrderItem,
  OrderPaymentStatus,
  OrderServiceCharge,
} from '@npco/component-dto-order/dist';

import type {
  Order,
  OrderDiscount as Discount,
  OrderPayment,
  OrderServiceCharge as ServiceCharge,
} from '../domain/entities';

import { AUDMoney } from './types';

export const getAUDMoneyForCents = (cents?: number): AUDMoney => {
  return new AUDMoney((cents ?? 0) * 100); // Convert cents to dollars
};

export const mapDiscounts = (discounts?: Discount[]) => {
  return (discounts ?? []).map(
    (discount) =>
      ({
        ...discount,
        discountedAmount: new AUDMoney(discount.discountedAmount),
        value: String(discount.value),
        amount: discount.discountedAmount,
      } as OrderDiscount),
  );
};

export const mapServiceCharges = (serviceCharges?: ServiceCharge[]) => {
  return (serviceCharges ?? []).map(
    (serviceCharge) =>
      ({
        ...serviceCharge,
        serviceChargeAmount: new AUDMoney(serviceCharge.serviceChargeAmount),
        value: String(serviceCharge.value),
        amount: serviceCharge.serviceChargeAmount,
      } as OrderServiceCharge),
  );
};

export const getOrderItem = (order: Order) => {
  return (order.items ?? []).map(
    (item) =>
      ({
        id: item.id,
        name: item.name,
        price: new AUDMoney(item.price),
        amount: item.price,
        ordinal: item.ordinal,
        unit: item.unit,
        type: item.type,
        discounts: mapDiscounts(item.discounts),
        serviceCharges: mapServiceCharges(item.serviceCharges),
        catalogItem: item.catalogItem,
        taxes: item.taxes,
        quantity: item.quantity,
        variantName: item.variantName,
        modifiers: item.modifiers?.map((m) => ({
          ...m,
          price: new AUDMoney(m.price),
          amount: m.price,
          subtotal: m.subtotalAmount,
          // currency: m.currency,
          subtotalAmount: new AUDMoney(m.subtotalAmount),
        })),
        // currency: item.currency,
        subtotalAmount: new AUDMoney(item.subtotalAmount),
        subtotal: item.subtotalAmount,
      } as unknown as OrderItem),
  );
};

export const getOrderDiscount = (order: Order) => {
  return mapDiscounts(order.discounts);
};

export const getOrderServiceCharge = (order: Order) => {
  return mapServiceCharges(order.serviceCharges);
};

export const getOrderPayment = (newPayment: OrderPayment) => {
  return {
    id: newPayment.id,
    entityUuid: newPayment.entityUuid,
    amount: getAUDMoneyForCents(newPayment.amount),
    amountTendered: getAUDMoneyForCents(newPayment.amountTendered),
    change: getAUDMoneyForCents(newPayment.change),
    type: newPayment.type as any,
    status: newPayment.status as OrderPaymentStatus,
    transactionUuid: newPayment.transactionUuid,
    surchargeAmount: getAUDMoneyForCents(newPayment.surchargeAmount),
    tips: getAUDMoneyForCents(newPayment.tips),
    taxAmounts: newPayment.taxAmounts,
    tenderType: newPayment.tenderType,
    tenderSubType: newPayment.tenderSubType,
    note: newPayment.note,
    shortId: newPayment.shortId,
    timestamp: newPayment.timestamp,
    timestampLocal: newPayment.timestampLocal,
    currency: newPayment.currency,
    amounts: {
      amount: newPayment.amount,
      tips: newPayment.tips,
      surchargeAmount: newPayment.surchargeAmount,
      amountTendered: newPayment.amountTendered,
      change: newPayment.change,
      cashRoundingAdjustment: newPayment.cashRoundingAdjustment,
    },
  };
};

export const getOrderAmounts = (order: Order): OrderAmounts => {
  return {
    paidAmount: order.paidAmount,
    dueAmount: order.dueAmount,
    orderAmount: order.orderAmount,
    totalSurcharge: order.totalSurcharge,
    totalGst: order.totalGst,
    orderGst: order.orderGst,
    totalDiscount: order.totalDiscount,
    totalServiceCharge: order.totalServiceCharge,
    totalTips: order.totalTips,
    subtotalAmount: order.subtotalAmount,
    orderDisplayAmount: order.orderDisplayAmount,
    totalAmountTendered: order.totalAmountTendered,
    cashRoundingAdjustment: order.cashRoundingAdjustment,
    totalChange: order.totalChange,
  };
};
