import type { ISO4217, Money } from '@npco/component-dto-core/dist';

export interface CommandDto {
  aggregateId: string;
  aggregateUri: string;
  event: any;
}

/**
 * @deprecated: UK currency refactor. Only used to support legacy code.
 */
export class AUDMoney implements Money {
  readonly currency: ISO4217 = 'AUD' as ISO4217;

  readonly value: string;

  constructor(amount: number | undefined) {
    this.value = String(Number(amount ?? 0)); // Convert cents to centi-cents;
  }
}
