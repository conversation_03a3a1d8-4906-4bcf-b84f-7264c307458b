import { OrderPaidDto, OrderPartPaidDto, OrderPaymentStatus, OrderStatus } from '@npco/component-dto-order/dist';

import type { Order, OrderPayment } from '../domain/entities';

import { OrderBaseCommand } from './baseCommand';
import type { CommandDto } from './types';
import {
  getAUDMoneyForCents,
  getOrderAmounts,
  getOrderDiscount,
  getOrderItem,
  getOrderPayment,
  getOrderServiceCharge,
} from './utils';

export class OrderPartPaidCommand extends OrderBaseCommand {
  constructor(protected readonly order: Order, protected readonly newPayment?: OrderPayment) {
    super(order);
  }

  getCommandDto(): CommandDto {
    const order = this.order;
    let dto = new OrderPartPaidDto({
      id: order.id,
      entityUuid: order.entityUuid,
      status: order.status,
      paidAmount: getAUDMoneyForCents(order.paidAmount),
      dueAmount: getAUDMoneyForCents(order.dueAmount),
      orderAmount: getAUDMoneyForCents(order.orderAmount),
      totalAmount: getAUDMoneyForCents(order.totalAmount),
      totalSurcharge: getAUDMoneyForCents(order.totalSurcharge),
      totalGst: getAUDMoneyForCents(order.totalGst),
      totalDiscount: getAUDMoneyForCents(order.totalDiscount),
      totalServiceCharge: getAUDMoneyForCents(order.totalServiceCharge),
      totalTips: getAUDMoneyForCents(order.totalTips),
      paidTime: order.paidTime,
      updatedTimeInMilliseconds: Number(order.updatedTimeInMilliseconds),
      updatedTime: order.updatedTime,
      orderDisplayAmount: getAUDMoneyForCents(order.orderDisplayAmount),
      totalChange: getAUDMoneyForCents(order.totalChange),
      totalAmountTendered: getAUDMoneyForCents(order.totalAmountTendered),
      cashRoundingAdjustment: getAUDMoneyForCents(order.cashRoundingAdjustment),
      newPayment: this.newPayment
        ? {
            ...getOrderPayment(this.newPayment),
            status: OrderPaymentStatus.APPROVED,
          }
        : undefined,
      amounts: getOrderAmounts(order),
    });
    let aggregateUri = 'Order.PartPaid';
    if (![OrderStatus.PAID, OrderStatus.PART_PAID].includes(order.status)) {
      throw new Error(`Order status ${order.status} is not allowed in this command dto`);
    }
    if (order.status === OrderStatus.PAID) {
      aggregateUri = 'Order.Paid';
      dto = new OrderPaidDto({
        ...dto,
        items: getOrderItem(order),
        discounts: getOrderDiscount(order),
        serviceCharges: getOrderServiceCharge(order),
        createdFromDeviceUuid: order.createdFromDeviceUuid ?? '',
        siteUuid: order.siteUuid,
        catalogSettings: order.catalogSettings,
        referenceNumber: order.referenceNumber,
      });
    }
    return {
      aggregateId: order.id,
      aggregateUri,
      event: dto,
    };
  }
}
