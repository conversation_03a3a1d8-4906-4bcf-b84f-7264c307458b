import { OrderCreatedDto } from '@npco/component-dto-order/dist';

import { OrderBaseCommand } from './baseCommand';
import type { CommandDto } from './types';
import { getAUDMoneyForCents, getOrderAmounts, getOrderDiscount, getOrderItem, getOrderServiceCharge } from './utils';

export class OrderCreatedCommand extends OrderBaseCommand {
  getCommandDto(): CommandDto {
    const order = this.order;
    const dto = new OrderCreatedDto({
      id: order.id,
      entityUuid: order.entityUuid,
      createdFromDeviceUuid: order.createdFromDeviceUuid,
      status: order.status,
      referenceNumber: order.referenceNumber,
      siteUuid: order.siteUuid,
      createdTime: order.createdTime,
      items: getOrderItem(order),
      discounts: getOrderDiscount(order),
      serviceCharges: getOrderServiceCharge(order),
      paidAmount: getAUDMoneyForCents(order.paidAmount),
      dueAmount: getAUDMoneyForCents(order.dueAmount),
      orderAmount: getAUDMoneyForCents(order.orderAmount),
      totalAmount: getAUDMoneyForCents(order.totalAmount),
      totalSurcharge: getAUDMoneyForCents(order.totalSurcharge),
      totalGst: getAUDMoneyForCents(order.totalGst),
      orderGst: getAUDMoneyForCents(order.orderGst),
      totalDiscount: getAUDMoneyForCents(order.totalDiscount),
      totalServiceCharge: getAUDMoneyForCents(order.totalServiceCharge),
      totalTips: getAUDMoneyForCents(order.totalTips),
      subtotalAmount: getAUDMoneyForCents(order.subtotalAmount),
      catalogSettings: order.catalogSettings,
      updatedTime: order.updatedTime,
      createdTimestampLocal: order.createdTimestampLocal,
      updatedTimeInMilliseconds: Number(order.updatedTimeInMilliseconds),
      orderDisplayAmount: getAUDMoneyForCents(order.orderDisplayAmount),
      currency: order.currency,
      amounts: getOrderAmounts(order),
    });
    return {
      aggregateId: order.id,
      aggregateUri: 'Order.Created',
      event: dto,
    };
  }
}
