import { OrderPaymentUpdatedDto } from '@npco/component-dto-order/dist';

import type { Order, OrderPayment } from '../domain/entities';

import { OrderBaseCommand } from './baseCommand';
import type { CommandDto } from './types';
import { AUDMoney } from './types';
import { getOrderAmounts, getOrderPayment } from './utils';

export class OrderPaymentUpdatedCommand extends OrderBaseCommand {
  constructor(protected readonly order: Order, protected readonly updatedPayment: OrderPayment) {
    super(order);
  }

  getCommandDto(): CommandDto {
    const order = this.order;
    const dto = new OrderPaymentUpdatedDto({
      id: order.id,
      entityUuid: order.entityUuid,
      referenceNumber: order.referenceNumber,
      orderAmount: new AUDMoney(order.orderAmount),
      totalAmount: new AUDMoney(order.totalAmount),
      paidAmount: new AUDMoney(order.paidAmount),
      totalTips: new AUDMoney(order.totalTips),
      orderDisplayAmount: new AUDMoney(order.orderDisplayAmount),
      totalChange: new AUDMoney(order.totalChange),
      totalAmountTendered: new AUDMoney(order.totalAmountTendered),
      updatedPayment: getOrderPayment(this.updatedPayment),
      updatedTimeInMilliseconds: order.updatedTimeInMilliseconds!,
      amounts: getOrderAmounts(order),
    });
    return {
      aggregateId: order.id,
      aggregateUri: 'Order.PaymentUpdated',
      event: dto,
    };
  }
}
