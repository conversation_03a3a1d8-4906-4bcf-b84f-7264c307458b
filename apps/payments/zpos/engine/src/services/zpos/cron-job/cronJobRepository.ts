import { OrderStatus } from '@npco/component-events-core/dist/order/types';

import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { Brackets } from 'typeorm/query-builder/Brackets';

import { Order } from '../../../domain/entities/order';
import { OrderModel } from '../../../domain/models/orderModel';
import { BaseRepository } from '../../base/baseRepository';
import type { OrderOutlierSummary } from '../../types';

@Injectable()
export class CronJobRepository extends BaseRepository<Order> {
  constructor(private readonly ds: DataSource, private readonly orderModel: OrderModel) {
    super('order', ds);
  }

  getRepository = () => this.ds.getRepository(Order);

  getMismatchedPaidOrderUuids = async (): Promise<OrderOutlierSummary[]> =>
    this.getRepository()
      .createQueryBuilder('order')
      .select([
        'order.id',
        'order.status',
        'order.paidAmount',
        'order.orderAmount',
        'order.totalTips',
        'order.totalSurcharge',
      ])
      .where('order.status = :status', { status: OrderStatus.PAID })
      .andWhere('order.paidAmount IS NOT NULL')
      .andWhere('order.orderAmount IS NOT NULL')
      .andWhere('order.paidAmount <> (order.orderAmount + order.totalTips + order.totalSurcharge)')
      .andWhere('to_timestamp(order.createdTime)::date = CURRENT_DATE')
      .getRawMany();

  getOpenOrderUuidsWithPayments = async (): Promise<OrderOutlierSummary[]> =>
    this.getRepository()
      .createQueryBuilder('order')
      .innerJoin('order.payments', 'payment')
      .select(['order.id', 'order.status', 'order.paidAmount', 'order.orderAmount'])
      .where('order.status = :status', { status: OrderStatus.OPEN })
      .andWhere(
        new Brackets((qb) => {
          qb.where('payment.status = :approved', { approved: 'APPROVED' }).orWhere('payment.amount > 0');
        }),
      )
      .andWhere('to_timestamp(order.createdTime)::date = CURRENT_DATE')
      .distinct(true)
      .getRawMany();
}
