import { info } from '@npco/component-bff-core/dist/utils/logger';

import { Injectable } from '@nestjs/common';
import axios from 'axios';

import type { OrderOutlierSummary } from '../../types';

import { CronJobRepository } from './cronJobRepository';

const SLACK_BLOCK_TEXT_LIMIT = 2500; // Slack limit for section block text
const SLACK_SAFE_MARGIN = 100; // buffer to prevent edge-case overflows
const SLACK_CHUNK_THRESHOLD = SLACK_BLOCK_TEXT_LIMIT - SLACK_SAFE_MARGIN;
const SLACK_HEADER_PREFIX_LENGTH = 60; // Estimated length of heading like "*ZPOS Report (1/N)*"

@Injectable()
export class CronJobService {
  constructor(private readonly cronJobRepository: CronJobRepository) {}

  async orderOutlierCronJob(): Promise<void> {
    const [mismatchedPaidOrders, openOrdersWithPayments] = await Promise.all([
      this.cronJobRepository.getMismatchedPaidOrderUuids(),
      this.cronJobRepository.getOpenOrderUuidsWithPayments(),
    ]);

    info(`Mismatched paid orders: ${JSON.stringify(mismatchedPaidOrders, null, 2)}`);
    info(`Open orders with payments: ${JSON.stringify(openOrdersWithPayments, null, 2)}`);

    const messages = this.getSlackMessageTextChunks(mismatchedPaidOrders, openOrdersWithPayments);

    for (const text of messages) {
      const blocks = [
        {
          type: 'section',
          text: {
            type: 'mrkdwn',
            text,
          },
        },
      ];
      console.log(`Sending Slack message: ${JSON.stringify(blocks, null, 2)}`);
      await axios.post(process.env.SLACK_CHANNEL_WEBHOOK_URL as string, {
        blocks,
      });
    }
  }

  private getSlackMessageTextChunks(
    mismatchedPaidOrders: OrderOutlierSummary[],
    openOrdersWithPayments: OrderOutlierSummary[],
  ): string[] {
    if (mismatchedPaidOrders.length === 0 && openOrdersWithPayments.length === 0) {
      return ['*ZPOS Daily Order Outlier Report* 🚨\n*No Outlier* was found today ✅'];
    }

    const header = `order id                               status   paid amount     order amount`;
    const rows: string[] = [];

    if (mismatchedPaidOrders.length) {
      rows.push(`*PAID orders with paid amount different from order amount:*`);
      rows.push(`\`\`\`${header}`);
      rows.push(
        ...mismatchedPaidOrders.map((o) => {
          const orderId = String(o.order_id || '').padEnd(38);
          const status = String(o.order_status || '').padEnd(8);
          const paid = `$${Number((o.order_paidAmount ?? 0) / 100).toFixed(2)}`.padEnd(12);
          const order = `$${Number((o.order_amount ?? 0) / 100).toFixed(2)}`;
          return `${orderId}${status}${paid}${order}`;
        }),
      );
      rows.push('```');
    }

    if (openOrdersWithPayments.length) {
      rows.push(`*OPEN orders with APPROVED transactions:*`);
      rows.push(`\`\`\`${header}`);
      rows.push(
        ...openOrdersWithPayments.map((o) => {
          const orderId = String(o.order_id || '').padEnd(38);
          const status = String(o.order_status || '').padEnd(8);
          const paid = `$${Number((o.order_paidAmount ?? 0) / 100).toFixed(2)}`.padEnd(12);
          const order = `$${Number((o.order_amount ?? 0) / 100).toFixed(2)}`;
          return `${orderId}${status}${paid}${order}`;
        }),
      );
      rows.push('```');
    }

    const combinedMessage = rows.join('\n');

    // Check if the combined message fits the Slack block text limit (with a safe margin)
    if (combinedMessage.length + SLACK_HEADER_PREFIX_LENGTH < SLACK_BLOCK_TEXT_LIMIT) {
      return [`*ZPOS Daily Order Outlier Report* 🚨\n${combinedMessage}`];
    }

    // If too big, split into multiple chunks — but keep formatting intact per chunk
    const chunks: string[] = [];
    let buffer = '';
    for (const line of rows) {
      if (`${buffer}\n${line}`.length > SLACK_CHUNK_THRESHOLD) {
        chunks.push(buffer.trim());
        buffer = '';
      }
      buffer += `${line}\n`;
    }
    if (buffer.trim()) chunks.push(buffer.trim());

    const total = chunks.length;
    return chunks.map((chunk, i) => {
      // handles the edge case in formatting of the code block for the first and last messages
      if (i + 1 === total) return `*ZPOS Daily Order Outlier Report (${i + 1}/${total})* 🚨\n\`\`\`${chunk}`;
      if (i === 0) return `*ZPOS Daily Order Outlier Report (${i + 1}/${total})* 🚨\n${chunk}\`\`\``;
      return `*ZPOS Daily Order Outlier Report (${i + 1}/${total})* 🚨\n\`\`\`${chunk}\`\`\``;
    });
  }
}
