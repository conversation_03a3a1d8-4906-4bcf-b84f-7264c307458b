import { CatalogUnit } from '@npco/component-dto-catalog/dist';
import { OrderItemType, OrderStatus } from '@npco/component-dto-order/dist';

import type { INestApplicationContext } from '@nestjs/common';
import mock from 'jest-mock-extended/lib/Mock';
import { DataSource } from 'typeorm';
import { v4 } from 'uuid';

import type { CommandService } from '../../../commands';
import { mockEnvServiceForLocalDbConnection } from '../../../config/__mocks__/localDbConnection';
import type { Order, OrderPayment } from '../../../domain/entities';
import { OrderDiscountModel } from '../../../domain/models/orderDiscountModel';
import { OrderItemModel } from '../../../domain/models/orderItemModel';
import { OrderModel } from '../../../domain/models/orderModel';
import { OrderServiceChargeModel } from '../../../domain/models/orderServiceChargeModel';
import type { CreateOrderInput } from '../../../domain/types';
import { initiateOrderMutationNestModule } from '../../../lambdas/testcases/mockNestModule';
import { PURCHASE, TenderType } from '../../types';
import { OrderRepository } from '../order/orderRepository';
import { OrderService } from '../order/orderService';

import { CronJobRepository } from './cronJobRepository';

jest.mock('../../../config/envService', () => mockEnvServiceForLocalDbConnection());

jest.mock('aws-xray-sdk', () => {
  return {
    getSegment: () => ({
      addNewSubsegment: jest.fn().mockReturnValue({}),
      addAnnotation: jest.fn(),
    }),
    captureAsyncFunc: async (name: string, cb: any) => {
      await cb({ addAnnotation: jest.fn(), close: jest.fn() });
    },
  };
});

describe('CronJob Repository test suite', () => {
  let appContext: INestApplicationContext;
  let dataSource: DataSource;
  let orderRepository: OrderRepository;
  let orderService: OrderService;
  let orderModel: OrderModel;
  let cronJobRepository: CronJobRepository;

  beforeAll(async () => {
    appContext = await initiateOrderMutationNestModule();
    dataSource = appContext.get(DataSource);
    const discountModel = new OrderDiscountModel();
    const serviceChargeModel = new OrderServiceChargeModel();
    const orderItemModel = new OrderItemModel(discountModel, serviceChargeModel);
    orderModel = new OrderModel(orderItemModel, discountModel, serviceChargeModel);
    orderRepository = new OrderRepository(dataSource, orderModel);
    const mockCommandService = mock<CommandService>();
    orderService = new OrderService(orderRepository, mockCommandService);
    cronJobRepository = new CronJobRepository(dataSource, orderModel);
  });

  afterAll(async () => {
    await dataSource.destroy();
  });

  const getOrderInput = (): CreateOrderInput => {
    return {
      id: v4(),
      entityUuid: v4(),
      siteUuid: v4(),
      createdFromDeviceUuid: v4(),
      catalogSettings: { itemsTaxInclusive: true, itemsApplyTax: true, autoSkuEnabled: true },
      updatedTime: new Date().toISOString(),
      items: [
        {
          id: v4(),
          name: 'test item',
          price: '10000',
          ordinal: 1,
          type: OrderItemType.SINGLE,
          unit: CatalogUnit.HOUR,
          quantity: 1,
          modifiers: [
            {
              id: v4(),
              name: 'test modifier',
              price: '1000',
              ordinal: 1,
              unit: CatalogUnit.QUANTITY,
              quantity: 1,
            },
          ],
          taxes: [{ name: 'GST', enabled: true, percent: 10 }],
        },
      ],
    };
  };

  describe('getMismatchedPaidOrderUuids', () => {
    it('should return order ids with mismatched paid amount', async () => {
      const [order1] = await Promise.all([
        orderService.createOrder(getOrderInput()),
        orderService.createOrder(getOrderInput()),
        orderService.createOrder(getOrderInput()),
      ]);

      const updateOrderWithInvalidDetail = await orderRepository.saveOrderPayments({
        id: order1.id,
        entityUuid: order1.entityUuid,
        paidAmount: 20000,
        orderAmount: 10000,
        status: OrderStatus.PAID,
        totalSurcharge: 1,
        totalTips: 1,
      } as Order);

      const mismatchedOrders = await cronJobRepository.getMismatchedPaidOrderUuids();
      expect(mismatchedOrders.length).not.toBe(0);
      expect(mismatchedOrders).toStrictEqual(
        expect.arrayContaining([
          {
            order_id: updateOrderWithInvalidDetail.id,
            order_orderAmount: String(updateOrderWithInvalidDetail.orderAmount),
            order_paidAmount: String(updateOrderWithInvalidDetail.paidAmount),
            order_status: OrderStatus.PAID,
            order_totalSurcharge: String(updateOrderWithInvalidDetail.totalSurcharge),
            order_totalTips: String(updateOrderWithInvalidDetail.totalTips),
          },
        ]),
      );
    });
  });

  describe('getOpenOrderUuidsWithPayments', () => {
    it('should return order ids with mismatched open orders having approved state', async () => {
      const [order1] = await Promise.all([
        orderService.createOrder(getOrderInput()),
        orderService.createOrder(getOrderInput()),
        orderService.createOrder(getOrderInput()),
      ]);

      const updateOrderWithInvalidDetail = await orderRepository.saveOrderPayments({
        id: order1.id,
        entityUuid: order1.entityUuid,
        status: OrderStatus.OPEN,
        payments: [
          {
            id: v4(),
            orderId: order1.id,
            entityUuid: order1.entityUuid,
            transactionUuid: v4(),
            status: 'APPROVED',
            amount: 10000,
            amountTendered: 10000,
            change: 0,
            timestampLocal: new Date().toISOString(),
            order: order1,
            type: PURCHASE,
            timestamp: new Date().toISOString(),
            tenderType: TenderType.CASH,
          } as OrderPayment,
        ],
      } as Order);

      const mismatchedOrders = await cronJobRepository.getOpenOrderUuidsWithPayments();
      expect(mismatchedOrders.length).toBeDefined();
      expect(mismatchedOrders).toStrictEqual(
        expect.arrayContaining([
          {
            order_id: updateOrderWithInvalidDetail.id,
            order_orderAmount: '120',
            order_paidAmount: '0',
            order_status: OrderStatus.OPEN,
          },
        ]),
      );
    });
  });
});
