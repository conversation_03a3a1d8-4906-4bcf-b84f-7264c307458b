import axios from 'axios';
import { mock } from 'jest-mock-extended';

import type { OrderOutlierSummary } from '../../types';

import type { CronJobRepository } from './cronJobRepository';
import { CronJobService } from './cronJobService';

jest.mock('axios');

describe('CronJobService test suite', () => {
  process.env.SLACK_CHANNEL_WEBHOOK_URL = 'test-webhook-id';
  const mockCronJobRepository = mock<CronJobRepository>();

  const cronJobService = new CronJobService(mockCronJobRepository);

  afterEach(() => jest.resetAllMocks());

  beforeEach(() => (axios as any).mockReset());

  describe('orderOutlierCronJob', () => {
    it('should handle orderOutlierCronJob event', async () => {
      mockCronJobRepository.getMismatchedPaidOrderUuids.mockResolvedValue([{ order_id: '1' } as any]);
      mockCronJobRepository.getOpenOrderUuidsWithPayments.mockResolvedValue([{ order_id: '1' } as any]);
      (axios as any).post.mockResolvedValueOnce({ data: {}, status: 200 });

      await cronJobService.orderOutlierCronJob();

      expect(mockCronJobRepository.getMismatchedPaidOrderUuids).toHaveBeenCalled();
      expect(mockCronJobRepository.getOpenOrderUuidsWithPayments).toHaveBeenCalled();
      expect(axios.post).toHaveBeenCalledTimes(1);
      expect(axios.post).toHaveBeenCalledWith(process.env.SLACK_CHANNEL_WEBHOOK_URL, {
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '*ZPOS Daily Order Outlier Report* 🚨\n*PAID orders with paid amount different from order amount:*\n```order id                               status   paid amount     order amount\n1                                             $0.00       $0.00\n```\n*OPEN orders with APPROVED transactions:*\n```order id                               status   paid amount     order amount\n1                                             $0.00       $0.00\n```',
            },
          },
        ],
      });
    });

    it('should send slack message even if no order_ids are found for the day with the right parameter', async () => {
      mockCronJobRepository.getMismatchedPaidOrderUuids.mockResolvedValue([]);
      mockCronJobRepository.getOpenOrderUuidsWithPayments.mockResolvedValue([]);

      await cronJobService.orderOutlierCronJob();

      expect(mockCronJobRepository.getMismatchedPaidOrderUuids).toHaveBeenCalled();
      expect(mockCronJobRepository.getOpenOrderUuidsWithPayments).toHaveBeenCalled();
      expect(axios.post).toHaveBeenCalledTimes(1);
      expect(axios.post).toHaveBeenCalledWith(process.env.SLACK_CHANNEL_WEBHOOK_URL, {
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '*ZPOS Daily Order Outlier Report* 🚨\n*No Outlier* was found today ✅',
            },
          },
        ],
      });
    });

    it('should send multiple Slack messages when content exceeds block size limit', async () => {
      // simulate a large order row
      const generateLargeOrder = (id: number): OrderOutlierSummary => ({
        order_id: `order-${id.toString().padStart(30, '0')}`, // Long UUID-like
        order_status: 'PAID',
        order_paidAmount: 100000, // $1000.00
        order_amount: 100000,
      });

      // Create enough rows to force >2 messages
      const rowCount = 80;
      const largeDataSet = Array.from({ length: rowCount }, (_, i) => generateLargeOrder(i));

      mockCronJobRepository.getMismatchedPaidOrderUuids.mockResolvedValue(largeDataSet);
      mockCronJobRepository.getOpenOrderUuidsWithPayments.mockResolvedValue(largeDataSet); // keep it simple

      (axios as any).post.mockResolvedValue({ data: {}, status: 200 });

      await cronJobService.orderOutlierCronJob();

      const calls = (axios.post as jest.Mock).mock.calls;
      const actualCallCount = calls.length;

      expect(actualCallCount).toStrictEqual(5);

      // Validates each message content
      for (let i = 0; i < actualCallCount; i += 1) {
        const [url, body] = calls[i];

        expect(url).toBe(process.env.SLACK_CHANNEL_WEBHOOK_URL);

        // Validate message structure
        expect(body).toHaveProperty('blocks');
        expect(body.blocks).toHaveLength(1);

        const block = body.blocks[0];
        expect(block).toHaveProperty('type', 'section');
        expect(block).toHaveProperty('text');
        expect(block.text).toHaveProperty('type', 'mrkdwn');
        expect(typeof block.text.text).toBe('string');

        const expectedPrefix = `*ZPOS Daily Order Outlier Report (${i + 1}/${actualCallCount})* 🚨`;
        expect(block.text.text.startsWith(expectedPrefix)).toBe(true);
      }
    });

    it('should send multiple Slack with proper formatting when paid orders exists with no open order', async () => {
      // simulate a large order row
      const generateLargeOrder = (id: number): OrderOutlierSummary => ({
        order_id: `order-${id.toString().padStart(30, '0')}`, // Long UUID-like
        order_status: 'PAID',
        order_paidAmount: 100000, // $1000.00
        order_amount: 100000,
      });

      // Create enough rows to force >2 messages
      const rowCount = 80;
      const largeDataSet = Array.from({ length: rowCount }, (_, i) => generateLargeOrder(i));

      mockCronJobRepository.getMismatchedPaidOrderUuids.mockResolvedValue(largeDataSet);
      mockCronJobRepository.getOpenOrderUuidsWithPayments.mockResolvedValue([]);

      (axios as any).post.mockResolvedValue({ data: {}, status: 200 });

      await cronJobService.orderOutlierCronJob();

      const calls = (axios.post as jest.Mock).mock.calls;
      const actualCallCount = calls.length;

      expect(actualCallCount).toStrictEqual(3);

      // Validates each message content
      for (let i = 0; i < actualCallCount; i += 1) {
        const [url, body] = calls[i];

        expect(url).toBe(process.env.SLACK_CHANNEL_WEBHOOK_URL);

        // Validate message structure
        expect(body).toHaveProperty('blocks');
        expect(body.blocks).toHaveLength(1);

        const block = body.blocks[0];
        expect(block).toHaveProperty('type', 'section');
        expect(block).toHaveProperty('text');
        expect(block.text).toHaveProperty('type', 'mrkdwn');
        expect(typeof block.text.text).toBe('string');

        const expectedPrefix = `*ZPOS Daily Order Outlier Report (${i + 1}/${actualCallCount})* 🚨`;
        expect(block.text.text.startsWith(expectedPrefix)).toBe(true);
      }
    });

    it('should send multiple Slack with proper formatting when open orders exists with no paid order', async () => {
      // simulate a large order row
      const generateLargeOrder = (id: number): OrderOutlierSummary => ({
        order_id: `order-${id.toString().padStart(30, '0')}`, // Long UUID-like
        order_status: 'PAID',
        order_paidAmount: 100000, // $1000.00
        order_amount: 100000,
      });

      // Create enough rows to force >2 messages
      const rowCount = 80;
      const largeDataSet = Array.from({ length: rowCount }, (_, i) => generateLargeOrder(i));

      mockCronJobRepository.getMismatchedPaidOrderUuids.mockResolvedValue([]);
      mockCronJobRepository.getOpenOrderUuidsWithPayments.mockResolvedValue(largeDataSet);

      (axios as any).post.mockResolvedValue({ data: {}, status: 200 });

      await cronJobService.orderOutlierCronJob();

      const calls = (axios.post as jest.Mock).mock.calls;
      const actualCallCount = calls.length;

      expect(actualCallCount).toStrictEqual(3);

      // Validates each message content
      for (let i = 0; i < actualCallCount; i += 1) {
        const [url, body] = calls[i];

        expect(url).toBe(process.env.SLACK_CHANNEL_WEBHOOK_URL);

        // Validate message structure
        expect(body).toHaveProperty('blocks');
        expect(body.blocks).toHaveLength(1);

        const block = body.blocks[0];
        expect(block).toHaveProperty('type', 'section');
        expect(block).toHaveProperty('text');
        expect(block.text).toHaveProperty('type', 'mrkdwn');
        expect(typeof block.text.text).toBe('string');

        const expectedPrefix = `*ZPOS Daily Order Outlier Report (${i + 1}/${actualCallCount})* 🚨`;
        expect(block.text.text.startsWith(expectedPrefix)).toBe(true);
      }
    });
  });
});
