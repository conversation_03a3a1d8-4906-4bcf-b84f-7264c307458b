import type { OrderUpdatedDto } from '@npco/component-dto-order/dist';
import { OrderStatus } from '@npco/component-dto-order/dist';
import { TransactionStatus } from '@npco/component-dto-transaction/dist/types';

import type { INestApplicationContext } from '@nestjs/common';
import { mock } from 'jest-mock-extended';
import { DataSource } from 'typeorm';
import { v4 } from 'uuid';

import type { CommandService } from '../../../commands/commandService';
import { OrderUpdatedCommand } from '../../../commands/orderUpdated';
import { mockEnvServiceForLocalDbConnection } from '../../../config/__mocks__/localDbConnection';
import { OrderDiscountModel } from '../../../domain/models/orderDiscountModel';
import { OrderItemModel } from '../../../domain/models/orderItemModel';
import { OrderModel } from '../../../domain/models/orderModel';
import { OrderServiceChargeModel } from '../../../domain/models/orderServiceChargeModel';
import { initiateOrderMutationNestModule } from '../../../lambdas/testcases/mockNestModule';
import { OrderRepository } from '../order/orderRepository';
import { OrderService } from '../order/orderService';
import { getOrderInput, getTransactionInput } from '../tests/utils';

import { MigrationService } from './migrationService';

jest.mock('../../../config/envService', () => mockEnvServiceForLocalDbConnection());

jest.mock('aws-xray-sdk', () => {
  return {
    getSegment: () => ({
      addNewSubsegment: jest.fn().mockReturnValue({}),
      addAnnotation: jest.fn(),
    }),
    captureAsyncFunc: async (name: string, cb: any) => {
      await cb({ addAnnotation: jest.fn(), close: jest.fn() });
    },
  };
});

describe('Migration Service with Repository tests', () => {
  const mockCommandService = mock<CommandService>();
  let appContext: INestApplicationContext;
  let dataSource: DataSource;
  let orderRepository: OrderRepository;
  let orderModel: OrderModel;
  let orderService: OrderService;
  let migrationService: MigrationService;

  const mockTransactionService = { getTransaction: jest.fn() } as any;

  beforeAll(async () => {
    appContext = await initiateOrderMutationNestModule();
    dataSource = appContext.get(DataSource);
    const discountModel = new OrderDiscountModel();
    const orderServiceChargeModel = new OrderServiceChargeModel();

    const orderItemModel = new OrderItemModel(discountModel, orderServiceChargeModel);
    orderModel = new OrderModel(orderItemModel, discountModel, orderServiceChargeModel);
    orderRepository = new OrderRepository(dataSource, orderModel);
    orderService = new OrderService(orderRepository, mockCommandService);
    migrationService = new MigrationService(
      mockTransactionService,
      orderRepository,
      orderService,
      orderItemModel,
      orderModel,
      mockCommandService,
    );
  });

  it('should run migrationSetOrderGst successfully on order that has gst', async () => {
    const orderInput = getOrderInput();
    const order = await orderRepository.createOrder(orderInput);
    expect(order).toBeDefined();
    expect(order.status).toBe(OrderStatus.OPEN);
    // approved transaction received for order
    const approvedTransaction1 = getTransactionInput(order.id, order.totalAmount);
    mockTransactionService.getTransaction.mockResolvedValue(approvedTransaction1 as any);

    await orderService.processPaymentMessages([
      {
        body: JSON.stringify(approvedTransaction1),
        receiptHandle: 'receiptHandle',
      },
    ]);
    const paidOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
    expect(paidOrder).toBeDefined();
    expect(paidOrder!.status).toBe(OrderStatus.PAID);
    expect(paidOrder!.payments).toHaveLength(1);
    expect(paidOrder!.payments![0].status).toBe(TransactionStatus.APPROVED);
    expect(paidOrder!.payments![0].transactionUuid).toBe(approvedTransaction1.id);
    expect(paidOrder!.dueAmount).toBe(0);
    expect(paidOrder!.orderDisplayAmount).toBe(paidOrder!.paidAmount);
    expect(paidOrder!.orderGst).toBe(9);

    // set order fields to be incorrect in the db
    await orderRepository.getRepository().update(
      { id: paidOrder!.id },
      {
        orderGst: -1,
        orderDisplayAmount: -1,
        createdTimestampLocal: null as any,
        totalAmount: -1,
      },
    );
    const brokenOrder = await orderRepository.getOrder(paidOrder!);
    expect(brokenOrder).toBeDefined();
    expect(brokenOrder!.orderGst).toBe(-1);
    expect(brokenOrder!.orderDisplayAmount).toBe(-1);
    expect(brokenOrder!.createdTimestampLocal).toBeNull();
    expect(brokenOrder!.totalAmount).toBe(-1);
    // reset commandHandler mock
    mockCommandService.sendCommand.mockReset();
    // run migration to fix the order amounts
    await migrationService.migrationSetOrderGst({
      id: paidOrder!.id,
      entityUuid: paidOrder!.entityUuid,
      updatedTime: new Date().toISOString(),
    });
    const orderAfterMigration = await orderRepository.getOrder({
      id: paidOrder!.id,
      entityUuid: paidOrder!.entityUuid,
    });
    expect(orderAfterMigration).toBeDefined();
    expect(orderAfterMigration!.orderGst).toBe(9);
    expect(orderAfterMigration!.orderDisplayAmount).toBe(paidOrder!.paidAmount);
    expect(orderAfterMigration!.totalAmount).toBe(paidOrder!.totalAmount);
    expect(orderAfterMigration!.createdTimestampLocal).toBeDefined();
    // verify dto sent to commandService
    const orderUpdatedCommand = new OrderUpdatedCommand(orderAfterMigration!);
    expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
    const { event: dto } = orderUpdatedCommand.getCommandDto();
    expect(Number((dto as OrderUpdatedDto).orderGst!.value)).toBe(900);
    expect(Number((dto as OrderUpdatedDto).orderDisplayAmount!.value)).toBe(paidOrder!.paidAmount! * 100);
    expect(Number((dto as OrderUpdatedDto).totalAmount!.value)).toBe(paidOrder!.totalAmount * 100);
    expect((dto as OrderUpdatedDto).createdTimestampLocal).toBeDefined();
    expect((dto as OrderUpdatedDto).status).toBe('PAID');
  });

  it('should run migrationSetOrderGst successfully on order that DOES NOT HAVE gst', async () => {
    const orderInputWithGst = getOrderInput();
    const orderInput = {
      ...orderInputWithGst,
      catalogSettings: {
        ...orderInputWithGst.catalogSettings,
        itemsApplyTax: false,
      },
      items: orderInputWithGst.items.map((item) => ({
        ...item,
        taxes: [{ name: 'GST', enabled: false }],
      })),
    };

    const order = await orderRepository.createOrder(orderInput);
    expect(order).toBeDefined();
    expect(order.status).toBe(OrderStatus.OPEN);
    // approved transaction received for order
    const approvedTransaction1 = getTransactionInput(order.id, order.totalAmount);
    mockTransactionService.getTransaction.mockResolvedValue(approvedTransaction1 as any);

    await orderService.processPaymentMessages([
      {
        body: JSON.stringify(approvedTransaction1),
        receiptHandle: 'receiptHandle',
      },
    ]);
    const paidOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
    expect(paidOrder).toBeDefined();
    expect(paidOrder!.status).toBe(OrderStatus.PAID);
    expect(paidOrder!.payments).toHaveLength(1);
    expect(paidOrder!.payments![0].status).toBe(TransactionStatus.APPROVED);
    expect(paidOrder!.payments![0].transactionUuid).toBe(approvedTransaction1.id);
    expect(paidOrder!.dueAmount).toBe(0);
    expect(paidOrder!.orderDisplayAmount).toBe(paidOrder!.paidAmount);
    expect(paidOrder!.orderGst).toBe(0);

    // set order fields to be incorrect in the db
    await orderRepository.getRepository().update(
      { id: paidOrder!.id },
      {
        orderGst: -1,
        orderDisplayAmount: -1,
        createdTimestampLocal: null as any,
        totalAmount: -1,
      },
    );
    const brokenOrder = await orderRepository.getOrder(paidOrder!);
    expect(brokenOrder).toBeDefined();
    expect(brokenOrder!.orderGst).toBe(-1);
    expect(brokenOrder!.orderDisplayAmount).toBe(-1);
    expect(brokenOrder!.createdTimestampLocal).toBeNull();
    expect(brokenOrder!.totalAmount).toBe(-1);
    // reset commandHandler mock
    mockCommandService.sendCommand.mockReset();
    // run migration to fix the order amounts
    await migrationService.migrationSetOrderGst({
      id: paidOrder!.id,
      entityUuid: paidOrder!.entityUuid,
      updatedTime: new Date().toISOString(),
    });
    const orderAfterMigration = await orderRepository.getOrder({
      id: paidOrder!.id,
      entityUuid: paidOrder!.entityUuid,
    });
    expect(orderAfterMigration).toBeDefined();
    expect(orderAfterMigration!.orderGst).toBe(0);
    expect(orderAfterMigration!.orderDisplayAmount).toBe(paidOrder!.paidAmount);
    expect(orderAfterMigration!.totalAmount).toBe(paidOrder!.totalAmount);
    expect(orderAfterMigration!.createdTimestampLocal).toBeDefined();
    // verify dto sent to commandService
    const orderUpdatedCommand = new OrderUpdatedCommand(orderAfterMigration!);
    expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
    const { event: dto } = orderUpdatedCommand.getCommandDto();
    expect(Number((dto as OrderUpdatedDto).orderGst!.value)).toBe(0);
    expect(Number((dto as OrderUpdatedDto).orderDisplayAmount!.value)).toBe(paidOrder!.paidAmount! * 100);
    expect(Number((dto as OrderUpdatedDto).totalAmount!.value)).toBe(paidOrder!.totalAmount * 100);
    expect((dto as OrderUpdatedDto).createdTimestampLocal).toBeDefined();
    expect((dto as OrderUpdatedDto).status).toBe('PAID');
  });

  describe('reprocessOrderPayment', () => {
    it('should process APPROVED transaction for OPEN order with 1 APPROVED payment', async () => {
      const orderInput = getOrderInput();
      const order = await orderRepository.createOrder(orderInput);
      expect(order).toBeDefined();
      expect(order.status).toBe(OrderStatus.OPEN);
      // approved transaction received for order
      const approvedTransaction1 = getTransactionInput(order.id, order.totalAmount);
      mockTransactionService.getTransaction.mockResolvedValue(approvedTransaction1 as any);
      await orderService.processPaymentMessages([
        {
          body: JSON.stringify(approvedTransaction1),
          receiptHandle: 'receiptHandle',
        },
      ]);
      const paidOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(paidOrder).toBeDefined();
      expect(paidOrder!.status).toBe(OrderStatus.PAID);

      await orderRepository.getRepository().update(
        {
          id: paidOrder!.id,
        },
        {
          status: OrderStatus.OPEN,
          paidTime: 1,
          paidAmount: 0,
          totalGst: 0,
          totalSurcharge: 0,
          totalTips: 0,
          dueAmount: order.dueAmount,
          updatedTimeInMilliseconds: paidOrder!.updatedTimeInMilliseconds! + 1,
        },
      );
      const wrongOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(wrongOrder!.status).toEqual(OrderStatus.OPEN);
      await migrationService.reprocessOrderPayment({
        id: order.id,
        entityUuid: order.entityUuid,
      });
      const fixedOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(fixedOrder!.updatedTimeInMilliseconds! > paidOrder!.updatedTimeInMilliseconds!).toBeTruthy();
      expect(
        JSON.stringify({
          ...fixedOrder,
          updatedTimeInMilliseconds: undefined,
          updatedTime: undefined,
        }),
      ).toStrictEqual(
        JSON.stringify({
          ...paidOrder,
          updatedTime: undefined,
          updatedTimeInMilliseconds: undefined,
        }),
      );
    });
    it('should process APPROVED transaction for OPEN order with 2, DECLINED and APPROVED payments', async () => {
      const orderInput = getOrderInput();
      const order = await orderRepository.createOrder(orderInput);
      expect(order).toBeDefined();
      expect(order.status).toBe(OrderStatus.OPEN);
      // declined then approved transaction received for order
      const approvedTransaction1 = getTransactionInput(order.id, order.totalAmount);
      const declinedTransaction1 = {
        ...approvedTransaction1,
        id: v4(),
        status: 'DECLINED',
        timestamp: new Date(new Date(approvedTransaction1.timestamp).getTime() - 10000).toISOString(),
      };

      mockTransactionService.getTransaction.mockResolvedValueOnce(approvedTransaction1 as any);

      await orderService.processPaymentMessages([
        {
          body: JSON.stringify(declinedTransaction1),
          receiptHandle: 'receiptHandle',
        },
      ]);

      await orderService.processPaymentMessages([
        {
          body: JSON.stringify(approvedTransaction1),
          receiptHandle: 'receiptHandle',
        },
      ]);

      const paidOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(paidOrder).toBeDefined();
      expect(paidOrder!.status).toBe(OrderStatus.PAID);
      expect(paidOrder!.payments).toHaveLength(2);
      expect(paidOrder!.payments!.find((p) => p.transactionUuid === declinedTransaction1.id)!.status).toBe(
        TransactionStatus.DECLINED,
      );
      // set order values to be incorrect
      await orderRepository.getRepository().update(
        {
          id: paidOrder!.id,
        },
        {
          status: OrderStatus.OPEN,
          paidTime: 1,
          paidAmount: 0,
          totalGst: 0,
          totalSurcharge: 0,
          totalTips: 0,
          updatedTimeInMilliseconds: new Date().getTime() + 1,
          dueAmount: order.dueAmount,
        },
      );
      const wrongOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(wrongOrder!.status).toEqual(OrderStatus.OPEN);
      // run migration to fix the order status and amounts
      await migrationService.reprocessOrderPayment({
        id: order.id,
        entityUuid: order.entityUuid,
      });
      // verify order is fixed
      const fixedOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(fixedOrder!.updatedTimeInMilliseconds! > paidOrder!.updatedTimeInMilliseconds!).toBeTruthy();
      expect(
        JSON.stringify({
          ...fixedOrder,
          updatedTimeInMilliseconds: undefined,
          updatedTime: undefined,
        }),
      ).toStrictEqual(
        JSON.stringify({
          ...paidOrder,
          updatedTime: undefined,
          updatedTimeInMilliseconds: undefined,
        }),
      );
    });

    it('should not process order when its OPEN and has no payments', async () => {
      const orderInput = getOrderInput();
      const order = await orderRepository.createOrder(orderInput);
      expect(order).toBeDefined();
      expect(order.status).toBe(OrderStatus.OPEN);
      mockCommandService.sendCommand.mockReset();
      // run migration to fix the order status and amounts
      await migrationService.reprocessOrderPayment({
        id: order.id,
        entityUuid: order.entityUuid,
      });
      // verify order is not updated
      const migrationCallMock = jest.spyOn(orderRepository, 'migrationSaveOrderPayment');
      const sameOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(sameOrder!.status).toEqual(OrderStatus.OPEN);
      expect(sameOrder!.updatedTimeInMilliseconds).toEqual(order.updatedTimeInMilliseconds);
      expect(migrationCallMock).toHaveBeenCalledTimes(0);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(0);
    });
    it('should not process order when its OPEN and has no APPROVED payments', async () => {
      const orderInput = getOrderInput();
      const order = await orderRepository.createOrder(orderInput);
      expect(order).toBeDefined();
      expect(order.status).toBe(OrderStatus.OPEN);
      const approvedTransaction1 = getTransactionInput(order.id, order.totalAmount);
      const declinedTransaction1 = {
        ...approvedTransaction1,
        status: 'DECLINED',
        timestamp: new Date(new Date(approvedTransaction1.timestamp).getTime() - 10000).toISOString(),
      };
      await orderService.processPaymentMessages([
        {
          body: JSON.stringify({ ...declinedTransaction1, timestamp: new Date().toISOString(), id: v4() }),
          receiptHandle: 'receiptHandle',
        },
      ]);

      await orderService.processPaymentMessages([
        {
          body: JSON.stringify(declinedTransaction1),
          receiptHandle: 'receiptHandle',
        },
      ]);

      mockCommandService.sendCommand.mockReset();
      // run migration
      const migrationCallMock = jest.spyOn(orderRepository, 'migrationSaveOrderPayment');

      await migrationService.reprocessOrderPayment({
        id: order.id,
        entityUuid: order.entityUuid,
      });
      // verify order is not updated
      const sameOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(sameOrder!.status).toEqual(OrderStatus.OPEN);
      expect(sameOrder!.payments).toHaveLength(2);
      expect(migrationCallMock).toHaveBeenCalledTimes(0);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(0);
    });
    it('should not process order when its PAID', async () => {
      const orderInput = getOrderInput();
      const order = await orderRepository.createOrder(orderInput);
      expect(order).toBeDefined();
      expect(order.status).toBe(OrderStatus.OPEN);
      // approved transaction received for order
      const approvedTransaction1 = getTransactionInput(order.id, order.totalAmount);
      mockTransactionService.getTransaction.mockResolvedValue(approvedTransaction1 as any);
      await orderService.processPaymentMessages([
        {
          body: JSON.stringify(approvedTransaction1),
          receiptHandle: 'receiptHandle',
        },
      ]);
      const paidOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(paidOrder).toBeDefined();
      expect(paidOrder!.status).toBe(OrderStatus.PAID);

      mockCommandService.sendCommand.mockReset();
      const migrationCallMock = jest.spyOn(orderRepository, 'migrationSaveOrderPayment');
      // act
      await migrationService.reprocessOrderPayment({
        id: order.id,
        entityUuid: order.entityUuid,
      });

      // verify
      const sameOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(sameOrder!.status).toEqual(OrderStatus.PAID);
      expect(sameOrder!.payments).toHaveLength(1);
      expect(migrationCallMock).toHaveBeenCalledTimes(0);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(0);
    });
    it('should not process order when it does not exist', async () => {
      const migrationCallMock = jest.spyOn(orderRepository, 'migrationSaveOrderPayment');
      await migrationService.reprocessOrderPayment({
        id: v4(),
        entityUuid: v4(),
      });
      expect(migrationCallMock).toHaveBeenCalledTimes(0);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(0);
    });
  });

  describe('migrationOpenOrderAmountMismatch', () => {
    it('should update order amount when there is a mismatch and re-process Approved transaction', async () => {
      const orderInput = getOrderInput();
      const order = await orderRepository.createOrder(orderInput);
      expect(order).toBeDefined();
      expect(order.status).toBe(OrderStatus.OPEN);
      // get a mock payload for the order that the payment will be processed for
      const updatedOrderData = await orderModel.update(JSON.parse(JSON.stringify(order)), {
        ...order,
        items: order.items?.map((item) => ({
          ...item,
          quantity: item.quantity + 1, // increase quantity to change order amount
        })),
      });
      const { event: updateOrderEventPayload } = new OrderUpdatedCommand(updatedOrderData).getCommandDto();

      // approved transaction received for order with updated amount
      const approvedTransaction1 = getTransactionInput(order.id, updatedOrderData.orderAmount);
      mockTransactionService.getTransaction.mockResolvedValue(approvedTransaction1 as any);
      // run migration
      await migrationService.migrateOpenOrderAmountMismatch({
        orderUuid: order.id,
        entityUuid: order.entityUuid,
        transactionUuid: approvedTransaction1.id,
        payload: updateOrderEventPayload,
      });
      // verify order is fixed
      const fixedOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(fixedOrder!.status).toEqual(OrderStatus.PAID);
      expect(fixedOrder!.updatedTimeInMilliseconds! > order!.updatedTimeInMilliseconds!).toBeTruthy();
      expect(fixedOrder!.orderAmount).toEqual(updatedOrderData.orderAmount);
      expect(fixedOrder!.orderGst).toEqual(updatedOrderData.orderGst);
      expect(fixedOrder!.items?.length).toEqual(updatedOrderData.items?.length);
      expect(fixedOrder!.items?.[0].quantity).toEqual(updatedOrderData.items?.[0].quantity);
      expect(fixedOrder!.items?.[0].price).toEqual(Number(updatedOrderData.items?.[0].price));
      expect(fixedOrder!.items?.[0].modifiers?.length).toEqual(updatedOrderData.items?.[0].modifiers?.length);
      expect(fixedOrder!.items?.[0].modifiers?.[0].price).toEqual(
        Number(updatedOrderData.items?.[0].modifiers?.[0].price),
      );
      expect(fixedOrder!.discounts?.length).toEqual(updatedOrderData.discounts?.length);
      expect(fixedOrder!.discounts?.[0].value).toEqual(updatedOrderData.discounts?.[0].value);
      expect(fixedOrder!.payments).toHaveLength(1);
      expect(fixedOrder!.payments![0].status).toBe(TransactionStatus.APPROVED);
      expect(fixedOrder!.payments![0].transactionUuid).toBe(approvedTransaction1.id);
      expect(fixedOrder!.payments![0].amount).toBe(
        updatedOrderData.orderAmount + (approvedTransaction1.tipAmount + approvedTransaction1.surchargeAmount),
      );
    });
    it('should throw error when the orderAmount does not match the transaction amount', async () => {
      const orderInput = getOrderInput();
      const order = await orderRepository.createOrder(orderInput);
      expect(order).toBeDefined();
      expect(order.status).toBe(OrderStatus.OPEN);
      // get a mock payload for the order that the payment will be processed for
      const updatedOrderData = await orderModel.update(JSON.parse(JSON.stringify(order)), {
        ...order,
        items: order.items?.map((item) => ({
          ...item,
          quantity: item.quantity + 1, // increase quantity to change order amount
        })),
      });
      const { event: updateOrderEventPayload } = new OrderUpdatedCommand(updatedOrderData).getCommandDto();

      // approved transaction received for order with updated amount
      const approvedTransaction1 = getTransactionInput(order.id, 9999);
      mockTransactionService.getTransaction.mockResolvedValue(approvedTransaction1 as any);
      // run migration
      await expect(
        migrationService.migrateOpenOrderAmountMismatch({
          orderUuid: order.id,
          entityUuid: order.entityUuid,
          transactionUuid: approvedTransaction1.id,
          payload: updateOrderEventPayload,
        }),
      ).rejects.toThrowError(`migrateOpenOrderAmountMismatch: Migration failed for order ${order.id}`);
    });
  });
});
