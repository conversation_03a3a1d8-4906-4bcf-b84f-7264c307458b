import { TransactionType } from '@npco/component-dto-core/dist/types';
import { OrderStatus } from '@npco/component-dto-order/dist/types';
import { TransactionStatus } from '@npco/component-dto-transaction/dist';

import { mock, mockReset } from 'jest-mock-extended';

import type { CommandService } from '../../../commands';
import { OrderDiscountModel } from '../../../domain/models/orderDiscountModel';
import { OrderItemModel } from '../../../domain/models/orderItemModel';
import { OrderModel } from '../../../domain/models/orderModel';
import { OrderServiceChargeModel } from '../../../domain/models/orderServiceChargeModel';
import type { OrderService } from '../order';
import type { OrderRepository } from '../order/orderRepository';
import { getOrderInput } from '../tests/utils';
import type { TransactionService } from '../transaction/transactionService';

import { MigrationService } from './migrationService';

describe('migrationService', () => {
  const mockTransactionService = mock<TransactionService>();

  const mockOrderRepository = mock<OrderRepository>();
  const mockOrderService = mock<OrderService>();
  const mockCommandService = mock<CommandService>();
  const orderDiscountModel = new OrderDiscountModel();
  const orderServiceChargeModel = new OrderServiceChargeModel();
  const orderItemModel = new OrderItemModel(orderDiscountModel, orderServiceChargeModel);
  const orderModel = new OrderModel(orderItemModel, orderDiscountModel, orderServiceChargeModel);
  const migrationService = new MigrationService(
    mockTransactionService,
    mockOrderRepository,
    mockOrderService,
    orderItemModel,
    orderModel,
    mockCommandService,
  );

  beforeEach(() => {
    mockReset(mockTransactionService);
    mockReset(mockOrderRepository);
    mockReset(mockOrderService);
  });
  describe('markOpenOrderAsPaid', () => {
    it('should resolve when markOpenOrderAsPaid is called', async () => {
      await expect(migrationService.markOpenOrderAsPaid('transaction-uuid')).resolves.toEqual(undefined);
      expect(mockTransactionService.sendPaymentSqsMessage).toHaveBeenCalledWith({ id: 'transaction-uuid' });
    });
  });

  describe('getOrderPaidEventMissingAttributes', () => {
    it('should throw an error when order not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      await expect(
        migrationService.getOrderPaidEventMissingAttributes({ id: 'id', entityUuid: 'entity-uuid' }),
      ).rejects.toThrow(`order not found for id`);
    });

    it('should resolve and return', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({
        createdFromDeviceUuid: 'deviceUuid',
        siteUuid: 'siteUuid',
        catalogSettings: { itemsTaxInclusive: true },
      } as any);
      const order = await migrationService.getOrderPaidEventMissingAttributes({
        id: 'id',
        entityUuid: 'entity-uuid',
      });
      expect(order).toEqual({
        items: [],
        discounts: [],
        createdFromDeviceUuid: 'deviceUuid',
        siteUuid: 'siteUuid',
        catalogSettings: { itemsTaxInclusive: true },
      });
    });
  });

  describe('recalculateOrderItem', () => {
    it('should throw an error when order not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      await expect(migrationService.recalculateOrderItem({ id: 'id', entityUuid: 'entity-uuid' })).rejects.toThrow(
        `order not found for id`,
      );
    });

    it('should resolve and return', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({
        items: [],
      } as any);
      await migrationService.recalculateOrderItem({
        id: 'id',
        entityUuid: 'entity-uuid',
      });
      expect(mockOrderService.orderUpdated).toHaveBeenCalledTimes(1);
    });
  });

  describe('rematerialiseOrder', () => {
    it('should throw an error when order not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      await expect(migrationService.rematerialiseOrder({ id: 'id', entityUuid: 'entity-uuid' })).rejects.toThrow(
        `order not found for id`,
      );
    });

    it('should resolve and return', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({} as any);
      await expect(
        migrationService.rematerialiseOrder({
          id: 'id',
          entityUuid: 'entity-uuid',
        }),
      ).resolves.toEqual(undefined);
    });
  });

  describe('recalculateOrderAmount', () => {
    it('should throw an error when order not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      await expect(
        migrationService.recalculateOrderAmount({
          id: 'id',
          entityUuid: 'entity-uuid',
        }),
      ).rejects.toThrow(`order not found for id`);
    });

    it('should throw an error when a paid order does not have payments', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({
        id: 'id',
        status: OrderStatus.PAID,
        payments: [
          {
            type: TransactionType.PURCHASE,
            status: TransactionStatus.DECLINED,
          },
        ],
      } as any);
      await expect(
        migrationService.recalculateOrderAmount({
          id: 'id',
          entityUuid: 'entity-uuid',
        }),
      ).rejects.toThrow(`order payment not found for paid order id`);
    });

    it('should fix the paid order total and send event', async () => {
      const transaction = {};
      const order = {
        id: 'id',
        status: OrderStatus.PAID,
        payments: [
          {
            type: TransactionType.PURCHASE,
            status: TransactionStatus.APPROVED,
            transactionUuid: 'transactionUuid',
          },
        ],
      };
      const updatedOrder = {
        ...order,
        updated: true,
      };
      mockOrderRepository.getOrder.mockResolvedValue(order as any);
      mockTransactionService.getTransaction.mockResolvedValue(transaction as any);
      mockOrderRepository.migrationFixOrderTotal.mockResolvedValue(updatedOrder as any);
      await migrationService.recalculateOrderAmount({ id: 'id', entityUuid: 'entity-uuid' });
      expect(mockOrderRepository.migrationFixOrderTotal).toHaveBeenCalledWith(order, transaction);
      expect(mockOrderService.orderUpdated).toHaveBeenCalledWith(updatedOrder);
    });

    it('should fix the OPEN order total and send event', async () => {
      const order = {
        id: 'id',
        status: OrderStatus.OPEN,
      };
      const updatedOrder = {
        ...order,
        updated: true,
      };
      mockOrderRepository.getOrder.mockResolvedValue(order as any);
      mockOrderRepository.migrationFixOrderTotal.mockResolvedValue(updatedOrder as any);
      await migrationService.recalculateOrderAmount({ id: 'id', entityUuid: 'entity-uuid' });
      expect(mockOrderRepository.migrationFixOrderTotal).toHaveBeenCalledWith(order, undefined);
      expect(mockOrderService.orderUpdated).toHaveBeenCalledWith(updatedOrder);
    });

    it('should not send event', async () => {
      const order = {
        id: 'id',
        status: OrderStatus.OPEN,
      };
      mockOrderRepository.getOrder.mockResolvedValue(order as any);
      mockOrderRepository.migrationFixOrderTotal.mockResolvedValue(undefined);
      await migrationService.recalculateOrderAmount({ id: 'id', entityUuid: 'entity-uuid' });
      expect(mockOrderRepository.migrationFixOrderTotal).toHaveBeenCalledWith(order, undefined);
      expect(mockOrderService.orderUpdated).not.toHaveBeenCalled();
    });
  });

  describe('forceUpdatePaidOrder', () => {
    it('should throw an error when order not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      await expect(
        migrationService.forceUpdatePaidOrder({ id: 'id', entityUuid: 'entity-uuid' } as any),
      ).rejects.toThrow(`order not found for id`);
    });

    it('should throw an error when a paid order does not have payments', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({
        id: 'id',
        status: OrderStatus.PAID,
        payments: [
          {
            type: TransactionType.PURCHASE,
            status: TransactionStatus.DECLINED,
          },
        ],
      } as any);
      await expect(
        migrationService.forceUpdatePaidOrder({ id: 'id', entityUuid: 'entity-uuid' } as any),
      ).rejects.toThrow(`order payment not found for paid order id`);
    });

    it('should force update the paid order', async () => {
      const transaction = {};
      const order = {
        id: 'id',
        status: OrderStatus.PAID,
        payments: [
          {
            type: TransactionType.PURCHASE,
            status: TransactionStatus.APPROVED,
            transactionUuid: 'transactionUuid',
          },
        ],
      };
      const updatedOrder = {
        ...order,
        updated: true,
      };
      mockOrderRepository.getOrder.mockResolvedValue(order as any);
      mockTransactionService.getTransaction.mockResolvedValue(transaction as any);
      mockOrderRepository.migrationForceUpdatePaidOrder.mockResolvedValue(updatedOrder as any);
      await migrationService.forceUpdatePaidOrder({ id: 'id', entityUuid: 'entity-uuid' } as any);
      expect(mockOrderRepository.migrationForceUpdatePaidOrder).toHaveBeenCalledTimes(1);
      expect(mockOrderService.orderUpdated).toHaveBeenCalledWith(updatedOrder);
    });
  });

  describe('findOrderItemsNotMatched', () => {
    const order = {
      entityUuid: 'c49bc2e9-ed7c-4a39-94c1-41d5b3481fb4',
      totalSurcharge: 37400,
      totalTips: 187200,
      subtotalAmount: 3459900,
      totalAmount: 3968800,
      totalGst: 320591,
      discounts: [
        {
          updatedTime: 1723619580,
          entityUuid: 'c49bc2e9-ed7c-4a39-94c1-41d5b3481fb4',
          orderId: 'a9e0029d-f506-4455-8a3f-8124a2b738b6',
          orderItemId: null,
          catalogDiscount: {
            name: 'Zapp decimal',
            id: '1843fa12-b1bf-4207-bba7-ee5f80e781c3',
            config: 'PERCENTAGE',
            value: '1.05',
          },
          type: 'BASIC',
          discountedAmount: 36300,
          name: 'Zapp decimal',
          createdTime: 1723619580,
          id: '9a41cbdf-ca82-4c65-acdf-70d41c526828',
          catalogDiscountUuid: '1843fa12-b1bf-4207-bba7-ee5f80e781c3',
          config: 'PERCENTAGE',
          value: '1.05',
          ordinal: 6,
        },
      ],
      referenceNumber: 'ZP8xvwh6ga',
      dueAmount: null,
      totalDiscount: 36300,
      catalogSettings: { itemsTaxInclusive: false },
      createdTime: 1723619423,
      createdFromDeviceUuid: '142f50ea-a370-4c3c-a63e-8d8d301f5282',
      siteUuid: '68ae31b0-ec71-4e17-af0e-7d1a4eda79fb',
      items: [
        {
          quantity: 1,
          taxes: [
            {
              name: 'GST',
              percent: 10,
              enabled: true,
            },
          ],
          type: 'SINGLE',
          modifiers: [],
          subtotalAmount: 1010000,
          totalAmount: 1010000,
          unit: 'QUANTITY',
          discounts: [],
          price: 1010000,
          name: 'new item',
          id: 'bee29b09-b71d-4154-a4de-b29ed476a93d',
          variantName: null,
          ordinal: 1,
        },
        {
          quantity: 1,
          taxes: [
            {
              name: 'GST',
              percent: 10,
              enabled: true,
            },
          ],
          type: 'VARIANT',
          modifiers: [
            {
              unit: 'QUANTITY',
              quantity: 1,
              price: 50000,
              name: 'Premium Box',
              catalogModifierUuid: 'c0625f44-afb0-4bde-bc3d-5382da2a29af',
              catalogModifier: {
                entityUuid: 'c49bc2e9-ed7c-4a39-94c1-41d5b3481fb4',
                price: 50000,
                modifierSetUuid: '5a1f00bf-ddd5-426d-9342-c55af961d7d9',
                name: 'Premium Box',
                id: 'c0625f44-afb0-4bde-bc3d-5382da2a29af',
                ordinal: 0,
                status: 'ACTIVE',
              },
              id: 'ec0e3a87-0dbd-44dd-a62f-36f215a434c8',
              parentId: null,
              ordinal: 1,
            },
          ],
          subtotalAmount: 2249900,
          totalAmount: 2139900,
          unit: 'QUANTITY',
          discounts: [
            {
              updatedTime: 1723619602,
              entityUuid: 'c49bc2e9-ed7c-4a39-94c1-41d5b3481fb4',
              orderItemId: 'e877ee08-acf5-4038-80a4-e975a48dddbb',
              catalogDiscount: {
                name: 'Large discount',
                id: '72096ef6-1e27-45ff-a153-0e1fd2375bde',
                config: 'AMOUNT',
                value: '110000',
              },
              type: 'BASIC',
              discountedAmount: 110000,
              name: 'Large discount',
              createdTime: 1723619602,
              id: '4c9ec9b4-64d3-494c-a63e-861d45765815',
              catalogDiscountUuid: '72096ef6-1e27-45ff-a153-0e1fd2375bde',
              config: 'AMOUNT',
              value: 110000,
              ordinal: 1,
            },
          ],
          price: 2199900,
          name: 'Test Item 1',
          id: 'e877ee08-acf5-4038-80a4-e975a48dddbb',
          variantName: '200 g',
          ordinal: 2,
        },
        {
          quantity: 2,
          taxes: [
            {
              name: 'GST',
              percent: 10,
              enabled: false,
            },
          ],
          type: 'SINGLE',
          modifiers: [],
          subtotalAmount: 220000,
          totalAmount: 220000,
          unit: 'QUANTITY',
          discounts: [],
          price: 110000,
          name: 'test1',
          id: '691ebb8e-2f3a-4738-a0e6-4a7694ff074c',
          variantName: null,
          ordinal: 3,
        },
        {
          quantity: 1,
          taxes: [
            {
              name: 'GST',
              percent: 10,
              enabled: true,
            },
          ],
          type: 'VARIANT',
          modifiers: [],
          subtotalAmount: 90000,
          totalAmount: 90000,
          unit: 'QUANTITY',
          discounts: [],
          price: 90000,
          name: 'Test1',
          id: 'c8f98ac4-3342-41a5-9c0f-8f678f2b8cd9',
          variantName: 'Red',
          ordinal: 4,
        },
        {
          quantity: 1,
          taxes: [
            {
              name: 'GST',
              percent: 10,
              enabled: true,
            },
          ],
          type: 'SINGLE',
          modifiers: [],
          subtotalAmount: 0,
          totalAmount: 0,
          unit: 'QUANTITY',
          discounts: [],
          price: 0,
          name: '# Testing',
          id: 'f7bc9949-bcbd-4819-b916-1cdff49a163b',
          variantName: null,
          ordinal: 5,
        },
      ],
      paidAmount: 3968800,
      status: 'PAID',
    };
    it('should throw an error when order not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      await expect(
        migrationService.findOrderItemsNotMatched({ id: 'id', entityUuid: 'entity-uuid' } as any),
      ).rejects.toThrow(`order not found for id`);
    });

    it('should return false when order due amount large than 0', async () => {
      mockOrderRepository.getOrder.mockResolvedValueOnce({ ...order, dueAmount: 1 } as any);
      const result = await migrationService.findOrderItemsNotMatched({
        id: 'id',
        entityUuid: 'entity-uuid',
      } as any);
      expect(result).toEqual({ matched: false });
    });

    xit('should return true when order matches', async () => {
      mockOrderRepository.getOrder.mockResolvedValueOnce(order as any);
      const result = await migrationService.findOrderItemsNotMatched({
        id: 'id',
        entityUuid: 'entity-uuid',
      } as any);
      expect(result).toEqual({ matched: true });
    });
    it('should return false when order does not match', async () => {
      const wrongOrder = JSON.parse(JSON.stringify(order));
      wrongOrder.items = [];
      mockOrderRepository.getOrder.mockResolvedValueOnce(wrongOrder as any);
      const result = await migrationService.findOrderItemsNotMatched({
        id: 'id',
        entityUuid: 'entity-uuid',
      } as any);
      expect(result).toEqual({ matched: false });
    });
    it('should return false when order does not match', async () => {
      const wrongOrder = JSON.parse(JSON.stringify(order));
      wrongOrder.paidAmount = undefined;
      wrongOrder.totalSurcharge = undefined;
      wrongOrder.totalTips = undefined;
      mockOrderRepository.getOrder.mockResolvedValueOnce(wrongOrder as any);
      const result = await migrationService.findOrderItemsNotMatched({
        id: 'id',
        entityUuid: 'entity-uuid',
      } as any);
      expect(result).toEqual({ matched: false });
    });
  });

  describe('addGstToPayment', () => {
    it('should throw an error when order not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      await expect(
        migrationService.addGstToPayment({
          id: 'id',
          entityUuid: 'entity-uuid',
        } as any),
      ).rejects.toThrow(`order not found for id`);
    });
    it('should throw an error when order payment not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({} as any);
      await expect(
        migrationService.addGstToPayment({
          id: 'id',
          entityUuid: 'entity-uuid',
        } as any),
      ).rejects.toThrow(`order not found for id`);
    });
    it('should call save payment', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({
        payments: [
          {
            type: TransactionType.PURCHASE,
            status: TransactionStatus.APPROVED,
            transactionUuid: 'transactionUuid',
          },
        ],
      } as any);
      mockTransactionService.getTransaction.mockResolvedValue({} as any);
      await migrationService.addGstToPayment({ id: 'id', entityUuid: 'entity-uuid' } as any);
      expect(mockOrderRepository.saveOrderPayments).toHaveBeenCalled();
      expect(mockOrderService.orderPaymentMigration).toHaveBeenCalled();
    });
  });

  describe('updateOpenOrderDueAmount', () => {
    it('should throw an error when order not found', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      await expect(
        migrationService.updateOpenOrderDueAmount({
          id: 'id',
          entityUuid: 'entity-uuid',
        }),
      ).rejects.toThrow(`order not found for id`);
    });

    it('should throw an error when order status not OPEN', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({
        status: OrderStatus.PART_PAID,
      } as any);
      await expect(
        migrationService.updateOpenOrderDueAmount({
          id: 'id',
          entityUuid: 'entity-uuid',
        }),
      ).rejects.toThrow(`order status not open for id`);
    });

    it('should migrate', async () => {
      mockOrderRepository.getOrder.mockResolvedValue({
        status: OrderStatus.OPEN,
      } as any);
      await migrationService.updateOpenOrderDueAmount({ id: 'id', entityUuid: 'entity-uuid' });
      expect(mockOrderService.orderUpdated).toHaveBeenCalledTimes(1);
    });
  });

  describe('migrationSetOrderGst', () => {
    it('should get order created timestamp in transaction local time', () => {
      const order = {
        id: 'id',
        entityUuid: 'entity-uuid',
        createdTime: 1723525987, // Tue Aug 13 2024 05:13:07 GMT+0000
      };
      const transaction = {
        timestampLocal: '2024-08-13T10:43:49.300+05:30',
      };

      const res = migrationService.getCreatedTimeInLocalTimestamp(order.createdTime, transaction.timestampLocal);
      expect(res).toEqual('2024-08-13T10:43:07.000+05:30');
    });
    it('should throw error when order has no APPROVED payment', async () => {
      mockOrderRepository.getOrderOrThrowNotFound.mockResolvedValue({ payments: [{ status: 'DECLINED' }] } as any);
      await expect(() =>
        migrationService.migrationSetOrderGst({
          id: 'id',
          entityUuid: 'entity-uuid',
          updatedTime: new Date().toISOString(),
        }),
      ).rejects.toThrow('No approved payment found for order id');
    });
    it('should call orderService.orderUpdated', async () => {
      const input = {
        id: 'id',
        entityUuid: 'entity-uuid',
        updatedTime: new Date().toISOString(),
        catalogSettings: { itemsTaxInclusive: false },
      };
      mockOrderRepository.getOrderOrThrowNotFound.mockResolvedValue({
        createdTime: new Date().getTime() / 1000,
        payments: [{ status: 'APPROVED' }],
        catalogSettings: { itemsTaxInclusive: false },
      } as any);
      mockTransactionService.getTransaction.mockResolvedValue({ timestampLocal: new Date().toString() } as any);

      await migrationService.migrationSetOrderGst(input);
      expect(mockOrderService.orderUpdated).toHaveBeenCalledTimes(1);
    });
    it('should set amounts in the order', () => {
      const transaction = {
        id: 'id',
        entityUuid: 'entity-uuid',
        amount: 100,
        taxAmounts: [{ amount: 10 }],
      };
      const order = { ...getOrderInput(), payments: [{ status: 'APPROVED' }] };

      const res = migrationService.setOrderAmounts(order as any, transaction as any);
      expect(res).toEqual({
        ...order,
        paidAmount: 100,
        orderAmount: 99,
        orderGst: 9,
        subtotalAmount: 109,
        totalGst: 10,
        orderDisplayAmount: 100,
        updatedTime: expect.any(Number),
        updatedTimeInMilliseconds: expect.any(Number),
      });
    });
  });
});
