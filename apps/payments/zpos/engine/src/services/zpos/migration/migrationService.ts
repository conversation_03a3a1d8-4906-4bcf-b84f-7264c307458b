import { debug, error, info } from '@npco/component-bff-core/dist/utils/logger';
import type { ZposOrderUpdatedEventPayload } from '@npco/component-domain-events/dist/zellerPosService';
import { ISO4217, TransactionType } from '@npco/component-dto-core/dist';
import { OrderStatus } from '@npco/component-dto-order/dist/types';
import type { Transaction } from '@npco/component-dto-transaction/dist';
import { TransactionStatus } from '@npco/component-dto-transaction/dist';

import { Injectable } from '@nestjs/common';

import { CommandService, OrderPartPaidCommand } from '../../../commands';
import { getOrderDiscount, getOrderItem } from '../../../commands/utils';
import type { Order } from '../../../domain/entities';
import { OrderItemModel } from '../../../domain/models/orderItemModel';
import { OrderModel } from '../../../domain/models/orderModel';
import type { CreateOrderInput } from '../../../domain/types';
import { OrderService } from '../order';
import { OrderRepository } from '../order/orderRepository';
import { convertTransactionCentsToCentiCents } from '../tests/utils';
import { TransactionService } from '../transaction/transactionService';

@Injectable()
export class MigrationService {
  constructor(
    private readonly transactionService: TransactionService,
    private readonly orderRepository: OrderRepository,
    private readonly orderService: OrderService,
    private readonly orderItemModel: OrderItemModel,
    private readonly orderModel: OrderModel,
    private readonly commandService: CommandService,
  ) {}

  markOpenOrderAsPaid = async (transactionUuid: string): Promise<void> => {
    info(`query transaction: ${transactionUuid}`);
    await this.transactionService.sendPaymentSqsMessage({ id: transactionUuid } as any);
  };

  getOrderPaidEventMissingAttributes = async (input: { id: string; entityUuid: string }) => {
    const order = await this.orderRepository.getOrder(input);
    if (!order) {
      throw new Error(`order not found for ${input.id}`);
    }
    return {
      items: getOrderItem(order),
      discounts: getOrderDiscount(order),
      createdFromDeviceUuid: order.createdFromDeviceUuid,
      siteUuid: order.siteUuid,
      catalogSettings: order.catalogSettings,
      referenceNumber: order.referenceNumber,
    };
  };

  recalculateOrderItem = async (input: { id: string; entityUuid: string }) => {
    const order = await this.orderRepository.getOrder(input);
    if (!order?.items) {
      throw new Error(`order not found for ${input.id}`);
    }
    order.items.forEach((item) => this.orderItemModel.setAmounts(item, order.catalogSettings));
    order.updatedTime = Math.floor(new Date().getTime() / 1000);
    order.updatedTimeInMilliseconds = new Date().getTime();
    await this.orderRepository.migrationUpdateOrder(order);
    await this.orderService.orderUpdated(order);
  };

  rematerialiseOrder = async (input: { id: string; entityUuid: string }) => {
    const order = await this.orderRepository.getOrder(input);
    if (!order) {
      throw new Error(`order not found for ${input.id}`);
    }
    await this.orderService.orderUpdated({ ...order, updatedTime: new Date().getTime() / 1000 });
  };

  recalculateOrderAmount = async (input: { id: string; entityUuid: string }) => {
    const order = await this.orderRepository.getOrder(input);
    let transaction: Transaction | undefined;
    if (!order) {
      throw new Error(`order not found for ${input.id}`);
    }
    if (order.status === OrderStatus.PAID) {
      const payment = order.payments?.find(
        (p) => p.status === TransactionStatus.APPROVED && p.type === TransactionType.PURCHASE,
      );
      if (!payment) {
        error(`order payment not found for paid order ${input.id}`);
        throw new Error(`order payment not found for paid order ${input.id}`);
      }
      transaction = await this.transactionService.getTransaction(payment.transactionUuid);
    }
    order.updatedTime = undefined;
    const updatedOrder = await this.orderRepository.migrationFixOrderTotal(order, transaction);
    if (updatedOrder) {
      await this.orderService.orderUpdated(updatedOrder);
    }
  };

  recalculateAmountsInOrder = (order: Order, transaction: Transaction) => {
    const paidAmount = convertTransactionCentsToCentiCents(transaction.amount);
    const totalGstWithSurcharge = this.orderModel.getTotalGstFromTransaction(transaction);
    const { orderAmount, totalGst, subtotalAmount } = this.orderModel.calculateOrderAmounts(order as any);
    return {
      paidAmount,
      orderAmount,
      orderGst: totalGst,
      totalGst: totalGstWithSurcharge,
      subtotalAmount,
    };
  };

  setOrderAmounts = (order: Order, transaction: Transaction) => {
    const { paidAmount, orderAmount, orderGst, totalGst, subtotalAmount } = this.recalculateAmountsInOrder(
      order,
      transaction,
    );
    const infoLog = {
      'order.paidAmount': order.paidAmount,
      'new paidAmount': paidAmount,
      'order.orderAmount': order.orderAmount,
      'new orderAmount': orderAmount,
      'order.totalAmount': order.totalAmount,
      'new totalAmount': paidAmount,
      'order.orderGst': order.orderGst,
      'new orderGst': orderGst,
      'order.totalGst': order.totalGst,
      'new totalGst': totalGst,
      'order.orderDisplayAmount': order.orderDisplayAmount,
      'new orderDisplayAmount': paidAmount,
    };
    debug(infoLog);

    const updatedOrder = order;
    updatedOrder.paidAmount = paidAmount;
    updatedOrder.totalAmount = paidAmount;
    updatedOrder.orderAmount = orderAmount;
    updatedOrder.orderGst = orderGst;
    updatedOrder.totalGst = totalGst;
    updatedOrder.subtotalAmount = subtotalAmount;
    updatedOrder.orderDisplayAmount = paidAmount;
    updatedOrder.updatedTime = this.orderModel.getTimeUnix();
    updatedOrder.updatedTimeInMilliseconds = this.orderModel.getTimeUnixInMs();

    return updatedOrder;
  };

  getOffset = (s: string) => {
    /* eslint-disable no-useless-escape */
    return (/z$|[+\-]\d\d:\d\d$/i.exec(s) || [])[0];
  };

  offsetToMins = (offset: string) => {
    // Deal with Z or z
    if (/z/i.test(offset)) return 0;
    // Deal +/-HH:mm
    const sign: number = offset.startsWith('-') ? 1 : -1;
    const [h, m] = offset.slice(-5).split(':');
    return sign * (Number(h) * 60 + Number(m));
  };

  adjustToOffset = (date: Date, offset: string) => {
    const o = this.offsetToMins(offset);
    const d = date;
    d.setUTCMinutes(d.getUTCMinutes() - o);
    return d.toISOString().replace('Z', offset);
  };

  getCreatedTimeInLocalTimestamp = (createdTimeUnix: number, localTimestamp: string) => {
    const offset = this.getOffset(localTimestamp);
    if (offset) {
      return this.adjustToOffset(new Date(createdTimeUnix * 1000), offset);
    }
    return localTimestamp;
  };

  migrationSetOrderGst = async (input: { id: string; entityUuid: string; updatedTime: string }) => {
    const existingOrder = await this.orderRepository.getOrderOrThrowNotFound(input);
    const approvedPayment = existingOrder.payments?.find((p) => p.status === TransactionStatus.APPROVED);
    if (!approvedPayment) {
      throw new Error(`No approved payment found for order ${input.id}`);
    }
    const transaction = await this.transactionService.getTransaction(approvedPayment.transactionUuid);
    const updatedOrder = this.setOrderAmounts(existingOrder, transaction);
    updatedOrder.createdTimestampLocal = this.getCreatedTimeInLocalTimestamp(
      existingOrder.createdTime,
      transaction.timestampLocal,
    );
    const order = await this.orderRepository.migrationUpdateOrder(updatedOrder);
    await this.orderService.orderUpdated(order);
  };

  forceUpdatePaidOrder = async (input: CreateOrderInput) => {
    const existingOrder = await this.orderRepository.getOrder(input);
    if (!existingOrder) {
      error(`order not found for ${input.id}`);
      throw new Error(`order not found for ${input.id}`);
    }
    const payment = existingOrder.payments?.find(
      (p) => p.status === TransactionStatus.APPROVED && p.type === TransactionType.PURCHASE,
    );
    if (!payment) {
      error(`order payment not found for paid order ${input.id}`);
      throw new Error(`order payment not found for paid order ${input.id}`);
    }
    const transaction = await this.transactionService.getTransaction(payment.transactionUuid);
    const order = await this.orderRepository.migrationForceUpdatePaidOrder(input, existingOrder, transaction);
    await this.orderService.orderUpdated(order);
  };

  findOrderItemsNotMatched = async (input: { id: string; entityUuid: string }) => {
    const existingOrder = await this.orderRepository.getOrder(input);
    if (!existingOrder) {
      error(`order not found for ${input.id}`);
      throw new Error(`order not found for ${input.id}`);
    }
    if (existingOrder.dueAmount) {
      return {
        matched: false,
      };
    }
    const paidAmount = existingOrder.paidAmount ?? 0;
    const existingTotalSurcharge = existingOrder.totalSurcharge ?? 0;
    const existingTotalTips = existingOrder.totalTips ?? 0;

    const updatedOrder = await this.orderModel.update(existingOrder, JSON.parse(JSON.stringify(existingOrder)), true);
    if (Math.abs(updatedOrder.orderAmount + existingTotalSurcharge + existingTotalTips - paidAmount) <= 100) {
      return {
        matched: true,
      };
    }
    return {
      matched: false,
    };
  };

  addGstToPayment = async (input: { id: string; entityUuid: string }) => {
    const existingOrder = await this.orderRepository.getOrder(input);
    if (!existingOrder?.payments) {
      error(`order not found or no payments for ${input.id}`);
      throw new Error(`order not found for ${input.id}`);
    }
    for (const payment of existingOrder.payments) {
      if (payment.type === TransactionType.PURCHASE) {
        const transaction = await this.transactionService.getTransaction(payment.transactionUuid);
        payment.taxAmounts = transaction.taxAmounts;
        payment.timestampLocal = transaction.timestampLocal;
        payment.shortId = transaction.reference;
        payment.currency = ISO4217.AUD;
      }
    }
    await this.orderRepository.saveOrderPayments(existingOrder);
    const order = await this.orderRepository.getOrder(input);
    if (order) {
      await this.orderService.orderPaymentMigration(order);
    }
  };

  updateOpenOrderDueAmount = async (input: { id: string; entityUuid: string }) => {
    const existingOrder = await this.orderRepository.getOrder(input);
    if (!existingOrder) {
      error(`order not found for ${input.id}`);
      throw new Error(`order not found for ${input.id}`);
    }
    if (existingOrder.status !== OrderStatus.OPEN) {
      throw new Error(`order status not open for ${input.id}`);
    }
    existingOrder.dueAmount = existingOrder.orderAmount;
    await this.orderRepository.migrationUpdateOrder(existingOrder);
    await this.orderService.orderUpdated(existingOrder);
  };

  reprocessOrderPayment = async (input: { id: string; entityUuid: string }) => {
    const existingOrder = await this.orderRepository.getOrder(input);
    const isExistingOpenOrder = existingOrder?.status === OrderStatus.OPEN;
    const havePayments = existingOrder?.payments && existingOrder?.payments?.length > 0;
    if (!isExistingOpenOrder || !havePayments) {
      info('skipping order as it does not exist or it does not have payments');
      return;
    }
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const lastPayment = existingOrder!.payments!.sort((a, b) => (a.timestamp < b.timestamp ? 1 : -1))[0];

    if (lastPayment.status !== TransactionStatus.APPROVED) {
      info('Last payment was not APPROVED, skipping order migration.');
      info(`Order: ${existingOrder.id}, Last Payment: ${lastPayment}`);
      return;
    }
    const transaction = await this.transactionService.getTransaction(lastPayment.transactionUuid);
    const { order } = await this.orderRepository.migrationSaveOrderPayment({ order: existingOrder, transaction });
    const updatedOrder = await this.orderRepository.getOrderOrThrowNotFound(order);
    info(`Order payment processed. Order status: ${updatedOrder.status}, Payment: ${lastPayment.id}`);
    await this.commandService.sendCommand(new OrderPartPaidCommand(updatedOrder, lastPayment));
  };

  migrateOpenOrderAmountMismatch = async (input: {
    orderUuid: string;
    entityUuid: string;
    payload: any;
    transactionUuid: string;
  }) => {
    const { orderUuid, entityUuid, payload, transactionUuid } = input;
    const existingOrder = await this.orderRepository.getOrderOrThrowNotFound({ id: orderUuid, entityUuid });
    const transaction = await this.transactionService.getTransaction(transactionUuid);

    const correctOrder = payload as ZposOrderUpdatedEventPayload;
    const items = this.orderModel.safeGetArray(correctOrder.items).map((item) => {
      return {
        ...item,
        price: this.orderModel.safeGetNumber(item.price.value),
        discounts: this.orderModel.safeGetArray(item.discounts).map((discount) => ({
          ...discount,
          value: this.orderModel.safeGetNumber(discount.value),
        })),
        modifiers: this.orderModel.safeGetArray(item.modifiers).map((modifier) => ({
          ...modifier,
          price: this.orderModel.safeGetNumber(modifier.price.value),
        })),
      };
    });
    const discounts = this.orderModel.safeGetArray(correctOrder.discounts).map((discount) => ({
      ...discount,
      value: this.orderModel.safeGetNumber(discount.value),
    }));

    const updateInput = {
      id: orderUuid,
      entityUuid,
      items,
      discounts,
      createdFromDeviceUuid: existingOrder.createdFromDeviceUuid,
      siteUuid: existingOrder.siteUuid,
      catalogSettings: existingOrder.catalogSettings,
      updatedTimeInMilliseconds: new Date().getTime(),
    };
    const updatedOrderData = await this.orderModel.update(existingOrder, updateInput as any);
    if (updatedOrderData.orderAmount !== transaction.saleAmount) {
      error(
        `Migration failed for order ${orderUuid}, orderAmount ${updatedOrderData.orderAmount} does not match the transaction saleAmount ${transaction.saleAmount} after applying the provided payload`,
      );
      throw new Error(`migrateOpenOrderAmountMismatch: Migration failed for order ${orderUuid}`);
    }
    await this.orderRepository.updateOrder(existingOrder, updateInput as any);
    const updatedOrder = await this.orderRepository.getOrderOrThrowNotFound({ id: orderUuid, entityUuid });
    await this.orderService.saveOrderPayment({ order: updatedOrder, transaction });
    info(
      `Migration successful for order ${orderUuid}, orderAmount ${updatedOrderData.orderAmount} matches transaction saleAmount ${transaction.saleAmount}`,
    );
  };
}
