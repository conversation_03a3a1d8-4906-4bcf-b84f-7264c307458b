import { CatalogDiscountConfig, CatalogServiceChargeConfig, CatalogUnit } from '@npco/component-dto-catalog/dist';
import { ISO4217 } from '@npco/component-dto-core/dist/types';
import { OrderItemType, OrderStatus } from '@npco/component-dto-order/dist';
import { TransactionStatus } from '@npco/component-dto-transaction/dist/types';

import type { INestApplicationContext } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { v4 } from 'uuid';

import { mockEnvServiceForLocalDbConnection } from '../../../config/__mocks__/localDbConnection';
import { TRANSACTION_AMOUNT_TO_CENTICENTS_RATE } from '../../../domain/const';
import { OrderItem } from '../../../domain/entities';
import { OrderDiscountModel } from '../../../domain/models/orderDiscountModel';
import { OrderItemModel } from '../../../domain/models/orderItemModel';
import { OrderModel } from '../../../domain/models/orderModel';
import { OrderServiceChargeModel } from '../../../domain/models/orderServiceChargeModel';
import type { CreateOrderInput } from '../../../domain/types';
import { initiateOrderMutationNestModule } from '../../../lambdas/testcases/mockNestModule';
import { TenderType } from '../../types';

import { OrderRepository } from './orderRepository';

jest.mock('../../../config/envService', () => mockEnvServiceForLocalDbConnection());

jest.mock('aws-xray-sdk', () => {
  return {
    getSegment: () => ({
      addNewSubsegment: jest.fn().mockReturnValue({}),
      addAnnotation: jest.fn(),
    }),
    captureAsyncFunc: async (name: string, cb: any) => {
      await cb({ addAnnotation: jest.fn(), close: jest.fn() });
    },
  };
});

const sleep = async (time: number) => {
  return new Promise((resolve) => {
    setTimeout(resolve, time);
  });
};

const convertTransactionCentsToCentiCents = (cents: number) => {
  return cents * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE;
};

describe('Order Repository test suite', () => {
  let appContext: INestApplicationContext;
  let dataSource: DataSource;
  let orderRepository: OrderRepository;
  let orderModel: OrderModel;

  beforeAll(async () => {
    appContext = await initiateOrderMutationNestModule();
    dataSource = appContext.get(DataSource);
    const discountModel = new OrderDiscountModel();
    const serviceChargeModel = new OrderServiceChargeModel();
    const orderItemModel = new OrderItemModel(discountModel, serviceChargeModel);
    orderModel = new OrderModel(orderItemModel, discountModel, serviceChargeModel);
    orderRepository = new OrderRepository(dataSource, orderModel);
  });

  afterAll(async () => {
    await dataSource.destroy();
  });

  const getOrderInput = (): CreateOrderInput => {
    return {
      id: v4(),
      entityUuid: v4(),
      siteUuid: v4(),
      createdFromDeviceUuid: v4(),
      catalogSettings: { itemsTaxInclusive: true, itemsApplyTax: true, autoSkuEnabled: true },
      updatedTime: new Date().toISOString(),
      discounts: [
        {
          id: v4(),
          config: CatalogDiscountConfig.AMOUNT,
          value: '1000',
          name: 'order level',
          ordinal: 1,
        },
      ],
      serviceCharges: [
        { id: v4(), config: CatalogServiceChargeConfig.AMOUNT, value: '1000', name: 'order level', ordinal: 1 },
      ],
      items: [
        {
          id: v4(),
          name: 'test item',
          price: '10000',
          ordinal: 1,
          type: OrderItemType.SINGLE,
          unit: CatalogUnit.HOUR,
          discounts: [
            {
              id: v4(),
              config: CatalogDiscountConfig.PERCENTAGE,
              value: '10',
              name: 'item level',
              ordinal: 1,
            },
          ],
          serviceCharges: [
            {
              id: v4(),
              config: CatalogServiceChargeConfig.PERCENTAGE,
              value: '10',
              name: 'item level',
              ordinal: 1,
            },
          ],
          quantity: 1,
          modifiers: [
            {
              id: v4(),
              name: 'test modifier',
              price: '1000',
              ordinal: 1,
              unit: CatalogUnit.QUANTITY,
              quantity: 1,
            },
          ],
          taxes: [{ name: 'GST', enabled: true, percent: 10 }],
        },
      ],
    };
  };

  describe('afterLoad', () => {
    it('should update order amounts to cents when its in centi-cents after load', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.saveOrder(input, null);
      expect(order).toBeDefined();
      await orderRepository.getRepository().update(order.id, {
        currencyMinorUnit: 4,
        orderAmount: order!.orderAmount! * 100,
        dueAmount: order!.dueAmount! * 100,
        orderGst: order!.orderGst! * 100,
        updatedTimeInMilliseconds: order!.updatedTimeInMilliseconds! + 1,
      });

      const result = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(result).toBeDefined();
      expect(result?.id).toEqual(order.id);
      // verify amounts
      expect(result?.orderAmount).toBe(order!.orderAmount);
      expect(result?.dueAmount).toBe(order!.dueAmount);
      expect(result?.totalGst).toBe(order!.totalGst);
      expect(result?.orderGst).toBe(order!.orderGst);
    });
  });

  describe('getOrder', () => {
    it('should get order', async () => {
      const input: CreateOrderInput = getOrderInput();

      const order = await orderRepository.saveOrder(input, null);
      const result = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.entityUuid).toBeDefined();
      expect(result?.createdFromDeviceUuid).toBe(input.createdFromDeviceUuid);
      expect(result?.status).toBe(OrderStatus.OPEN);
      expect(result?.referenceNumber).toBeDefined();
      expect(result?.createdTime).toBeDefined();
      expect(result?.updatedTime).toBeDefined();
      expect(result?.currency).toBe(ISO4217.AUD);
      // verify amounts
      expect(result?.orderAmount).toBe(119);
      expect(result?.dueAmount).toBe(119);
      expect(result?.totalGst).toBe(11);
      expect(result?.orderGst).toBe(11);
      expect(result?.totalSurcharge).toBe(0);
      expect(result?.totalSurchargedGst).toBe(0);
      expect(result?.items).toHaveLength(1);
      // verify item
      const item = result?.items![0] as any;
      expect(item.id).toBeDefined();
      expect(item.catalogItem).toBeNull();
      expect(item.name).toBe('test item');
      expect(item.price).toEqual(10000);
      expect(item.ordinal).toBe(1);
      expect(item.unit).toBe(CatalogUnit.HOUR);
      expect(item.quantity).toBe(1);
      expect(item.taxes).toHaveLength(1);
      expect(item.taxes[0].name).toBe('GST');
      expect(item.taxes[0].enabled).toBe(true);
      expect(item.taxes[0].percent).toBe(10);
      expect(item.subtotalAmount).toBe(11000);
      expect(item.totalAmount).toBe(11900);
    });

    it('should get order whithout items', async () => {
      const input: CreateOrderInput = getOrderInput();
      input.items = null as any;
      const order = await orderRepository.saveOrder(input, null);
      const result = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.entityUuid).toBeDefined();
      expect(result?.createdFromDeviceUuid).toBe(input.createdFromDeviceUuid);
      expect(result?.status).toBe(OrderStatus.OPEN);
      expect(result?.referenceNumber).toBeDefined();
      expect(result?.createdTime).toBeDefined();
      expect(result?.updatedTime).toBeDefined();
      expect(result?.currency).toBe(ISO4217.AUD);
      // verify item
      expect(result?.items).toHaveLength(0);
    });

    it('should throw error when id or entityUuid not provided', async () => {
      await expect(orderRepository.getOrder({ id: '', entityUuid: '' })).rejects.toThrow(
        'id and entityUuid are required',
      );
      await expect(orderRepository.getOrder({ id: 'id', entityUuid: '' })).rejects.toThrow(
        'id and entityUuid are required',
      );
      await expect(orderRepository.getOrder({ id: '', entityUuid: 'id' })).rejects.toThrow(
        'id and entityUuid are required',
      );
    });
  });

  describe('getOrders', () => {
    it('should get orders', async () => {
      const input: CreateOrderInput = getOrderInput();

      const order = await orderRepository.saveOrder(input, null);
      const result = (await orderRepository.getOrders([order.id]))[0];
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.items).toHaveLength(1);
      // verify item
      const item = result?.items![0] as any;
      expect(item.id).toBeDefined();
    });
  });

  describe('getOrderOrThrowNotFound', () => {
    it('should get order', async () => {
      const input: CreateOrderInput = getOrderInput();

      const order = await orderRepository.saveOrder(input, null);
      const result = await orderRepository.getOrderOrThrowNotFound({ id: order.id, entityUuid: order.entityUuid });
      expect(result).toBeDefined();
    });

    it('should throw an error when order not found', async () => {
      const input = {
        id: v4(),
        entityUuid: v4(),
      };
      await expect(orderRepository.getOrderOrThrowNotFound(input)).rejects.toThrow(
        `id ${input.id} and entityUuid ${input.entityUuid} not found for order`,
      );
    });
  });

  describe('createOrder', () => {
    it('should create order with NO relation', async () => {
      const input: CreateOrderInput = { ...getOrderInput(), items: [], discounts: [], serviceCharges: [] };

      const result = await orderRepository.saveOrder(input, null);
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.entityUuid).toBeDefined();
      expect(result?.status).toBe(OrderStatus.OPEN);
      expect(result?.createdFromDeviceUuid).toBe(input.createdFromDeviceUuid);
      expect(result?.referenceNumber).toBeDefined();
      expect(result?.createdTime).toBeDefined();
      expect(result?.updatedTime).toBeDefined();
      expect(result?.currency).toBe(ISO4217.AUD);
      // verify amounts
      expect(result?.orderAmount).toBe(0);
      expect(result?.totalGst).toBe(0);
      expect(result?.totalSurcharge).toBe(0);
      expect(result?.totalSurchargedGst).toBe(0);
      expect(result?.items).toBeUndefined();
      expect(result?.discounts).toEqual([]);
      expect(result?.serviceCharges).toEqual([]);
    });

    it('should create order with relations: discounts, serviceCharges, items and item modifiers', async () => {
      const input: CreateOrderInput = getOrderInput();
      input.items[0] = {
        ...input.items[0],
        discounts: [
          { id: v4(), config: CatalogDiscountConfig.AMOUNT, value: '100', name: 'item level 1', ordinal: 1 },
          { id: v4(), config: CatalogDiscountConfig.PERCENTAGE, value: '10', name: 'item level 2', ordinal: 2 },
        ],
        serviceCharges: [
          {
            id: v4(),
            config: CatalogServiceChargeConfig.AMOUNT,
            value: '100',
            name: 'item level 1',
            ordinal: 1,
          },
          {
            id: v4(),
            config: CatalogServiceChargeConfig.PERCENTAGE,
            value: '10',
            name: 'item level 2',
            ordinal: 2,
          },
        ],
      };

      await orderRepository.saveOrder(input, null);
      const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.entityUuid).toBeDefined();
      expect(result?.status).toBe(OrderStatus.OPEN);
      expect(result?.referenceNumber).toBeDefined();
      expect(result?.createdTime).toBeDefined();
      expect(result?.updatedTime).toBeDefined();
      expect(result?.currency).toBe(ISO4217.AUD);
      // verify amounts
      expect(result?.orderAmount).toBe(119);
      expect(result?.totalGst).toBe(11);
      expect(result?.orderGst).toBe(11);

      expect(result?.totalSurcharge).toBe(0);
      expect(result?.totalSurchargedGst).toBe(0);
      expect(result?.items).toHaveLength(1);
      // verify order discount
      expect(result?.discounts).toHaveLength(1);
      const orderDiscount = result?.discounts![0];
      expect(orderDiscount?.discountedAmount).toBe(1000);
      // verify order serviceCharge
      expect(result?.serviceCharges).toHaveLength(1);
      const orderServiceCharge = result?.serviceCharges![0];
      expect(orderServiceCharge?.serviceChargeAmount).toBe(1000);
      // verify item
      const item = result!.items![0];
      expect(item.id).toBeDefined();
      expect(item.catalogItem).toBeNull();
      expect(item.name).toBe('test item');
      expect(item.type).toEqual(OrderItemType.SINGLE);
      expect(item.price).toEqual(10000);
      expect(item.ordinal).toBe(1);
      expect(item.unit).toBe(CatalogUnit.HOUR);
      expect(item.quantity).toBe(1);
      expect(item.taxes).toHaveLength(1);
      expect(item.taxes[0].name).toBe('GST');
      expect(item.taxes[0].enabled).toBe(true);
      expect(item.taxes[0].percent).toBe(10);
      expect(item.subtotalAmount).toBe(11000);
      expect(item.totalAmount).toBe(11900);
      // verify item modifiers
      expect(item.modifiers).toHaveLength(1);
      const modifier = item.modifiers![0];
      expect(modifier.name).toBe('test modifier');
      expect(modifier.id).toBeDefined();
      expect(modifier.parentId).toEqual(item.id);
      expect(modifier.price).toBe(1000);
      expect(modifier.subtotalAmount).toBe(1000);
      expect(modifier.ordinal).toBe(1);
      expect(modifier.unit).toBe(CatalogUnit.QUANTITY);
      expect(modifier.quantity).toBe(1);
      // verify item discounts
      expect(item.discounts).toHaveLength(2);
      expect(item.discounts).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            name: 'item level 1',
            value: 100,
            config: CatalogDiscountConfig.AMOUNT,
            ordinal: 1,
            discountedAmount: 100,
          }),
          expect.objectContaining({
            id: expect.any(String),
            name: 'item level 2',
            value: 10,
            config: CatalogDiscountConfig.PERCENTAGE,
            ordinal: 2,
            discountedAmount: 1200,
          }),
        ]),
      );
      // verify item serviceCharges
      expect(item.serviceCharges).toHaveLength(2);
      expect(item.serviceCharges).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            name: 'item level 1',
            value: 100,
            config: CatalogServiceChargeConfig.AMOUNT,
            ordinal: 1,
            serviceChargeAmount: 100,
          }),
          expect.objectContaining({
            id: expect.any(String),
            name: 'item level 2',
            value: 10,
            config: CatalogServiceChargeConfig.PERCENTAGE,
            ordinal: 2,
            serviceChargeAmount: 1100,
          }),
        ]),
      );
    }, 999999);

    it('should create order with non-integer percentage discounts and serviceCharges', async () => {
      const input: CreateOrderInput = { ...getOrderInput(), discounts: [], serviceCharges: [] };
      input.discounts = [
        {
          id: v4(),
          config: CatalogDiscountConfig.PERCENTAGE,
          value: '10.5',
          name: 'order level',
          ordinal: 1,
        },
      ];

      input.serviceCharges = [
        {
          id: v4(),
          config: CatalogServiceChargeConfig.PERCENTAGE,
          value: '10.5',
          name: 'order level',
          ordinal: 1,
        },
      ];

      await orderRepository.saveOrder(input, null);
      const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.entityUuid).toBeDefined();
      expect(result?.status).toBe(OrderStatus.OPEN);
      expect(result?.referenceNumber).toBeDefined();
      expect(result?.createdTime).toBeDefined();
      expect(result?.updatedTime).toBeDefined();
      expect(result?.currency).toBe(ISO4217.AUD);
      // verify amounts
      expect(result?.orderAmount).toBe(118);
      expect(result?.totalGst).toBe(11);
      expect(result?.totalSurcharge).toBe(0);
      expect(result?.totalSurchargedGst).toBe(0);
      expect(result?.items).toHaveLength(1);
      // verify item
      const item = result!.items![0];
      expect(item.id).toBeDefined();
      expect(item.catalogItem).toBeNull();
      expect(item.name).toBe('test item');
      expect(item.type).toEqual(OrderItemType.SINGLE);
      expect(item.price).toEqual(10000);
      expect(item.ordinal).toBe(1);
      expect(item.unit).toBe(CatalogUnit.HOUR);
      expect(item.quantity).toBe(1);
      expect(item.taxes).toHaveLength(1);
      expect(item.taxes[0].name).toBe('GST');
      expect(item.taxes[0].enabled).toBe(true);
      expect(item.taxes[0].percent).toBe(10);
      // verify item modifiers
      expect(item.modifiers).toHaveLength(1);
      const modifier = item.modifiers![0];
      expect(modifier.name).toBe('test modifier');
      expect(modifier.id).toBeDefined();
      expect(modifier.parentId).toEqual(item.id);
      expect(modifier.price).toBe(1000);
      expect(modifier.subtotalAmount).toBe(1000);
      expect(modifier.ordinal).toBe(1);
      expect(modifier.unit).toBe(CatalogUnit.QUANTITY);
      expect(modifier.quantity).toBe(1);
      // verify item discounts
      expect(item.discounts).toHaveLength(1);
      const discount = item.discounts![0];
      expect(discount.name).toBe('item level');
      expect(discount.value).toBe(10);
      expect(discount.config).toBe(CatalogDiscountConfig.PERCENTAGE);
      expect(discount.id).toBeDefined();
      // verify item serviceCharges
      expect(item.serviceCharges).toHaveLength(1);
      const serviceCharge = item.serviceCharges![0];
      expect(serviceCharge.name).toBe('item level');
      expect(serviceCharge.value).toBe(10);
      expect(serviceCharge.config).toBe(CatalogServiceChargeConfig.PERCENTAGE);
      expect(serviceCharge.id).toBeDefined();
    });
  });

  describe('updateOrder', () => {
    it('should update existing order add a new order item', async () => {
      const input: CreateOrderInput = getOrderInput();

      const createdOrder = await orderRepository.createOrder(input);
      const updateInput = JSON.parse(JSON.stringify({ ...createdOrder }));
      updateInput.items!.push({
        id: v4(),
        name: 'updated test item',
        price: '10000',
        ordinal: 1,
        type: OrderItemType.SINGLE,
        unit: CatalogUnit.HOUR,
        quantity: 1,
        taxes: [{ name: 'GST', enabled: true, percent: 10 }],
      } as any);
      // act
      const result = await orderRepository.saveOrder(
        { ...updateInput, updatedTime: new Date(new Date().getTime() + 1000).toISOString() } as any,
        createdOrder,
      );
      // verify
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.entityUuid).toBeDefined();
      expect(result?.status).toBe(OrderStatus.OPEN);
      expect(result?.referenceNumber).toBeDefined();
      // expect(result?.createdTime).toBeDefined();
      expect(result?.updatedTime).toBeDefined();
      expect(result?.currency).toBe(ISO4217.AUD);
      // verify amounts
      expect(result?.orderAmount).toBe(229);
      expect(result?.dueAmount).toBe(229);
      expect(result?.totalGst).toBe(21);
      expect(result?.orderGst).toBe(21);
      expect(result?.totalSurcharge).toBe(0);
      expect(result?.totalSurchargedGst).toBe(0);
      expect(result?.items).toHaveLength(2);
      // verify item
      const item1 = result?.items![0];
      expect(item1.id).toBe(createdOrder.items![0].id);
      expect(item1.catalogItem).toBeUndefined();
      expect(item1.name).toBe('test item');
      expect(item1.price).toEqual(10000);
      expect(item1.ordinal).toBe(1);
      expect(item1.unit).toBe(CatalogUnit.HOUR);
      expect(item1.quantity).toBe(1);
      expect(item1.taxes).toHaveLength(1);
      expect(item1.taxes[0].name).toBe('GST');
      expect(item1.taxes[0].enabled).toBe(true);
      expect(item1.taxes[0].percent).toBe(10);
      expect(item1.subtotalAmount).toBe(11000);
      expect(item1.totalAmount).toBe(11900);

      const item2 = result?.items![1];
      expect(item2.id).toBeDefined();
      expect(item2.catalogItem).toBeUndefined();
      expect(item2.name).toBe('updated test item');
      expect(item2.price).toEqual(10000);
      expect(item2.ordinal).toBe(1);
      expect(item2.unit).toBe(CatalogUnit.HOUR);
      expect(item2.quantity).toBe(1);
      expect(item2.taxes).toHaveLength(1);
      expect(item2.taxes[0].name).toBe('GST');
      expect(item2.taxes[0].enabled).toBe(true);
      expect(item2.taxes[0].percent).toBe(10);
      expect(item2.subtotalAmount).toBe(11000);
      expect(item2.totalAmount).toBe(11000);
    });

    it('should delete orderItem and create a new order item from existing order', async () => {
      const input: CreateOrderInput = getOrderInput();

      const createdOrder = await orderRepository.createOrder(input);
      // verify
      expect(createdOrder).toBeDefined();
      expect(createdOrder?.items).toHaveLength(1);
      const updateInput = JSON.parse(JSON.stringify({ ...createdOrder }));

      updateInput.items = [
        {
          id: v4(),
          name: 'updated test item',
          price: '10000',
          ordinal: 1,
          type: OrderItemType.SINGLE,
          unit: CatalogUnit.HOUR,
          discounts: [
            {
              id: v4(),
              config: CatalogDiscountConfig.PERCENTAGE,
              value: '10',
              name: 'item level',
              ordinal: 1,
            },
          ],
          serviceCharges: [
            {
              id: v4(),
              config: CatalogServiceChargeConfig.PERCENTAGE,
              value: '10',
              name: 'item level',
              ordinal: 1,
            },
          ],
          quantity: 1,
          modifiers: [
            {
              id: v4(),
              name: 'test modifier',
              price: '1000',
              ordinal: 1,
              unit: CatalogUnit.QUANTITY,
              quantity: 1,
            },
          ],
          taxes: [{ name: 'GST', enabled: true, percent: 11 }],
        },
      ] as any;
      await orderRepository.saveOrder(
        { ...updateInput, updatedTime: new Date(new Date().getTime() + 1000).toISOString() } as any,
        createdOrder,
      );
      const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(result!.items!.length).toBe(1);
      expect(result!.items![0].name).toEqual('updated test item');
      expect(result!.items![0].modifiers![0].name).toEqual('test modifier');
      expect(result!.items![0].discounts![0].name).toEqual('item level');
      expect(result!.items![0].serviceCharges![0].name).toEqual('item level');
    });

    it('should update orderItem from existing order', async () => {
      const input: CreateOrderInput = getOrderInput();

      const createdOrder = await orderRepository.createOrder(input);
      // verify
      expect(createdOrder).toBeDefined();
      expect(createdOrder?.items).toHaveLength(1);
      const updateInput = JSON.parse(JSON.stringify({ ...createdOrder }));

      updateInput.items[0].name = 'updated test item';
      await orderRepository.saveOrder(
        { ...updateInput, updatedTime: new Date(new Date().getTime() + 1000).toISOString() } as any,
        createdOrder,
      );
      const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(result!.items!.length).toBe(1);
      expect(result!.items![0].name).toEqual('updated test item');
      expect(result!.items![0].modifiers!.length).toEqual(1);
    });

    it('should add a new modifier to the orderItem', async () => {
      const input: CreateOrderInput = getOrderInput();

      const createdOrder = await orderRepository.createOrder(input);
      // verify
      expect(createdOrder).toBeDefined();
      expect(createdOrder?.items).toHaveLength(1);
      const updateInput = JSON.parse(JSON.stringify({ ...createdOrder }));

      updateInput!.items![0].modifiers![0].name = 'updated modifier';
      updateInput!.items![0].modifiers!.push({
        id: v4(),
        name: 'new modifier',
        price: '1000',
        ordinal: 2,
        unit: CatalogUnit.QUANTITY,
        quantity: 1,
      });
      await orderRepository.saveOrder(
        { ...updateInput, updatedTime: new Date(new Date().getTime() + 1000).toISOString() } as any,
        createdOrder,
      );
      const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(result!.items!.length).toBe(1);
      expect(result!.items![0]!.modifiers!.length).toEqual(2);
      const updatedModifier = result!.items![0]!.modifiers!.find((m) => m.name === 'updated modifier');
      expect(updatedModifier).toBeDefined();
      const newModifier = result!.items![0]!.modifiers!.find((m) => m.name === 'new modifier');
      expect(newModifier).toBeDefined();
    });

    it('should add a new discount and serviceCharge to the orderItem', async () => {
      const input: CreateOrderInput = getOrderInput();

      const createdOrder = await orderRepository.createOrder(input);
      // verify
      expect(createdOrder).toBeDefined();
      expect(createdOrder?.items).toHaveLength(1);
      const updateInput = JSON.parse(JSON.stringify({ ...createdOrder }));

      updateInput!.items![0].discounts![0].name = 'updated order level';
      updateInput!.items![0].discounts!.push({
        id: v4(),
        config: CatalogDiscountConfig.AMOUNT,
        value: '1000',
        name: 'order level 2',
        ordinal: 2,
      });
      updateInput!.items![0].serviceCharges![0].name = 'updated order level';
      updateInput!.items![0].serviceCharges!.push({
        id: v4(),
        config: CatalogServiceChargeConfig.AMOUNT,
        value: '1000',
        name: 'order level 2',
        ordinal: 2,
      });
      await orderRepository.saveOrder(
        { ...updateInput, updatedTime: new Date(new Date().getTime() + 1000).toISOString() },
        createdOrder,
      );
      const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(result!.items!.length).toBe(1);
      expect(result!.items![0]!.discounts!.length).toEqual(2);
      const updatedDiscount = result!.items![0]!.discounts!.find((m) => m.name === 'updated order level');
      expect(updatedDiscount).toBeDefined();
      const newDiscount = result!.items![0]!.discounts!.find((m) => m.name === 'order level 2');
      expect(newDiscount).toBeDefined();
      expect(result!.items![0]!.serviceCharges!.length).toEqual(2);
      const updatedServiceCharge = result!.items![0]!.serviceCharges!.find((m) => m.name === 'updated order level');
      expect(updatedServiceCharge).toBeDefined();
      const newServiceCharge = result!.items![0]!.serviceCharges!.find((m) => m.name === 'order level 2');
      expect(newServiceCharge).toBeDefined();
    });

    it('should delete orderDiscount and orderServiceCharge', async () => {
      const input: CreateOrderInput = getOrderInput();
      input.discounts = [
        { id: v4(), config: CatalogDiscountConfig.AMOUNT, value: '100', name: 'order level 1', ordinal: 1 },
      ];
      input.serviceCharges = [
        { id: v4(), config: CatalogServiceChargeConfig.AMOUNT, value: '100', name: 'order level 1', ordinal: 1 },
      ];

      await orderRepository.createOrder(input);
      const createdOrder = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(createdOrder!.totalDiscount).toEqual(1);
      expect(createdOrder!.totalServiceCharge).toEqual(1);

      // verify
      expect(createdOrder).toBeDefined();
      expect(createdOrder?.discounts).toHaveLength(1);
      expect(createdOrder?.serviceCharges).toHaveLength(1);
      const updateInput = JSON.parse(JSON.stringify({ ...createdOrder }));

      updateInput.discounts = [] as any;
      updateInput.serviceCharges = [] as any;
      await orderRepository.saveOrder(
        { ...updateInput, updatedTime: new Date(new Date().getTime() + 1000).toISOString() },
        createdOrder,
      );
      const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(result!.discounts!.length).toBe(0);
      expect(result!.totalDiscount).toEqual(0);
      expect(result!.serviceCharges!.length).toBe(0);
      expect(result!.totalServiceCharge).toEqual(0);
    });

    it('should add orderDiscount and orderServiceCharge', async () => {
      const input: CreateOrderInput = getOrderInput();
      input.discounts = [
        { id: v4(), config: CatalogDiscountConfig.AMOUNT, value: '100', name: 'order level 1', ordinal: 1 },
      ];
      input.serviceCharges = [
        { id: v4(), config: CatalogServiceChargeConfig.AMOUNT, value: '100', name: 'order level 1', ordinal: 1 },
      ];

      await orderRepository.createOrder(input);
      const createdOrder = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });

      // verify
      expect(createdOrder).toBeDefined();
      expect(createdOrder?.discounts).toHaveLength(1);
      expect(createdOrder?.serviceCharges).toHaveLength(1);
      const updateInput = JSON.parse(JSON.stringify({ ...createdOrder }));

      updateInput.discounts.push({
        id: v4(),
        config: CatalogDiscountConfig.AMOUNT,
        value: '200',
        name: 'order level 2',
        ordinal: 2,
      });
      updateInput.serviceCharges.push({
        id: v4(),
        config: CatalogServiceChargeConfig.AMOUNT,
        value: '200',
        name: 'order level 2',
        ordinal: 2,
      });
      await orderRepository.saveOrder(
        { ...updateInput, updatedTime: new Date(new Date().getTime() + 1000).toISOString() },
        createdOrder,
      );
      const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
      expect(result!.discounts!.length).toBe(2);
      expect(result!.serviceCharges!.length).toBe(2);
    });
  });

  describe('saveOrderPayment', () => {
    it('should add new payment to order', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transaction = {
        id: v4(),
        amount: 100,
        saleAmount: order.orderAmount,
        tipAmount: 1,
        status: 'APPROVED',
        type: 'CNP',
        surchargeAmount: 0,
        taxAmounts: [{ amount: 10 } as any],
        timestamp: new Date().toISOString(),
      } as any;

      // act
      const { order: res } = await orderRepository.saveOrderPayment({ order, transaction });
      // verify
      expect(res).toBeDefined();
      expect(res?.id).toBeDefined();
      expect(res?.entityUuid).toBeDefined();
      expect(res?.status).toBe(OrderStatus.PAID);
      expect(res?.payments).toBeDefined();
      expect(res?.payments![0].taxAmounts).toEqual([{ amount: 10 } as any]);
      expect(res?.orderGst).toBe(order.orderGst);
      expect(res?.totalGst).toBe(transaction.taxAmounts[0].amount);
      const orderWithRelation = await orderRepository.getOrder(res);
      expect(orderWithRelation?.status).toEqual(OrderStatus.PAID);
      expect(orderWithRelation!.payments).toHaveLength(1);
      expect(orderWithRelation!.items!.length).toBe(1);
    });

    it('should reset amount when payment is cancelled', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transaction = {
        id: v4(),
        amount: 100,
        saleAmount: order.orderAmount,
        tipAmount: 1,
        status: 'APPROVED',
        type: 'CNP',
        surchargeAmount: 1,
        taxAmounts: [{ amount: 10 } as any],
        timestamp: new Date().toISOString(),
      } as any;

      // act
      const { order: res } = await orderRepository.saveOrderPayment({ order, transaction });
      // verify
      expect(res).toBeDefined();
      expect(res?.status).toBe(OrderStatus.PAID);
      const orderWithRelation = await orderRepository.getOrder(res);
      expect(orderWithRelation?.status).toEqual(OrderStatus.PAID);
      // cancel payment
      await orderRepository.saveOrderPayment({
        order,
        transaction: {
          ...transaction,
          status: 'CANCELLED',
        },
      });

      // verify cancelled payment in total amounts
      const orderWithRelation2 = await orderRepository.getOrder(res);
      expect(orderWithRelation2!.status).toEqual(OrderStatus.OPEN);
      expect(orderWithRelation2!.orderAmount).toEqual(order.orderAmount);
      expect(orderWithRelation2!.paidAmount).toEqual(0);
      expect(orderWithRelation2!.dueAmount).toEqual(order.dueAmount);
      expect(orderWithRelation2!.subtotalAmount).toEqual(order.subtotalAmount);
      expect(orderWithRelation2!.totalTips).toEqual(0);
      expect(orderWithRelation2!.totalSurcharge).toEqual(0);
    });

    it('should not update the subtotalAmount when payment fails', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transaction = {
        id: v4(),
        amount: 1000,
        saleAmount: 1000,
        tipAmount: 100,
        status: 'DECLINED',
        type: 'CNP',
        surchargeAmount: 0,
        taxAmounts: [{ amount: 100 } as any],
        timestamp: new Date().toISOString(),
      } as any;
      const ordersFromService = await orderRepository.getOrders([order.id]);
      const orderToUpdate = ordersFromService[0];

      // act
      const { order: res } = await orderRepository.saveOrderPayment({ order: orderToUpdate, transaction });
      // verify
      expect(res).toBeDefined();
      expect(res?.id).toBeDefined();
      expect(res?.entityUuid).toBeDefined();
      expect(res?.status).toBe(OrderStatus.OPEN);
      expect(res?.payments).toBeDefined();
      expect(res?.subtotalAmount).toEqual(119);

      const orderWithRelation = await orderRepository.getOrder(res);
      expect(orderWithRelation!.payments).toHaveLength(1);
      expect(orderWithRelation!.items!.length).toBe(1);
    });
  });

  describe('updateOrderPayment', () => {
    it('should update existing CASH payment tip', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const paymentUuid = v4();
      const txnAmount = order.orderAmount;
      const txnAmountInCentiCents = txnAmount * TRANSACTION_AMOUNT_TO_CENTICENTS_RATE;
      const { order: orderWithCashPayment } = await orderRepository.saveOrderNonCardPayment({
        order,
        nonCardCheckoutInput: {
          id: paymentUuid,
          tenderType: 'CASH' as any,
          amount: txnAmount,
          tipAmount: 0,
          timestampLocal: new Date().toISOString(),
        } as any,
        shouldFinalise: true,
      });
      // act
      await orderRepository.updateOrderPayment({
        orderUuid: orderWithCashPayment!.id,
        entityUuid: orderWithCashPayment!.entityUuid,
        updatedTime: new Date().toISOString(),
        paymentUuid,
        tips: 100,
      });
      const res = await orderRepository.getOrder(orderWithCashPayment!);
      // verify
      expect(res).toBeDefined();
      expect(res?.id).toBeDefined();
      expect(res?.entityUuid).toBeDefined();
      expect(res?.status).toBe(OrderStatus.PAID);
      expect(res?.payments).toBeDefined();
      expect(res?.payments![0].tips).toEqual(100);
      expect(res?.orderAmount).toEqual(order.orderAmount);
      expect(res!.paidAmount!).toEqual(txnAmountInCentiCents + 100);
    });
  });

  describe('multiple payments enabled', () => {
    it('should add new multiple payment to order', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const txnAmount = 50;
      const transaction = {
        id: v4(),
        amount: 52,
        saleAmount: txnAmount,
        tipAmount: 0,
        status: 'APPROVED',
        type: 'CNP',
        surchargeAmount: 2,
        timestamp: new Date().toISOString(),
      } as any;

      const { order: res } = await orderRepository.saveOrderPayment({ order, transaction });

      const orderWithRelation1 = await orderRepository.getOrder(res);
      expect(orderWithRelation1!.status).toEqual(OrderStatus.PART_PAID);
      expect(orderWithRelation1!.paidAmount).toEqual(52);
      expect(orderWithRelation1!.dueAmount).toEqual(order.orderAmount - txnAmount);
      expect(orderWithRelation1!.orderAmount).toEqual(order.orderAmount);
      expect(orderWithRelation1!.totalSurcharge).toEqual(2);
      expect(orderWithRelation1!.payments).toHaveLength(1);

      const secondTransaction = {
        id: v4(),
        amount: 51,
        saleAmount: Math.floor(orderWithRelation1!.dueAmount!),
        tipAmount: 1,
        status: 'APPROVED',
        type: 'CNP',
        surchargeAmount: 0,
        timestamp: new Date().toISOString(),
      } as any;

      const { order: secondRes } = await orderRepository.saveOrderPayment({ order, transaction: secondTransaction });

      expect(secondRes).toBeDefined();
      expect(secondRes.status).toBe(OrderStatus.PAID);

      const orderWithRelation2 = await orderRepository.getOrder(secondRes);
      expect(orderWithRelation2?.status).toEqual(OrderStatus.PAID);
      expect(orderWithRelation2!.payments).toHaveLength(2);
    });

    it('should save cash payment', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transactionId = v4();
      await orderRepository.saveOrderNonCardPayment({
        order,
        nonCardCheckoutInput: {
          id: transactionId,
          entityUuid: '',
          orderUuid: '',
          note: 'note',
          tenderType: TenderType.CASH,
          amount: order.dueAmount!,
          tipAmount: 1,
          taxAmounts: [{ amount: 1 } as any],
          amountTendered: 120,
          change: 20,
          timestampLocal: 'local',
          cashRoundingAdjustment: -1,
        },
        shouldFinalise: true,
      });
      const savedOrder = await orderRepository.getOrder(input);
      expect(savedOrder!.status).toEqual(OrderStatus.PAID);
      expect(savedOrder!.payments![0].id).toEqual(transactionId);
      expect(savedOrder!.payments![0].tenderType).toEqual(TenderType.CASH);
      expect(savedOrder!.payments![0].amount).toEqual(119);
      expect(savedOrder!.payments![0].surchargeAmount).toEqual(0);
      expect(savedOrder!.payments![0].tips).toEqual(1);
      expect(savedOrder!.payments![0].taxAmounts).toEqual([{ amount: 1 } as any]);
      expect(savedOrder!.payments![0].amountTendered).toEqual(120);
      expect(savedOrder!.payments![0].change).toEqual(20);
      expect(savedOrder!.payments![0].timestampLocal).toEqual('local');
      expect(savedOrder!.payments![0].note).toEqual('note');
      expect(savedOrder!.payments![0].cashRoundingAdjustment).toEqual(-1);
      expect(savedOrder?.cashRoundingAdjustment).toEqual(-1);
      expect(savedOrder?.totalSurcharge).toEqual(0);
    });

    it('should not mark the order status as paid when shouldFinalise is false', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transactionId = v4();
      await orderRepository.saveOrderNonCardPayment({
        order,
        nonCardCheckoutInput: {
          id: transactionId,
          entityUuid: '',
          orderUuid: '',
          note: 'note',
          tenderType: TenderType.CASH,
          amount: order.orderAmount,
          tipAmount: 1,
          taxAmounts: [{ amount: 1 } as any],
          amountTendered: 120,
          change: 20,
          timestampLocal: 'local',
        },
        shouldFinalise: false,
      });
      const savedOrder = await orderRepository.getOrder(input);
      expect(savedOrder!.status).toEqual(OrderStatus.PART_PAID);
      expect(savedOrder!.payments![0].id).toEqual(transactionId);
    });

    it('should save other tender type', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transactionId = v4();
      await orderRepository.saveOrderNonCardPayment({
        order,
        nonCardCheckoutInput: {
          id: transactionId,
          entityUuid: '',
          orderUuid: '',
          tenderType: TenderType.OTHER,
          tenderSubType: 'loyalty',
          amount: order.orderAmount,
          tipAmount: 0,
          taxAmounts: [],
          timestampLocal: 'local',
        },
        shouldFinalise: true,
      });
      const savedOrder = await orderRepository.getOrder(input);
      expect(savedOrder!.status).toEqual(OrderStatus.PAID);
      expect(savedOrder!.payments![0].id).toEqual(transactionId);
      expect(savedOrder!.payments![0].tenderType).toEqual(TenderType.OTHER);
      expect(savedOrder!.payments![0].tenderSubType).toEqual('loyalty');
      expect(savedOrder!.payments![0].timestampLocal).toEqual('local');
    });

    it('should add failed payment without success payment', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transaction = {
        id: v4(),
        amount: 1000,
        tipAmount: 100,
        status: 'DECLINED',
        type: 'CNP',
        surchargeAmount: 0,
        taxAmounts: [{ amount: 100 } as any],
        timestamp: new Date().toISOString(),
      } as any;

      // act
      await orderRepository.saveOrderPayment({ order, transaction });
      const orderWithFailedPayment = await orderRepository.getOrder(input);
      expect(orderWithFailedPayment!.payments?.length).toEqual(1);
      expect(orderWithFailedPayment!.payments![0].status).toEqual(TransactionStatus.DECLINED);
      expect(orderWithFailedPayment!.totalGst).toEqual(order.totalGst);
      expect(orderWithFailedPayment!.orderAmount).toEqual(order.orderAmount);
    });

    it('should rollback payments', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transaction = {
        id: v4(),
        amount: 51,
        saleAmount: 50,
        tipAmount: 1,
        status: 'APPROVED',
        type: 'CNP',
        surchargeAmount: 0,
        timestamp: new Date().toISOString(),
      } as any;

      const { order: res } = await orderRepository.saveOrderPayment({ order, transaction });

      const orderWithRelation1 = await orderRepository.getOrder(res);
      expect(orderWithRelation1!.status).toEqual(OrderStatus.PART_PAID);
      const secondTransaction = {
        id: v4(),
        amount: 51,
        saleAmount: 48,
        tipAmount: 0,
        status: 'APPROVED',
        type: 'CNP',
        surchargeAmount: 1,
        taxAmounts: [{ amount: 100 } as any],
        timestamp: new Date().toISOString(),
      } as any;

      const { order: secondRes } = await orderRepository.saveOrderPayment({ order, transaction: secondTransaction });

      expect(secondRes).toBeDefined();
      expect(secondRes.status).toBe(OrderStatus.PART_PAID);

      const orderWithRelation2 = await orderRepository.getOrder(secondRes);
      expect(orderWithRelation2?.status).toEqual(OrderStatus.PART_PAID);
      expect(orderWithRelation2!.payments).toHaveLength(2);
      // cancel 2. payment
      await orderRepository.saveOrderPayment({
        order,
        transaction: {
          ...secondTransaction,
          status: 'CANCELLED',
        },
      });
      const orderWithRelation3 = await orderRepository.getOrder(res);
      expect(orderWithRelation3!.orderAmount).toEqual(order.orderAmount);
      expect(orderWithRelation3!.paidAmount).toEqual(51);
      expect(orderWithRelation3!.totalSurcharge).toEqual(0);
      expect(orderWithRelation3!.totalTips).toEqual(1);
      expect(orderWithRelation3!.subtotalAmount).toEqual(order.subtotalAmount);
      expect(orderWithRelation3!.dueAmount).toEqual(order.orderAmount - 50);
      expect(orderWithRelation3!.status).toEqual(OrderStatus.PART_PAID);
      // cancel 1. payment
      await orderRepository.saveOrderPayment({
        order: orderWithRelation3!,
        transaction: {
          ...transaction,
          status: 'CANCELLED',
        },
      });
      const orderWithRelation4 = await orderRepository.getOrder(res);

      expect(orderWithRelation4!.orderAmount).toEqual(order.orderAmount);
      expect(orderWithRelation4!.subtotalAmount).toEqual(order.subtotalAmount);
      expect(orderWithRelation4!.dueAmount).toEqual(order.orderAmount);
      expect(orderWithRelation4!.paidAmount).toEqual(0);
      expect(orderWithRelation4!.totalSurcharge).toEqual(0);
      expect(orderWithRelation4!.totalTips).toEqual(0);
      expect(orderWithRelation4!.status).toEqual(OrderStatus.OPEN);
      expect(orderWithRelation4!.payments!.length).toEqual(2);
    });

    it('should update partly paid orders', async () => {
      const input: CreateOrderInput = getOrderInput();
      input.items[0].price = '20000';
      const transaction = {
        id: v4(),
        amount: 52,
        saleAmount: 50,
        tipAmount: 1,
        status: 'APPROVED',
        type: 'CNP',
        surchargeAmount: 1,
        timestamp: new Date().toISOString(),
      };
      const createdOrder = await orderRepository.createOrder(input);
      // verify
      expect(createdOrder).toBeDefined();
      expect(createdOrder?.items).toHaveLength(1);

      const { order: res } = await orderRepository.saveOrderPayment({
        order: createdOrder,
        transaction: transaction as any,
      });
      const updateInput = JSON.parse(JSON.stringify({ ...createdOrder }));

      updateInput.items[0].name = 'updated test item';
      updateInput.items[0].price = '20000';
      await orderRepository.saveOrder(
        { ...updateInput, updatedTime: new Date(new Date().getTime() + 1000).toISOString() } as any,
        res,
      );
      const updatedOrder = await orderRepository.getOrder(createdOrder);
      expect(updatedOrder!.paidAmount).toEqual(convertTransactionCentsToCentiCents(transaction.amount));
      expect(updatedOrder!.totalSurcharge).toEqual(convertTransactionCentsToCentiCents(transaction.surchargeAmount));
      expect(updatedOrder!.totalTips).toEqual(convertTransactionCentsToCentiCents(transaction.tipAmount));
      expect(updatedOrder!.dueAmount).toEqual(
        createdOrder.orderAmount - convertTransactionCentsToCentiCents(transaction.saleAmount),
      );
      expect(updatedOrder!.orderAmount).toEqual(createdOrder.orderAmount);
      expect(updatedOrder!.subtotalAmount).toEqual(createdOrder.subtotalAmount);
    });
  });

  describe('saveOrderPayments', () => {
    it('should save the order payments', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      const transaction = {
        id: v4(),
        amount: 100,
        tipAmount: 1,
        status: 'APPROVED',
        type: 'CNP',
        surchargeAmount: 0,
        taxAmounts: [{ amount: 100 } as any],
        timestamp: new Date().toISOString(),
      } as any;

      // act
      await orderRepository.saveOrderPayment({ order, transaction });
      const savedOrder = await orderRepository.getOrder(input);

      const orderWithUpdatedPayments = JSON.parse(JSON.stringify(savedOrder));
      orderWithUpdatedPayments.payments[0].taxAmounts = [{ amount: 200 } as any];
      await orderRepository.saveOrderPayments(orderWithUpdatedPayments);
      const updatedOrder = await orderRepository.getOrder(input);
      expect(updatedOrder!.payments![0].taxAmounts).toEqual([{ amount: 200 } as any]);
      expect(savedOrder!.payments![0]).toEqual({
        ...updatedOrder!.payments![0],
        taxAmounts: [{ amount: 100 } as any],
      });
    });
  });

  describe('cancelOrder', () => {
    it('should cancel order', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);

      // act
      const result = await orderRepository.cancelOrder({ id: order.id, entityUuid: order.entityUuid });
      // verify
      expect(result).toBeDefined();
      expect(result?.id).toBeDefined();
      expect(result?.entityUuid).toBeDefined();
      expect(result?.status).toBe(OrderStatus.CANCELLED);
    });

    it('should throw error when order is in PAID status', async () => {
      const input: CreateOrderInput = getOrderInput();
      const order = await orderRepository.createOrder(input);
      order.status = OrderStatus.PAID;
      await orderRepository.getRepository().save({ ...order, updatedTimeInMilliseconds: new Date().getTime() + 1 });
      // act
      await expect(orderRepository.cancelOrder({ id: order.id, entityUuid: order.entityUuid })).rejects.toThrow(
        'Order cannot be cancelled',
      );
    });

    it('should throw error when order not found', async () => {
      await expect(orderRepository.cancelOrder({ id: v4(), entityUuid: v4() })).rejects.toThrow('Order not found');
    });
  });

  describe('migration', () => {
    describe('migrationUpdateOrder', () => {
      it('should update orderItems only', async () => {
        const input: CreateOrderInput = getOrderInput();
        input.items[0] = {
          ...input.items[0],
          discounts: [
            {
              id: v4(),
              config: CatalogDiscountConfig.AMOUNT,
              value: '100',
              name: 'item level 1',
              ordinal: 1,
            },
            {
              id: v4(),
              config: CatalogDiscountConfig.PERCENTAGE,
              value: '10',
              name: 'item level 2',
              ordinal: 2,
            },
          ],
          serviceCharges: [
            {
              id: v4(),
              config: CatalogServiceChargeConfig.AMOUNT,
              value: '100',
              name: 'item level 1',
              ordinal: 1,
            },
            {
              id: v4(),
              config: CatalogServiceChargeConfig.PERCENTAGE,
              value: '10',
              name: 'item level 2',
              ordinal: 2,
            },
          ],
        };

        const order = await orderRepository.saveOrder(input, null);
        const transaction = {
          id: v4(),
          amount: 1000,
          tipAmount: 100,
          status: 'APPROVED',
          type: 'CNP',
          surchargeAmount: 0,
          taxAmounts: [{ amount: 100 } as any],
          timestamp: new Date().toISOString(),
        } as any;

        await orderRepository.saveOrderPayment({ order, transaction });
        const result = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
        const item = result!.items![0];
        expect(item.subtotalAmount).toBe(11000);
        await dataSource.getRepository(OrderItem).update(
          {
            id: item.id,
          },
          {
            subtotalAmount: 12000,
          },
        );
        await dataSource.getRepository(OrderItem).update(
          {
            id: item.modifiers![0].id,
          },
          {
            subtotalAmount: 12000,
          },
        );
        const wrongResult = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
        expect(wrongResult!.items![0].subtotalAmount).toBe(12000);
        expect(wrongResult!.items![0].modifiers![0].subtotalAmount).toBe(12000);

        // act
        // should fix the subtotalAmount and everything else should be unchanged
        wrongResult!.items![0].subtotalAmount = 11000;
        wrongResult!.items![0].modifiers![0].subtotalAmount = 1000;
        await orderRepository.migrationUpdateOrder(wrongResult!);
        const fixedResult = await orderRepository.getOrder({ id: input.id, entityUuid: input.entityUuid });
        expect(fixedResult!.items![0].subtotalAmount).toEqual(11000);
        expect(fixedResult!.items![0].modifiers![0].subtotalAmount).toEqual(1000);
      });
    });

    describe('migrationFixOrderTotal', () => {
      it('should fix the total amount for a OPEN order', async () => {
        const input: CreateOrderInput = getOrderInput();
        const order = await orderRepository.saveOrder(input, null);
        const result = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
        expect(result!.orderAmount).toEqual(119);
        expect(result!.updatedTime).toBeDefined();
        await orderRepository.getRepository().update(
          {
            id: result!.id,
          },
          {
            orderAmount: 0,
            subtotalAmount: 0,
            updatedTimeInMilliseconds: new Date().getTime() + 1,
          },
        );
        const wrongOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
        expect(wrongOrder!.orderAmount).toEqual(0);
        await sleep(1000);
        // fix order
        await orderRepository.migrationFixOrderTotal(wrongOrder!);
        const fixedOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
        expect(fixedOrder!.orderAmount).toEqual(119);
        expect(fixedOrder!.updatedTime! > result!.updatedTime!).toEqual(true);
      });

      xit('should fix the total amount for a PAID order', async () => {
        const input: CreateOrderInput = getOrderInput();
        const order = await orderRepository.createOrder(input);
        const transaction = {
          id: v4(),
          amount: 1000,
          tipAmount: 100,
          status: 'APPROVED',
          type: 'CNP',
          surchargeAmount: 100,
          taxAmounts: [{ amount: 100 } as any],
          timestamp: new Date().toISOString(),
        } as any;

        // act
        await orderRepository.saveOrderPayment({ order, transaction });
        const result = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
        expect(result!.orderAmount).toEqual(order.orderAmount);
        expect(result!.totalGst).toEqual(100);
        expect(result!.totalSurcharge).toEqual(100);
        expect(result!.subtotalAmount).toEqual(108);
        expect(result!.totalTips).toEqual(100);
        expect(result!.paidAmount).toEqual(1200);
        await orderRepository.getRepository().update(
          {
            id: result!.id,
          },
          {
            orderAmount: 0,
            subtotalAmount: 0,
            totalTips: 0,
            totalGst: 0,
            totalSurcharge: 0,
          },
        );
        const wrongOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
        expect(wrongOrder!.orderAmount).toEqual(0);

        // fix order
        await orderRepository.migrationFixOrderTotal(wrongOrder!, transaction);
        const fixedOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
        expect(fixedOrder!.orderAmount).toEqual(1000);
        expect(fixedOrder!.totalGst).toEqual(100);
        expect(fixedOrder!.totalSurcharge).toEqual(100);
        expect(fixedOrder!.subtotalAmount).toEqual(108);
        expect(fixedOrder!.totalTips).toEqual(100);
        expect(fixedOrder!.paidAmount).toEqual(1000);
      });
    });
  });

  describe('updateOrderStatus', () => {
    const mockOrder = {
      ...getOrderInput(),
      catalogSettings: { itemsTaxInclusive: true, itemsApplyTax: true, autoSkuEnabled: true },
      status: OrderStatus.OPEN,
      items: [
        {
          id: v4(),
          name: 'test item',
          price: '1000',
          ordinal: 1,
          type: OrderItemType.SINGLE,
          unit: CatalogUnit.QUANTITY,
          quantity: 1,

          taxes: [],
          discounts: [
            { id: v4(), config: CatalogDiscountConfig.AMOUNT, value: '1000', name: 'item level', ordinal: 1 },
          ],
        },
      ],
      discounts: [],
      serviceCharges: [],
      orderAmount: 0,
    };

    it('should mark zero dollar sale as paid with order', async () => {
      const input: CreateOrderInput = mockOrder;
      await orderRepository.createOrder(input);
      await orderRepository.updateOrderStatus({
        orderUuid: input.id,
        entityUuid: input.entityUuid,
        status: OrderStatus.PAID,
      });
      const order = await orderRepository.getOrder(input);
      expect(order).toBeDefined();
      expect(order!.status).toBe(OrderStatus.PAID);
    });

    it('should throw error if order not found', async () => {
      const input: CreateOrderInput = getOrderInput();
      await expect(
        orderRepository.updateOrderStatus({
          orderUuid: input.id,
          entityUuid: input.entityUuid,
          status: OrderStatus.PAID,
        }),
      ).rejects.toThrow('Order not found');
    });
    it('should throw error if Order status not supported', async () => {
      const input: CreateOrderInput = getOrderInput();
      await orderRepository.createOrder(input);
      await expect(
        orderRepository.updateOrderStatus({
          orderUuid: input.id,
          entityUuid: input.entityUuid,
          status: OrderStatus.OPEN,
        }),
      ).rejects.toThrow('Order status not supported');
    });
  });
});
