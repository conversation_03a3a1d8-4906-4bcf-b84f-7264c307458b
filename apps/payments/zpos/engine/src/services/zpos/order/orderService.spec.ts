import { OrderStatus } from '@npco/component-dto-order/dist/types';

import { mock } from 'jest-mock-extended';

import type { CommandService } from '../../../commands';
import {
  OrderCancelledCommand,
  OrderCreatedCommand,
  OrderPartPaidCommand,
  OrderPaymentFailedCommand,
  OrderUpdatedCommand,
} from '../../../commands';
import type { CreateOrderInput } from '../../../domain/types';

import type { OrderRepository } from './orderRepository';
import { OrderService } from './orderService';

describe('OrderService tests', () => {
  const mockOrderRepository = mock<OrderRepository>();
  const mockCommandService = mock<CommandService>();
  const orderService = new OrderService(mockOrderRepository, mockCommandService);

  afterEach(() => jest.resetAllMocks());
  describe('orderPaymentMigration', () => {
    it('should call sendCommand', async () => {
      await orderService.orderPaymentMigration({ payments: [] } as any);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
    });
  });
  describe('createOrder', () => {
    it('should handle create order request', async () => {
      mockOrderRepository.getOrder.mockResolvedValue(null);
      const mockOrder = { id: '1' };
      mockOrderRepository.saveOrder.mockResolvedValue(mockOrder as any);
      // act
      const response = await orderService.createOrder({} as CreateOrderInput);
      // verify
      expect(response.id).toBe(mockOrder.id);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(new OrderCreatedCommand(mockOrder as any));
    });
  });

  describe('cancelOrder', () => {
    it('should handle cancel order request', async () => {
      const mockOrder = { id: '1' };
      mockOrderRepository.cancelOrder.mockResolvedValue(mockOrder as any);
      // act
      const response = await orderService.cancelOrder({} as any);
      // verify
      expect(response.id).toBe(mockOrder.id);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(new OrderCancelledCommand(mockOrder as any));
    });
  });

  describe('updateOrder', () => {
    it('should handle update order request', async () => {
      const mockOrder = { id: '1' };
      mockOrderRepository.getOrder.mockResolvedValue(mockOrder as any);
      mockOrderRepository.saveOrder.mockResolvedValue(mockOrder as any);
      // act
      const response = await orderService.saveOrder({} as CreateOrderInput);
      // verify
      expect(response.id).toBe(mockOrder.id);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(new OrderUpdatedCommand(mockOrder as any));
    });
    it('should reject update order request when order is paid', async () => {
      const mockOrder = { id: '1' };
      mockOrderRepository.getOrder.mockResolvedValue({ ...mockOrder, status: 'PAID' } as any);
      // act
      await expect(orderService.saveOrder({} as CreateOrderInput)).rejects.toThrow('[400] Cannot update a paid order');
      // verify
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(0);
    });
  });

  describe('processPaymentMessages', () => {
    it('should send Order.PartPaid command when txn.status is APPROVED ', async () => {
      const mockOrder = { id: '1' };
      const paymentMessage = {
        body: JSON.stringify({ externalReference: '1', status: 'APPROVED' }),
      };
      mockOrderRepository.getOrders.mockResolvedValue([mockOrder] as any);
      mockOrderRepository.saveOrderPayment.mockResolvedValue({
        order: mockOrder,
        newPayment: { status: 'APPROVED' },
      } as any);
      mockOrderRepository.getOrderOrThrowNotFound.mockResolvedValue(mockOrder as any);
      // act
      await orderService.processPaymentMessages([paymentMessage as any]);
      // verify
      expect(mockOrderRepository.saveOrderPayment).toHaveBeenCalledTimes(1);
      expect(mockOrderRepository.saveOrderPayment).toHaveBeenCalledWith({
        order: mockOrder,
        transaction: { externalReference: '1', status: 'APPROVED' },
      });
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(
        new OrderPartPaidCommand(mockOrder as any, { status: 'APPROVED' } as any),
      );
    });

    it('should send Order.PaymentFailed command when txn.status is DECLINED', async () => {
      const mockOrder = { id: '1' };
      const paymentMessage = {
        body: JSON.stringify({ externalReference: '1', status: 'DECLINED' }),
      };
      mockOrderRepository.getOrders.mockResolvedValue([mockOrder] as any);
      mockOrderRepository.saveOrderPayment.mockResolvedValue({
        order: mockOrder,
        newPayment: { status: 'DECLINED' },
      } as any);
      mockOrderRepository.getOrderOrThrowNotFound.mockResolvedValue(mockOrder as any);
      // act
      await orderService.processPaymentMessages([paymentMessage as any]);
      // verify
      expect(mockOrderRepository.saveOrderPayment).toHaveBeenCalledTimes(1);
      expect(mockOrderRepository.saveOrderPayment).toHaveBeenCalledWith({
        order: mockOrder,
        transaction: { externalReference: '1', status: 'DECLINED' },
      });
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(
        new OrderPaymentFailedCommand(mockOrder as any, { status: 'DECLINED' } as any),
      );
    }, 999999);

    it('should handle processPaymentMessages when order not found', async () => {
      const paymentMessage = {
        body: JSON.stringify({ externalReference: '1' }),
      };
      mockOrderRepository.getOrders.mockResolvedValue([] as any);
      // act & verify
      await orderService.processPaymentMessages([paymentMessage as any]);
      // verify
      expect(mockOrderRepository.saveOrderPayment).toHaveBeenCalledTimes(0);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(0);
    });

    it('should handle processPaymentMessages when error is thrown', async () => {
      const mockOrder = { id: '1' };
      const paymentMessage = {
        body: JSON.stringify({ externalReference: '1', status: 'APPROVED' }),
      };
      mockOrderRepository.getOrders.mockResolvedValue([mockOrder] as any);
      mockOrderRepository.saveOrderPayment.mockRejectedValue(new Error('error'));
      // act & verify
      await orderService.processPaymentMessages([paymentMessage as any]);
      // verify
      expect(mockOrderRepository.saveOrderPayment).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(0);
    });
  });

  describe('updateOrderStatus', () => {
    it('should handle updateOrderStatus request when new order.status = OPEN', async () => {
      const mockOrder = { id: '1' };
      mockOrderRepository.updateOrderStatus.mockResolvedValue(mockOrder as any);
      // act
      const response = await orderService.updateOrderStatus({
        orderUuid: 'order-12',
        entityUuid: 'entity-12',
        status: OrderStatus.OPEN,
      });
      // verify
      expect(response.id).toBe(mockOrder.id);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(new OrderUpdatedCommand(mockOrder as any));
    });

    it('should handle updateOrderStatus request when new order.status = PAID', async () => {
      const mockOrder = { id: '1' };
      mockOrderRepository.updateOrderStatus.mockResolvedValue(mockOrder as any);
      // act
      const response = await orderService.updateOrderStatus({
        orderUuid: 'order-12',
        entityUuid: 'entity-12',
        status: OrderStatus.OPEN,
      });
      // verify
      expect(response.id).toBe(mockOrder.id);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(new OrderUpdatedCommand(mockOrder as any));
    });

    it('should handle updateOrderStatus request when new order.status = PAID', async () => {
      const mockOrder = { id: '1', updatedTime: 1, updatedTimeInMilliseconds: 100, status: 'PAID' };
      mockOrderRepository.updateOrderStatus.mockResolvedValue(mockOrder as any);
      // act
      const response = await orderService.updateOrderStatus({
        orderUuid: 'order-12',
        entityUuid: 'entity-12',
        status: OrderStatus.PAID,
      });
      // verify
      expect(response.id).toBe(mockOrder.id);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(new OrderPartPaidCommand(mockOrder as any));
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(
        new OrderPartPaidCommand(mockOrder as any, undefined),
      );
    });
  });

  describe('updateOrderPayment', () => {
    it('should handle updateOrderStatus request', async () => {
      const mockOrder = { id: '1', payments: [{ id: 'payment-12' }] };
      mockOrderRepository.updateOrderPayment.mockResolvedValue(mockOrder as any);
      // act
      const response = await orderService.updateOrderPayment({
        orderUuid: 'order-12',
        entityUuid: 'entity-12',
        paymentUuid: 'payment-12',
        updatedTime: '2021-09-01T00:00:00Z',
      });
      // verify
      expect(response.id).toBe(mockOrder.id);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
    });
  });

  describe('createOrderPayment', () => {
    it('should create a new order payment and send OrderPartPaidCommand', async () => {
      const mockOrder = { id: '1', entityUuid: 'entity-12', paidTime: 1234567890 };
      const mockPayment = { id: 'payment-1', status: 'APPROVED' };
      mockOrderRepository.getOrderOrThrowNotFound.mockResolvedValue(mockOrder as any);
      mockOrderRepository.saveOrderNonCardPayment.mockResolvedValue({
        order: mockOrder,
        newPayment: mockPayment,
      } as any);
      mockOrderRepository.getOrderOrThrowNotFound.mockResolvedValue(mockOrder as any);

      const response = await orderService.createOrderPayment(
        { orderUuid: 'order-12', entityUuid: 'entity-12' } as any,
        true,
      );

      expect(response.id).toBe(mockOrder.id);
      expect(response.paidTime).toBe(mockOrder.paidTime);
      expect(mockCommandService.sendCommand).toHaveBeenCalledTimes(1);
      expect(mockCommandService.sendCommand).toHaveBeenCalledWith(
        new OrderPartPaidCommand(mockOrder as any, mockPayment as any),
      );
    });
  });
});
