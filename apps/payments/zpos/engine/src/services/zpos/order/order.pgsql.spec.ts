import { CatalogDiscountConfig, CatalogUnit } from '@npco/component-dto-catalog/dist';
import { OrderItemType } from '@npco/component-dto-order/dist';

import type { INestApplicationContext } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { v4 } from 'uuid';

import { mockEnvServiceForLocalDbConnection } from '../../../config/__mocks__/localDbConnection';
import { OrderDiscountModel } from '../../../domain/models/orderDiscountModel';
import { OrderItemModel } from '../../../domain/models/orderItemModel';
import { OrderModel } from '../../../domain/models/orderModel';
import { OrderServiceChargeModel } from '../../../domain/models/orderServiceChargeModel';
import type { CreateOrderInput } from '../../../domain/types';
import { initiateOrderMutationNestModule } from '../../../lambdas/testcases/mockNestModule';

import { OrderRepository } from './orderRepository';

jest.mock('../../../config/envService', () => mockEnvServiceForLocalDbConnection());

jest.mock('aws-xray-sdk', () => {
  return {
    getSegment: () => ({
      addNewSubsegment: jest.fn().mockReturnValue({}),
      addAnnotation: jest.fn(),
    }),
    captureAsyncFunc: async (name: string, cb: any) => {
      await cb({ addAnnotation: jest.fn(), close: jest.fn() });
    },
  };
});

describe('Order Repository test suite', () => {
  let appContext: INestApplicationContext;
  let dataSource: DataSource;
  let orderRepository: OrderRepository;
  let orderModel: OrderModel;

  beforeAll(async () => {
    appContext = await initiateOrderMutationNestModule();
    dataSource = appContext.get(DataSource);
    const discountModel = new OrderDiscountModel();
    const serviceChargeModel = new OrderServiceChargeModel();
    const orderItemModel = new OrderItemModel(discountModel, serviceChargeModel);
    orderModel = new OrderModel(orderItemModel, discountModel, serviceChargeModel);
    orderRepository = new OrderRepository(dataSource, orderModel);
  });

  afterAll(async () => {
    await dataSource.destroy();
  });

  const getOrderInput = (): CreateOrderInput => {
    return {
      id: v4(),
      entityUuid: v4(),
      siteUuid: v4(),
      createdFromDeviceUuid: v4(),
      catalogSettings: { itemsTaxInclusive: true, itemsApplyTax: true, autoSkuEnabled: true },
      updatedTime: new Date().toISOString(),
      discounts: [
        {
          id: v4(),
          config: CatalogDiscountConfig.AMOUNT,
          value: '1000',
          name: 'order level',
          ordinal: 1,
        },
      ],
      items: [
        {
          id: v4(),
          name: 'test item',
          price: '10000',
          ordinal: 1,
          type: OrderItemType.SINGLE,
          unit: CatalogUnit.HOUR,
          discounts: [
            {
              id: v4(),
              config: CatalogDiscountConfig.PERCENTAGE,
              value: '10',
              name: 'item level',
              ordinal: 1,
            },
          ],
          quantity: 1,
          modifiers: [
            {
              id: v4(),
              name: 'test modifier',
              price: '1000',
              ordinal: 1,
              unit: CatalogUnit.QUANTITY,
              quantity: 1,
            },
          ],
          taxes: [{ name: 'GST', enabled: true, percent: 11 }],
        },
      ],
    };
  };

  describe('validate_order_updated_timestamp_milliseconds', () => {
    it('should update existing order', async () => {
      const input: CreateOrderInput = getOrderInput();

      const createdOrder = await orderRepository.createOrder(input);
      // act
      await expect(
        orderRepository.getRepository().update(
          {
            id: createdOrder!.id,
          },
          {
            orderAmount: 0,
            subtotalAmount: 0,
          },
        ),
      ).rejects.toThrowError('INVALID_UPDATED_TIMESTAMP');
    });
  });
});
