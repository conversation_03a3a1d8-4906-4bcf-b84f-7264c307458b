import { OrderStatus } from '@npco/component-dto-order/dist';
import { TransactionStatus } from '@npco/component-dto-transaction/dist/types';

import type { INestApplicationContext } from '@nestjs/common';
import type { CommandService } from 'commands/commandService';
import { mock } from 'jest-mock-extended';
import { DataSource } from 'typeorm';
import { v4 } from 'uuid';

import { mockEnvServiceForLocalDbConnection } from '../../../config/__mocks__/localDbConnection';
import { OrderDiscountModel } from '../../../domain/models/orderDiscountModel';
import { OrderItemModel } from '../../../domain/models/orderItemModel';
import { OrderModel } from '../../../domain/models/orderModel';
import { OrderServiceChargeModel } from '../../../domain/models/orderServiceChargeModel';
import { initiateOrderMutationNestModule } from '../../../lambdas/testcases/mockNestModule';
import { TenderType } from '../../types';
import { getOrderInput, getTransactionInput } from '../tests/utils';

import { OrderRepository } from './orderRepository';
import { OrderService } from './orderService';

jest.mock('../../../config/envService', () => mockEnvServiceForLocalDbConnection());

jest.mock('aws-xray-sdk', () => {
  return {
    getSegment: () => ({
      addNewSubsegment: jest.fn().mockReturnValue({}),
      addAnnotation: jest.fn(),
    }),
    captureAsyncFunc: async (name: string, cb: any) => {
      await cb({ addAnnotation: jest.fn(), close: jest.fn() });
    },
  };
});

describe('Order Service with Repository tests', () => {
  const mockCommandService = mock<CommandService>();
  let appContext: INestApplicationContext;
  let dataSource: DataSource;
  let orderRepository: OrderRepository;
  let orderModel: OrderModel;
  let orderService: OrderService;

  beforeAll(async () => {
    appContext = await initiateOrderMutationNestModule();
    dataSource = appContext.get(DataSource);
    const discountModel = new OrderDiscountModel();
    const orderServiceChargeModel = new OrderServiceChargeModel();
    const orderItemModel = new OrderItemModel(discountModel, orderServiceChargeModel);
    orderModel = new OrderModel(orderItemModel, discountModel, orderServiceChargeModel);
    orderRepository = new OrderRepository(dataSource, orderModel);
    orderService = new OrderService(orderRepository, mockCommandService);
  });

  it('should not save payment for same transactionUuid + status multiple times', async () => {
    const orderInput = getOrderInput();
    const order = await orderRepository.createOrder(orderInput);
    expect(order).toBeDefined();
    expect(order.status).toBe(OrderStatus.OPEN);
    // declined transaction received for order
    const declinedTransaction = { ...getTransactionInput(order.id, order.orderAmount), status: 'DECLINED' };
    await orderService.processPaymentMessages([
      {
        body: JSON.stringify(declinedTransaction),
        receiptHandle: 'receiptHandle',
      },
    ]);
    const orderWithPayment1 = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
    expect(orderWithPayment1!.payments).toHaveLength(1);
    expect(orderWithPayment1!.payments![0].status).toBe(TransactionStatus.DECLINED);
    expect(orderWithPayment1!.payments![0].transactionUuid).toBe(declinedTransaction.id);

    // declined transaction with reversal info received for order
    await orderService.processPaymentMessages([
      {
        body: JSON.stringify(declinedTransaction),
        receiptHandle: 'receiptHandle',
      },
    ]);
    const orderWithPayment2 = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
    expect(orderWithPayment2!.payments).toHaveLength(1);
    expect(orderWithPayment2!.payments![0].status).toBe(TransactionStatus.DECLINED);
    expect(orderWithPayment2!.payments![0].transactionUuid).toBe(declinedTransaction.id);
  });

  it('should process APPROVED -> CANCELLED -> APPROVED transaction flow', async () => {
    const orderInput = getOrderInput();
    const order = await orderRepository.createOrder(orderInput);
    expect(order).toBeDefined();
    expect(order.status).toBe(OrderStatus.OPEN);
    // approved transaction received for order
    const approvedTransaction1 = getTransactionInput(order.id, order.orderAmount);
    await orderService.processPaymentMessages([
      {
        body: JSON.stringify(approvedTransaction1),
        receiptHandle: 'receiptHandle',
      },
    ]);
    const paidOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
    expect(paidOrder).toBeDefined();
    expect(paidOrder!.status).toBe(OrderStatus.PAID);
    expect(paidOrder!.payments).toHaveLength(1);
    expect(paidOrder!.payments![0].status).toBe(TransactionStatus.APPROVED);
    expect(paidOrder!.payments![0].transactionUuid).toBe(approvedTransaction1.id);
    expect(paidOrder!.dueAmount).toBe(0);
    expect(paidOrder!.orderDisplayAmount).toBe(paidOrder!.paidAmount);
    // cancelled transaction received for order
    await orderService.processPaymentMessages([
      {
        body: JSON.stringify({ ...approvedTransaction1, status: 'DECLINED' }),
        receiptHandle: 'receiptHandle',
      },
    ]);
    const unpaidOrder = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
    expect(unpaidOrder).toBeDefined();
    expect(unpaidOrder!.status).toBe(OrderStatus.OPEN);
    expect(unpaidOrder!.payments).toHaveLength(1);
    expect(unpaidOrder!.payments![0].status).toBe(TransactionStatus.DECLINED);
    expect(unpaidOrder!.payments![0].transactionUuid).toBe(approvedTransaction1.id);
    expect(unpaidOrder!.dueAmount).toBe(order.orderAmount);
    expect(unpaidOrder!.orderDisplayAmount).toBe(order.orderAmount);

    // second approved transaction received for order
    const approvedTransaction2 = getTransactionInput(order.id, order.orderAmount);
    await orderService.processPaymentMessages([
      {
        body: JSON.stringify(approvedTransaction2),
        receiptHandle: 'receiptHandle',
      },
    ]);
    const paidOrder2 = await orderRepository.getOrder({ id: order.id, entityUuid: order.entityUuid });
    expect(paidOrder2).toBeDefined();
    expect(paidOrder2!.status).toBe(OrderStatus.PAID);
    expect(paidOrder2!.payments).toHaveLength(2);
    expect(paidOrder2!.payments!.find((p) => p.transactionUuid === approvedTransaction2.id)!.status).toBe(
      TransactionStatus.APPROVED,
    );
    expect(paidOrder2!.payments!.find((p) => p.transactionUuid === approvedTransaction1.id)!.status).toBe(
      TransactionStatus.DECLINED,
    );
    expect(paidOrder2!.dueAmount).toBe(0);
  });

  it('should save CASH payment', async () => {
    const orderInput = getOrderInput();
    const order = await orderRepository.createOrder(orderInput);
    const nonCardSaleInput = {
      id: v4(),
      orderUuid: order.id,
      entityUuid: order.entityUuid,
      tenderType: TenderType.CASH,
      amount: order.dueAmount! + 10,
      amountTendered: 1050,
      change: 10,
      tipAmount: 10,
      taxAmounts: [{ amount: 10 } as any],
      timestampLocal: new Date().toISOString(),
    };
    const { order: orderWithCashPayment } = await orderRepository.saveOrderNonCardPayment({
      order,
      nonCardCheckoutInput: nonCardSaleInput as any,
      shouldFinalise: true,
    });
    expect(orderWithCashPayment.orderDisplayAmount).toBe(109);
    expect(orderWithCashPayment.orderAmount).toBe(order.orderAmount);
    expect(orderWithCashPayment.paidAmount).toBe(109);
    expect(orderWithCashPayment.totalTips).toBe(10);
    expect(orderWithCashPayment.totalChange).toBe(10);
    expect(orderWithCashPayment.totalAmountTendered).toBe(1050);
  });
});
