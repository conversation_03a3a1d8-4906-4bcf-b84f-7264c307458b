build:
	go mod tidy && git status --porcelain
	GOARCH=amd64 GOOS=linux go build -tags lambda.norpc -o bootstrap

build-mac:
	cd engine go build -o dist/engine-mac

run:
	go run main.go
lint:
	golangci-lint run ./...

vulnerability:
	go install golang.org/x/vuln/cmd/govulncheck@latest
	govulncheck ./...

test: launch-db run-test shutdown-db

run-test:
	mkdir -p dist
	go install gotest.tools/gotestsum@v1.12.1
	go install github.com/becheran/go-testreport@v0.3.2
	gotestsum --format testname --junitfile dist/report.xml --jsonfile dist/report.json -- -v ./... -coverpkg=./... -coverprofile=dist/lcov.info -cover 2>&1
	go tool cover -html dist/lcov.info -o dist/coverage.html
	go-testreport -input dist/report.json -output dist/output.html
	yarn dlx xunit-viewer -r dist/report.xml -o dist/report.html

run-test-client:
	mkdir -p dist
	go install gotest.tools/gotestsum@v1.12.1
	go install github.com/becheran/go-testreport@v0.3.2
	gotestsum --format testname --junitfile dist/report.xml --jsonfile dist/report.json -- -v ./internal/tevalis_client/... -coverpkg=./internal/tevalis_client/... -coverprofile=dist/lcov.info -cover 2>&1
	go tool cover -html dist/lcov.info -o dist/coverage.html
	go-testreport -input dist/report.json -output dist/output.html
	yarn dlx xunit-viewer -r dist/report.xml -o dist/report.html

launch-db:
	docker compose up -d
	sleep 5

shutdown-db:
	docker compose down
