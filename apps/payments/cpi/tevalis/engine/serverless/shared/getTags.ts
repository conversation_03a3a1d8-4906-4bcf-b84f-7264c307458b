/* eslint no-template-curly-in-string: 0 */
import type { EnvService } from '../envService';

export function getTags(env: EnvService) {
  return {
    COMPONENT_NAME: `${env.COMPONENT_NAME}`,
    PART_NAME: `${env.PART_NAME}`,
    STAGE: '${opt:stage}',
    service: `${env.COMPONENT_NAME}-${env.PART_NAME}`,
    env: '${opt:stage}',
  };
}

export function getTagsArray(env: EnvService) {
  return [
      { Key: "COMPONENT_NAME", Value: `${env.COMPONENT_NAME}`},
      { Key: "PART_NAME", Value: `${env.PART_NAME}`},
      { Key: "STAGE", Value: '${opt:stage}'},
      { Key: "service", Value: `${env.COMPONENT_NAME}-${env.PART_NAME}`},
      { Key: "env", Value: '${opt:stage}'},
  ];
}
