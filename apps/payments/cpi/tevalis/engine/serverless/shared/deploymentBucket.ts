import type { DeploymentBucket } from 'serverless/plugins/aws/provider/awsProvider';

import type { EnvService } from '../envService';

export async function getDeploymentBucket(env: EnvService): Promise<DeploymentBucket> {
  const db = `\${cf:${env.COMPONENT_NAME}-${env.PART_NAME}-iac-s3.deploymentBucket}`;
  return {
    name: db,
    maxPreviousDeploymentArtifacts: 100,
    blockPublicAccess: true,
  };
}
