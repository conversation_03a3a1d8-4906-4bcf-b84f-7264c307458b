export const getManagedPolicies = (stackName: string) =>
  ({
    logPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        ManagedPolicyName: `${stackName}-logPolicy`,
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['logs:CreateLogStream', 'logs:PutLogEvents', 'logs:CreateLogGroup'],
              Resource: 'arn:aws:logs:${opt:region}:${aws:accountId}:log-group:/aws/lambda/' + `${stackName}-*:*`,
            },
          ],
        },
      },
    },
    xrayPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        ManagedPolicyName: '${self:provider.stackName}-xrayPolicy',
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: [
                'xray:PutTraceSegments',
                'xray:PutTelemetryRecords',
                'xray:GetSamplingRules',
                'xray:GetSamplingTargets',
                'xray:GetSamplingStatisticSummaries',
              ],
              Resource: '*',
            },
          ],
        },
      },
    },
    lambdaVpcPolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        ManagedPolicyName: '${self:provider.stackName}-lambdaVpcPolicy',
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['ec2:CreateNetworkInterface', 'ec2:DescribeNetworkInterfaces', 'ec2:DeleteNetworkInterface'],
              Resource: '*',
            },
          ],
        },
      },
    },
    cpiTablePolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        ManagedPolicyName: '${self:provider.stackName}-cpiTablePolicy',
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: ['dynamodb:Query'],
              Resource:
                'arn:aws:dynamodb:${opt:region}:${aws:accountId}:table/${env:STATIC_ENV_NAME}-cpi-engine-dynamodb-Entities*',
            },
          ],
        },
      },
    },
  } as const);
