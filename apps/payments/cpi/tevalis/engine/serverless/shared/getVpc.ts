import Aws = require('serverless/aws');

import type { EnvService } from '../envService';

export async function getVpc(env: EnvService): Promise<Aws.Vpc> {
  return {
    securityGroupIds: [`\${ssm:${env.STATIC_ENV_NAME}-vpc-shared-01-lambda-sg}`],
    subnetIds: [
      `\${ssm:${env.STATIC_ENV_NAME}-vpc-shared-01-lambda-subnet01}`,
      `\${ssm:${env.STATIC_ENV_NAME}-vpc-shared-01-lambda-subnet02}`,
      `\${ssm:${env.STATIC_ENV_NAME}-vpc-shared-01-lambda-subnet03}`,
    ],
  };
}
