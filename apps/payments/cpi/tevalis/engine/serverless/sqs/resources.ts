export const getSqsResources = (stackName: string, tagsArray: Record<string, string>[]) => {
  return {
    pollQueue: {
      Type: 'AWS::SQS::Queue',
      Properties: {
        QueueName: `${stackName}-queue`,
        VisibilityTimeout: 60,
        MessageRetentionPeriod: 60,
        ReceiveMessageWaitTimeSeconds: 0,
      },
    },
    pollQueuePolicy: {
      Type: 'AWS::IAM::ManagedPolicy',
      Properties: {
        ManagedPolicyName: `${stackName}-pollQueuePolicy`,
        PolicyDocument: {
          Version: '2012-10-17',
          Statement: [
            {
              Effect: 'Allow',
              Action: [
                'sqs:ReceiveMessage',
                'sqs:SendMessage',
                'sqs:DeleteMessage',
                'sqs:ChangeMessageVisibility',
                'sqs:GetQueueAttributes',
                'sqs:GetQueueUrl',
              ],
              Resource: [{'Fn::GetAtt': ['pollQueue', 'Arn']}],
            },
          ],
        },
      },
    },
  };
};
