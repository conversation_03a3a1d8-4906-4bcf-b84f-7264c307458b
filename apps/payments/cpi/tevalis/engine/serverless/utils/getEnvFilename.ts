/* eslint-disable prefer-template */
/* eslint no-template-curly-in-string: 0 */
/* eslint no-useless-concat: 0 */
import * as fs from 'fs';
import * as path from 'path';

function isFileExist(filepath: string) {
  return fs.existsSync(filepath);
}

export function getEnvFilename() {
  const stageEnv = path.resolve(process.cwd(), 'config', '.env.' + process.env.STAGE);

  if (isFileExist(stageEnv)) {
    console.log('Using file ' + stageEnv);
    return stageEnv;
  }
  console.log('Using file ' + stageEnv);
  return path.resolve(process.cwd(), 'config', '.env');
}
