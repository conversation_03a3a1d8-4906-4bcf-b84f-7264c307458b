export class EnvService {
  COMPONENT_NAME: string;

  PART_NAME: string;

  PRIMARY_REGION: string;

  LAMBDA_TIMEOUT_IN_SECONDS: number;

  LAMBDA_MEMORY_DEFAULT: number;

  VPC_ENV_NAME: string;

  LOG_LEVEL: string;

  API_VERSION: string;

  immediatelyResolveCrossStackValues: boolean;

  STATIC_ENV_NAME: string;
  
  COMPONENT_TABLE: string;

  ZELLER_DEVID: string;
  
  TEVALIS_API_URL: string;

  constructor() {
    this.COMPONENT_NAME = this.getVariableOrThrowError('COMPONENT_NAME');
    this.PART_NAME = this.getVariableOrThrowError('PART_NAME');
    this.VPC_ENV_NAME = this.getVariableOrThrowError('VPC_ENV_NAME');
    this.PRIMARY_REGION = this.getVariableOrThrowError('PRIMARY_REGION');
    this.LAMBDA_TIMEOUT_IN_SECONDS = parseInt(this.getVariableOrThrowError('LAMBDA_TIMEOUT_IN_SECONDS'), 10);
    this.LAMBDA_MEMORY_DEFAULT = parseInt(this.getVariableOrThrowError('LAMBDA_MEMORY_DEFAULT'), 10);
    this.LOG_LEVEL = this.getVariableOrDefault('LOG_LEVEL', '');
    this.API_VERSION = this.getVariableOrDefault('API_VERSION', 'v1');
    this.immediatelyResolveCrossStackValues =
      this.getVariableOrDefault('IMMEDIATELY_RESOLVE_CROSS_STACK_VALUES', 'FALSE').toLowerCase() === 'true';
    this.STATIC_ENV_NAME = this.getVariableOrThrowError('STATIC_ENV_NAME');
    this.COMPONENT_TABLE = `${this.STATIC_ENV_NAME}-cpi-engine-dynamodb-Entities`;
    this.ZELLER_DEVID = this.getVariableOrThrowError('ZELLER_DEVID');
    this.TEVALIS_API_URL = this.getVariableOrThrowError('TEVALIS_API_URL');
  }

  getVariableOrDefault(variableName: string, defaultValue: string) {
    const value = process.env[variableName];
    if (value) {
      return value;
    }
    console.warn(`variable name ${variableName} not found, falling back to default ${defaultValue}`);
    return defaultValue;
  }

  getVariableOrThrowError(variableName: string): string {
    const value = process.env[variableName];
    if (value) {
      return value;
    }
    throw new Error(`${variableName} was not defined in the environment variables`);
  }
}
