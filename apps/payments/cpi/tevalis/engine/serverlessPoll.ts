/* eslint import/no-import-module-exports: 0 */
/* eslint no-template-curly-in-string: 0 */
/* eslint no-useless-concat: 0 */
// eslint-disable-next-line import/no-extraneous-dependencies
import * as dotenv from 'dotenv';
import type { Serverless } from 'serverless/aws';

import { EnvService } from './serverless/envService';
import { golangExecutable, packagePatterns, providedAl2023 } from './serverless/globals';
import { getDeploymentBucket } from './serverless/shared/deploymentBucket';
import { getTags, getTagsArray } from './serverless/shared/getTags';
import { getVpc } from './serverless/shared/getVpc';
import { getEnvFilename } from './serverless/utils/getEnvFilename';
import { getSqsResources } from './serverless/sqs/resources';
import { getManagedPolicies } from './serverless/shared/getManagedPolicies';

dotenv.config({ path: getEnvFilename(), debug: true });
const env = new EnvService();
const region = '${opt:region}';
const stage = '${opt:stage}';

const serviceBaseName = `\${opt:stage}-${env.COMPONENT_NAME}-${env.PART_NAME}` as const;
const serviceName = `${serviceBaseName}-poll` as const;
const stackName = serviceName;


const generateServerless = async (): Promise<Serverless> => {
  return {
    service: serviceName,
    plugins: ['serverless-plugin-resource-tagging', 'serverless-plugin-tracing', 'serverless-plugin-scripts'],
    useDotenv: true,
    variablesResolutionMode: '20210326',
    frameworkVersion: '3',
    provider: {
      name: 'aws',
      runtime: providedAl2023,
      apiName: serviceName,
      region,
      stackName,
      vpc: await getVpc(env),
      tracing: {
        lambda: true,
      },
      versionFunctions: false,
      deploymentBucket: await getDeploymentBucket(env),
      environment: {
        COMPONENT_NAME: env.COMPONENT_NAME,
        PART_NAME: env.PART_NAME,
        STAGE: stage,
        LOG_LEVEL: env.LOG_LEVEL,
        COMPONENT_TABLE: env.COMPONENT_TABLE,
        ZELLER_DEVID: env.ZELLER_DEVID,
        TEVALIS_API_URL: env.TEVALIS_API_URL,
      },
      tags: getTags(env),
      stackTags: getTags(env),
    },
    package: packagePatterns,
    custom: {},
    functions: {
      pollScheduler: {
        handler: golangExecutable,
        name: `${stackName}-scheduler`,
        timeout: 60,
        tracing: true,
        role: 'pollSchedulerProcessorRole',
        environment: {
          HANDLER_NAME: 'poll-scheduler',
        },
        events: [
          {
            schedule: {
              rate: 'rate(1 minute)',
              enabled: true,
            },
          },
        ],
      },
      pollProcessor: {
        handler: golangExecutable,
        name: `${stackName}-processor`,
        timeout: 60,
        tracing: true,
        role: 'pollSchedulerProcessorRole',
        environment: {
          HANDLER_NAME: 'poll-processor',
        },
        events: [
          {
            sqs: { arn: {'Fn::GetAtt': ['pollQueue', 'Arn']}, batchSize: 1 },
          },
        ],
      },
    },
    resources: {
        Resources: {
          ...getManagedPolicies(stackName),
          ...getSqsResources(stackName, getTagsArray(env)),
          pollSchedulerProcessorRole: {
            Type: 'AWS::IAM::Role',
            Properties: {
              AssumeRolePolicyDocument: {
                Version: '2012-10-17',
                Statement: [
                  {
                    Effect: 'Allow',
                    Principal: {
                      Service: ['lambda.amazonaws.com'],
                    },
                    Action: 'sts:AssumeRole',
                  },
                ],
              },
              ManagedPolicyArns: [
                {
                  Ref: 'lambdaVpcPolicy',
                },
                {
                  Ref: 'xrayPolicy',
                },
                {
                  Ref: 'logPolicy',
                },
                {
                  Ref: 'pollQueuePolicy',
                },
                {
                  Ref: 'cpiTablePolicy',
                },
              ],
            },
          },
        },
      },
  } as any;
};

module.exports = generateServerless();
