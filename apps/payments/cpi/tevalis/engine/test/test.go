package test

import (
	"context"
	"fmt"
	"log"
	"os"
	"slices"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	coreTypes "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/types"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
)

const EntityTableName = "Entities"

func LoadAwsMockConfig(ctx context.Context) context.Context {
	endpointResolver := aws.EndpointResolverWithOptionsFunc(func(service string, region string, options ...interface{}) (aws.Endpoint, error) {
		return aws.Endpoint{URL: "http://localhost:8006"}, nil
	})
	cfg, err := config.LoadDefaultConfig(
		ctx,
		config.WithRegion("local"),
		config.WithEndpointResolverWithOptions(endpointResolver),
	)
	if err != nil {
		log.Panic(ctx, "Failed to load aws config")
	}
	return context.WithValue(ctx, coreTypes.AwsDynamodbConfig{}, cfg)
}

func SetTestEnv(ctx context.Context) env.IEnvironmentService {
	os.Setenv("COMPONENT_TABLE", EntityTableName)
	os.Setenv("ZELLER_DEVID", "test-zeller-dev-id")

	return env.NewEnvService(ctx)
}

func SetupLocalTable(ctx context.Context) *dynamodb.Client {
	cfg, _ := ctx.Value(coreTypes.AwsDynamodbConfig{}).(aws.Config)
	db := dynamodb.NewFromConfig(cfg)
	var lastEvaluatedTableName *string
	existTable := false
	for {
		tables, err := db.ListTables(ctx, &dynamodb.ListTablesInput{
			ExclusiveStartTableName: lastEvaluatedTableName,
		})
		if err != nil {
			fmt.Println("err", err)
			break
		}
		fmt.Println("table name", tables.TableNames)
		if slices.Contains(tables.TableNames, EntityTableName) {
			existTable = true
			break
		}
		if tables.LastEvaluatedTableName == nil {
			break
		}
		lastEvaluatedTableName = tables.LastEvaluatedTableName
	}
	if !existTable {
		output, err := db.CreateTable(ctx, &dynamodb.CreateTableInput{
			TableName: aws.String(EntityTableName),
			AttributeDefinitions: []types.AttributeDefinition{
				{
					AttributeName: aws.String("id"),
					AttributeType: types.ScalarAttributeTypeS,
				},
				{
					AttributeName: aws.String("type"),
					AttributeType: types.ScalarAttributeTypeS,
				},
				{
					AttributeName: aws.String("entityUuid"),
					AttributeType: types.ScalarAttributeTypeS,
				},
				{
					AttributeName: aws.String("shortId"),
					AttributeType: types.ScalarAttributeTypeS,
				},
				{
					AttributeName: aws.String("siteUuid"),
					AttributeType: types.ScalarAttributeTypeS,
				},
				{
					AttributeName: aws.String("venueId"),
					AttributeType: types.ScalarAttributeTypeS,
				},
			},
			KeySchema: []types.KeySchemaElement{
				{
					AttributeName: aws.String("id"),
					KeyType:       types.KeyTypeHash,
				},
				{
					AttributeName: aws.String("type"),
					KeyType:       types.KeyTypeRange,
				},
			},
			BillingMode: types.BillingModePayPerRequest,
			GlobalSecondaryIndexes: []types.GlobalSecondaryIndex{
				{
					IndexName: aws.String("typeGsi"),
					KeySchema: []types.KeySchemaElement{
						{
							AttributeName: aws.String("type"),
							KeyType:       types.KeyTypeHash,
						},
						{
							AttributeName: aws.String("id"),
							KeyType:       types.KeyTypeRange,
						},
					},
					Projection: &types.Projection{
						ProjectionType: types.ProjectionTypeAll,
					},
				},
				{
					IndexName: aws.String("entityGsi"),
					KeySchema: []types.KeySchemaElement{
						{
							AttributeName: aws.String("entityUuid"),
							KeyType:       types.KeyTypeHash,
						},
						{
							AttributeName: aws.String("type"),
							KeyType:       types.KeyTypeRange,
						},
					},
					Projection: &types.Projection{
						ProjectionType: types.ProjectionTypeAll,
					},
				},
				{
					IndexName: aws.String("shortIdGsi"),
					KeySchema: []types.KeySchemaElement{
						{
							AttributeName: aws.String("shortId"),
							KeyType:       types.KeyTypeHash,
						},
						{
							AttributeName: aws.String("type"),
							KeyType:       types.KeyTypeRange,
						},
					},
					Projection: &types.Projection{
						ProjectionType: types.ProjectionTypeAll,
					},
				},
				{
					IndexName: aws.String("siteGsi"),
					KeySchema: []types.KeySchemaElement{
						{
							AttributeName: aws.String("siteUuid"),
							KeyType:       types.KeyTypeHash,
						},
						{
							AttributeName: aws.String("type"),
							KeyType:       types.KeyTypeRange,
						},
					},
					Projection: &types.Projection{
						ProjectionType: types.ProjectionTypeAll,
					},
				},
				{
					IndexName: aws.String("venueGsi"),
					KeySchema: []types.KeySchemaElement{
						{
							AttributeName: aws.String("venueId"),
							KeyType:       types.KeyTypeHash,
						},
						{
							AttributeName: aws.String("type"),
							KeyType:       types.KeyTypeRange,
						},
					},
					Projection: &types.Projection{
						ProjectionType: types.ProjectionTypeAll,
					},
				},
			},
		})
		if err != nil {
			log.Panic("Failed to create db table", err)
		}
		fmt.Printf("create table response %v\n", &output.TableDescription.TableName)
	}

	return db
}

func GenerateRandomInt(length int) int {
	if length <= 0 {
		return 0
	}
	min := 1
	for i := 1; i < length; i++ {
		min *= 10
	}
	max := min*10 - 1
	return min + int(time.Now().UnixNano())%(max-min+1)
}
