package lambdas

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	AWSLambda "github.com/aws/aws-sdk-go-v2/service/lambda"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/lambda"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
)

func init() {
	os.Setenv("TEVALIS_API_URL", "https://dummy-tevalis-api-url")
	os.Setenv("ZELLER_DEVID", "dummy-zeller-dev-id")
}

func TestCanInstantiateLambdaClass(t *testing.T) {
	New(context.Background())
}

func TestCanInvokeApiHandler(t *testing.T) {
	ctx := context.Background()
	lambda := New(ctx)
	result, err := lambda.ApiHandler(ctx, events.APIGatewayProxyRequest{})

	assert.Nil(t, err)

	if result.StatusCode != 404 {
		t.Errorf("Expected status code 400, got %d", result.StatusCode)
	}
}

type MockLambda struct {
	mock.Mock
}

func (m *MockLambda) Invoke(ctx context.Context, params *AWSLambda.InvokeInput, optFns ...func(*AWSLambda.Options)) (*AWSLambda.InvokeOutput, error) {
	args := m.Called(ctx, *params.FunctionName, params.Payload, optFns)
	return args.Get(0).(*AWSLambda.InvokeOutput), args.Error(1)
}

func TestCanInvokeWarmupHandler(t *testing.T) {
	m := new(MockLambda)
	m.On("Invoke", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(&AWSLambda.InvokeOutput{}, nil).Times(1)
	ctx := context.WithValue(context.Background(), lambda.LambdaService{}, m)
	os.Setenv(string(env.EnvComponentName), "cpi")
	os.Setenv(string(env.EnvPartName), "tevalis-engine")
	os.Setenv(string(env.EnvStage), "test")
	lambda := New(ctx)
	lambda.WarmupHandler(ctx)

	m.AssertExpectations(t)
}

func TestCanInvokePollScheduler(t *testing.T) {
	ctx := context.Background()
	lambda := New(ctx)
	result, err := lambda.PollSchedulerHandler(ctx, events.CloudWatchEvent{
		Version:    "0",
		ID:         "test-id",
		Detail:     json.RawMessage{},
		DetailType: "aws-cloudwatch-event",
		Resources:  []string{},
		Region:     "ap-souteast-2",
		AccountID:  "************",
		Time:       time.Now(),
		Source:     "aws.events",
	})

	assert.Nil(t, err)
	assert.Nil(t, result)
}

func TestCanInvokePollProcessor(t *testing.T) {
	ctx := context.Background()
	lambda := New(ctx)
	result, err := lambda.PollProcessorHandler(ctx, events.SQSEvent{
		Records: []events.SQSMessage{
			{
				MessageId:              "d970210c-3905-4be5-95c0-abf7f33b5723",
				ReceiptHandle:          "AQEBpTK58m1Cvobf2ZxptgBEUK5Usg1bnK512faoJITSK9Iee3/TXfkijU4/HUrUDZ5sRgFNcxsaSlgX/MxIdKJcEjkKULUebLIO/3bomTL8bJgN7QjYC0ND95lOROrGJtcr2m9OBJFQVwXDvXojXmQTdSt1Vj115Bf2n4iTbD1vlMFoczgaMWxn1PJ3CIsW1hrsTQHo4db/Nw3qOdh9wPoeIKqwWsAjHbyZapeTpYOwsDssKSw+c5hL7pvKOc+fQhuUlVg4Tjbw6OUOojAv295qGHJrsV6CbtGtPbypxI1TY65Fb7vTb596GdqwIAbpflBIdS4OoiT7bmY0KsFXXNC8vqVHNsG5PSPhLefkeOtf0+76RsuFuhGeuuhumZ6bWA3Tl2L5wn+ECYkxi0f1BrV6AN6IBeE66szU1Av18N6nMVk=",
				Body:                   "Message 7, delayed by 30 seconds at 02/06/2025, 3:32:11 pm",
				Md5OfBody:              "6935fe77e03d63c47af9db1ba24e7b54",
				Md5OfMessageAttributes: "",
				Attributes: map[string]string{
					"ApproximateFirstReceiveTimestamp": "1748842331355",
					"ApproximateReceiveCount":          "1",
					"SenderId":                         "AROAT27MWNFCRQTYEYKL6",
					"SentTimestamp":                    "1748842301355",
				},
				MessageAttributes: map[string]events.SQSMessageAttribute{},
				EventSourceARN:    "arn:aws:sqs:ap-southeast-2:264100014405:dev-cpi-tevalis-engine-poll-queue",
				EventSource:       "aws:sqs",
				AWSRegion:         "ap-southeast-2",
			},
		},
	})

	assert.Nil(t, err)
	assert.Equal(t, result, events.SQSEventResponse{})
}
