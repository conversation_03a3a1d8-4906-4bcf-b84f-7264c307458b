package lambdas

import (
	"context"
	"fmt"

	"github.com/aws/aws-lambda-go/events"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/warmup"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/handlers/api"
)

type Lambda struct {
	apiHandler *api.ApiHandler
	// warmupHandler *warmup.WarmupHandler
}

func New(ctx context.Context) *Lambda {
	return &Lambda{api.NewHandler(ctx)}
}

func (l *Lambda) ApiHandler(ctx context.Context, event events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	result, _ := l.apiHandler.HandleApiRequest(ctx, event)
	// If an error is returned, http gateway will return 500 and not the correct error code or message
	return result, nil
}

func (l *Lambda) PollSchedulerHandler(ctx context.Context, event events.CloudWatchEvent) (any, error) {
	logger.Info(ctx, "PollSchedulerHandler to be implemented")
	return nil, nil
}

func (l *Lambda) PollProcessorHandler(ctx context.Context, event events.SQSEvent) (events.SQSEventResponse, error) {
	logger.Info(ctx, "event: "+utils.BestEffortStringify(event, false))
	logger.Info(ctx, "PollProcessorHandler to be implemented")
	return events.SQSEventResponse{}, nil
}

func (l *Lambda) WarmupHandler(ctx context.Context) {
	envService := env.NewEnvService(ctx)
	componentName := envService.GetString(env.EnvComponentName)
	partName := envService.GetString(env.EnvPartName)
	stage := envService.GetString(env.EnvStage)
	baseService := fmt.Sprintf("%s-%s-%s", stage, componentName, partName)

	keepWarmList := []warmup.WarmupLambda{
		{Name: baseService + "-api-api-handler", Count: 1},
	}

	warmup.New(ctx).Warmup(ctx, keepWarmList)
}
