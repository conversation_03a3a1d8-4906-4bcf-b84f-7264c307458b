{"name": "cpi-tevalis-engine", "scripts": {"build": "rm -fr dist && rm -rf .build/ && yarn tsc --build tsconfig.json && make build", "lint": "make lint", "test": "make test && cat dist/output.html >> $GITHUB_STEP_SUMMARY", "deploy": "yarn sls deploy", "clean": "rm -rf dist"}, "devDependencies": {"@types/node": "18.19.14", "@types/serverless": "^3.12.22", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "dotenv": "^16.0.0", "eslint": "8.45.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-folders": "^1.0.4", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-unicorn": "^46.0.0", "serverless": "^3.39.0", "serverless-plugin-scripts": "^1.0.2", "tsx": "^4.19.3", "typescript": "^5.4.5"}}