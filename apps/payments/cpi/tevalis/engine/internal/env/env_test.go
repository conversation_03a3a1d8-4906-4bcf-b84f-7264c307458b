package env

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

var ctx = context.Background()

func TestNewEnv(t *testing.T) {
	envService := NewEnvService(ctx)
	assert.NotNil(t, envService)
}

func TestNewEnvStage(t *testing.T) {
	os.Setenv("STAGE", "dev")
	os.Setenv("CONFIG_PATH", "../../config")

	envService := NewEnvService(ctx)
	assert.NotNil(t, envService)
}

func TestGetStringNotSet(t *testing.T) {
	envService := NewEnvService(ctx)
	assert.PanicsWithError(t, "TEST_ENV is not set", func() {
		envService.GetInt("TEST_ENV")
	})
}

func TestGetStringEmptyValue(t *testing.T) {
	envService := NewEnvService(ctx)

	os.Setenv("TEST_ENV", "")

	assert.PanicsWithError(t, "TEST_ENV is not set", func() {
		envService.GetInt("TEST_ENV")
	})
}

func TestGetIntNotSet(t *testing.T) {
	envService := NewEnvService(ctx)
	assert.PanicsWithError(t, "TEST_INT is not set", func() {
		envService.GetInt("TEST_INT")
	})
}

func TestGetInt(t *testing.T) {
	envService := NewEnvService(ctx)
	os.Setenv("TEST_INT", "150")
	assert.Equal(t, 150, envService.GetInt("TEST_INT"))
}

func TestGetIntInvalidInt(t *testing.T) {
	envService := NewEnvService(ctx)

	os.Setenv("TEST_INT", "not an integer")

	assert.PanicsWithError(t, "TEST_INT is not a valid integer", func() {
		envService.GetInt("TEST_INT")
	})
}
