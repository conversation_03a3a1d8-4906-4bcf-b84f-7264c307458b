package env

import (
	"context"
	"fmt"
	"strconv"

	"github.com/spf13/viper"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
)

type EnvVariable string

const (
	EnvComponentTable EnvVariable = "COMPONENT_TABLE"
	EnvComponentName  EnvVariable = "COMPONENT_NAME"
	EnvPartName       EnvVariable = "PART_NAME"
	EnvStage          EnvVariable = "STAGE"
	EnvZellerDevId    EnvVariable = "ZELLER_DEVID"
	EnvTevalisApiUrl  EnvVariable = "TEVALIS_API_URL"
)

type IEnvironmentService interface {
	GetString(envVar EnvVariable) string
	GetInt(envVar EnvVariable) int
}

func NewEnvService(ctx context.Context) IEnvironmentService {
	vEnv := viper.New()
	vEnv.AutomaticEnv()
	envService := &envService{vEnv}
	envService.init(ctx)

	return envService
}

type envService struct {
	viper *viper.Viper
}

func (env *envService) bindEnv(ctx context.Context, key string) {
	err := env.viper.BindEnv(key)
	if err != nil {
		logger.Panic(ctx, err)
	}
}

func (env *envService) init(ctx context.Context) {
	env.bindEnv(ctx, string(EnvComponentTable))
	env.bindEnv(ctx, string(EnvZellerDevId))
	env.bindEnv(ctx, string(EnvTevalisApiUrl))
}

func (env *envService) GetString(envVar EnvVariable) string {
	isSet := env.viper.IsSet(string(envVar))
	value := env.viper.GetString(string(envVar))

	if !isSet || value == "" {
		logger.Error(context.Background(), fmt.Errorf("%s is not set", envVar))
		panic(fmt.Errorf("%s is not set", envVar))
	}
	return value
}

func (env *envService) GetInt(envVar EnvVariable) int {
	raw := env.GetString(envVar)
	parsed, err := strconv.ParseInt(raw, 10, 32)
	if err != nil {
		logger.Error(context.Background(), fmt.Errorf("%s is not a valid integer", envVar))
		panic(fmt.Errorf("%s is not a valid integer", envVar))
	}

	return int(parsed)
}
