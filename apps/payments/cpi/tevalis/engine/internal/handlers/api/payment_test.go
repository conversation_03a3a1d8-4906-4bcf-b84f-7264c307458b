package api

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
	tevalis "github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/tevalis_client"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestPaymentMissingPathParams(t *testing.T) {
	apiHandler := NewHandler(ctx)
	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod:     http.MethodPost,
		Path:           "/v1/payment/" + uuid.NewString(),
		PathParameters: map[string]string{}, // empty path parameters
	})
	assert.Equal(t, http.StatusBadRequest, res.StatusCode)
	assert.Nil(t, err)
}

func TestPaymentInvalidPosconnectorEvent(t *testing.T) {
	tst := newTest()
	apiHandler := NewHandler(ctx)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodPost,
		Path:       "/v1/payment/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
		Body: `{Invalid Json}`,
	})
	assert.Equal(t, http.StatusBadRequest, res.StatusCode)
	assert.Nil(t, err)
	assert.Contains(t, res.Body, "Failed to parse posconnector event")
}

func TestPaymentInvalidPosconnectorEventData(t *testing.T) {
	tst := newTest()
	apiHandler := NewHandler(ctx)

	event := types.PosConnectorEvent{
		EventId:   ptr.String(uuid.NewString()),
		Timestamp: ptr.String(fmt.Sprintf("%d", (time.Now().UnixMilli()))),
		Action:    types.ActionPayment,
		Data:      "invalid json",
	}
	jsonEvent, _ := json.Marshal(event)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodPost,
		Path:       "/v1/payment/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
		Body: string(jsonEvent),
	})
	assert.Equal(t, http.StatusBadRequest, res.StatusCode)
	assert.Nil(t, err)
	assert.Contains(t, res.Body, "Failed to parse data to payment")
}

func TestPaymentNoPairingData(t *testing.T) {
	tst := newTest()
	apiHandler := NewHandler(ctx)

	payment := protocol.Payment{
		OrderId: ptr.String("100"),
	}

	event := types.PosConnectorEvent{
		EventId:   ptr.String(uuid.NewString()),
		Timestamp: ptr.String(fmt.Sprintf("%d", (time.Now().UnixMilli()))),
		Action:    types.ActionPayment,
		Data:      payment,
	}
	jsonEvent, _ := json.Marshal(event)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodPost,
		Path:       "/v1/payment/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
		Body: string(jsonEvent),
	})
	assert.Equal(t, http.StatusForbidden, res.StatusCode)
	assert.Nil(t, err)
	assert.Equal(t, fmt.Sprintf("Pairing record not found for siteUuid %s", tst.siteUuid), res.Body)
}

func TestPaymentNoConnectionData(t *testing.T) {
	tst := newTest()
	apiHandler := NewHandler(ctx)
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, "venueId", nil)

	payment := protocol.Payment{
		OrderId: ptr.String("100"),
	}

	event := types.PosConnectorEvent{
		EventId:   ptr.String(uuid.NewString()),
		Timestamp: ptr.String(fmt.Sprintf("%d", (time.Now().UnixMilli()))),
		Action:    types.ActionPayment,
		Data:      payment,
	}
	jsonEvent, _ := json.Marshal(event)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodPost,
		Path:       "/v1/payment/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
		Body: string(jsonEvent),
	})
	assert.Equal(t, http.StatusForbidden, res.StatusCode)
	assert.Nil(t, err)
	assert.Equal(t, fmt.Sprintf("Connection record not found for entityUuid %s", tst.entityUuid), res.Body)
}

func TestPayment(t *testing.T) {
	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, tevalis.ApiPathTablePaymentRequest) {
			response := []tevalis.TablePaymentResponseItem{{
				TableListItems: []tevalis.TableListItem{
					{
						TransactionId: 28,
						TotalValue:    1700,
						BalanceValue:  1700,
					},
				},
				IsSuccess: true,
				Message:   "Table available",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}

		if r.Method == http.MethodPost && strings.Contains(r.URL.Path, tevalis.ApiPathTablePaymentPost) {
			response := []tevalis.TablePaymentResponseItem{{
				TableListItems: []tevalis.TableListItem{
					{
						TransactionId: 28,
					},
				},
				IsSuccess: true,
				Message:   "Payment with tip successful",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server := httptest.NewServer(http.HandlerFunc(handler))
	defer server.Close()
	os.Setenv(string(env.EnvTevalisApiUrl), server.URL)

	tst := newTest()
	apiHandler := NewHandler(ctx)
	connection := addConnectionRecord(ctx, tst.entityUuid)
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, connection.Venues[0].Id, nil)

	payment := protocol.Payment{
		OrderId:         ptr.String("28"),
		Amount:          ptr.Int(2222),
		Tip:             ptr.Int(500),
		Surcharge:       ptr.Int(22),
		AccountType:     ptr.String("Zeller Card"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}

	event := types.PosConnectorEvent{
		EventId:   ptr.String(uuid.NewString()),
		Timestamp: ptr.String(fmt.Sprintf("%d", (time.Now().UnixMilli()))),
		Action:    types.ActionPayment,
		Data:      payment,
	}
	jsonEvent, _ := json.Marshal(event)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodPost,
		Path:       "/v1/payment/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
		Body: string(jsonEvent),
	})
	assert.Equal(t, http.StatusOK, res.StatusCode)
	assert.Nil(t, err)
}

func TestPaymentErrorPostPayment(t *testing.T) {
	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, tevalis.ApiPathTablePaymentRequest) {
			response := []tevalis.TablePaymentResponseItem{{
				TableListItems: []tevalis.TableListItem{
					{
						TransactionId: 28,
						TotalValue:    1700,
						BalanceValue:  1700,
					},
				},
				IsSuccess: true,
				Message:   "Table available",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}

		if r.Method == http.MethodPost && strings.Contains(r.URL.Path, tevalis.ApiPathTablePaymentPost) {
			w.WriteHeader(http.StatusBadRequest)
			_, _ = w.Write([]byte("Payment conflict"))
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server := httptest.NewServer(http.HandlerFunc(handler))
	defer server.Close()
	os.Setenv(string(env.EnvTevalisApiUrl), server.URL)

	tst := newTest()
	apiHandler := NewHandler(ctx)
	connection := addConnectionRecord(ctx, tst.entityUuid)
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, connection.Venues[0].Id, nil)

	payment := protocol.Payment{
		OrderId:         ptr.String("28"),
		Amount:          ptr.Int(2222),
		Tip:             ptr.Int(500),
		Surcharge:       ptr.Int(22),
		AccountType:     ptr.String("Zeller Card"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}

	event := types.PosConnectorEvent{
		EventId:   ptr.String(uuid.NewString()),
		Timestamp: ptr.String(fmt.Sprintf("%d", (time.Now().UnixMilli()))),
		Action:    types.ActionPayment,
		Data:      payment,
	}
	jsonEvent, _ := json.Marshal(event)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodPost,
		Path:       "/v1/payment/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
		Body: string(jsonEvent),
	})
	assert.Equal(t, http.StatusBadRequest, res.StatusCode)
	assert.Nil(t, err)
}
