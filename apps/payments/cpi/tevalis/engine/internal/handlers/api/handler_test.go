package api

import (
	"context"
	"net/http"
	"os"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	"github.com/stretchr/testify/assert"
)

func init() {
	os.Setenv("TEVALIS_API_URL", "https://dummy-tevalis-api-url")
	os.Setenv("ZELLER_DEVID", "dummy-zeller-dev-id")
}

func TestGetApiName(t *testing.T) {
	handler := NewHandler(context.Background())

	tests := []struct {
		name         string
		path         string
		expectedApi  ApiName
		expectedCode int
		expectedBody string
	}{
		{
			name:         "Orders Path",
			path:         "/v1/orders/123",
			expectedApi:  ORDERS,
			expectedCode: http.StatusOK,
		},
		{
			name:         "Order Path",
			path:         "/v1/order/456",
			expectedApi:  ORDER,
			expectedCode: http.StatusOK,
		},
		{
			name:         "Payment Path",
			path:         "/v1/payment/xyz",
			expectedApi:  PAYMENT,
			expectedCode: http.StatusOK,
		},
		{
			name:         "Health Path",
			path:         "/v1/health",
			expectedApi:  HEALTH,
			expectedCode: http.StatusOK,
		},
		{
			name:         "Invalid Path",
			path:         "/v1/unknown",
			expectedApi:  "",
			expectedCode: http.StatusNotFound,
			expectedBody: "invalid api path '/v1/unknown'",
		},
		{
			name:         "Empty Path",
			path:         "",
			expectedCode: http.StatusNotFound,
			expectedBody: "invalid api path ''",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			request := events.APIGatewayProxyRequest{
				Path: tt.path,
			}

			apiName, err := handler.getApiName(request.Path)

			assert.Equal(t, tt.expectedApi, apiName)

			if err != nil {
				assert.Equal(t, tt.expectedCode, err.statusCode)
				assert.Equal(t, tt.expectedBody, err.Error())
			}
		})
	}
}

func TestHandleApiRequest(t *testing.T) {
	handler := NewHandler(context.Background())
	ctx := context.Background()

	tests := []struct {
		name         string
		path         string
		expectedCode int
		expectedBody string
	}{
		{
			name:         "Valid Health Check Path",
			path:         "/v1/health",
			expectedCode: http.StatusOK,
		},
		{
			name:         "Root Path",
			path:         "/",
			expectedCode: http.StatusNotFound,
			expectedBody: "invalid api path '/'",
		},
		{
			name:         "Empty Path",
			path:         "",
			expectedCode: http.StatusNotFound,
			expectedBody: "invalid api path ''",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			request := events.APIGatewayProxyRequest{
				Path: tt.path,
			}

			response, err := handler.HandleApiRequest(ctx, request)

			assert.NoError(t, err)
			assert.Equal(t, tt.expectedCode, response.StatusCode)
			assert.Equal(t, tt.expectedBody, response.Body)
		})
	}
}
