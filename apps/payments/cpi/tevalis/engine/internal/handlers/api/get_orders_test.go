package api

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/model"
	tevalisclient "github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/tevalis_client"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/types"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/test"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	pconnTypes "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

var ctx = context.Background()
var dbClient *dynamodb.DynamoDb

type testObject struct {
	entityUuid string
	siteUuid   string
}

func newTest() *testObject {
	return &testObject{
		entityUuid: uuid.NewString(),
		siteUuid:   uuid.NewString(),
	}
}

func addPairingRecord(ctx context.Context, siteUuid string, entityUuid string, venueId string, locations []string) model.PosInterfacePairingModel {
	pairingModel := model.PosInterfacePairingModel{
		Id:         uuid.NewString(),
		EntityUuid: entityUuid,
		SiteUuid:   siteUuid,
		Provider:   string(pconnTypes.TEVALIS),
		VenueId:    venueId,
		Locations:  locations,
		Status:     "ACTIVE",
		Timestamp:  strconv.Itoa(int(time.Now().UnixMilli())),
	}
	pairingItem := model.PosInterfaceSitePairingItem{
		Type:                     string(types.POSINTERFACE_PAIR_SITE) + siteUuid,
		PosInterfacePairingModel: pairingModel,
	}
	p, _ := attributevalue.MarshalMap(pairingItem)
	_, err := dbClient.InsertItem(ctx, test.EntityTableName, &p, nil)
	if err != nil {
		panic(err)
	}

	return pairingModel
}

func addConnectionRecord(ctx context.Context, entityUuid string) model.ConnectionPosInterfaceModel {
	companyId := strconv.Itoa(test.GenerateRandomInt(6))
	locationId := strconv.Itoa(test.GenerateRandomInt(6))

	connectionModel := model.ConnectionPosInterfaceModel{
		Id:             uuid.NewString(),
		EntityUuid:     entityUuid,
		CustomerUuid:   ptr.String(uuid.NewString()),
		Provider:       string(pconnTypes.TEVALIS),
		OrganisationId: companyId,
		Status:         "CONNECTED",
		Credentials: model.TevalisCredentials{
			CompanyId: companyId,
			Guid:      uuid.NewString(),
			Guid2:     uuid.NewString(),
		},
		Venues: []model.PayAtTableVenue{
			{
				Id:   strconv.Itoa(test.GenerateRandomInt(6)),
				Name: "Test Venue",
				Locations: []model.PayAtTableLocation{
					{
						Id:     locationId,
						Name:   "Test Location",
						Number: locationId,
					},
				},
			},
		},
	}
	connectionItem := model.ConnectionPosInterfaceItem{
		Type:                        string(types.CONNECTION_POSINTERFACE),
		ConnectionPosInterfaceModel: connectionModel,
	}
	c, _ := attributevalue.MarshalMap(connectionItem)
	_, err := dbClient.InsertItem(ctx, test.EntityTableName, &c, nil)
	if err != nil {
		panic(err)
	}

	return connectionModel
}

func init() {
	ctx = test.LoadAwsMockConfig(context.Background())
	test.SetTestEnv(ctx)
	test.SetupLocalTable(ctx)
	dbClient = dynamodb.NewDynamoDb(ctx)
}

func TestGetOrdersNoSiteUuidParam(t *testing.T) {
	apiHandler := NewHandler(ctx)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod:     http.MethodGet,
		Path:           "/v1/orders/" + uuid.NewString(),
		PathParameters: map[string]string{
			// No siteUuid param
		},
	})
	assert.Equal(t, http.StatusBadRequest, res.StatusCode)
	assert.Nil(t, err)
}

func TestGetOrdersNoPairingData(t *testing.T) {
	tst := newTest()
	apiHandler := NewHandler(ctx)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		Path:       "/v1/orders/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
	})
	assert.Equal(t, http.StatusOK, res.StatusCode)
	assert.Nil(t, err)

	var response pconnTypes.PosConnectorEvent
	json.Unmarshal([]byte(res.Body), &response)
	assert.Equal(t, pconnTypes.ActionGetOrders, response.Action)
	assert.Equal(t, len(response.Data.([]any)), 0)
}

func TestGetOrdersNoConnectionData(t *testing.T) {
	tst := newTest()
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, "venueId", nil)
	apiHandler := NewHandler(ctx)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		Path:       "/v1/orders/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
	})
	assert.Equal(t, http.StatusOK, res.StatusCode)
	assert.Nil(t, err)

	var response pconnTypes.PosConnectorEvent
	json.Unmarshal([]byte(res.Body), &response)
	assert.Equal(t, pconnTypes.ActionGetOrders, response.Action)
	assert.Equal(t, len(response.Data.([]any)), 0)
}

func TestGetOrders(t *testing.T) {
	mockBillDataResponse := tevalisclient.LiveBillDataResponse{
		Count:      1,
		TotalGross: 123.45,
		TotalNet:   100.00,
		Transactions: []tevalisclient.Transaction{
			{
				TransactionID:       101,
				TableNumber:         1,
				TotalGross:          123.45,
				TransactionComplete: false,
				SalesArea:           "Main Dining",
				DateTimeOpened:      "2023-10-27T10:00:00Z",
				Items: []tevalisclient.Item{
					{
						Gross:     50.00,
						Net:       40.00,
						ExpNet:    35.00,
						ExpGross:  45.00,
						ItemName:  "Burger",
						Quantity:  2,
						ProductID: 1001,
						VATRate:   20.00,
					},
				},
			},
		},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	mockFloorplanResponse := tevalisclient.FloorplanResponse{
		SiteID: 8913,
		FloorplanArea: []tevalisclient.FloorplanArea{
			{
				FloorplanAreaID: 1,
				Name:            "Main Area",
				FloorplanAreaObjects: []tevalisclient.FloorplanAreaObject{
					{FloorplanAreaObjectID: 10, TableName: "Table 1", TableNumber: 1},
				},
			},
		},
	}
	mockFloorplanJSON, _ := json.Marshal(mockFloorplanResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			t.Errorf("Expected GET method, got %s", r.Method)
		}

		if strings.Contains(r.URL.Path, tevalisclient.ApiPathFloorplans) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockFloorplanJSON)
			return
		}

		if strings.Contains(r.URL.Path, tevalisclient.ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}

	}

	server := httptest.NewServer(http.HandlerFunc(handler))
	defer server.Close()
	os.Setenv(string(env.EnvTevalisApiUrl), server.URL)

	tst := newTest()
	connection := addConnectionRecord(ctx, tst.entityUuid)
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, connection.Venues[0].Id, nil)
	apiHandler := NewHandler(ctx)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		Path:       "/v1/orders/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
	})

	assert.Nil(t, err)
	assert.Equal(t, http.StatusOK, res.StatusCode)

	var response pconnTypes.PosConnectorEvent
	_ = json.Unmarshal([]byte(res.Body), &response)

	rawOrders, _ := json.Marshal(response.Data)
	var orders []protocol.DeviceOrder
	_ = json.Unmarshal(rawOrders, &orders)

	assert.Equal(t, 1, len(orders))

	transaction := mockBillDataResponse.Transactions[0]
	location := tevalisclient.FindLocationFromFloorplans(&mockFloorplanResponse, mockBillDataResponse.Transactions[0].TableNumber)

	assert.Equal(t, orders, []protocol.DeviceOrder{tevalisclient.MapTransactionToOrder(ctx, transaction, *location)})
}

func TestGetOrdersNoTables(t *testing.T) {
	mockBillDataResponse := tevalisclient.LiveBillDataResponse{
		Count:        0,
		TotalGross:   0.0,
		TotalNet:     0.0,
		Transactions: []tevalisclient.Transaction{},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(mockBillJSON)
		return
	}

	server := httptest.NewServer(http.HandlerFunc(handler))
	defer server.Close()
	os.Setenv(string(env.EnvTevalisApiUrl), server.URL)

	tst := newTest()
	connection := addConnectionRecord(ctx, tst.entityUuid)
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, connection.Venues[0].Id, nil)
	apiHandler := NewHandler(ctx)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		Path:       "/v1/orders/" + tst.siteUuid,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
		},
	})

	assert.Nil(t, err)
	assert.Equal(t, http.StatusOK, res.StatusCode)

	var response pconnTypes.PosConnectorEvent
	_ = json.Unmarshal([]byte(res.Body), &response)

	rawOrders, _ := json.Marshal(response.Data)
	var orders []protocol.DeviceOrder
	_ = json.Unmarshal(rawOrders, &orders)

	assert.Equal(t, 0, len(orders))

	assert.Equal(t, orders, []protocol.DeviceOrder{})
}
