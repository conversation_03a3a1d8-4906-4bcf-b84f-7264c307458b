package api

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"

	"github.com/aws/aws-lambda-go/events"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
	tevalisclient "github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/tevalis_client"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/test"
	protocol "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	pconnTypes "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func TestGetOrderMissingPathParams(t *testing.T) {
	apiHandler := NewHandler(ctx)

	testCases := []map[string]string{
		{
			"siteUuid": "missing orderId",
		},
		{
			"orderId": "missing siteUuid",
		},
		{
			// both params missing
		},
	}
	for _, testPathParameters := range testCases {
		t.Run("TestGetOrderMissingPathParams", func(t *testing.T) {
			res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
				HTTPMethod:     http.MethodGet,
				Path:           "/v1/order/" + uuid.NewString() + "/" + uuid.NewString(),
				PathParameters: testPathParameters,
			})
			assert.Equal(t, http.StatusBadRequest, res.StatusCode)
			assert.Nil(t, err)
		})
	}
}

func TestGetOrderNoPairingData(t *testing.T) {
	tst := newTest()
	apiHandler := NewHandler(ctx)

	orderId := strconv.Itoa(test.GenerateRandomInt(5))
	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		Path:       "/v1/order/" + tst.siteUuid + "/" + orderId,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
			"orderId":  orderId,
		},
	})
	assert.Equal(t, http.StatusNotFound, res.StatusCode)
	assert.Nil(t, err)
	assert.Equal(t, "order not found for site "+tst.siteUuid+" and orderId "+orderId, res.Body)
}

func TestGetOrderNoConnectionData(t *testing.T) {
	tst := newTest()
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, "venueId", nil)
	apiHandler := NewHandler(ctx)

	orderId := strconv.Itoa(test.GenerateRandomInt(5))
	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		Path:       "/v1/order/" + tst.siteUuid + "/" + orderId,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
			"orderId":  orderId,
		},
	})
	assert.Equal(t, http.StatusNotFound, res.StatusCode)
	assert.Nil(t, err)
	assert.Equal(t, "order not found for site "+tst.siteUuid+" and orderId "+orderId, res.Body)
}

func TestGetOrder(t *testing.T) {
	orderIdInt := test.GenerateRandomInt(5)
	orderId := strconv.Itoa(orderIdInt)
	mockBillDataResponse := tevalisclient.LiveBillDataResponse{
		Count:      1,
		TotalGross: 123.45,
		TotalNet:   100.00,
		Transactions: []tevalisclient.Transaction{
			{
				TransactionID:       orderIdInt,
				TableNumber:         1,
				TotalGross:          123.45,
				TransactionComplete: false,
				SalesArea:           "Main Dining",
				DateTimeOpened:      "2023-10-27T10:00:00Z",
				Items: []tevalisclient.Item{
					{
						Gross:     50.00,
						Net:       40.00,
						ExpNet:    35.00,
						ExpGross:  45.00,
						ItemName:  "Burger",
						Quantity:  2,
						ProductID: 1001,
						VATRate:   20.00,
					},
				},
			},
		},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	mockFloorplanResponse := tevalisclient.FloorplanResponse{
		SiteID: 8913,
		FloorplanArea: []tevalisclient.FloorplanArea{
			{
				FloorplanAreaID: 1,
				Name:            "Main Area",
				FloorplanAreaObjects: []tevalisclient.FloorplanAreaObject{
					{FloorplanAreaObjectID: 10, TableName: "Table 1", TableNumber: 1},
				},
			},
		},
	}
	mockFloorplanJSON, _ := json.Marshal(mockFloorplanResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			t.Errorf("Expected GET method, got %s", r.Method)
		}

		if strings.Contains(r.URL.Path, tevalisclient.ApiPathFloorplans) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockFloorplanJSON)
			return
		}

		if strings.Contains(r.URL.Path, tevalisclient.ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}

	}

	server := httptest.NewServer(http.HandlerFunc(handler))
	defer server.Close()
	os.Setenv(string(env.EnvTevalisApiUrl), server.URL)

	tst := newTest()
	connection := addConnectionRecord(ctx, tst.entityUuid)
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, connection.Venues[0].Id, nil)
	apiHandler := NewHandler(ctx)

	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		Path:       "/v1/order/" + tst.siteUuid + "/" + orderId,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
			"orderId":  orderId,
		},
	})

	assert.Nil(t, err)
	assert.Equal(t, http.StatusOK, res.StatusCode)

	var response pconnTypes.PosConnectorEvent
	_ = json.Unmarshal([]byte(res.Body), &response)

	rawOrder, _ := json.Marshal(response.Data)
	var order protocol.DeviceOrder
	_ = json.Unmarshal(rawOrder, &order)

	transaction := mockBillDataResponse.Transactions[0]
	location := tevalisclient.FindLocationFromFloorplans(&mockFloorplanResponse, mockBillDataResponse.Transactions[0].TableNumber)

	assert.Equal(t, order, tevalisclient.MapTransactionToOrder(ctx, transaction, *location))
}

func TestGetOrderNotFound(t *testing.T) {
	mockBillDataResponse := tevalisclient.LiveBillDataResponse{
		Count:        0,
		TotalGross:   0.0,
		TotalNet:     0.0,
		Transactions: []tevalisclient.Transaction{},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(mockBillJSON)
		return
	}

	server := httptest.NewServer(http.HandlerFunc(handler))
	defer server.Close()
	os.Setenv(string(env.EnvTevalisApiUrl), server.URL)

	tst := newTest()
	connection := addConnectionRecord(ctx, tst.entityUuid)
	addPairingRecord(ctx, tst.siteUuid, tst.entityUuid, connection.Venues[0].Id, nil)
	apiHandler := NewHandler(ctx)

	orderId := strconv.Itoa(test.GenerateRandomInt(5))
	res, err := apiHandler.HandleApiRequest(ctx, events.APIGatewayProxyRequest{
		HTTPMethod: http.MethodGet,
		Path:       "/v1/order/" + tst.siteUuid + "/" + orderId,
		PathParameters: map[string]string{
			"siteUuid": tst.siteUuid,
			"orderId":  orderId,
		},
	})

	assert.Equal(t, http.StatusNotFound, res.StatusCode)
	assert.Nil(t, err)
	assert.Equal(t, "order not found for site "+tst.siteUuid+" and orderId "+orderId, res.Body)
}
