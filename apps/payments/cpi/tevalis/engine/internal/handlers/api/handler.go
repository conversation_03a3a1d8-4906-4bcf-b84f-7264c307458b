package api

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/pairing"
)

type ApiName string

const (
	ORDER                 ApiName = "order"
	ORDERS                ApiName = "orders"
	UPDATE_ORDER_ITEM     ApiName = "updateOrderItem"
	CHECK_ORDERS_BALANCES ApiName = "checkOrdersBalances"
	PAYMENT               ApiName = "payment"
	HEALTH                ApiName = "health"
)

type ApiHandler struct {
	dbClient          *db.DynamoDb
	envService        env.IEnvironmentService
	pairingService    pairing.IPairingService
	connectionService connection.IConnectionService
	tevalisApiUrl     string
	zellerDevId       string
}

func NewHandler(ctx context.Context) *ApiHandler {
	dbClient := db.NewDynamoDb(ctx)
	envService := env.NewEnvService(ctx)
	pairingService := pairing.NewPairingService(ctx, dbClient, envService)
	connectionService := connection.NewConnectionService(ctx, dbClient, envService)
	tevalisApiUrl := envService.GetString(env.EnvTevalisApiUrl)
	zellerDevId := envService.GetString(env.EnvZellerDevId)
	return &ApiHandler{
		dbClient,
		envService,
		pairingService,
		connectionService,
		tevalisApiUrl,
		zellerDevId,
	}
}

func (h *ApiHandler) HandleApiRequest(ctx context.Context, event events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	logger.Debug(ctx, "API handler received request", "path", event.Path, "method", event.HTTPMethod)

	apiName, err := h.getApiName(event.Path)

	if err != nil {
		logger.Error(ctx, err.Error())
		// Handle the error and return a 404 response
		return events.APIGatewayProxyResponse{
			StatusCode: err.statusCode,
			Body:       err.Error(),
		}, nil
	}

	ctx = context.WithValue(ctx, logger.ApiName{}, apiName)

	switch apiName {
	case HEALTH:
		return h.handleHealth(ctx, event.Path)
	case ORDERS:
		return h.getOrdersHandler(ctx, event)
	case ORDER:
		return h.getOrderHandler(ctx, event)
	case PAYMENT:
		return h.paymentHandler(ctx, event)
	}

	return events.APIGatewayProxyResponse{StatusCode: 200}, nil
}

func (h *ApiHandler) handleHealth(ctx context.Context, path string) (events.APIGatewayProxyResponse, error) {
	logger.Trace(ctx, fmt.Sprintf("receive health check from path %s", path))
	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
	}, nil
}

func (h *ApiHandler) getApiName(path string) (ApiName, *ApiError) {
	var apiName ApiName
	var apiError *ApiError
	switch {
	case strings.Contains(path, "/orders/"):
		apiName = ORDERS
	case strings.Contains(path, "/order/"):
		apiName = ORDER
	case strings.Contains(path, "/payment/"):
		apiName = PAYMENT
	case strings.HasSuffix(path, "/health"):
		apiName = HEALTH
	default:
		err := NewError(NOT_FOUND, fmt.Sprintf("invalid api path '%s'", path), http.StatusNotFound)
		apiError = &err
	}

	return apiName, apiError
}
