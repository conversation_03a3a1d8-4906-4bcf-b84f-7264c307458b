package api

type API_ERROR string

const (
	SERVER_ERROR    API_ERROR = "server_error"
	INVALID_REQUEST API_ERROR = "invalid_request"
	UNKNOWN_ERROR   API_ERROR = "unknown_error"
	NOT_FOUND       API_ERROR = "not_found"
)

type ApiError struct {
	etype      API_ERROR
	message    string
	statusCode int
}

func NewError(etype API_ERROR, message string, statusCode int) ApiError {
	return ApiError{etype, message, statusCode}
}

func (e *ApiError) Error() string {
	return e.message
}
