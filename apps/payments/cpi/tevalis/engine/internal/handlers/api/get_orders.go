package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	tevalisclient "github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/tevalis_client"
	protocol "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	pconnTypes "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func (h *ApiHandler) getOrdersHandler(ctx context.Context, event events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	siteUuid, ok := event.PathParameters["siteUuid"]
	if !ok {
		errMsg := fmt.Sprintf("siteUuid not found in path %s", event.Path)
		logger.Error(ctx, errMsg)
		return events.APIGatewayProxyResponse{StatusCode: http.StatusBadRequest, Body: errMsg}, nil
	}
	ctx = logger.AddMetadata(ctx, "siteUuid", siteUuid)
	ctx = logger.AddMetadata(ctx, "action", string(pconnTypes.ActionGetOrders))

	orders := h.getOrders(ctx, siteUuid)

	response := pconnTypes.PosConnectorEvent{
		EventId:   ptr.String(uuid.NewString()),
		Timestamp: ptr.String(fmt.Sprintf("%d", time.Now().UnixMilli())),
		Action:    pconnTypes.ActionGetOrders,
		Data:      orders,
	}

	b, err := json.Marshal(response)
	if err != nil {
		logger.Error(ctx, err.Error())
		panic(err)
	}

	return events.APIGatewayProxyResponse{StatusCode: 200, Body: string(b)}, nil
}

func (h *ApiHandler) getOrders(ctx context.Context, siteUuid string) []protocol.DeviceOrder {
	pairData, err := h.pairingService.GetSitePairing(ctx, siteUuid)
	if err != nil {
		logger.Error(ctx, err.Error())
		return []protocol.DeviceOrder{}
	}

	venueId := pairData.VenueId
	entityUuid := pairData.EntityUuid

	ctx = logger.AddMetadata(ctx, "venueId", venueId)
	ctx = logger.AddMetadata(ctx, "entityUuid", entityUuid)

	logger.Debug(ctx, fmt.Sprintf("pairing data %s", utils.BestEffortStringify(pairData, false)))

	connectionRecord, err := h.connectionService.GetEntityConnection(ctx, entityUuid)
	if err != nil {
		logger.Error(ctx, err.Error())
		return []protocol.DeviceOrder{}
	}
	logger.Debug(ctx, fmt.Sprintf("connection record %s", utils.BestEffortStringify(connectionRecord, false)))

	credentials := connectionRecord.Credentials

	tevalis := tevalisclient.NewClient(
		h.tevalisApiUrl,
		credentials.CompanyId,
		credentials.Guid,
		h.zellerDevId,
		&credentials.Guid2,
	)

	result := tevalis.GetTables(ctx, entityUuid, venueId, pairData.Locations)
	logger.Info(ctx, fmt.Sprintf("get orders from TEVALIS, venue: %s, site %s, orders %v", venueId, siteUuid, utils.BestEffortStringify(result, false)))
	return result
}
