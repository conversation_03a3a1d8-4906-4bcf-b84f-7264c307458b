package api

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/aws/aws-lambda-go/events"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	tevalisclient "github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/tevalis_client"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
	pconnTypes "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

func (h *ApiHandler) paymentHandler(ctx context.Context, event events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	siteUuid, ok := event.PathParameters["siteUuid"]
	if !ok {
		errMsg := fmt.Sprintf("siteUuid not found in path %s", event.Path)
		logger.Error(ctx, errMsg)
		return events.APIGatewayProxyResponse{StatusCode: http.StatusBadRequest, Body: errMsg}, nil
	}
	ctx = logger.AddMetadata(ctx, "siteUuid", siteUuid)
	ctx = logger.AddMetadata(ctx, "action", string(pconnTypes.ActionPayment))

	var pconnEvent pconnTypes.PosConnectorEvent
	err := json.Unmarshal([]byte(event.Body), &pconnEvent)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to parse posconnector event %s", err.Error())
		logger.Error(ctx, errMsg)
		return events.APIGatewayProxyResponse{StatusCode: http.StatusBadRequest, Body: errMsg}, nil
	}

	var payment protocol.Payment
	jsonData, err := json.Marshal(pconnEvent.Data)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to marshal posonnector event data %s", err.Error())
		logger.Error(ctx, errMsg)
		return events.APIGatewayProxyResponse{StatusCode: http.StatusBadRequest, Body: errMsg}, nil
	}
	err = json.Unmarshal(jsonData, &payment)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to parse data to payment %s", err.Error())
		logger.Error(ctx, errMsg)
		return events.APIGatewayProxyResponse{StatusCode: http.StatusBadRequest, Body: errMsg}, nil
	}

	return h.payment(ctx, siteUuid, payment)
}

func (h *ApiHandler) payment(ctx context.Context, siteUuid string, payment protocol.Payment) (events.APIGatewayProxyResponse, error) {
	pairData, err := h.pairingService.GetSitePairing(ctx, siteUuid)
	if err != nil {
		logger.Error(ctx, err.Error())
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusForbidden,
			Body:       fmt.Sprintf("Pairing record not found for siteUuid %s", siteUuid),
		}, nil
	}

	venueId := pairData.VenueId
	entityUuid := pairData.EntityUuid

	ctx = logger.AddMetadata(ctx, "venueId", venueId)
	ctx = logger.AddMetadata(ctx, "entityUuid", entityUuid)

	logger.Debug(ctx, fmt.Sprintf("pairing data %s", utils.BestEffortStringify(pairData, false)))

	connectionRecord, err := h.connectionService.GetEntityConnection(ctx, entityUuid)
	if err != nil {
		logger.Error(ctx, err.Error())
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusForbidden,
			Body:       fmt.Sprintf("Connection record not found for entityUuid %s", entityUuid),
		}, nil
	}
	logger.Debug(ctx, fmt.Sprintf("connection record %s", utils.BestEffortStringify(connectionRecord, false)))

	credentials := connectionRecord.Credentials

	tevalis := tevalisclient.NewClient(
		h.tevalisApiUrl,
		credentials.CompanyId,
		credentials.Guid,
		h.zellerDevId,
		&credentials.Guid2,
	)

	err = tevalis.PostPayment(ctx, entityUuid, venueId, payment)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       err.Error(),
		}, nil
	}

	return events.APIGatewayProxyResponse{StatusCode: http.StatusOK}, nil
}
