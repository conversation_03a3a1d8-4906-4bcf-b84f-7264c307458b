package pairing

import (
	"context"
	"errors"
	"fmt"

	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/model"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/types"
	pconnTypes "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type IPairingService interface {
	GetSitePairing(ctx context.Context, siteUuid string) (*model.PosInterfacePairingModel, error)
}

type pairingService struct {
	dbClient   *db.DynamoDb
	envService env.IEnvironmentService
}

func NewPairingService(ctx context.Context, dbClient *db.DynamoDb, envService env.IEnvironmentService) IPairingService {
	return &pairingService{
		dbClient,
		envService,
	}
}

func (p *pairingService) GetSitePairing(ctx context.Context, siteUuid string) (*model.PosInterfacePairingModel, error) {
	var pairData []model.PosInterfacePairingModel
	err := p.dbClient.
		Query(p.envService.GetString(env.EnvComponentTable)).
		Index("typeGsi").Eq("type", fmt.Sprintf("%s%s", types.POSINTERFACE_PAIR_SITE, siteUuid)).
		Where().Eq("provider", pconnTypes.TEVALIS).
		ExecWithOutputStruct(ctx, &pairData)
	if err != nil {
		return nil, err
	}

	if len(pairData) == 0 {
		errMsg := fmt.Sprintf("no pairing data is found for site %s", siteUuid)
		return nil, errors.New(errMsg)
	}

	return &pairData[0], nil
}
