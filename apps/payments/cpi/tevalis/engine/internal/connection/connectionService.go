package connection

import (
	"context"
	"errors"
	"fmt"

	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/env"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/model"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/types"
	pconnTypes "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/types"
)

type IConnectionService interface {
	GetEntityConnection(ctx context.Context, entityUuid string) (*model.ConnectionPosInterfaceModel, error)
}

type connectionService struct {
	dbClient   *db.DynamoDb
	envService env.IEnvironmentService
}

func NewConnectionService(ctx context.Context, dbClient *db.DynamoDb, envService env.IEnvironmentService) IConnectionService {
	return &connectionService{
		dbClient,
		envService,
	}
}

func (p *connectionService) GetEntityConnection(ctx context.Context, entityUuid string) (*model.ConnectionPosInterfaceModel, error) {
	var connectionData []model.ConnectionPosInterfaceModel
	err := p.dbClient.Query(p.envService.GetString(env.EnvComponentTable)).
		Index("entityGsi").
		Eq("entityUuid", entityUuid).
		Eq("type", types.CONNECTION_POSINTERFACE).
		Where().Eq("provider", pconnTypes.TEVALIS).
		And().Exist("credentials").ExecWithOutputStruct(ctx, &connectionData)

	if err != nil {
		return nil, err
	}

	if len(connectionData) == 0 {
		errMsg := fmt.Sprintf("no connection record is found for entityUuid %s", entityUuid)
		return nil, errors.New(errMsg)
	}

	return &connectionData[0], nil
}
