package tevalisclient

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
)

// GetFloorplan fetches floorplan data for a specific site.
// Relative path e.g., "/Floorplans"
func (c *Client) GetFloorplan(
	ctx context.Context,
	siteID string,
	// companyID, guid, devID removed - use client's values
) (*FloorplanResponse, error) {
	log.Printf("GetFloorplan called: siteID=%s", siteID)

	// Append siteID to the path
	fullPath := fmt.Sprintf("%s/%s", ApiPathFloorplans, siteID)
	log.Printf("GetFloorplan path: %s", fullPath)

	// Use the client's doAPIRequest method
	bodyBytes, err := c.doAPIRequest(http.MethodGet, fullPath, nil, nil, false) // false: Don't use GUID2
	if err != nil {
		log.Printf("GetFloorplan request failed: %v", err)
		return nil, fmt.Errorf("GetFloorplan request failed: %w", err) // Reverted to wrapped error
	}

	var floorplanData FloorplanResponse
	err = json.Unmarshal(bodyBytes, &floorplanData)
	if err != nil {
		log.Printf("Failed to unmarshal JSON response: %v", err)
		return nil, fmt.Errorf("failed to unmarshal JSON response for GetFloorplan: %w", err)
	}

	log.Printf("GetFloorplan success: found %d areas", len(floorplanData.FloorplanArea))
	return &floorplanData, nil
}
