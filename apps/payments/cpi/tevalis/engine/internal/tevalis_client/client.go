package tevalisclient

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"slices"
	"strconv"

	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	protocol "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
)

// ============================================================================
// API Client
// ============================================================================

type IClient interface {
	GetTable(ctx context.Context, entityUuid string, siteId string, transactionId string) *protocol.DeviceOrder
	GetTables(ctx context.Context, entityUuid string, siteId string, locationIds []string) []protocol.DeviceOrder
	PostPayment(ctx context.Context, entityUuid string, siteId string, payment protocol.Payment) error
}

// Client holds the configuration and state for interacting with the Tevalis API.
type Client struct {
	baseURL    string
	companyID  string
	guid       string
	devID      string
	guid2      *string // Optional GUID2, required for some endpoints
	httpClient *http.Client
}

// NewClient creates a new Tevalis API client.
func NewClient(baseURL, companyID, guid, devID string, guid2 *string) IClient {
	return &Client{
		baseURL:   baseURL,
		companyID: companyID,
		guid:      guid,
		devID:     devID,
		guid2:     guid2,
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}
}

func (c *Client) GetTable(ctx context.Context, entityUuid string, siteId string, transactionId string) *protocol.DeviceOrder {
	// Implementation for GetTable
	billData, err := c.GetLiveBillDataByID(ctx, siteId, transactionId)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to get live bill data: %v", err))
		return nil
	}

	if len(billData.Transactions) <= 0 {
		logger.Error(ctx, fmt.Sprintf("No transactions found for siteId %s and tableId %s", siteId, transactionId))
		return nil
	}
	bill := billData.Transactions[0]

	floorplans, err := c.GetFloorplan(ctx, siteId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to get live bill data: %v", err))
		return nil
	}
	location := FindLocationFromFloorplans(floorplans, bill.TableNumber)

	if location == nil {
		logger.Error(ctx, fmt.Sprintf("No location found for siteId %s and tableId %s", siteId, transactionId))
		return nil
	}

	order := MapTransactionToOrder(ctx, billData.Transactions[0], *location)

	return &order
}

func (c *Client) GetTables(ctx context.Context, entityUuid string, siteId string, locationIds []string) []protocol.DeviceOrder {
	liveBillData, err := c.GetLiveBillData(ctx, siteId, false) // false for transactionCompleteFilter to get open bills
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to get live bill data for site %s: %v", siteId, err))
		return []protocol.DeviceOrder{}
	}

	if liveBillData == nil || len(liveBillData.Transactions) == 0 {
		logger.Info(ctx, fmt.Sprintf("No transactions found for siteId %s", siteId))
		return []protocol.DeviceOrder{}
	}

	floorplans, err := c.GetFloorplan(ctx, siteId)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Failed to get floorplan for site %s: %v", siteId, err))
		return []protocol.DeviceOrder{}
	}

	var orders []protocol.DeviceOrder

	for _, transaction := range liveBillData.Transactions {
		location := FindLocationFromFloorplans(floorplans, transaction.TableNumber)
		if location == nil {
			logger.Warn(ctx, fmt.Sprintf("No location found for table number %d in siteId %s", transaction.TableNumber, siteId))
			continue
		}

		if len(locationIds) > 0 {
			if location.LocationId == nil || !slices.Contains(locationIds, *location.LocationId) {
				continue // Skip if locationId is nil or not in the provided list
			}
		}

		order := MapTransactionToOrder(ctx, transaction, *location)
		orders = append(orders, order)
	}

	logger.Info(ctx, fmt.Sprintf("GetTables for siteId %s found %d orders after filtering", siteId, len(orders)))
	return orders
}

func (c *Client) PostPayment(ctx context.Context, entityUuid string, siteId string, payment protocol.Payment) error {
	if payment.OrderId == nil {
		errMsg := "OrderID is missing in payment request for PostPayment"
		logger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}
	transactionIdInt, err := strconv.Atoi(*payment.OrderId)
	if err != nil {
		errMsg := fmt.Sprintf("Invalid OrderID format for PostPayment: %s, error: %v", *payment.OrderId, err)
		logger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}
	siteIdInt, err := strconv.Atoi(siteId)
	if err != nil {
		errMsg := fmt.Sprintf("Invalid siteId format for PostPayment: %s, error: %v", siteId, err)
		logger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}

	logger.Info(ctx, fmt.Sprintf("Attempting GetTablePaymentRequest for SiteID: %s, TransactionID: %d", siteId, transactionIdInt))

	paymentRequestResponse, err := c.GetTablePaymentRequest(siteId, *payment.OrderId)
	if err != nil {
		errMsg := fmt.Sprintf("GetTablePaymentRequest failed for TransactionID %d: %v", transactionIdInt, err)
		logger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}

	if len(paymentRequestResponse) == 0 || !paymentRequestResponse[0].IsSuccess || len(paymentRequestResponse[0].TableListItems) == 0 {
		errMsg := "GetTablePaymentRequest was not successful or returned empty response"
		logger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}

	logger.Info(ctx, fmt.Sprintf("GetTablePaymentRequest successful for TransactionID %d. Message: %s", transactionIdInt, paymentRequestResponse[0].Message))

	return c.postTablePayment(ctx, siteIdInt, transactionIdInt, payment, paymentRequestResponse[0])
}

func (c *Client) postTablePayment(ctx context.Context, siteId int, transactionId int, payment protocol.Payment, paymentRequestResponse TablePaymentResponseItem) error {
	var paymentItems []TablePaymentItem
	if payment.Amount == nil {
		errMsg := "amount is missing in payment request for PostPayment"
		logger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}

	logger.Info(ctx, fmt.Sprintf("Payment request for TransactionID: %d %s", transactionId, utils.BestEffortStringify(payment, false)))

	baseAmount := *payment.Amount
	if payment.Surcharge != nil && *payment.Surcharge > 0 {
		baseAmount -= *payment.Surcharge
	}

	gratuity := 0
	if payment.Tip != nil && *payment.Tip > 0 {
		baseAmount -= *payment.Tip
		paymentItems = append([]TablePaymentItem{
			{
				PaymentType: "Zeller Pay At Table Tip",
				Value:       *payment.Tip,
			},
		}, paymentItems...)
		gratuity = *payment.Tip
	}

	paymentItems = append([]TablePaymentItem{
		{
			PaymentType: "Zeller Pay At Table",
			Value:       baseAmount,
		},
	}, paymentItems...)

	// Determine the balance value from the paymentRequestResponse
	closeTableOnRequest := false
	balance := paymentRequestResponse.TableListItems[0].BalanceValue
	amount := baseAmount
	if gratuity > 0 {
		balance += gratuity
		amount += gratuity
	}
	if amount >= balance {
		closeTableOnRequest = true

		if amount > balance {
			errMsg := "amount is greater than balance. Cannot overpay a table"
			logger.Warn(ctx, errMsg)
			return errors.New(errMsg)
		}
	}

	tablePaymentPostBody := TablePaymentPostRequest{
		SiteId:              siteId,
		TransactionId:       transactionId,
		CloseTableOnRequest: closeTableOnRequest,
		TablePaymentItems:   paymentItems,
		Gratuity:            gratuity,
	}

	logger.Info(ctx, fmt.Sprintf("Attempting PostTablePayment for TransactionID: %d with %d item(s)", transactionId, len(paymentItems)))

	postPaymentResponse, err := c.PostTablePayment(tablePaymentPostBody)
	if err != nil {
		errMsg := fmt.Sprintf("PostTablePayment failed for TransactionID %d: %v", transactionId, err)
		logger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}

	if len(postPaymentResponse) == 0 || !postPaymentResponse[0].IsSuccess {
		errMsg := "PostTablePayment was not successful or returned empty response."
		if len(postPaymentResponse) > 0 {
			errMsg = fmt.Sprintf("PostTablePayment not successful for TransactionID %d: %s", transactionId, postPaymentResponse[0].Message)
		}
		logger.Error(ctx, errMsg)
		return errors.New(errMsg)
	}

	logger.Info(ctx, fmt.Sprintf("PostTablePayment successful for TransactionID %d. Message: %s", transactionId, postPaymentResponse[0].Message))
	return nil
}
