package tevalisclient

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"testing"

	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	protocol "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
)

func TestPostPaymentInvalidFields(t *testing.T) {
	testCases := []protocol.Payment{
		{
			OrderId:         ptr.String("not-an-integer"), // Invalid format
			Amount:          ptr.Int(4450),
			Tip:             ptr.Int(0),
			Surcharge:       ptr.Int(150),
			AccountType:     ptr.String("Zeller Pay At Table"),
			TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
			TransactionUuid: ptr.String(uuid.NewString()),
		},
		{
			OrderId:         nil, // Missing OrderId
			Amount:          ptr.Int(4450),
			Tip:             ptr.Int(0),
			Surcharge:       ptr.Int(150),
			AccountType:     ptr.String("Zeller Pay At Table"),
			TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
			TransactionUuid: ptr.String(uuid.NewString()),
		},
		{
			OrderId:         ptr.String("28"),
			Amount:          nil, // Missing Amount
			Tip:             ptr.Int(0),
			Surcharge:       ptr.Int(150),
			AccountType:     ptr.String("Zeller Pay At Table"),
			TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
			TransactionUuid: ptr.String(uuid.NewString()),
		},
	}

	for _, mockPayment := range testCases {
		t.Run("TestPostPaymentInvalidFields", func(t *testing.T) {
			handler := func(w http.ResponseWriter, r *http.Request) {
				if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
					response := []TablePaymentResponseItem{{
						TableListItems: []TableListItem{
							{
								TransactionId: 28,
								TotalValue:    1700,
								BalanceValue:  1700,
							},
						},
						IsSuccess: true,
						Message:   "Table available",
					}}
					jsonResponse, _ := json.Marshal(response)
					w.WriteHeader(http.StatusOK)
					_, _ = w.Write(jsonResponse)
					return
				}
				t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
			}

			server, client := setupTestServerAndClient(t, handler)
			defer server.Close()

			resp := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)
			assert.NotNil(t, resp)
		})
	}
}

func TestPostPaymentSuccessNoTip(t *testing.T) {
	mockPayment := protocol.Payment{
		OrderId:         ptr.String("27"),
		Amount:          ptr.Int(4450),
		Tip:             ptr.Int(0),
		Surcharge:       ptr.Int(150),
		AccountType:     ptr.String("Zeller Pay At Table"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}
	transactionIDStr := "27"

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
			assert.Equal(t, "8913", r.URL.Query().Get("SiteId"))
			assert.Equal(t, transactionIDStr, r.URL.Query().Get("TransactionId"))

			response := []TablePaymentResponseItem{{
				TableListItems: []TableListItem{
					{
						TransactionId: 28,
						TotalValue:    7400,
						BalanceValue:  7400,
					},
				},
				IsSuccess: true,
				Message:   "Table available for payment",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}

		if r.Method == http.MethodPost && strings.Contains(r.URL.Path, ApiPathTablePaymentPost) {
			var reqBody TablePaymentPostRequest
			err := json.NewDecoder(r.Body).Decode(&reqBody)
			assert.NoError(t, err)

			assert.Equal(t, 8913, reqBody.SiteId)
			assert.Equal(t, 27, reqBody.TransactionId)
			assert.False(t, reqBody.CloseTableOnRequest)
			assert.Len(t, reqBody.TablePaymentItems, 1)
			assert.Equal(t, "Zeller Pay At Table", reqBody.TablePaymentItems[0].PaymentType)
			assert.Equal(t, *mockPayment.Amount-*mockPayment.Surcharge, reqBody.TablePaymentItems[0].Value)
			assert.Equal(t, 0, reqBody.Gratuity)

			response := []TablePaymentResponseItem{{IsSuccess: true, Message: "Payment successful"}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	resp := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)
	assert.Nil(t, resp)
}

func TestPostPaymentSuccessWithTip(t *testing.T) {
	mockPayment := protocol.Payment{
		OrderId:         ptr.String("28"),
		Amount:          ptr.Int(2222),
		Tip:             ptr.Int(500),
		Surcharge:       ptr.Int(22),
		AccountType:     ptr.String("Zeller Card"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}
	transactionIDStr := "28"

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
			assert.Equal(t, "8913", r.URL.Query().Get("SiteId"))
			assert.Equal(t, transactionIDStr, r.URL.Query().Get("TransactionId"))

			response := []TablePaymentResponseItem{{
				TableListItems: []TableListItem{
					{
						TransactionId: 28,
						TotalValue:    1700,
						BalanceValue:  1700,
					},
				},
				IsSuccess: true,
				Message:   "Table available",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}

		if r.Method == http.MethodPost && strings.Contains(r.URL.Path, ApiPathTablePaymentPost) {
			var reqBody TablePaymentPostRequest
			err := json.NewDecoder(r.Body).Decode(&reqBody)
			assert.NoError(t, err)

			assert.Equal(t, 8913, reqBody.SiteId)
			assert.Equal(t, 28, reqBody.TransactionId)
			assert.True(t, reqBody.CloseTableOnRequest)
			assert.Len(t, reqBody.TablePaymentItems, 2)
			assert.Equal(t, "Zeller Pay At Table", reqBody.TablePaymentItems[0].PaymentType)
			assert.Equal(t, *mockPayment.Amount-*mockPayment.Surcharge-*mockPayment.Tip, reqBody.TablePaymentItems[0].Value)
			assert.Equal(t, "Zeller Pay At Table Tip", reqBody.TablePaymentItems[1].PaymentType)
			assert.Equal(t, *mockPayment.Tip, reqBody.TablePaymentItems[1].Value)
			assert.Equal(t, *mockPayment.Tip, reqBody.Gratuity)

			response := []TablePaymentResponseItem{{
				TableListItems: []TableListItem{
					{
						TransactionId: 28,
					},
				},
				IsSuccess: true,
				Message:   "Payment with tip successful",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	err := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)
	assert.Nil(t, err)
}

func TestPostPaymentSplitPayment(t *testing.T) {
	mockPayment := protocol.Payment{
		OrderId:         ptr.String("28"),
		Amount:          ptr.Int(2000),
		Tip:             ptr.Int(0),
		Surcharge:       ptr.Int(0),
		AccountType:     ptr.String("Zeller Card"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}
	transactionIDStr := "28"

	callCount := 0

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
			assert.Equal(t, "8913", r.URL.Query().Get("SiteId"))
			assert.Equal(t, transactionIDStr, r.URL.Query().Get("TransactionId"))

			balanceValue := 4000
			if callCount == 1 {
				balanceValue = 2000
			}

			response := []TablePaymentResponseItem{{
				TableListItems: []TableListItem{
					{
						TransactionId: 28,
						TotalValue:    4000,
						BalanceValue:  balanceValue,
					},
				},
				IsSuccess: true,
				Message:   "Table available",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}

		if r.Method == http.MethodPost && strings.Contains(r.URL.Path, ApiPathTablePaymentPost) {
			var reqBody TablePaymentPostRequest
			err := json.NewDecoder(r.Body).Decode(&reqBody)
			assert.NoError(t, err)

			clostTableOnRequest := false
			if callCount == 1 {
				clostTableOnRequest = true
			}

			assert.Equal(t, 8913, reqBody.SiteId)
			assert.Equal(t, 28, reqBody.TransactionId)
			assert.Equal(t, clostTableOnRequest, reqBody.CloseTableOnRequest)
			assert.Len(t, reqBody.TablePaymentItems, 1)
			assert.Equal(t, "Zeller Pay At Table", reqBody.TablePaymentItems[0].PaymentType)
			assert.Equal(t, *mockPayment.Amount-*mockPayment.Surcharge, reqBody.TablePaymentItems[0].Value)

			response := []TablePaymentResponseItem{{
				TableListItems: []TableListItem{
					{
						TransactionId: 28,
					},
				},
				IsSuccess: true,
				Message:   "Payment successful",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	err := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)
	assert.Nil(t, err)

	err = client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)
	assert.Nil(t, err)
}

func TestPostPaymentOverpayment(t *testing.T) {
	mockPayment := protocol.Payment{
		OrderId:         ptr.String("28"),
		Amount:          ptr.Int(20000),
		Tip:             ptr.Int(500),
		Surcharge:       ptr.Int(0),
		AccountType:     ptr.String("Zeller Card"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}
	transactionIDStr := "28"

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
			assert.Equal(t, "8913", r.URL.Query().Get("SiteId"))
			assert.Equal(t, transactionIDStr, r.URL.Query().Get("TransactionId"))

			response := []TablePaymentResponseItem{{
				TableListItems: []TableListItem{
					{
						TransactionId: 28,
						TotalValue:    1700,
						BalanceValue:  1700,
					},
				},
				IsSuccess: true,
				Message:   "Table available",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	err := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)
	assert.NotNil(t, err)
}

func TestPostPaymentFailureGetTablePaymentRequestHTTPError(t *testing.T) {
	mockPayment := protocol.Payment{
		OrderId:         ptr.String("29"),
		Amount:          ptr.Int(1000),
		Tip:             ptr.Int(0),
		AccountType:     ptr.String("Cash"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
			w.WriteHeader(http.StatusInternalServerError) // Simulate server error
			_, _ = w.Write([]byte("Internal Server Error"))
			return
		}
		// PostTablePayment should not be called
		t.Fatalf("Unexpected request to %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	err := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)

	assert.NotNil(t, err)
}

func TestPostPaymentFailureGetTablePaymentRequestNotSuccessful(t *testing.T) {
	mockPayment := protocol.Payment{
		OrderId:         ptr.String("30"),
		Amount:          ptr.Int(2000),
		Tip:             ptr.Int(0),
		AccountType:     ptr.String("Voucher"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
			response := []TablePaymentResponseItem{{IsSuccess: false, Message: "Table locked by another process"}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}
		// PostTablePayment should not be called
		t.Fatalf("Unexpected request to %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	err := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)

	assert.NotNil(t, err)
}

func TestPostPaymentFailurePostTablePaymentHTTPError(t *testing.T) {
	mockPayment := protocol.Payment{
		OrderId:         ptr.String("31"),
		Amount:          ptr.Int(2500),
		Tip:             ptr.Int(0),
		AccountType:     ptr.String("Card"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
			response := []TablePaymentResponseItem{{
				TableListItems: []TableListItem{
					{
						TransactionId: 28,
						TotalValue:    3000,
						BalanceValue:  3000,
					},
				},
				IsSuccess: true,
				Message:   "Table ready for payment",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}

		if r.Method == http.MethodPost && strings.Contains(r.URL.Path, ApiPathTablePaymentPost) {
			w.WriteHeader(http.StatusConflict) // Simulate a conflict error
			_, _ = w.Write([]byte("Payment conflict"))
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	err := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)

	assert.NotNil(t, err)
}

func TestPostPaymentFailurePostTablePaymentNotSuccessful(t *testing.T) {
	mockPayment := protocol.Payment{
		OrderId:         ptr.String("32"),
		Amount:          ptr.Int(3000),
		Tip:             ptr.Int(0),
		AccountType:     ptr.String("Points"),
		TransactionTime: ptr.String("2024-01-01T00:00:00Z"),
		TransactionUuid: ptr.String(uuid.NewString()),
	}

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method == http.MethodGet && strings.Contains(r.URL.Path, ApiPathTablePaymentRequest) {
			response := []TablePaymentResponseItem{{
				TableListItems: []TableListItem{
					{
						TransactionId: 28,
						TotalValue:    3000,
						BalanceValue:  3000,
					},
				},
				IsSuccess: true,
				Message:   "Table ready for payment",
			}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}

		if r.Method == http.MethodPost && strings.Contains(r.URL.Path, ApiPathTablePaymentPost) {
			response := []TablePaymentResponseItem{{IsSuccess: false, Message: "Payment method not accepted"}}
			jsonResponse, _ := json.Marshal(response)
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(jsonResponse)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	err := client.PostPayment(context.Background(), uuid.NewString(), "8913", mockPayment)

	assert.NotNil(t, err)
}

/*
============================================================================
Payments Endpoint Tests
============================================================================
*/
