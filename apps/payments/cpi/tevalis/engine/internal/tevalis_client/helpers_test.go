package tevalisclient

import (
	"context"
	"testing"

	"github.com/aws/smithy-go/ptr"
	"github.com/stretchr/testify/assert"
	protocol "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
)

func TestFloatToCents(t *testing.T) {
	tests := []struct {
		name     string
		input    float64
		expected int
	}{
		{"zero value", 0.00, 0},
		{"simple dollar", 1.00, 100},
		{"dollars and cents", 123.45, 12345},
		{"only cents", 0.99, 99},
		{"fractional cents (should round)", 1.239, 124},
		{"negative value", -5.50, -550},
		{"large value (precise float)", 1234567.80, 123456780},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			actual := FloatToCents(tt.input)
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func TestIsRegularItem(t *testing.T) {
	tests := []struct {
		name     string
		item     Item
		expected bool
	}{
		{
			name:     "Regular item with positive ExpGross",
			item:     Item{ProductID: 1, ItemName: "Burger", ExpGross: 10.00, Quantity: 1},
			expected: true,
		},
		{
			name:     "Payment item (ProductID 8)",
			item:     Item{ProductID: 8, ItemName: "Card Payment", ExpGross: 10.00, Quantity: 1},
			expected: false,
		},
		{
			name:     "Gratuity item (ProductID 12)",
			item:     Item{ProductID: 12, ItemName: "Tip", ExpGross: 5.00, Quantity: 1},
			expected: false,
		},
		{
			name:     "Item with zero ExpGross",
			item:     Item{ProductID: 1, ItemName: "Free Item", ExpGross: 0.00, Quantity: 1},
			expected: false,
		},
		{
			name:     "Item with negative ExpGross",
			item:     Item{ProductID: 1, ItemName: "Discount Item", ExpGross: -2.00, Quantity: 1},
			expected: false,
		},
		{
			name:     "Regular item with different ProductID",
			item:     Item{ProductID: 100, ItemName: "Drink", ExpGross: 3.50, Quantity: 1},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			actual := IsRegularItem(tt.item)
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func TestGetSiteUuidFromPath(t *testing.T) {
	tests := []struct {
		name     string
		path     string
		expected string
	}{
		{
			name:     "Valid path with siteUuid",
			path:     "/v1/orders/some-uuid-12345/items",
			expected: "some-uuid-12345",
		},
		{
			name:     "Valid path, siteUuid is the last element",
			path:     "/v1/orders/another-uuid",
			expected: "another-uuid",
		},
		{
			name:     "Path too short",
			path:     "/v1/orders",
			expected: "",
		},
		{
			name:     "Empty path",
			path:     "",
			expected: "",
		},
		{
			name:     "Path with no slashes",
			path:     "justastring",
			expected: "",
		},
		{
			name:     "Path with leading/trailing slashes and correct structure",
			path:     "///v1/orders/uuid-from-messy-path//",
			expected: "uuid-from-messy-path",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := GetSiteUuidFromPath(tt.path)
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func makeItem(id string, amount, qty, total int, related []protocol.Item) protocol.Item {
	q := float64(qty)
	return protocol.Item{
		ItemId:       ptr.String(id),
		Amount:       ptr.Int(amount),
		Quantity:     ptr.Float64(q),
		TotalAmount:  ptr.Int(total),
		RelatedItems: related,
	}
}

func TestGroupItemsByItemIdAndAmount(t *testing.T) {
	ctx := context.Background()

	t.Run("groups items by ItemId and Amount, sums Quantity and TotalAmount", func(t *testing.T) {
		items := []protocol.Item{
			makeItem("1", 100, 1, 100, nil),
			makeItem("1", 100, 2, 200, nil),
			makeItem("2", 200, 1, 200, nil),
		}
		grouped := groupItemsByItemIdAndAmount(ctx, items)
		assert.Len(t, grouped, 2)
		for _, item := range grouped {
			if *item.ItemId == "1" {
				assert.Equal(t, 3.0, *item.Quantity)
				assert.Equal(t, 300, *item.TotalAmount)
			} else if *item.ItemId == "2" {
				assert.Equal(t, 1.0, *item.Quantity)
				assert.Equal(t, 200, *item.TotalAmount)
			}
		}
	})

	t.Run("skips items with nil fields", func(t *testing.T) {
		items := []protocol.Item{
			{ItemId: nil, Amount: ptr.Int(100), Quantity: ptr.Float64(1), TotalAmount: ptr.Int(100)},
			{ItemId: ptr.String("1"), Amount: nil, Quantity: ptr.Float64(1), TotalAmount: ptr.Int(100)},
			{ItemId: ptr.String("1"), Amount: ptr.Int(100), Quantity: nil, TotalAmount: ptr.Int(100)},
			{ItemId: ptr.String("1"), Amount: ptr.Int(100), Quantity: ptr.Float64(1), TotalAmount: nil},
			makeItem("2", 200, 1, 200, nil),
		}
		grouped := groupItemsByItemIdAndAmount(ctx, items)
		assert.Len(t, grouped, 1)
		assert.Equal(t, "2", *grouped[0].ItemId)
	})

	t.Run("skips items with TotalAmount == 0", func(t *testing.T) {
		items := []protocol.Item{
			makeItem("1", 100, 1, 0, nil),
			makeItem("2", 200, 1, 200, nil),
		}
		grouped := groupItemsByItemIdAndAmount(ctx, items)
		assert.Len(t, grouped, 1)
		assert.Equal(t, "2", *grouped[0].ItemId)
	})

	t.Run("preserves order of first appearance", func(t *testing.T) {
		items := []protocol.Item{
			makeItem("a", 1, 1, 10, nil),
			makeItem("b", 2, 1, 20, nil),
			makeItem("a", 1, 2, 20, nil),
		}
		grouped := groupItemsByItemIdAndAmount(ctx, items)
		assert.Equal(t, "a", *grouped[0].ItemId)
		assert.Equal(t, "b", *grouped[1].ItemId)
	})

	t.Run("recursively groups RelatedItems", func(t *testing.T) {
		related := []protocol.Item{
			makeItem("x", 5, 1, 5, nil),
			makeItem("x", 5, 2, 10, nil),
		}
		items := []protocol.Item{
			makeItem("p", 10, 1, 10, related),
			makeItem("p", 10, 2, 20, related),
		}
		grouped := groupItemsByItemIdAndAmount(ctx, items)
		assert.Len(t, grouped, 1)
		g := grouped[0]
		assert.Equal(t, 3.0, *g.Quantity)
		assert.Equal(t, 30, *g.TotalAmount)
		assert.Len(t, g.RelatedItems, 1)
		assert.Equal(t, "x", *g.RelatedItems[0].ItemId)
		assert.Equal(t, 6.0, *g.RelatedItems[0].Quantity)
		assert.Equal(t, 30, *g.RelatedItems[0].TotalAmount)
	})

	t.Run("empty input returns empty slice", func(t *testing.T) {
		grouped := groupItemsByItemIdAndAmount(ctx, nil)
		assert.Empty(t, grouped)
	})

	t.Run("all items invalid returns empty slice", func(t *testing.T) {
		items := []protocol.Item{
			{ItemId: nil, Amount: nil, Quantity: nil, TotalAmount: nil},
		}
		grouped := groupItemsByItemIdAndAmount(ctx, items)
		assert.Empty(t, grouped)
	})
}

func TestGroupItemsItemIdNil(t *testing.T) {
	ctx := context.Background()

	t.Run("skips items with nil ItemId", func(t *testing.T) {
		items := []protocol.Item{
			{ItemId: nil, Amount: ptr.Int(100), Quantity: ptr.Float64(1), TotalAmount: ptr.Int(100)},
			{ItemId: ptr.String("1"), Amount: ptr.Int(200), Quantity: ptr.Float64(2), TotalAmount: ptr.Int(400)},
		}
		grouped := groupItemsByItemIdAndAmount(ctx, items)
		assert.Len(t, grouped, 1)
		assert.Equal(t, "1", *grouped[0].ItemId)
	})
}

func TestMapTransactionToOrder_WithTenders_TriggersTenderLoop(t *testing.T) {
	ctx := context.Background()

	// Minimal protocol.Location. Adapt to your actual struct definition.
	// If your protocol.Location has required fields, populate them minimally.
	location := protocol.Location{
		// Example: ID: "test-location-1",
	}

	// Create a transaction with at least one tender to trigger the loop
	transactionWithTenders := Transaction{
		TransactionID: 123,
		TableNumber:   7,
		TotalGross:    100.00,
		Tenders: []Tender{ // This non-empty slice ensures the loop is triggered
			{TenderName: "Cash", Amount: 50.00},
			{TenderName: "Card", Amount: 25.00},
		},
		Items:         []Item{}, // Keep items empty for simplicity in this specific test
		ServiceCharge: 0.0,
	}

	// Expected owed amount: 100.00 (TotalGross) - (50.00 + 25.00) (Tenders) = 25.00
	expectedOwedAmountCents := FloatToCents(100.00 - 50.00 - 25.00) // 2500

	// Call the function under test
	// You might need to replace `Location` and `DeviceOrder` with `protocol.Location`
	// and `protocol.DeviceOrder` depending on your actual function signature.
	order := MapTransactionToOrder(ctx, transactionWithTenders, location)

	// Assert that OwedAmount reflects the tender processing
	// This implicitly confirms the loop was executed.
	assert.NotNil(t, order.OwedAmount, "OwedAmount should be calculated")
	if order.OwedAmount != nil { // Check for nil to prevent panic on dereference
		assert.Equal(t, expectedOwedAmountCents, *order.OwedAmount, "OwedAmount should be TotalGross minus sum of tender amounts")
	}

	// You can also add a very basic check to ensure the function ran through, e.g.:
	assert.NotNil(t, order.OrderId, "OrderId should be populated")
	assert.Equal(t, ptr.String("123"), order.OrderId) // Assuming ptr.String is your helper
}

func TestMapTransactionToOrder_TenderLoopVariants(t *testing.T) {
	ctx := context.Background()
	location := protocol.Location{}

	tests := []struct {
		name         string
		transaction  Transaction
		expectedOwed int
	}{
		{
			name: "No tenders",
			transaction: Transaction{
				TransactionID: 1,
				TableNumber:   1,
				TotalGross:    80.00,
				Tenders:       nil,
				Items:         []Item{},
				ServiceCharge: 0.0,
			},
			expectedOwed: FloatToCents(80.00),
		},
		{
			name: "Single tender full amount",
			transaction: Transaction{
				TransactionID: 2,
				TableNumber:   2,
				TotalGross:    60.00,
				Tenders:       []Tender{{TenderName: "Card", Amount: 60.00}},
				Items:         []Item{},
				ServiceCharge: 0.0,
			},
			expectedOwed: 0,
		},
		{
			name: "Multiple tenders, partial payment",
			transaction: Transaction{
				TransactionID: 3,
				TableNumber:   3,
				TotalGross:    120.00,
				Tenders:       []Tender{{TenderName: "Cash", Amount: 50.00}, {TenderName: "Voucher", Amount: 20.00}},
				Items:         []Item{},
				ServiceCharge: 0.0,
			},
			expectedOwed: FloatToCents(120.00 - 70.00),
		},
		{
			name: "Negative tender (refund)",
			transaction: Transaction{
				TransactionID: 4,
				TableNumber:   4,
				TotalGross:    100.00,
				Tenders:       []Tender{{TenderName: "Refund", Amount: -10.00}},
				Items:         []Item{},
				ServiceCharge: 0.0,
			},
			expectedOwed: FloatToCents(100.00 - (-10.00)), // 110.00
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			order := MapTransactionToOrder(ctx, tt.transaction, location)
			assert.NotNil(t, order.OwedAmount)
			if order.OwedAmount != nil {
				assert.Equal(t, tt.expectedOwed, *order.OwedAmount)
			}
		})
	}
}
