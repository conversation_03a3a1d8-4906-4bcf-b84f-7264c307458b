package tevalisclient

import (
	"bytes"
	"fmt"
	"net/http"
	"testing"

	"github.com/aws/smithy-go/ptr"
	"github.com/stretchr/testify/assert"
)

func TestApiSuccess(t *testing.T) {
	// Test the API client
	server := setupTestServer(t, func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
	defer server.Close()
	client := &Client{
		baseURL:   server.URL,
		guid:      "dummy-guid",
		devID:     "dummy-dev-id",
		companyID: "dummy-company-id",
		guid2:     nil,
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}

	res, err := client.doAPIRequest("GET", "/test", nil, nil, false)
	assert.NoError(t, err)
	assert.NotNil(t, res)

	res, err = client.doAPIRequest("GET", server.URL, nil, nil, false)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}

func TestApiSuccessWithBody(t *testing.T) {
	// Test the API client
	server := setupTestServer(t, func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
	defer server.Close()
	client := &Client{
		baseURL:   server.URL,
		guid:      "dummy-guid",
		devID:     "dummy-dev-id",
		companyID: "dummy-company-id",
		guid2:     ptr.String("dummy-guid2"),
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}

	body := []byte(`{"key":"value"}`)
	bodyReader := bytes.NewReader(body)

	res, err := client.doAPIRequest("GET", "/test", nil, bodyReader, true)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}

func TestApiSuccessWithBodyGUID2Required(t *testing.T) {
	// Test the API client
	server := setupTestServer(t, func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})
	defer server.Close()
	client := &Client{
		baseURL:   server.URL,
		guid:      "dummy-guid",
		devID:     "dummy-dev-id",
		companyID: "dummy-company-id",
		guid2:     nil,
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}

	body := []byte(`{"key":"value"}`)
	bodyReader := bytes.NewReader(body)

	res, err := client.doAPIRequest("GET", "/test", nil, bodyReader, true)
	assert.Error(t, err, fmt.Errorf("request requires GUID2, but it's not configured"))
	assert.Nil(t, res)
}

func TestApiStatusCodes(t *testing.T) {
	testCases := []int{
		http.StatusBadRequest,
		http.StatusConflict,
		http.StatusForbidden,
		http.StatusUnauthorized,
		http.StatusInternalServerError,
	}

	for _, testStatusCode := range testCases {
		t.Run(fmt.Sprintf("StatusCode %d", testStatusCode), func(t *testing.T) {
			server := setupTestServer(t, func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(testStatusCode)
			})
			defer server.Close()
			client := &Client{
				baseURL:   server.URL,
				guid:      "dummy-guid",
				devID:     "dummy-dev-id",
				companyID: "dummy-company-id",
				guid2:     nil,
				httpClient: &http.Client{
					Timeout: defaultTimeout,
				},
			}
			res, err := client.doAPIRequest("GET", "/test", nil, nil, false)
			assert.Error(t, err)
			assert.Nil(t, res)
			assert.Equal(t, testStatusCode, err.(*APIError).StatusCode)
			assert.Equal(t, fmt.Sprintf("API returned status %d", testStatusCode), err.Error())
		})
	}
}

func TestInvalidURL(t *testing.T) {
	client := &Client{
		baseURL:   "://bad-url",
		guid:      "dummy-guid",
		devID:     "dummy-dev-id",
		companyID: "dummy-company-id",
		guid2:     nil,
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}

	res, err := client.doAPIRequest("GET", "invalid-url", nil, nil, false)
	assert.Error(t, err)
	assert.Nil(t, res)
}
