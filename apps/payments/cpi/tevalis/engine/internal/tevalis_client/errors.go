package tevalisclient

// APIError represents an error returned by the API
type APIError struct {
	StatusCode int    // HTTP status code
	Message    string // Error message
	Body       string // Response body
	RateLimit  bool   // Whether this was a rate limit error
	Retriable  bool   // Whether this error might be retriable
}

// Error implements the error interface
func (e *APIError) Error() string {
	return e.Message
}

// GetStatusCode returns the HTTP status code
func (e *APIError) GetStatusCode() int {
	return e.StatusCode
}

// IsRateLimit returns whether this was a rate limit error
func (e *APIError) IsRateLimit() bool {
	return e.RateLimit
}

// IsRetriable returns whether this error might be retriable
func (e *APIError) IsRetriable() bool {
	return e.Retriable
}
