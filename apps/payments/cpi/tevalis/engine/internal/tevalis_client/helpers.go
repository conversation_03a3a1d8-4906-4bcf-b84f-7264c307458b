package tevalisclient

import (
	"context"
	"fmt"
	"log"
	"math"
	"strings"

	"github.com/aws/smithy-go/ptr"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/payments/cpi/tevalis/internal/model"
	protocol "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
)

// Helper function to find location from floorplan
func FindLocationFromFloorplans(floorplan *FloorplanResponse, tableNumber int) *protocol.Location {
	log.Printf("Finding location for table number: %d", tableNumber)

	for _, area := range floorplan.FloorplanArea {
		for _, obj := range area.FloorplanAreaObjects {
			if obj.TableNumber == tableNumber {
				log.Printf("Location found for table %d: Area ID=%d, Name=%s",
					tableNumber, area.FloorplanAreaID, area.Name)
				return &protocol.Location{
					LocationId:   ptr.String(fmt.Sprintf("%d", area.FloorplanAreaID)),
					LocationName: ptr.String(area.Name),
				}
			}
		}
	}

	log.Printf("No location found for table number: %d", tableNumber)
	return nil // Return empty location if not found
}

// Helper function to convert float amount to int cents
func FloatToCents(amount float64) int {
	cents := int(math.Round(amount * 100))
	log.Printf("Converting  float %.2f to cents: %d", amount, cents)
	return cents
}

// Helper function to filter out non-food/beverage items
func IsRegularItem(item Item) bool {
	// Filter out payments, gratuity, surcharges, etc.
	isPayment := item.ProductID == 8
	isGratuity := item.ProductID == 12
	hasPositiveAmount := item.ExpGross > 0

	isRegular := !isPayment && !isGratuity && hasPositiveAmount

	log.Printf("Item filtering: ProductID=%d, Name=%s, ExpGross=%.2f, IsPayment=%t, IsGratuity=%t, HasPositiveAmount=%t, IsRegular=%t",
		item.ProductID, item.ItemName, item.ExpGross, isPayment, isGratuity, hasPositiveAmount, isRegular)

	return isRegular
}

// groupItemsByItemIdAndAmount groups items with the same ItemId and Amount.
// It sums their Quantity and TotalAmount.
type ItemIdAndAmount struct {
	ItemId       string
	Amount       int
	RelatedItems string
}

func buildItemIdAndAmountKey(orderItem protocol.Item) ItemIdAndAmount {
	key := ItemIdAndAmount{}
	if orderItem.ItemId != nil {
		key.ItemId = *orderItem.ItemId
	}
	if orderItem.Amount != nil {
		key.Amount = *orderItem.Amount
	}
	if len(orderItem.RelatedItems) > 0 {
		relatedItemsItemIdComposite := utils.Reduce(orderItem.RelatedItems, func(acc string, current protocol.Item) string {
			if current.ItemId == nil {
				return acc // Skip related items with no ItemId
			}
			return fmt.Sprintf("%s#%s", acc, *current.ItemId)
		}, "")
		key.RelatedItems = relatedItemsItemIdComposite
	}
	return key
}

func mergeOrderItems(existingOrderItem, orderItem protocol.Item) protocol.Item {
	if existingOrderItem.TotalAmount == nil || existingOrderItem.Quantity == nil {
		logger.Error(context.Background(), "Skipping existing item due to missing fields: %+v", existingOrderItem)
		return orderItem
	}

	newTotalAmount := *orderItem.TotalAmount + *existingOrderItem.TotalAmount
	newQuantity := *orderItem.Quantity + *existingOrderItem.Quantity

	existingOrderItem.TotalAmount = &newTotalAmount
	existingOrderItem.Quantity = &newQuantity

	if existingOrderItem.RelatedItems == nil && orderItem.RelatedItems != nil && len(orderItem.RelatedItems) == 0 {
		existingOrderItem.RelatedItems = []protocol.Item{}
	} else if len(orderItem.RelatedItems) > 0 {
		existingOrderItem.RelatedItems = append(existingOrderItem.RelatedItems, orderItem.RelatedItems...)
	}

	return existingOrderItem
}

func shouldSkipOrderItem(orderItem protocol.Item) bool {
	return orderItem.ItemId == nil || orderItem.Amount == nil || orderItem.Quantity == nil || orderItem.TotalAmount == nil
}

func groupItemsByItemIdAndAmount(ctx context.Context, items []protocol.Item) []protocol.Item {
	mapItems := map[ItemIdAndAmount]protocol.Item{}
	itemIdAndAmountOrder := []ItemIdAndAmount{}

	for _, orderItem := range items {
		if shouldSkipOrderItem(orderItem) {
			logger.Error(ctx, "Skipping item due to missing fields: %+v", orderItem)
			continue
		}

		itemIdAndAmountKey := buildItemIdAndAmountKey(orderItem)

		existingOrderItem, pluInMap := mapItems[itemIdAndAmountKey]

		if pluInMap {
			mapItems[itemIdAndAmountKey] = mergeOrderItems(existingOrderItem, orderItem)
		} else {
			itemIdAndAmountOrder = append(itemIdAndAmountOrder, itemIdAndAmountKey)
			mapItems[itemIdAndAmountKey] = orderItem
		}
	}

	groupedItems := make([]protocol.Item, 0, len(mapItems))

	for _, itemIdAndAmountKey := range itemIdAndAmountOrder {
		mapItem := mapItems[itemIdAndAmountKey]
		if mapItem.TotalAmount == nil || *mapItem.TotalAmount == 0 {
			continue
		}
		if len(mapItem.RelatedItems) > 0 {
			mapItem.RelatedItems = groupItemsByItemIdAndAmount(ctx, mapItem.RelatedItems)
		}
		groupedItems = append(groupedItems, mapItem)
	}

	return groupedItems
}

// mapOrder converts a Tevalis transaction to our internal model.Order
func MapTransactionToOrder(ctx context.Context, transaction Transaction, location protocol.Location) protocol.DeviceOrder {
	log.Printf("Mapping transaction ID=%d, TableNumber=%d, TotalGross=%.2f",
		transaction.TransactionID, transaction.TableNumber, transaction.TotalGross)

	var tenderSum float64
	for _, tender := range transaction.Tenders {
		tenderSum += tender.Amount
		log.Printf("Tender: Name=%s, Amount=%.2f",
			tender.TenderName, tender.Amount)
	}
	owedAmount := transaction.TotalGross - tenderSum

	var items []protocol.Item
	filteredItemCount := 0
	for _, item := range transaction.Items {
		if IsRegularItem(item) {
			filteredItemCount++
			convertedItem := protocol.Item{
				ItemId:      ptr.String(fmt.Sprintf("%d", item.ProductID)),
				Description: ptr.String(item.ItemName),
				TotalAmount: ptr.Int(FloatToCents(item.ExpGross)),
				Amount:      ptr.Int(FloatToCents(item.ExpGross / item.Quantity)),
				Quantity:    &item.Quantity,
			}

			items = append(items, convertedItem)
		}
	}

	order := protocol.DeviceOrder{
		OrderId:            ptr.String(fmt.Sprintf("%d", transaction.TransactionID)),
		TableNumber:        ptr.String(fmt.Sprintf("%d", transaction.TableNumber)),
		TotalAmount:        ptr.Int(FloatToCents(transaction.TotalGross)),
		OwedAmount:         ptr.Int(FloatToCents(owedAmount)),
		PosSurchargeAmount: ptr.Int(FloatToCents(transaction.ServiceCharge)),
		Status:             ptr.String(string(model.OPEN)),
		Location:           &location,
		ExternalReference:  ptr.String(fmt.Sprintf("%d/%d", transaction.TransactionID, transaction.TableNumber)),
		Items:              groupItemsByItemIdAndAmount(ctx, items),
	}

	log.Printf("Created Order: ID=%v, TableNumber=%v, TotalAmount=%d, OwedAmount=%d, Items=%d",
		order.OrderId, order.TableNumber, order.TotalAmount, order.OwedAmount, len(order.Items))

	return order
}

// GetSiteUuidFromPath extracts the siteUuid from the path /v1/orders/{siteUuid}
func GetSiteUuidFromPath(path string) string {
	rawParts := strings.Split(path, "/")
	var parts []string
	for _, p := range rawParts {
		if p != "" {
			parts = append(parts, p)
		}
	}

	// Expected structure like ["v1", "orders", "some-uuid", ...]
	if len(parts) >= 3 && parts[0] == "v1" && parts[1] == "orders" {
		return parts[2]
	}
	return ""
}
