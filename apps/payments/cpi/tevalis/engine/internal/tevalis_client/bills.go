package tevalisclient

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"net/url"
)

// GetLiveBillData fetches multiple live bills.
// Relative path e.g., "/v2/OnlineOrders/GetLiveBillData"
func (c *Client) GetLiveBillData(
	ctx context.Context,
	siteID string,
	transactionCompleteFilter bool,
) (*LiveBillDataResponse, error) {
	log.Printf("GetLiveBillData called: siteID=%s, transactionCompleteFilter=%t", siteID, transactionCompleteFilter)

	queryParams := url.Values{}
	queryParams.Set("SiteID", siteID)
	queryParams.Set("TransactionComplete", fmt.Sprintf("%t", transactionCompleteFilter))

	return c.fetchAndUnmarshalLiveBillData(queryParams)
}

// GetLiveBillDataByID fetches a single live bill by its TransactionID.
// Relative path e.g., "/v2/OnlineOrders/GetLiveBillData"
func (c *Client) GetLiveBillDataByID(
	ctx context.Context,
	siteID string,
	transactionID string,
	// companyID, guid, devID removed - use client's values
) (*LiveBillDataResponse, error) {
	log.Printf("GetLiveBillDataByID called: siteID=%s, transactionID=%s", siteID, transactionID)

	queryParams := url.Values{}
	queryParams.Set("SiteID", siteID)
	queryParams.Set("TransactionId", transactionID) // Note 'Id' capitalization

	return c.fetchAndUnmarshalLiveBillData(queryParams)
}

// extract common logic for fetching and unmarshalling live bill data
func (c *Client) fetchAndUnmarshalLiveBillData(
	queryParams url.Values,
) (*LiveBillDataResponse, error) {
	bodyBytes, err := c.doAPIRequest(http.MethodGet, ApiPathGetLiveBillData, queryParams, nil, false)
	if err != nil {
		errMsg := fmt.Sprintf("%s request failed: %v", ApiPathGetLiveBillData, err)
		log.Print(errMsg)
		return nil, errors.New(errMsg)
	}

	var liveBillData LiveBillDataResponse
	err = json.Unmarshal(bodyBytes, &liveBillData)
	if err != nil {
		errMsg := fmt.Sprintf("failed to unmarshal JSON response for %s", ApiPathGetLiveBillData)
		log.Print(errMsg)
		return nil, errors.New(errMsg)
	}

	return &liveBillData, nil
}
