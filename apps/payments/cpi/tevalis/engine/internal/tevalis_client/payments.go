package tevalisclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
)

// GetTablePaymentRequest fetches detailed payment request data for a specific transaction.
// Relative path e.g., "/v2/OnlineOrders/TablePaymentRequest"
func (c *Client) GetTablePaymentRequest(
	siteID string,
	transactionID string,
	// companyID, guid, devID removed - use client's values
	// guid2 is handled by the client now
) ([]TablePaymentResponseItem, error) {
	log.Printf("GetTablePaymentRequest called: siteID=%s, transactionID=%s", siteID, transactionID)

	queryParams := url.Values{}
	queryParams.Set("SiteId", siteID)               // Note 'Id' capitalization
	queryParams.Set("TransactionId", transactionID) // Note 'Id' capitalization

	// Use the client's doAPIRequest method, indicating GUID2 is needed
	bodyBytes, err := c.doAPIRequest(http.MethodGet, ApiPathTablePaymentRequest, queryParams, nil, false)
	if err != nil {
		log.Printf("GetTablePaymentRequest request failed: %v", err)
		return nil, fmt.Errorf("GetTablePaymentRequest request failed: %w", err) // Reverted
	}

	var paymentResponse []TablePaymentResponseItem
	err = json.Unmarshal(bodyBytes, &paymentResponse)
	if err != nil {
		log.Printf("Failed to unmarshal JSON response: %v", err)
		return nil, fmt.Errorf("failed to unmarshal JSON response for GetTablePaymentRequest: %w", err)
	}

	log.Printf("GetTablePaymentRequest success: found %d items", len(paymentResponse))
	return paymentResponse, nil
}

// PostTablePayment sends a payment request to complete a transaction.
// Relative path e.g., "/v2/OnlineOrders/TablePaymentPost"
func (c *Client) PostTablePayment(
	requestBody TablePaymentPostRequest,
	// companyID, guid, devID removed - use client's values
	// guid2 is handled by the client now
) ([]TablePaymentResponseItem, error) {
	log.Printf("PostTablePayment called: path=%s", ApiPathTablePaymentPost)

	requestBodyBytes, err := json.Marshal(requestBody)
	if err != nil {
		log.Printf("Failed to marshal request body: %v", err)
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	log.Printf("PostTablePayment request body: %s", string(requestBodyBytes))

	bodyReader := bytes.NewReader(requestBodyBytes)

	// Use the client's doAPIRequest method, indicating GUID2 is needed
	bodyBytes, err := c.doAPIRequest(http.MethodPost, ApiPathTablePaymentPost, nil, bodyReader, true) // true: Use GUID2
	if err != nil {
		log.Printf("PostTablePayment request failed: %v", err)
		return nil, fmt.Errorf("PostTablePayment request failed: %w", err) // Reverted
	}

	var paymentResponse []TablePaymentResponseItem
	err = json.Unmarshal(bodyBytes, &paymentResponse)
	if err != nil {
		log.Printf("Failed to unmarshal JSON response: %v", err)
		return nil, fmt.Errorf("failed to unmarshal JSON response for PostTablePayment: %w", err)
	}

	log.Printf("PostTablePayment success: found %d items", len(paymentResponse))
	return paymentResponse, nil
}
