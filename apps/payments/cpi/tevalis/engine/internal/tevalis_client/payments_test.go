package tevalisclient

import (
	"encoding/json"
	"net/http"
	"testing"

	"github.com/aws/smithy-go/ptr"
	"github.com/stretchr/testify/assert"
)

// TO MOVE TEST TO CLIENT POST_PAYMENT TESTS

func TestGetTablePaymentRequest(t *testing.T) {
	// Test the GetTablePaymentRequest function
	server := setupTestServer(t, func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		mockJSON := []TablePaymentResponseItem{
			{
				SiteId:    123,
				TableName: "Test Table",
				TableListItems: []TableListItem{
					{
						TransactionId: 456,
					},
				},
				IsSuccess: true,
				Message:   "Success",
			},
		}
		b, _ := json.Marshal(mockJSON)
		w.Write(b)
	})
	defer server.Close()

	client := &Client{
		baseURL:   server.URL,
		guid:      "dummy-guid",
		devID:     "dummy-dev-id",
		companyID: "dummy-company-id",
		guid2:     nil,
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}

	siteID := "123"
	transactionID := "456"

	res, err := client.GetTablePaymentRequest(siteID, transactionID)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}

func TestGetTablePaymentRequestError(t *testing.T) {
	// Test the GetTablePaymentRequest function
	server := setupTestServer(t, func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	})
	defer server.Close()

	client := &Client{
		baseURL:   server.URL,
		guid:      "dummy-guid",
		devID:     "dummy-dev-id",
		companyID: "dummy-company-id",
		guid2:     nil,
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}

	siteID := "123"
	transactionID := "456"

	res, err := client.GetTablePaymentRequest(siteID, transactionID)
	assert.Error(t, err)
	assert.Nil(t, res)
}

func TestPostTablePayment(t *testing.T) {
	// Test the PostTablePayment function
	server := setupTestServer(t, func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		mockJSON := []TablePaymentResponseItem{
			{
				SiteId:    123,
				TableName: "Test Table",
				TableListItems: []TableListItem{
					{
						TransactionId: 456,
					},
				},
				IsSuccess: true,
				Message:   "Success",
			},
		}
		b, _ := json.Marshal(mockJSON)
		w.Write(b)
	})
	defer server.Close()

	client := &Client{
		baseURL:   server.URL,
		guid:      "dummy-guid",
		devID:     "dummy-dev-id",
		companyID: "dummy-company-id",
		guid2:     ptr.String("dummy-guid2"),
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}

	requestBody := TablePaymentPostRequest{
		SiteId:              123,
		TransactionId:       456,
		CloseTableOnRequest: true,
	}

	res, err := client.PostTablePayment(requestBody)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}

func TestPostTablePaymentError(t *testing.T) {
	// Test the PostTablePayment function
	server := setupTestServer(t, func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	})
	defer server.Close()

	client := &Client{
		baseURL:   server.URL,
		guid:      "dummy-guid",
		devID:     "dummy-dev-id",
		companyID: "dummy-company-id",
		guid2:     ptr.String("dummy-guid2"),
		httpClient: &http.Client{
			Timeout: defaultTimeout,
		},
	}

	requestBody := TablePaymentPostRequest{
		SiteId:              123,
		TransactionId:       456,
		CloseTableOnRequest: true,
	}

	res, err := client.PostTablePayment(requestBody)
	assert.Error(t, err)
	assert.Nil(t, res)
}
