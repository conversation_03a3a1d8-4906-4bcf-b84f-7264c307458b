package tevalisclient

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"strconv"
	"strings"
	"testing"

	"github.com/aws/smithy-go/ptr"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	protocol "github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/posconnector/pay-at-table/posconnector/broker/protocol"
)

func setupTestServer(t *testing.T, handler http.HandlerFunc) *httptest.Server {
	t.Helper()

	return httptest.NewServer(handler)
}

func setupTestClient(t *testing.T, serverUrl string) IClient {
	t.Helper()

	guid2 := "dummy-guid2"
	return NewClient(serverUrl, "dummy-company", "dummy-guid", "dummy-devid", &guid2)
}

// setupTestServerAndClient creates a mock HTTP server and a Tevalis client configured to use it.
// The handler function allows customizing the server's behavior for each test.
func setupTestServerAndClient(t *testing.T, handler http.HandlerFunc) (*httptest.Server, IClient) {
	t.Helper()

	server := setupTestServer(t, handler)
	client := setupTestClient(t, server.URL)

	return server, client
}

func TestGetTableSuccess(t *testing.T) {
	mockBillDataResponse := LiveBillDataResponse{
		Count:      1,
		TotalGross: 123.45,
		TotalNet:   100.00,
		Transactions: []Transaction{
			{
				TransactionID:       101,
				TableNumber:         1,
				TotalGross:          123.45,
				TransactionComplete: false,
				SalesArea:           "Main Dining",
				DateTimeOpened:      "2023-10-27T10:00:00Z",
				Items: []Item{
					{
						Gross:     50.00,
						Net:       40.00,
						ExpNet:    35.00,
						ExpGross:  45.00,
						ItemName:  "Burger",
						Quantity:  2,
						ProductID: 1001,
						VATRate:   20.00,
					},
				},
			},
		},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	mockFloorplanResponse := FloorplanResponse{
		SiteID: 8913,
		FloorplanArea: []FloorplanArea{
			{
				FloorplanAreaID: 1,
				Name:            "Main Area",
				FloorplanAreaObjects: []FloorplanAreaObject{
					{FloorplanAreaObjectID: 10, TableName: "Table 1", TableNumber: 1},
				},
			},
		},
	}
	mockFloorplanJSON, _ := json.Marshal(mockFloorplanResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			t.Errorf("Expected GET method, got %s", r.Method)
		}

		if strings.Contains(r.URL.Path, ApiPathFloorplans) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockFloorplanJSON)
			return
		}

		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}

	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	resp := client.GetTable(context.Background(), uuid.NewString(), "8913", "1000")

	log.Println(utils.BestEffortStringify(resp, true))
	assert.Equal(t, MapTransactionToOrder(t.Context(), mockBillDataResponse.Transactions[0], protocol.Location{
		LocationId:   ptr.String("1"),
		LocationName: ptr.String("Main Area"),
	}), *resp)
}

func TestGetTableFailureGetLiveBillDataError(t *testing.T) {
	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusInternalServerError) // Simulate an error
			return
		}
		w.WriteHeader(http.StatusOK)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	resp := client.GetTable(context.Background(), uuid.NewString(), "8913", "1000")

	assert.Nil(t, resp, "Expected GetTable to return nil when GetLiveBillDataByID fails")
}

func TestGetTableFailureNoTransactionsFound(t *testing.T) {
	mockBillDataResponse := LiveBillDataResponse{
		Count:        0,
		TotalGross:   0,
		TotalNet:     0,
		Transactions: []Transaction{}, // Empty transactions
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	mockFloorplanResponse := FloorplanResponse{
		SiteID: 8913,
		FloorplanArea: []FloorplanArea{
			{
				FloorplanAreaID: 1,
				Name:            "Main Area",
				FloorplanAreaObjects: []FloorplanAreaObject{
					{FloorplanAreaObjectID: 10, TableName: "Table 1", TableNumber: 1},
				},
			},
		},
	}
	mockFloorplanJSON, _ := json.Marshal(mockFloorplanResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}
		if strings.Contains(r.URL.Path, ApiPathFloorplans) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockFloorplanJSON)
			return
		}
		w.WriteHeader(http.StatusInternalServerError) // Should not be reached
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	resp := client.GetTable(context.Background(), uuid.NewString(), "8913", "1000")

	assert.Nil(t, resp, "Expected GetTable to return nil when no transactions are found")
}

func TestGetTableFailureGetFloorplanError(t *testing.T) {
	mockBillDataResponse := LiveBillDataResponse{
		Count:      1,
		TotalGross: 123.45,
		TotalNet:   100.00,
		Transactions: []Transaction{
			{
				TransactionID:       101,
				TableNumber:         1,
				TotalGross:          123.45,
				TransactionComplete: false,
				SalesArea:           "Main Dining",
				DateTimeOpened:      "2023-10-27T10:00:00Z",
				Items:               []Item{{Gross: 50.00, Net: 40.00, ItemName: "Burger", Quantity: 2, ProductID: 1001, VATRate: 20.00}},
			},
		},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}
		if strings.Contains(r.URL.Path, ApiPathFloorplans) {
			w.WriteHeader(http.StatusInternalServerError) // Simulate an error for floorplan
			return
		}
		w.WriteHeader(http.StatusInternalServerError) // Should not be reached if logic is correct
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	resp := client.GetTable(context.Background(), uuid.NewString(), "8913", "101")

	assert.Nil(t, resp, "Expected GetTable to return nil when GetFloorplan fails")
}

func TestGetTableFailureLocationNotFound(t *testing.T) {
	mockBillDataResponse := LiveBillDataResponse{
		Count:      1,
		TotalGross: 123.45,
		TotalNet:   100.00,
		Transactions: []Transaction{
			{
				TransactionID:       101,
				TableNumber:         99, // This table number should not exist in the mock floorplan
				TotalGross:          123.45,
				TransactionComplete: false,
				SalesArea:           "Main Dining",
				DateTimeOpened:      "2023-10-27T10:00:00Z",
				Items:               []Item{{Gross: 50.00, Net: 40.00, ItemName: "Burger", Quantity: 2, ProductID: 1001, VATRate: 20.00}},
			},
		},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	mockFloorplanResponse := FloorplanResponse{
		SiteID: 8913,
		FloorplanArea: []FloorplanArea{
			{
				FloorplanAreaID: 1,
				Name:            "Main Area",
				FloorplanAreaObjects: []FloorplanAreaObject{
					{FloorplanAreaObjectID: 10, TableName: "Table 1", TableNumber: 1},
					{FloorplanAreaObjectID: 11, TableName: "Table 2", TableNumber: 2},
				},
			},
		},
	}
	mockFloorplanJSON, _ := json.Marshal(mockFloorplanResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}
		if strings.Contains(r.URL.Path, ApiPathFloorplans) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockFloorplanJSON)
			return
		}
		w.WriteHeader(http.StatusInternalServerError) // Should not be reached
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	resp := client.GetTable(context.Background(), uuid.NewString(), "8913", "101")

	assert.Nil(t, resp, "Expected GetTable to return nil when location is not found in floorplan")
}

func TestGetTablesSuccess(t *testing.T) {
	mockSiteID := 8913
	mockTransaction1 := Transaction{
		TransactionID:       101,
		TableNumber:         1,
		TotalGross:          50.00,
		TransactionComplete: false,
		SalesArea:           "Main Area",
		DateTimeOpened:      "2023-10-27T10:00:00Z",
		Items:               []Item{{Gross: 50.00, ItemName: "Burger", Quantity: 1, ProductID: 1001}},
	}
	mockTransaction2 := Transaction{
		TransactionID:       102,
		TableNumber:         2,
		TotalGross:          25.00,
		TransactionComplete: false,
		SalesArea:           "Patio",
		DateTimeOpened:      "2023-10-27T11:00:00Z",
		Items:               []Item{{Gross: 25.00, ItemName: "Coke", Quantity: 1, ProductID: 1002}},
	}
	mockBillDataResponse := LiveBillDataResponse{
		Count:        2,
		TotalGross:   75.00,
		Transactions: []Transaction{mockTransaction1, mockTransaction2},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	mockFloorplanResponse := FloorplanResponse{
		SiteID: mockSiteID,
		FloorplanArea: []FloorplanArea{
			{
				FloorplanAreaID: 1, Name: "Main Area",
				FloorplanAreaObjects: []FloorplanAreaObject{{TableNumber: 1, TableName: "Table 1"}},
			},
			{
				FloorplanAreaID: 2, Name: "Patio",
				FloorplanAreaObjects: []FloorplanAreaObject{{TableNumber: 2, TableName: "Table 2"}},
			},
		},
	}
	mockFloorplanJSON, _ := json.Marshal(mockFloorplanResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			assert.Equal(t, "false", r.URL.Query().Get("TransactionComplete"))
			assert.Equal(t, fmt.Sprintf("%d", mockSiteID), r.URL.Query().Get("SiteID"))
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}
		if strings.Contains(r.URL.Path, ApiPathFloorplans) {
			assert.Contains(t, r.URL.Path, fmt.Sprintf("%d", mockSiteID))
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockFloorplanJSON)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	expectedLocation1 := protocol.Location{LocationId: ptr.String("1"), LocationName: ptr.String("Main Area")}
	expectedOrder1 := MapTransactionToOrder(t.Context(), mockTransaction1, expectedLocation1)

	expectedLocation2 := protocol.Location{LocationId: ptr.String("2"), LocationName: ptr.String("Patio")}
	expectedOrder2 := MapTransactionToOrder(t.Context(), mockTransaction2, expectedLocation2)

	expectedOrders := []protocol.DeviceOrder{expectedOrder1, expectedOrder2}

	orders := client.GetTables(context.Background(), uuid.NewString(), strconv.Itoa(mockSiteID), []string{})

	assert.ElementsMatch(t, expectedOrders, orders, "Expected orders do not match returned orders")
}

func TestGetTablesSuccessWithLocationFilter(t *testing.T) {
	mockSiteID := 8914
	mockTransaction1 := Transaction{TransactionID: 201, TableNumber: 10, TotalGross: 10.00}
	mockTransaction2 := Transaction{TransactionID: 202, TableNumber: 20, TotalGross: 20.00} // This one should be filtered out
	mockTransaction3 := Transaction{TransactionID: 203, TableNumber: 30, TotalGross: 30.00}

	mockBillDataResponse := LiveBillDataResponse{
		Transactions: []Transaction{mockTransaction1, mockTransaction2, mockTransaction3},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	mockFloorplanResponse := FloorplanResponse{
		SiteID: mockSiteID,
		FloorplanArea: []FloorplanArea{
			{FloorplanAreaID: 1, Name: "Area 1", FloorplanAreaObjects: []FloorplanAreaObject{{TableNumber: 10}}},
			{FloorplanAreaID: 2, Name: "Area 2", FloorplanAreaObjects: []FloorplanAreaObject{{TableNumber: 20}}}, // Location ID "2"
			{FloorplanAreaID: 3, Name: "Area 3", FloorplanAreaObjects: []FloorplanAreaObject{{TableNumber: 30}}},
		},
	}
	mockFloorplanJSON, _ := json.Marshal(mockFloorplanResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}
		if strings.Contains(r.URL.Path, ApiPathFloorplans) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockFloorplanJSON)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	locationFilter := []string{"1", "3"} // Filter for location IDs "1" and "3"

	expectedLocation1 := protocol.Location{LocationId: ptr.String("1"), LocationName: ptr.String("Area 1")}
	expectedOrder1 := MapTransactionToOrder(t.Context(), mockTransaction1, expectedLocation1)

	expectedLocation3 := protocol.Location{LocationId: ptr.String("3"), LocationName: ptr.String("Area 3")}
	expectedOrder3 := MapTransactionToOrder(t.Context(), mockTransaction3, expectedLocation3)

	expectedOrders := []protocol.DeviceOrder{expectedOrder1, expectedOrder3}

	orders := client.GetTables(context.Background(), uuid.NewString(), strconv.Itoa(mockSiteID), locationFilter)

	assert.ElementsMatch(t, expectedOrders, orders)
	assert.Len(t, orders, 2)
}

func TestGetTablesFailureGetLiveBillDataError(t *testing.T) {
	mockSiteID := 8915

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	orders := client.GetTables(context.Background(), uuid.NewString(), strconv.Itoa(mockSiteID), []string{})

	assert.Empty(t, orders, "Expected GetTables to return an empty slice when GetLiveBillData fails")
}

func TestGetTablesFailureNoTransactionsFound(t *testing.T) {
	mockSiteID := 8916
	mockBillDataResponse := LiveBillDataResponse{
		Count:        0,
		Transactions: []Transaction{},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}
		// Floorplan should not be called if no transactions
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	orders := client.GetTables(context.Background(), uuid.NewString(), strconv.Itoa(mockSiteID), []string{})

	assert.Empty(t, orders, "Expected GetTables to return an empty slice when no transactions are found")
}

func TestGetTablesFailureGetFloorplanError(t *testing.T) {
	mockSiteID := 8917
	mockBillDataResponse := LiveBillDataResponse{
		Transactions: []Transaction{{TransactionID: 301, TableNumber: 1}},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}
		if strings.Contains(r.URL.Path, ApiPathFloorplans) {
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	orders := client.GetTables(context.Background(), uuid.NewString(), strconv.Itoa(mockSiteID), []string{})

	assert.Empty(t, orders, "Expected GetTables to return an empty slice when GetFloorplan fails")
}

func TestGetTablesLocationNotFoundForSome(t *testing.T) {

	mockSiteID := 8918
	mockTransaction1 := Transaction{TransactionID: 401, TableNumber: 1}  // Will be found
	mockTransaction2 := Transaction{TransactionID: 402, TableNumber: 99} // Will NOT be found
	mockBillDataResponse := LiveBillDataResponse{
		Transactions: []Transaction{mockTransaction1, mockTransaction2},
	}
	mockBillJSON, _ := json.Marshal(mockBillDataResponse)

	mockFloorplanResponse := FloorplanResponse{
		SiteID: mockSiteID,
		FloorplanArea: []FloorplanArea{
			{FloorplanAreaID: 1, Name: "Found Area", FloorplanAreaObjects: []FloorplanAreaObject{{TableNumber: 1}}},
		},
	}
	mockFloorplanJSON, _ := json.Marshal(mockFloorplanResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		if strings.Contains(r.URL.Path, ApiPathGetLiveBillData) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockBillJSON)
			return
		}
		if strings.Contains(r.URL.Path, ApiPathFloorplans) {
			w.WriteHeader(http.StatusOK)
			_, _ = w.Write(mockFloorplanJSON)
			return
		}
		t.Fatalf("Unexpected request: %s %s", r.Method, r.URL.Path)
	}

	server, client := setupTestServerAndClient(t, handler)
	defer server.Close()

	expectedLocation1 := protocol.Location{LocationId: ptr.String("1"), LocationName: ptr.String("Found Area")}
	expectedOrder1 := MapTransactionToOrder(t.Context(), mockTransaction1, expectedLocation1)
	expectedOrders := []protocol.DeviceOrder{expectedOrder1}

	orders := client.GetTables(context.Background(), uuid.NewString(), strconv.Itoa(mockSiteID), []string{})

	assert.ElementsMatch(t, expectedOrders, orders)
	assert.Len(t, orders, 1, "Expected only one order to be returned")
}

// TestNewClient has been refactored to be table-driven.
func TestNewClient(t *testing.T) {
	baseURL := "http://fakeapi.com"
	companyID := "test-company"
	guid := "test-guid"
	devID := "test-devid"
	guid2Val := "test-guid2"

	testCases := []struct {
		name             string
		guid2            *string
		expectGuid2IsSet bool
		expectedGuid2Val string // Only relevant if expectGuid2IsSet is true
	}{
		{
			name:             "With GUID2",
			guid2:            &guid2Val,
			expectGuid2IsSet: true,
			expectedGuid2Val: guid2Val,
		},
		{
			name:             "With nil GUID2",
			guid2:            nil,
			expectGuid2IsSet: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			client := NewClient(baseURL, companyID, guid, devID, tc.guid2).(*Client)

			assert.Equal(t, baseURL, client.baseURL, "baseURL mismatch")
			assert.Equal(t, companyID, client.companyID, "companyID mismatch")
			assert.Equal(t, guid, client.guid, "guid mismatch")
			assert.Equal(t, devID, client.devID, "devID mismatch")
			assert.NotNil(t, client.httpClient, "httpClient should be initialized")

			if tc.expectGuid2IsSet {
				assert.NotNil(t, client.guid2, "guid2 should not be nil")
				assert.Equal(t, tc.expectedGuid2Val, *client.guid2, "guid2 value mismatch")
			} else {
				assert.Nil(t, client.guid2, "guid2 should be nil")
			}
		})
	}
}

// Test for APIError methods (basic check)
func TestAPIErrorMethods(t *testing.T) {
	apiErr := &APIError{
		StatusCode: 429,
		Message:    "Rate Limited",
		Body:       "Too many requests",
		RateLimit:  true,
		Retriable:  false,
	}

	if apiErr.Error() != "Rate Limited" {
		t.Errorf("Error() mismatch. Expected 'Rate Limited', got '%s'", apiErr.Error())
	}
	if apiErr.GetStatusCode() != 429 {
		t.Errorf("GetStatusCode() mismatch. Expected 429, got %d", apiErr.GetStatusCode())
	}
	if !apiErr.IsRateLimit() {
		t.Errorf("IsRateLimit() mismatch. Expected true, got false")
	}
	if apiErr.IsRetriable() {
		t.Errorf("IsRetriable() mismatch. Expected false, got true")
	}
}

func TestGetLiveBillDataSuccess(t *testing.T) {
	mockResponse := LiveBillDataResponse{
		Count:      2,
		TotalGross: 200.50,
		TotalNet:   180.00,
		Transactions: []Transaction{
			{TransactionID: 1, TableNumber: 1, TotalGross: 100.25},
			{TransactionID: 2, TableNumber: 2, TotalGross: 100.25},
		},
	}
	mockJSON, _ := json.Marshal(mockResponse)

	handler := func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, http.MethodGet, r.Method)
		assert.Contains(t, r.URL.Path, ApiPathGetLiveBillData)
		assert.Equal(t, "8913", r.URL.Query().Get("SiteID"))
		assert.Equal(t, "false", r.URL.Query().Get("TransactionComplete")) // Example filter

		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(mockJSON)
	}

	server, iClient := setupTestServerAndClient(t, handler)
	defer server.Close()

	client, ok := iClient.(*Client)
	assert.True(t, ok, "Failed to cast IClient to *Client")

	resp, err := client.GetLiveBillData(context.Background(), "8913", false)

	assert.NoError(t, err)
	assert.NotNil(t, resp)
	assert.Equal(t, mockResponse, *resp)
}

func TestGetLiveBillDataAPIError(t *testing.T) {
	handler := func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte("server blew up"))
	}

	server, iClient := setupTestServerAndClient(t, handler)
	defer server.Close()

	client, ok := iClient.(*Client)
	assert.True(t, ok, "Failed to cast IClient to *Client")

	resp, err := client.GetLiveBillData(context.Background(), "8913", false)

	assert.Error(t, err)
	assert.Nil(t, resp)
	assert.Contains(t, err.Error(), "request failed")
}
