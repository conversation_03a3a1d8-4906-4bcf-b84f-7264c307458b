package tevalisclient

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"
)

// ============================================================================
// API Client
// ============================================================================

var ApiPath string

const (
	// API paths
	ApiPathGetLiveBillData     = "/v2/OnlineOrders/GetLiveBillData"
	ApiPathSites               = "/Sites"
	ApiPathFloorplans          = "/Floorplans"
	ApiPathTablePaymentRequest = "/v2/OnlineOrders/TablePaymentRequest"
	ApiPathTablePaymentPost    = "/v2/OnlineOrders/TablePaymentPost"
)

const defaultTimeout = 30 * time.Second

// doAPIRequest performs an HTTP request using the client's configuration.
func (c *Client) doAPIRequest(
	method string,
	path string, // Relative path or full URL override
	queryParams url.Values,
	body io.Reader,
	useGUID2 bool, // Flag to indicate if GUID2 should be used for this request
) ([]byte, error) {
	// Construct the full URL
	urlString, err := c.constructFullURL(path, queryParams)
	if err != nil {
		log.Printf("Failed to construct URL: %v", err)
		return nil, fmt.Errorf("failed to construct URL: %w", err)
	}

	log.Printf("API Request: %s %s", method, urlString)

	// If body is provided, log it
	if body != nil {
		// Read the body
		bodyBytes, err := io.ReadAll(body)
		if err != nil {
			log.Printf("Error reading request body: %v", err)
		} else {
			log.Printf("Request Body: %s", string(bodyBytes))
			// Recreate the body reader since we've consumed it
			body = bytes.NewReader(bodyBytes)
		}
	}

	// Use the client's http client
	req, err := http.NewRequest(method, urlString, body)
	if err != nil {
		log.Printf("Failed to create HTTP request: %v", err)
		return nil, fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// Use credentials from the client struct
	req.Header.Add("CompanyId", c.companyID)
	req.Header.Add("GUID", c.guid)
	req.Header.Add("DEVID", c.devID)
	req.Header.Add("Content-Type", "application/json")

	log.Printf("Request Headers: CompanyId=%s, GUID=%s, DEVID=%s", c.companyID, c.guid, c.devID)

	// Add GUID2 if required and available
	if useGUID2 {
		if c.guid2 != nil {
			req.Header.Add("GUID2", *c.guid2)
			log.Printf("Additional Header: GUID2=%s", *c.guid2)
		} else {
			log.Printf("Warning: Request requires GUID2, but it's not configured in the client.")
			// Potentially return an error here, or let the API handle it
			return nil, fmt.Errorf("request requires GUID2, but it's not configured")
		}
	}

	// Use the client's http client
	resp, err := c.httpClient.Do(req)

	if err != nil {
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("Response received with status code: %d", resp.StatusCode)
	log.Printf("Response Headers: %v", resp.Header)

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Failed to read response body: %v", err)
		return nil, fmt.Errorf("failed to read response body for status %d: %w", resp.StatusCode, err)
	}

	log.Printf("Response Body (%d bytes): %s", len(bodyBytes), string(bodyBytes))

	return c.handleResponse(resp, bodyBytes)
}

func (c *Client) constructFullURL(path string, queryParams url.Values) (string, error) {
	// Construct the full URL
	finalURL, err := url.Parse(c.baseURL)
	if err != nil {
		log.Printf("Failed to parse base URL '%s': %v", c.baseURL, err)
		return "", fmt.Errorf("invalid base URL in client: %w", err)
	}
	// Check if path is already a full URL, otherwise join with base
	if pathURL, _ := url.Parse(path); pathURL == nil || !pathURL.IsAbs() {
		finalURL = finalURL.JoinPath(path)
	} else {
		finalURL = pathURL // Use path as the full URL
	}

	if queryParams != nil {
		finalURL.RawQuery = queryParams.Encode()
	}
	return finalURL.String(), nil
}

func (c *Client) handleResponse(resp *http.Response, bodyBytes []byte) ([]byte, error) {
	if resp.StatusCode == http.StatusOK {
		return bodyBytes, nil
	}

	return nil, &APIError{
		StatusCode: resp.StatusCode,
		Message:    fmt.Sprintf("API returned status %d", resp.StatusCode),
		Body:       string(bodyBytes),
	}
}
