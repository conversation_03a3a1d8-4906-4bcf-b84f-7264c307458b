package tevalisclient

// ============================================================================
// Type Definitions
// ============================================================================

// Option represents an item option within a transaction item.
type Option struct {
	Gross      float64 `json:"Gross"`
	Net        float64 `json:"Net"`
	Quantity   float64 `json:"Quantity"`
	OptionName string  `json:"OptionName"`
	ProductID  int     `json:"ProductID"`
	VATRate    float64 `json:"VATRate"`
}

// Item represents an item within a transaction.
type Item struct {
	Gross     float64  `json:"Gross"`
	ExpGross  float64  `json:"ExpGross"`
	Net       float64  `json:"Net"`
	ExpNet    float64  `json:"ExpNet"`
	ItemName  string   `json:"ItemName"`
	Quantity  float64  `json:"Quantity"`
	ProductID int      `json:"ProductID"`
	VATRate   float64  `json:"VATRate"`
	Options   []Option `json:"Options"`
}

// Tender represents a payment method used for a transaction.
type Tender struct {
	TenderName string  `json:"TenderName"`
	Amount     float64 `json:"Amount"`
}

// Transaction represents a single bill or order returned by GetLiveBillData.
type Transaction struct {
	TransactionID       int      `json:"TransactionID"`
	ReservationID       string   `json:"ReservationID"`
	TransactionComplete bool     `json:"TransactionComplete"`
	TableNumber         int      `json:"TableNumber"`
	TotalGross          float64  `json:"TotalGross"`
	TotalNet            float64  `json:"TotalNet"`
	ServiceCharge       float64  `json:"ServiceCharge"`
	SalesArea           string   `json:"SalesArea"`
	DateTimeOpened      string   `json:"DateTimeOpened"`
	DateTimeClosed      *string  `json:"DateTimeClosed"` // Nullable
	Items               []Item   `json:"Items"`
	Tenders             []Tender `json:"Tenders"`
}

// LiveBillDataResponse is the structure for GetLiveBillData responses.
type LiveBillDataResponse struct {
	Count        int           `json:"Count"`
	TotalGross   float64       `json:"TotalGross"`
	TotalNet     float64       `json:"TotalNet"`
	Transactions []Transaction `json:"Transactions"`
}

// FloorplanAreaObject represents a table or object within a floorplan area.
type FloorplanAreaObject struct {
	FloorplanAreaObjectID int    `json:"FloorplanAreaObjectID"`
	TableName             string `json:"TableName"`
	IsDeleted             bool   `json:"IsDeleted"`
	TableNumber           int    `json:"TableNumber"`
}

// FloorplanArea represents a section within a floorplan.
type FloorplanArea struct {
	WSFloorplanAreaID    int                   `json:"WSFloorplanAreaID"`
	FloorplanAreaID      int                   `json:"FloorplanAreaID"`
	FloorplanID          int                   `json:"FloorplanID"`
	Name                 string                `json:"Name"`
	ColourCode           string                `json:"ColourCode"`
	IsDeleted            bool                  `json:"IsDeleted"`
	DisplaySequence      int                   `json:"DisplaySequence"`
	FloorplanAreaObjects []FloorplanAreaObject `json:"FloorplanAreaObjects"`
}

// FloorplanResponse is the structure for GetFloorplan responses.
type FloorplanResponse struct {
	SiteID        int             `json:"SiteID"`
	FloorplanArea []FloorplanArea `json:"FloorplanArea"`
}

// Site represents a single site returned by the GetSites endpoint.
type Site struct {
	SiteID                int    `json:"SiteID"`
	Name                  string `json:"Name"`
	IsAvailableThroughAPI bool   `json:"IsAvailableThroughAPI"`
	LastWebsync           string `json:"LastWebsync"`
	ThirdPartyReference   string `json:"ThirdPartyReference"`
	LastOnlineOrderPoll   string `json:"LastOnlineOrderPoll"`
	PosVersionName        string `json:"PosVersionName"`
	PosVersion            string `json:"PosVersion"`
	TimeZoneID            string `json:"TimeZoneID"`
}

// TableListItem represents an item within the TableListItems array in TablePayment responses.
type TableListItem struct {
	TransactionId  int        `json:"TransactionId"`
	TotalValue     int        `json:"TotalValue"`
	BalanceValue   int        `json:"BalanceValue"`
	ServiceCharge  int        `json:"ServiceCharge"`
	Covers         int        `json:"Covers"`
	LastUser       string     `json:"LastUser"`
	SalesArea      string     `json:"SalesArea"`
	DateTimeOpened string     `json:"DateTimeOpened"`
	PurchaseList   []struct{} `json:"PurchaseList"` // Define specific struct if data expected
	DiscountList   []struct{} `json:"DiscountList"` // Define specific struct if data expected
	PaymentList    []struct{} `json:"PaymentList"`  // Define specific struct if data expected
	TaxList        []struct{} `json:"TaxList"`      // Define specific struct if data expected
	BillHeader     string     `json:"BillHeader"`
	BillFooter     string     `json:"BillFooter"`
}

// TablePaymentResponseItem is the structure of the objects within the array
// returned by TablePaymentRequest and TablePaymentPost.
type TablePaymentResponseItem struct {
	SiteId         int             `json:"SiteId"`
	TableName      string          `json:"TableName"`
	TableListItems []TableListItem `json:"TableListItems"`
	IsSuccess      bool            `json:"IsSuccess"`
	Message        string          `json:"Message"`
}

// TablePaymentItem represents a single payment item within the TablePaymentPost request body.
type TablePaymentItem struct {
	PaymentType string `json:"PaymentType"`
	Value       int    `json:"Value"`
}

// TablePaymentPostRequest is the structure of the request body for the TablePaymentPost call.
type TablePaymentPostRequest struct {
	SiteId              int                `json:"SiteId"`
	TransactionId       int                `json:"TransactionId"`
	CloseTableOnRequest bool               `json:"CloseTableOnRequest"`
	TablePaymentItems   []TablePaymentItem `json:"TablePaymentItems"`
	Gratuity            int                `json:"Gratuity"`
}
