package model

type OrderStatus string

const (
	OPEN   OrderStatus = "OPEN"
	CLOSED OrderStatus = "CLOSED"
)

type TevalisCredentials struct {
	CompanyId string `dynamodbav:"companyId" json:"companyId"`
	Guid      string `dynamodbav:"guid" json:"guid"`
	Guid2     string `dynamodbav:"guid2" json:"guid2"`
}

type PayAtTableVenue struct {
	Id        string               `dynamodbav:"id" json:"id"`
	Name      string               `dynamodbav:"name" json:"name"`
	Locations []PayAtTableLocation `dynamodbav:"locations" json:"locations"`
}
type PayAtTableLocation struct {
	Id     string `dynamodbav:"id" json:"id"`
	Name   string `dynamodbav:"name" json:"name"`
	Number string `dynamodbav:"number" json:"number"`
}

// ConnectionPosInterfaceModel is the connection to the TEVALIS POS system
type ConnectionPosInterfaceModel struct {
	Id               string             `dynamodbav:"id" json:"id"`
	EntityUuid       string             `dynamodbav:"entityUuid" json:"entityUuid"`
	CustomerUuid     *string            `dynamodbav:"customerUuid" json:"customerUuid"`
	Provider         string             `dynamodbav:"provider" json:"provider"`
	OrganisationId   string             `dynamodbav:"organisationId" json:"organisationId"`
	OrganisationName string             `dynamodbav:"organisationName" json:"organisationName"`
	Venues           []PayAtTableVenue  `dynamodbav:"venues" json:"venues"`
	Status           string             `dynamodbav:"status" json:"status"`
	Credentials      TevalisCredentials `dynamodbav:"credentials" json:"credentials"`
}

type ConnectionPosInterfaceItem struct {
	ConnectionPosInterfaceModel
	Type string `dynamodbav:"type" json:"type"`
}

type PosInterfacePairingModel struct {
	Id         string   `dynamodbav:"id"`
	Provider   string   `dynamodbav:"provider"`
	SiteUuid   string   `dynamodbav:"siteUuid"`
	VenueId    string   `dynamodbav:"venueId"`
	Locations  []string `dynamodbav:"locations"`
	EntityUuid string   `dynamodbav:"entityUuid"`
	Status     string   `dynamodbav:"status"`
	Timestamp  string   `dynamodbav:"timestamp"`
	StationId  *string  `dynamodbav:"stationId"`
}

type PosInterfaceSitePairingItem struct {
	PosInterfacePairingModel
	Type string `dynamodbav:"type"`
}
