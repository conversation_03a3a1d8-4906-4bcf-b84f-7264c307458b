
componentName=cpi
partName=tevalis-engine
awsRegion=ap-southeast-2
environment=$1

# check if environment is dev, and exit if it is
if [ "$environment" = "dev" ]; then
  echo 'Dont delete dev environment.'
  exit 255
fi

baseName="$environment"-"$componentName"-"$partName"

echo Running "$baseName"-iac destroy-test.sh

# stacks to destroy.
# The general rule is to reverse the order of the action runOrder in the appPipeline.ts .
stacks='warmup api'

# iterate through the stacks and delete them
for stack in ${stacks[*]} ;do
  stackName=$baseName-$stack
  echo "Destroy stack: $stackName"
  aws cloudformation delete-stack --stack-name "$stackName" --region "$awsRegion"
  aws cloudformation wait stack-delete-complete --stack-name "$stackName" --region "$awsRegion"
done