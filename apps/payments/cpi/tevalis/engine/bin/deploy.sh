#!/bin/sh

if [ $# -lt 1 ];then
  echo "ERROR: Please specify stage"
  exit 2
fi

stack_lists='api warmup'

stage=$1

# If there is a stack specified, ensure first letter is capitalized and only deploy that stack, otherwise deploy all stacks
if [ $# -eq 2 ]; then
  stacks=($(tr '[:lower:]' '[:upper:]' <<< ${2:0:1})${2:1})
else
  stacks=$stack_lists
fi

echo Deploy $stacks to stage $stage
rm -fr dist
make build
for stack in ${stacks[*]} ;do
  echo "Deploy stack: $stack on stage: $stage, serverless$stack.ts"
  STAGE=$stage yarn sls deploy --config serverless$stack.ts --verbose --region ap-southeast-2 --stage $stage
  echo "Complete to deploy $stack on stage $stage"
done