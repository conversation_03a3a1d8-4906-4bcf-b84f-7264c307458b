package commandHandler

import (
	"context"
	"encoding/xml"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/aws/smithy-go/ptr"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	db "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/dynamodb"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
	"github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/utils"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/oraclepos/engine/internal/api"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/oraclepos/engine/internal/common"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/oraclepos/engine/internal/connection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/oraclepos/engine/internal/oracle"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/oraclepos/engine/internal/projection"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/oraclepos/engine/internal/publisher"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/oraclepos/engine/internal/session"
	"github.com/zeller-engineering/component-bff/apps/payments/pay-at-table/oraclepos/engine/types"
)

type OracleCommandHandler struct {
	envService  common.IEnvironmentService
	dbClient    *db.DynamoDb
	session     session.ISession
	connections connection.Repository
	publisher   publisher.IPublisher
}

func New(ctx context.Context) *OracleCommandHandler {
	dbClient := db.NewDynamoDb(ctx)
	env := common.NewEnvService()
	return &OracleCommandHandler{
		envService:  env,
		dbClient:    dbClient,
		session:     session.New(dbClient, env),
		connections: connection.NewRepository(dbClient, env),
		publisher:   publisher.New(ctx, env, dbClient),
	}
}

func (h *OracleCommandHandler) handlePurchase(ctx context.Context, command oracle.OracleTransactionRequest, deviceUuid *string, entityUuid *string) (api.LambdaApiResponse, error) {
	sessionRecord := h.mapOracleCommandToSessionRecord(ctx, command, entityUuid)

	return h.handleTransactionRequest(ctx, sessionRecord, deviceUuid)
}

func (h *OracleCommandHandler) getSessionForRefundBasedOnType(ctx context.Context, command oracle.OracleTransactionRequest) (*session.SessionRecord, error) {

	// Void and linked Refund
	if command.OriginalRRN != nil {
		return h.session.GetSessionByRRN(ctx, *command.OriginalRRN)
	}

	// Reversal
	if *command.TransType == types.TransactionTypeZellerToOracle[types.REVERSAL] {
		return h.session.GetSessionBySequenceNo(ctx, *command.SequenceNo, types.PURCHASE)
	}

	return nil, nil
}

func (h *OracleCommandHandler) handleReversalLogic(ctx context.Context, originalSession *session.SessionRecord, command oracle.OracleTransactionRequest) *api.LambdaApiResponse {
	if !h.isSupportedTransactionType(*command.OriginalType) || types.TransactionTypeOracleToTransactionType[*command.OriginalType] == types.REFUND {
		logger.Warn(ctx, fmt.Sprintf("original transaction type %s is not supported for reversal", *command.OriginalType))
		res := h.handleDefaultReversal(ctx, command)
		return &res
	}

	if originalSession == nil {
		logger.Warn(ctx, fmt.Sprintf("original session not found %s", *command.SequenceNo))
		res := h.handleDefaultReversal(ctx, command)
		return &res
	}

	if originalSession.Status != nil && (*originalSession.Status == string(types.FAILED) || *originalSession.Status == string(types.TERMINAL_BUSY)) {
		logger.Info(ctx, fmt.Sprintf("original session has failed %s", *command.SequenceNo))
		res := h.handleDefaultReversal(ctx, command)
		return &res
	}

	if originalSession.Status != nil && (*originalSession.Status == string(types.ACKNOWLEDGED) || *originalSession.Status == string(types.PROCESSING)) {
		logger.Warn(ctx, fmt.Sprintf("original session not finished processing %s", *command.SequenceNo))
		res, _ := api.ApiGatewayProxyResponse(http.StatusConflict, nil)
		return &res
	}

	return nil
}

func (h *OracleCommandHandler) originalSessionIsOlderThan3Days(ctx context.Context, originalSessionTimestamp *string) bool {
	if originalSessionTimestamp == nil {
		logger.Error(ctx, "original session timestamp is nil")
		return true
	}

	originalSessionTime, err := time.Parse(time.RFC3339, *originalSessionTimestamp)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error parsing original session timestamp to RFC3339 format - %s", *originalSessionTimestamp))
		return true
	}
	time3DaysAgo := time.Now().AddDate(0, 0, -3)
	return originalSessionTime.Before(time3DaysAgo)
}

func (h *OracleCommandHandler) handleRefund(ctx context.Context, transactionType types.TransactionType, command oracle.OracleTransactionRequest, deviceUuid *string, entityUuid *string) (api.LambdaApiResponse, error) {
	var originalSession *session.SessionRecord
	var err error

	// https://npco-dev.atlassian.net/browse/ZD-20267
	originalSession, err = h.getSessionForRefundBasedOnType(ctx, command)

	if err != nil {
		logger.Error(ctx, err.Error())
		return api.ApiGatewayProxyResponse(http.StatusBadRequest, nil)
	}

	// https://npco-dev.atlassian.net/browse/ZD-20267
	if transactionType == types.REVERSAL {
		reversalResponse := h.handleReversalLogic(ctx, originalSession, command)
		if reversalResponse != nil {
			return *reversalResponse, nil
		}
	}

	if transactionType == types.VOID && (originalSession == nil || originalSession.ResponseCode == nil || originalSession.IsoProcessingCode == nil) {
		logger.Error(ctx, fmt.Sprintf("original session missing required fields %s", *command.SequenceNo))
		return api.ApiGatewayProxyResponse(http.StatusBadRequest, nil)
	}

	sessionRecord := h.mapOracleCommandToSessionRecord(ctx, command, entityUuid)

	if originalSession != nil {
		if sessionIsOld := h.originalSessionIsOlderThan3Days(ctx, originalSession.Timestamp); sessionIsOld {
			// RC 29 - No Privilege to Perform Refund
			return h.handleDefaultResponse(ctx, sessionRecord, RT_ORIGINAL_SESSION_TOO_OLD, RC_29), nil
		}

		h.mapOriginalSessionFieldsToSessionRecord(originalSession, &sessionRecord)
	}

	// Hardcode the original processing code for unseen reversal
	if transactionType == types.REVERSAL && originalSession.IsoProcessingCode == nil {
		sessionRecord.OriginalIsoProcessingCode = ptr.String("003000")
	}

	return h.handleTransactionRequest(ctx, sessionRecord, deviceUuid)
}

func (h *OracleCommandHandler) mapOriginalSessionFieldsToSessionRecord(originalSession *session.SessionRecord, sessionRecord *session.SessionRecord) {
	sessionRecord.OriginalTransactionUuid = originalSession.TransactionUuid
	sessionRecord.OriginalIsoProcessingCode = originalSession.IsoProcessingCode
	sessionRecord.OriginalCvm = originalSession.Cvm
	sessionRecord.OriginalCardMedia = originalSession.CardMedia
	sessionRecord.OriginalCardExpiryDate = originalSession.CardExpiryDate
	sessionRecord.OriginalPanMasked = originalSession.PanMasked
}

func (h *OracleCommandHandler) handleDefaultReversal(ctx context.Context, command oracle.OracleTransactionRequest) api.LambdaApiResponse {
	// Send default decline response.
	// RC 21 - No Action Taken
	defaultResponse := oracle.OracleTransactionResponse{
		SequenceNo:  command.SequenceNo,
		TransType:   command.TransType,
		TransAmount: command.TransAmount,
		RespCode:    ptr.String(string(RC_21)),
		RespText:    ptr.String(string(types.DECLINED)),
		RRN:         ptr.String(DEFAULT_RRN),
		OfflineFlag: ptr.String(string(oracle.N)),
	}

	return h.handleLambdaResponse(ctx, defaultResponse)
}

func (h *OracleCommandHandler) handleDefaultResponse(ctx context.Context, session session.SessionRecord, responseText string, responseCode ResponseCode) api.LambdaApiResponse {
	defaultResponse := oracle.OracleTransactionResponse{
		SequenceNo:  session.SequenceNo,
		TransType:   ptr.String(*session.OracleTransactionType),
		TransAmount: session.Amount,
		RespCode:    ptr.String(string(responseCode)),
		RespText:    &responseText,
		RRN:         ptr.String(DEFAULT_RRN),
		OfflineFlag: ptr.String(string(oracle.N)),
	}

	return h.handleLambdaResponse(ctx, defaultResponse)
}

func (h *OracleCommandHandler) handleDefaultDecline(ctx context.Context, command oracle.OracleTransactionRequest, responseText string, responseCode ResponseCode) api.LambdaApiResponse {
	defaultResponse := oracle.OracleTransactionResponse{
		SequenceNo:  command.SequenceNo,
		TransType:   command.TransType,
		TransAmount: command.TransAmount,
		RespCode:    ptr.String(string(responseCode)),
		RespText:    &responseText,
		RRN:         ptr.String(DEFAULT_RRN),
		OfflineFlag: ptr.String(string(oracle.N)),
	}

	return h.handleLambdaResponse(ctx, defaultResponse)
}

func (h *OracleCommandHandler) handleLambdaResponse(ctx context.Context, response oracle.OracleTransactionResponse) api.LambdaApiResponse {
	xmlString, _ := xml.MarshalIndent(response, "", "  ")
	xmlString = []byte(xml.Header + string(xmlString))
	logger.Info(ctx, fmt.Sprintf("transactionResponse: \n %+v", string(xmlString)))

	return api.LambdaApiResponse{
		StatusCode:      int(http.StatusOK),
		Body:            string(xmlString),
		Headers:         HEADER_XML,
		IsBase64Encoded: false,
	}
}

// func (h *OracleCommandHandler) handleReversal(ctx context.Context, command oracle.OracleTransactionRequest, deviceUuid *string) (api.LambdaApiResponse, error) {
// 	originalTransType := types.TransactionTypeOracleToZeller[*command.OriginalType]
// 	originalSession, err := h.session.GetSessionBySequenceNo(ctx, *command.SequenceNo, originalTransType)
// 	if err != nil {
// 		logger.Error(ctx, err.Error())
// 		return api.ApiGatewayProxyResponse(http.StatusBadRequest, nil)
// 	}

// 	if originalSession == nil {
// 		logger.Error(ctx, fmt.Sprintf("original session not found %s", *command.SequenceNo))
// 		return h.handleDefaultReversal(ctx, command)
// 	}

// 	sessionRecord := h.mapOracleCommandToSessionRecord(ctx, command)

// 	sessionRecord.OriginalTransactionUuid = originalSession.TransactionUuid

// 	return h.handleTransactionRequest(ctx, sessionRecord, deviceUuid)
// }

func (h *OracleCommandHandler) handleTransactionRequest(ctx context.Context, sessionRecord session.SessionRecord, deviceUuid *string) (api.LambdaApiResponse, error) {
	err := h.session.InsertSession(ctx, sessionRecord)
	if err != nil {
		panic(err)
	}
	logger.Debug(ctx, fmt.Sprintf("sessionRecord: %+v", utils.BestEffortStringify(sessionRecord, false)))

	transactionRequest := h.mapSessionRecordToTransactionRequest(sessionRecord, deviceUuid)

	// https://npco-dev.atlassian.net/browse/ZD-20267
	if *sessionRecord.TransactionType == string(types.REVERSAL) {
		transactionRequest.Type = ptr.String(string(types.REFUND))
	}

	// send to posconnector and retry to check for an ack
	ack, ackError := h.publishRequestAndRetryForAck(ctx, transactionRequest)
	if !ack {
		logger.Warn(ctx, fmt.Sprintf("no acknowledgement was received: %s", ackError.Error()))
		// Send default transaction not acknowledged by terminal response.
		// RC P5 - User Response Timed Out
		err := h.session.UpdateSession(ctx, *sessionRecord.Id, map[string]interface{}{
			"status": string(types.FAILED),
		})
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error updating session record %s", err.Error()))
		}
		return h.handleDefaultResponse(ctx, sessionRecord, RT_TERMINAL_BUSY, RC_P5), nil
	}

	// retry to check for a response code
	updatedSessionRecord, retryError := h.retryForSessionResponse(ctx, *sessionRecord.Id)

	if retryError != nil {
		logger.Warn(ctx, fmt.Sprintf("no transaction response was received: %v", retryError.Error()))
		err := h.session.UpdateSession(ctx, *sessionRecord.Id, map[string]interface{}{
			"status": string(types.FAILED),
		})
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error updating session record %s", err.Error()))
		}
		return api.LambdaApiResponse{
			StatusCode: http.StatusRequestTimeout,
			Headers:    api.HEADER_TEXT_PLAIN,
		}, retryError
	}

	if *updatedSessionRecord.Status == string(types.TERMINAL_BUSY) {
		logger.Warn(ctx, fmt.Sprintf("device %s is busy and unable to accept the request", *transactionRequest.DeviceUuid))
		// Send default terminal busy response.
		// RC 09 - Request in progress
		return h.handleDefaultResponse(ctx, sessionRecord, RT_TERMINAL_BUSY, RC_09), nil
	}

	transactionResponse := h.mapSessionRecordToTransactionResponse(ctx, *updatedSessionRecord)

	return h.handleLambdaResponse(ctx, transactionResponse), nil
}

func (h *OracleCommandHandler) publishRequestAndRetryForAck(ctx context.Context, transactionRequest oracle.TransactionRequest) (bool, error) {
	retries := h.envService.CommandAckReceivedRetries()
	milliseconds := h.envService.CommandAckReceivedRetryIntervalMilliseconds()
	retryInterval := time.Duration(milliseconds) * time.Millisecond

	var eventId *string

	isSessionAcknowledged, retryError := utils.Retry(
		retries,
		0,
		func(lastErr error, retryAttempt int) (bool, error) {
			event := h.publisher.PublishToPosConnectorApi(ctx, transactionRequest, eventId, types.TransactionTypeToPosconnectorAction[*transactionRequest.Type])
			eventId = event.EventId

			time.Sleep(retryInterval)

			sessionRecord, err := h.session.GetSession(ctx, *transactionRequest.SessionUuid)

			if err != nil {
				return false, err
			}

			if sessionRecord == nil {
				return false, fmt.Errorf("session record not found")
			}

			if sessionRecord.Status != nil {
				return true, nil
			}

			if retryAttempt != retries {
				logger.Warn(ctx, fmt.Sprintf("retry the transaction request publish %s %s", utils.BestEffortStringify(transactionRequest, false), *eventId))
			}

			return false, fmt.Errorf("session record not updated with acknowledged")
		}, func(e error) {
			// Not interested in the error, we assume it will fail sometimes, just log it to debug
			// We log all the error messages in the end if all retries fail
			logger.Debug(ctx, fmt.Sprintf("session record not updated with acknowledged %v", e.Error()))
		})

	if retryError != nil {
		return false, retryError
	}

	return isSessionAcknowledged, nil
}

func (h *OracleCommandHandler) retryForSessionResponse(ctx context.Context, sessionUuid string) (*session.SessionRecord, error) {
	retries := h.envService.CommandResponseReceivedRetries()
	milliseconds := h.envService.CommandResponseReceivedRetryIntervalMilliseconds()
	updatedSessionRecord, retryError := utils.Retry(
		retries,
		time.Duration(milliseconds)*time.Millisecond,
		func(lastErr error, retryNo int) (*session.SessionRecord, error) {

			sessionRecord, err := h.session.GetSession(ctx, sessionUuid)

			if err != nil {
				return nil, err
			}

			if sessionRecord == nil {
				return nil, fmt.Errorf("session record not found")
			}

			if sessionRecord.ResponseCode != nil || (sessionRecord.Status != nil && *sessionRecord.Status == string(types.TERMINAL_BUSY)) {
				return sessionRecord, nil
			}

			return nil, fmt.Errorf("session record not updated with transaction response")
		}, func(e error) {
			// Not interested in the error, we assume it will fail sometimes, just log it to debug
			// We log all the error messages in the end if all retries fail
			logger.Debug(ctx, fmt.Sprintf("session record not updated with transaction response %v", e.Error()))
		})

	if retryError != nil {
		return nil, retryError
	}

	return updatedSessionRecord, nil
}

func (h *OracleCommandHandler) handleCommand(ctx context.Context, event api.LambdaApiRequest) (response api.LambdaApiResponse, e error) {
	defer func() {
		err := recover()
		if err != nil {
			logger.Warn(ctx, "recover from panic")
			response, _ = api.HandlePanic(err)
			logger.Error(ctx, fmt.Sprintf("response status %d, body %s", response.StatusCode, response.Body))
		}
	}()

	sessionUuid := uuid.NewString()
	ctx = context.WithValue(ctx, types.SessionUuid{}, sessionUuid)
	ctx = context.WithValue(ctx, logger.AggregateId{}, sessionUuid)

	oracleCommand := h.parseEventToOracleCommand(event.Body)

	transactionType := types.TransactionTypeOracleToTransactionType[*oracleCommand.TransType]

	ctx = logger.AddMetadata(ctx, "transactionUuid", sessionUuid)
	ctx = logger.AddMetadata(ctx, "transactionType", transactionType)
	ctx = logger.AddMetadata(ctx, "workstationNo", *oracleCommand.WSNo)
	ctx = logger.AddMetadata(ctx, "sequenceNo", *oracleCommand.SequenceNo)
	ctx = logger.AddMetadata(ctx, "oracleTransactionType", *oracleCommand.TransType)

	logger.Debug(ctx, fmt.Sprintf("event: %v", utils.BestEffortStringify(event, false)))
	logger.Info(ctx, fmt.Sprintf("transactionRequest: %v", event.Body))

	if commandValidation := h.commandValidation(ctx, event, oracleCommand); !commandValidation.isValid {
		return api.ApiGatewayProxyResponse(*commandValidation.httpErrorCode, nil)
	}

	// check if there is a config based on the SiteId (caid), reject if not
	config, err := h.connections.GetConfigByCaid(ctx, *oracleCommand.SiteId)
	if err != nil || !config.IsFound {
		logger.Error(ctx, fmt.Sprintf("config for caid %s is not found", *oracleCommand.SiteId))
		return api.ApiGatewayProxyResponse(http.StatusForbidden, nil)
	}

	ctx = logger.AddMetadata(ctx, "entityUuid", config.Item.EntityUuid)

	httpStatusCode := h.validateApiKey(ctx, event.QueryStringParameters, config.Item)
	if httpStatusCode != nil {
		return api.ApiGatewayProxyResponse(*httpStatusCode, nil)
	}

	// check if there is a pairing based on the WSNo (workstation number), reject if not
	pairing, err := h.connections.GetPairingByWorkstationNo(ctx, *oracleCommand.WSNo, config.Item.EntityUuid)
	if err != nil || !pairing.IsFound {
		logger.Error(ctx, fmt.Sprintf("workstation %s is not paired to a device for entity %s", *oracleCommand.WSNo, config.Item.EntityUuid))
		return h.handleDefaultDecline(ctx, oracleCommand, fmt.Sprintf("Workstation %s is not paired to a Zeller Terminal.", *oracleCommand.WSNo), RC_17), nil
	}

	ctx = logger.AddMetadata(ctx, "deviceUuid", pairing.Item.DeviceUuid)

	switch transactionType {
	case types.PURCHASE:
		return h.handlePurchase(ctx, oracleCommand, &pairing.Item.DeviceUuid, &config.Item.EntityUuid)
	case types.VOID, types.REFUND, types.REVERSAL:
		return h.handleRefund(ctx, transactionType, oracleCommand, &pairing.Item.DeviceUuid, &config.Item.EntityUuid)
		// https://npco-dev.atlassian.net/browse/ZD-20267
		// case transactionType == types.REVERSAL:
		// return h.handleReversal(ctx, oracleCommand, &pairing.Item.DeviceUuid)
	}

	logger.Error(ctx, fmt.Sprintf("%s/%s transaction type handler not implemented", *oracleCommand.TransType, transactionType))
	return api.ApiGatewayProxyResponse(http.StatusBadRequest, nil)
}

func (h *OracleCommandHandler) commandValidation(ctx context.Context, event api.LambdaApiRequest, command oracle.OracleTransactionRequest) commandValidation {
	// Early return for known unsupported types 44 and 10
	if command.TransType != nil && (*command.TransType == "44" || *command.TransType == "10") {
		msg := fmt.Sprintf("transaction type %s is not supported", *command.TransType)
		logger.Info(ctx, msg)
		return commandValidation{
			isValid:       false,
			httpErrorCode: ptr.Int(http.StatusBadRequest),
		}
	}

	cmdValidation := commandValidation{
		isValid:       true,
		httpErrorCode: nil,
	}

	validate := validator.New()
	err := validate.Struct(command)
	if err != nil {
		logger.Error(ctx, err.Error())
		cmdValidation = commandValidation{
			isValid:       false,
			httpErrorCode: ptr.Int(http.StatusBadRequest),
		}
	}

	// map transaction type based on Payment app mapping and reject if not supported
	if !h.isSupportedTransactionType(*command.TransType) {
		logger.Warn(ctx, fmt.Sprintf("transaction type %s is not supported", *command.TransType))
		cmdValidation = commandValidation{
			isValid:       false,
			httpErrorCode: ptr.Int(http.StatusBadRequest),
		}
	}

	if *command.TransType == types.TransactionTypeZellerToOracle[types.VOID] && command.OriginalRRN == nil {
		logger.Error(ctx, "OriginalRRN is required for void/refund")
		cmdValidation = commandValidation{
			isValid:       false,
			httpErrorCode: ptr.Int(http.StatusBadRequest),
		}
	}

	if !cmdValidation.isValid {
		if _, headersHasClientCert := event.Headers[HEADER_MTLS_CLIENTCERT_LEAF]; headersHasClientCert {
			event.Headers[HEADER_MTLS_CLIENTCERT_LEAF] = "N/A"
		}
		if _, queryStringParamsHasApiKey := event.QueryStringParameters[QUERY_STRING_PARAM_API_KEY]; queryStringParamsHasApiKey {
			event.QueryStringParameters[QUERY_STRING_PARAM_API_KEY] = h.maskApiKey(event.QueryStringParameters[QUERY_STRING_PARAM_API_KEY])
		}
		logger.Error(ctx, fmt.Sprintf("command failed validation: %v", utils.BestEffortStringify(event, false)))
	}

	return cmdValidation
}

func (h *OracleCommandHandler) isSupportedTransactionType(transactionType string) bool {
	_, ok := types.TransactionTypeOracleToTransactionType[transactionType]
	return ok
}

func (h *OracleCommandHandler) validateApiKey(ctx context.Context, eventQueryParameters map[string]string, config projection.ConnectionPosInterfaceModel) *int {
	apiKey, foundApiKey := eventQueryParameters[QUERY_STRING_PARAM_API_KEY]

	if !foundApiKey {
		logger.Error(ctx, "API key not found in query parameters")
		return ptr.Int(http.StatusUnauthorized)
	}

	if config.ClientApiKey == nil {
		logger.Error(ctx, "API key not found in config")
		return ptr.Int(http.StatusUnauthorized)
	}

	if *config.ClientApiKey != apiKey {
		logger.Error(ctx, fmt.Sprintf("API key %s does not match the config API key %s", h.maskApiKey(apiKey), h.maskApiKey(*config.ClientApiKey)))
		return ptr.Int(http.StatusForbidden)
	}

	return nil
}

func (h *OracleCommandHandler) maskApiKey(apiKey string) string {
	if len(apiKey) <= 6 {
		return strings.Repeat("*", len(apiKey))

	}
	return strings.Repeat("*", len(apiKey)-6) + apiKey[len(apiKey)-6:]
}
