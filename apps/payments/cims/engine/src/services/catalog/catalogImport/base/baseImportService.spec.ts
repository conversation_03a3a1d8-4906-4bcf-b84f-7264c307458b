import { ItemStatus } from '@npco/component-events-core/dist/catalog/types';

import type { INestApplicationContext } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { v4 } from 'uuid';

import { mockEnvServiceForLocalDbConnection } from '../../../../config/__mocks__/localDbConnection';
import { CatalogItemType } from '../../../../domain';
import type { ItemImport } from '../../../../domain/entities';
import {
  CatalogAttributeValue,
  CatalogItemAttribute,
  CatalogItemParent,
  CatalogItemVariant,
  CatalogAttributeSet,
  CatalogCategory,
  CatalogItem,
  CatalogItemSingle,
} from '../../../../domain/entities';
import { ItemSiteSettingsModel } from '../../../../domain/models/items/itemSiteSettingsModel';
import { initiateCatalogNestModule } from '../../../../lambdas/testcases/mockNestModule';
import { CatalogEntitySettingsService } from '../../catalogEntitySettings';
import { ItemImportService } from '../itemImportService';
import { ImportFailureReason } from '../types';

import { BaseImportService } from './baseImportService';

jest.mock('../../../../config/envService', () => mockEnvServiceForLocalDbConnection());

jest.mock('aws-xray-sdk', () => {
  return {
    getSegment: () => ({
      addNewSubsegment: jest.fn().mockReturnValue({}),
      addAnnotation: jest.fn(),
    }),
    captureAsyncFunc: async (name: string, cb: any) => {
      await cb({ addAnnotation: jest.fn(), close: jest.fn() });
    },
  };
});

describe('BaseImportSerice', () => {
  let appContext: INestApplicationContext;
  let dataSource: DataSource;
  let importService: ItemImportService;
  let catalogEntitySettingsService: CatalogEntitySettingsService;

  beforeAll(async () => {
    appContext = await initiateCatalogNestModule();
    dataSource = appContext.get(DataSource);
    importService = appContext.get(ItemImportService);
    catalogEntitySettingsService = appContext.get(CatalogEntitySettingsService);
  });

  afterAll(async () => {
    await dataSource.destroy();
  });

  it('should get only 1 new category for batch if others existing', async () => {
    const entityUuid = v4();
    const category = new CatalogCategory();
    category.name = 'APPLE';
    category.entityUuid = entityUuid;
    category.id = v4();
    category.color = 'white';

    await dataSource.getRepository(CatalogCategory).save(category);

    // act
    const { newCategories } = await importService.getCategoriesForBatch([
      {
        entityUuid,
        categories: ['apple', ' APPLE ', 'orange'],
      } as any,
    ]);

    // verify
    expect(newCategories).toHaveLength(1);
    expect(newCategories[0].name).toBe('orange');
    expect(newCategories[0].createdTime).toBeDefined();
  });

  it('should get only 1 new category for batch if others existing and have spaces', async () => {
    const entityUuid = v4();
    const category = new CatalogCategory();
    category.name = 'APPLE';
    category.entityUuid = entityUuid;
    category.id = v4();
    category.color = 'white';
    await dataSource.getRepository(CatalogCategory).save(category);

    // act
    const { newCategories } = await importService.getCategoriesForBatch([
      {
        entityUuid,
        categories: ['apple', 'APPLE ', 'orange'],
      } as any,
    ]);

    // verify
    expect(newCategories).toHaveLength(1);
    expect(newCategories[0].name).toBe('orange');
    expect(newCategories[0].createdTime).toBeDefined();
  });

  it('should get NO new categories for batch if all existing', async () => {
    const entityUuid = v4();
    const category1 = new CatalogCategory();
    category1.name = 'APPLE';
    category1.entityUuid = entityUuid;
    category1.id = v4();
    category1.color = 'white';
    await dataSource.getRepository(CatalogCategory).save(category1);

    const category2 = new CatalogCategory();
    category2.name = 'oRANGe';
    category2.entityUuid = entityUuid;
    category2.id = v4();
    category2.color = 'white';
    await dataSource.getRepository(CatalogCategory).save(category2);

    // act
    const { newCategories } = await importService.getCategoriesForBatch([
      {
        entityUuid,
        categories: ['apple', 'APPLE', 'orange'],
      } as any,
    ]);

    // verify
    expect(newCategories).toHaveLength(0);
  });

  it('should get only 1 new category for batch if others existing', async () => {
    const entityUuid = v4();
    const category = new CatalogCategory();
    category.name = 'APPLE';
    category.entityUuid = entityUuid;
    category.id = v4();
    category.color = 'white';
    await dataSource.getRepository(CatalogCategory).save(category);

    // act
    const { newCategories } = await importService.getCategoriesForBatch([
      {
        entityUuid,
        categories: ['apple', 'APPLE', 'orange'],
      } as any,
    ]);

    // verify
    expect(newCategories).toHaveLength(1);
    expect(newCategories[0].name).toBe('orange');
  });

  it('should get ALL as new categories for batch if NO existing', async () => {
    const entityUuid = v4();
    // act
    const { newCategories } = await importService.getCategoriesForBatch([
      {
        entityUuid,
        categories: ['apple', 'APPLE', 'orange'],
      } as any,
      {
        entityUuid,
        categories: ['PAPPLE', 'TAPPLE', 'OORANGE'],
      } as any,
      {
        entityUuid,
        categories: ['arange', 'aple', 'lime'],
      } as any,
    ]);

    // verify
    expect(newCategories).toHaveLength(8);
  });

  it('should get item price for gst applicable item', async () => {
    const price = await importService.getItemPrice({ gst: 'Y', price: 100000 } as any);
    expect(price).toEqual(90909);
  });

  it('should get item price for gst is not applicable  for item', async () => {
    const price = await importService.getItemPrice({ gst: 'N', price: 100000 } as any);
    expect(price).toEqual(100000);
  });

  test.each(['Y', 'N'])('should assign appropriate invoicesEnabled for item', async (enableForInvoice) => {
    const { createdItems } = await importService.batchCreateItemsAndCategories([
      {
        entityUuid: v4(),
        categories: ['a'],
        name: 'name',
        description: 'description',
        price: 1000,
        gst: 'Y',
        orderIndex: 1,
        sku: v4(),
        enableForInvoice,
        type: CatalogItemType.SINGLE,
      } as any,
    ]);

    expect(createdItems).toBeDefined();
    expect(createdItems![0].invoicesEnabled).toBe(enableForInvoice === 'Y');
  });

  it('should assign default value for attributeSetsEnabled and modifierEnabled for item', async () => {
    const { createdItems } = await importService.batchCreateItemsAndCategories([
      {
        entityUuid: v4(),
        categories: ['a'],
        name: 'name',
        description: 'description',
        price: 1000,
        gst: 'Y',
        orderIndex: 1,
        sku: v4(),
        type: CatalogItemType.SINGLE,
      } as any,
    ]);
    expect(createdItems).toBeDefined();
    expect(createdItems![0].attributeSetsEnabled).toBe(false);
    expect(createdItems![0].modifiersEnabled).toBe(false);
  });

  test('Should set siteSetting for item', async () => {
    const entityUuid = v4();
    const { createdItems } = await importService.batchCreateItemsAndCategories([
      {
        entityUuid,
        categories: ['a'],
        name: 'name',
        description: 'description',
        price: 1000,
        gst: 'Y',
        orderIndex: 1,
        sku: v4(),
        enableForInvoice: 'Y',
        sites: { '730725a6-ea0a-42af-9f56-302d1e76fb22': 'Y', site2: 'N' },
        type: CatalogItemType.SINGLE,
      } as any,
    ]);
    expect(createdItems).toBeDefined();
    expect(createdItems![0].siteSettings).toBeDefined();
    expect(createdItems![0].siteSettings![0].siteUuid).toBe('730725a6-ea0a-42af-9f56-302d1e76fb22');
  });

  it('should not auto assign sku setting not exist', async () => {
    const entityUuid = v4();
    const parentItems: ItemImport[] = [];
    const siteId1 = v4();
    const siteId2 = v4();
    parentItems.push({
      id: v4(),
      entityUuid,
      name: 't-shirt',
      categories: ['shirt'],
      price: 7,
      gst: 'N',
      description: 'parent description',
      available: 'Y',
      enableForInvoice: 'N',
      sku: null,
      gtin: 123456789,
      type: CatalogItemType.PARENT,
      sites: {
        [siteId1]: 'Y',
        [siteId2]: 'N',
      },
    } as any);

    const { createdItems } = await importService.batchCreateItemsAndCategories(parentItems as any);
    expect(createdItems).not.toBeFalsy();
    expect(createdItems.length).toEqual(1);
    const createdParent = createdItems[0] as CatalogItemParent;
    expect(createdParent.variants).toEqual([]);
    expect(createdParent.categories).toHaveLength(1);
    expect(createdParent.categories[0]?.name).toBe('shirt');
    expect(createdParent.attributes).toEqual([]);
    expect(createdParent.sku).toEqual(null);
    expect(createdParent.invoicesEnabled).toEqual(false);
    expect(createdParent.available).toEqual(true);
    expect(createdParent.gtin).toEqual('123456789');
    expect(createdParent.siteSettings![0].siteUuid).toBe(siteId1);
    expect(createdParent.siteSettings).toHaveLength(1);
  });

  it('should build parent items with variants, parent item and attribute sets', async () => {
    const entityUuid = v4();
    const siteId1 = v4();
    const siteId2 = v4();

    const variantItems = [
      {
        id: v4(),
        entityUuid,
        name: 't-shirt',
        categories: ['shirt'],
        price: 5,
        gst: 'Y',
        description: 'desc',
        attributeSet1: 'Color',
        attribute1: 'blue',
        attributeSet2: 'Size',
        attribute2: 'medium',
        sites: {
          [siteId1]: 'Y',
        },
        type: CatalogItemType.VARIANT,
      } as any,
      {
        id: v4(),
        entityUuid,
        name: 't-shirt',
        categories: ['shirt-x'],
        price: 5,
        gst: 'Y',
        description: 'desc',
        attributeSet1: 'Color',
        attribute1: 'green',
        sites: {
          [siteId1]: 'Y',
          [siteId2]: 'Y',
        },
        type: CatalogItemType.VARIANT,
      } as any,
    ];

    const { createdItems } = await importService.batchCreateItemsAndCategories(variantItems as any);
    expect(createdItems).not.toBeFalsy();
    expect(createdItems.length).toEqual(1);
    const updatedParent = createdItems[0] as CatalogItemParent;
    expect(updatedParent.variants).not.toBeFalsy();
    expect(updatedParent.variants[0].parentId).toStrictEqual(updatedParent.id); // this check verifies the fix for the bug: #POSI-3173
    expect(updatedParent.variants[0].name).toBe('blue, medium');
    expect(updatedParent.variants[1].name).toBe('green');
    expect(updatedParent.siteSettings?.length).toBe(2);
    expect(updatedParent.categories).toHaveLength(2);
  });

  it('should build parent items with variants, parent item and attribute', async () => {
    const entityUuid = v4();
    const siteId1 = v4();
    const siteId2 = v4();

    const variantItems = [
      {
        id: v4(),
        entityUuid,
        name: 't-shirt',
        categories: ['shirt'],
        price: 5,
        gst: 'Y',
        description: 'desc',
        attributeSet1: 'Color',
        attribute1: 'blue',
        attributeSet2: 'Size',
        attribute2: 'medium',
        sites: {
          [siteId1]: 'Y',
        },
        type: CatalogItemType.VARIANT,
      } as any,
      {
        id: v4(),
        entityUuid,
        name: 't-shirt',
        categories: ['shirt-x'],
        price: 5,
        gst: 'Y',
        description: 'desc',
        attributeSet1: 'Color',
        attribute1: 'green',
        sites: {
          [siteId1]: 'Y',
          [siteId2]: 'Y',
        },
        type: CatalogItemType.VARIANT,
      } as any,
    ];

    const { createdItems } = await importService.batchCreateItemsAndCategories(variantItems as any);
    expect(createdItems).not.toBeFalsy();
    expect(createdItems.length).toEqual(1);
    const updatedParent = createdItems[0] as CatalogItemParent;
    expect(updatedParent.variants).not.toBeFalsy();
    expect(updatedParent.variants[0].name).toBe('blue, medium');
    expect(updatedParent.variants[1].name).toBe('green');
    expect(updatedParent.siteSettings?.length).toBe(2);
    expect(updatedParent.categories).toHaveLength(2);
  });

  it('should create new attributes if none exist', async () => {
    const entityUuid = v4();
    const items = [
      {
        entityUuid,
        attributeSet1: 'Color',
        attribute1: 'green',
        attributeSet2: 'Size',
        attribute2: 'Small',
      },
      {
        entityUuid,
        attributeSet1: 'Flavour',
        attribute1: 'vanilla',
        attributeSet2: 'Color',
        attribute2: 'white',
        attributeSet3: 'Size',
        attribute3: 'Small',
      },
    ] as ItemImport[];

    const { batchAttributeSets, newAttributeSets, updatedAttributeSets } = await importService.getAttributeSetsForBatch(
      items,
    );

    expect(batchAttributeSets.size).toEqual(3);
    expect(newAttributeSets.length).toEqual(3);
    expect(updatedAttributeSets.length).toEqual(0);
  });

  it('should append itemImportRecords in the failedItems when category creation fails ', async () => {
    const entityUuid = v4();
    const items = [
      {
        name: 'item1',
        id: v4(),
        entityUuid,
        attributeSet1: 'Color',
        attribute1: 'green',
        attributeSet2: 'Size',
        attribute2: 'Small',
        categories: ['a'],
      },
      {
        name: 'item1',
        id: v4(),
        entityUuid,
        attributeSet1: 'Flavour',
        attribute1: 'vanilla',
        attributeSet2: 'Color',
        attribute2: 'white',
        attributeSet3: 'Size',
        attribute3: 'Small',
        categories: ['b'],
      },
    ] as ItemImport[];

    const attributeSetDb = {
      getRepository: () => ({
        createQueryBuilder: jest.fn().mockReturnValue({
          leftJoinAndSelect: jest.fn().mockReturnValue({
            where: jest.fn().mockReturnValue({
              getMany: jest.fn().mockRejectedValue('Database query failed'),
            }),
          }),
        }),
      }),
    };

    const mockCategoryDb = {
      getRepository: () => ({
        save: jest.fn(),
        create: jest.fn(),
      }),
      getExistingCategoriesByNames: jest.fn().mockRejectedValue('Db query failed'),
    };

    const catalogSettingService = {
      getSettings: jest.fn().mockResolvedValue({ autoSkuEnabled: false }),
    };

    const itemImportService = new BaseImportService(
      {} as any,
      mockCategoryDb as any,
      catalogSettingService as any,
      attributeSetDb as any,
    );

    try {
      const batchCreate = await itemImportService.batchCreateItemsAndCategories(items);
      expect(batchCreate.failedToCreateItems).toHaveLength(2);
      const batchUpdate = await itemImportService.batchUpdateItemsAndCategories(items);
      expect(batchUpdate.failedToUpdateItems).toHaveLength(2);
    } catch (e: any) {
      expect(false).toBe(true);
    }
  });

  it('should append itemImportRecords in the failedItems when create attribute fails ', async () => {
    const entityUuid = v4();
    const items = [
      {
        name: 'item1',
        id: v4(),
        entityUuid,
        attributeSet1: 'Color',
        attribute1: 'green',
        attributeSet2: 'Size',
        attribute2: 'Small',
        categories: ['a'],
      },
      {
        name: 'item1',
        id: v4(),
        entityUuid,
        attributeSet1: 'Flavour',
        attribute1: 'vanilla',
        attributeSet2: 'Color',
        attribute2: 'white',
        attributeSet3: 'Size',
        attribute3: 'Small',
        categories: ['b'],
      },
    ] as ItemImport[];

    const attributeSetDb = {
      getRepository: () => ({
        createQueryBuilder: jest.fn().mockReturnValue({
          leftJoinAndSelect: jest.fn().mockReturnValue({
            where: jest.fn().mockReturnValue({
              getMany: jest.fn().mockRejectedValue('Database query failed'),
            }),
          }),
        }),
      }),
    };

    const mockCategoryDb = {
      getRepository: () => ({
        save: jest.fn(),
        create: jest.fn(),
      }),
      getExistingCategoriesByNames: jest.fn().mockResolvedValue([
        {
          id: v4(),
          name: 'a',
        },
      ]),
    };

    const catalogSettingService = {
      getSettings: jest.fn().mockResolvedValue({ autoSkuEnabled: false }),
    };

    const itemImportService = new BaseImportService(
      {} as any,
      mockCategoryDb as any,
      catalogSettingService as any,
      attributeSetDb as any,
    );

    try {
      const batchCreate = await itemImportService.batchCreateItemsAndCategories(items);
      expect(batchCreate.failedToCreateItems).toHaveLength(2);
      const batchUpdate = await itemImportService.batchUpdateItemsAndCategories(items);
      expect(batchUpdate.failedToUpdateItems).toHaveLength(2);
    } catch (e: any) {
      expect(false).toBe(true);
    }
  });

  it('should build parent item first and then save variants', async () => {
    const entityUuid = v4();
    const parentItems: ItemImport[] = [];
    const siteId1 = v4();
    const siteId2 = v4();
    parentItems.push({
      id: v4(),
      entityUuid,
      name: 't-shirt',
      categories: ['shirt'],
      price: 7,
      gst: 'N',
      description: 'parent description',
      available: 'Y',
      enableForInvoice: 'N',
      sku: 'AZW3Z3KW',
      type: CatalogItemType.PARENT,
      sites: {
        [siteId1]: 'Y',
        [siteId2]: 'N',
      },
    } as any);

    const { createdItems } = await importService.batchCreateItemsAndCategories(parentItems as any);
    expect(createdItems).not.toBeFalsy();
    expect(createdItems.length).toEqual(1);
    const createdParent = createdItems[0] as CatalogItemParent;
    expect(createdParent.variants).toEqual([]);
    expect(createdParent.categories).toHaveLength(1);
    expect(createdParent.categories[0]?.name).toBe('shirt');
    expect(createdParent.attributes).toEqual([]);
    expect(createdParent.sku).toEqual('AZW3Z3KW');
    expect(createdParent.siteSettings![0].siteUuid).toBe(siteId1);
    expect(createdParent.siteSettings).toHaveLength(1);

    const variantItems = [
      {
        id: v4(),
        entityUuid,
        name: 't-shirt',
        categories: ['shirt'],
        price: 5,
        gst: 'Y',
        description: 'desc',
        attributeSet1: 'Color',
        attribute1: 'blue',
        attributeSet2: 'Size',
        attribute2: 'medium',
        sites: {
          [siteId1]: 'Y',
        },
        type: CatalogItemType.VARIANT,
      } as any,
      {
        id: v4(),
        entityUuid,
        name: 't-shirt',
        categories: ['shirt-x'],
        price: 5,
        gst: 'Y',
        description: 'desc',
        attributeSet1: 'Color',
        attribute1: 'green',
        sites: {
          [siteId1]: 'Y',
          [siteId2]: 'Y',
        },
        type: CatalogItemType.VARIANT,
      } as any,
    ];

    const { updatedItems } = await importService.batchCreateItemsAndCategories(variantItems as any);
    expect(updatedItems).not.toBeFalsy();
    expect(updatedItems.length).toEqual(1);
    const updatedParent = updatedItems[0] as CatalogItemParent;
    expect(updatedParent.variants).not.toBeFalsy();
    expect(updatedParent.variants[0].name).toBe('blue, medium');
    expect(updatedParent.variants[1].name).toBe('green');
    expect(updatedParent.siteSettings?.length).toBe(2);
    expect(updatedParent.categories).toHaveLength(2);
  });

  it('should create only 2 new attributeSets if one exists', async () => {
    const entityUuid = v4();
    const attributeSetId = v4();
    const unixTime = Math.floor(new Date().getTime() / 1000);
    const attributeSet = new CatalogAttributeSet();
    attributeSet.id = attributeSetId;
    attributeSet.entityUuid = entityUuid;
    attributeSet.name = 'Size';
    attributeSet.status = ItemStatus.ACTIVE;
    attributeSet.createdTime = unixTime;
    attributeSet.values = [
      {
        id: v4(),
        entityUuid,
        catalogAttributeSetId: attributeSetId,
        ordinal: 1,
        value: 'Small',
        status: ItemStatus.ACTIVE,
        createdTime: unixTime,
      } as CatalogAttributeValue,
    ];

    await dataSource.getRepository(CatalogAttributeSet).save(attributeSet);

    const items = [
      {
        entityUuid,
        attributeSet1: 'Color',
        attribute1: 'green',
        attributeSet2: 'Size',
        attribute2: 'Small',
      },
      {
        entityUuid,
        attributeSet1: 'Flavour',
        attribute1: 'vanilla',
        attributeSet3: 'Size',
        attribute3: 'Small',
      },
    ] as ItemImport[];

    const { batchAttributeSets, newAttributeSets, updatedAttributeSets } = await importService.getAttributeSetsForBatch(
      items,
    );

    console.log('updateSet', updatedAttributeSets);
    expect(batchAttributeSets.size).toEqual(3);
    expect(newAttributeSets.length).toEqual(2);
    expect(updatedAttributeSets.length).toEqual(0);
  });

  it('should create a 2 new attribute sets and update one', async () => {
    const entityUuid = v4();
    const attributeSetId = v4();
    const unixTime = Math.floor(new Date().getTime() / 1000);
    const attributeSet = new CatalogAttributeSet();
    attributeSet.id = attributeSetId;
    attributeSet.entityUuid = entityUuid;
    attributeSet.name = 'Size';
    attributeSet.status = ItemStatus.ACTIVE;
    attributeSet.createdTime = unixTime;
    attributeSet.values = [
      {
        id: v4(),
        entityUuid,
        catalogAttributeSetId: attributeSetId,
        ordinal: 1,
        value: 'Small',
        status: ItemStatus.ACTIVE,
        createdTime: unixTime,
      } as CatalogAttributeValue,
    ];

    await dataSource.getRepository(CatalogAttributeSet).save(attributeSet);

    const items = [
      {
        entityUuid,
        attributeSet1: 'Color',
        attribute1: 'green',
        attributeSet2: 'Size',
        attribute2: 'Medium',
      },
      {
        entityUuid,
        attributeSet1: 'Flavour',
        attribute1: 'vanilla',
        attributeSet3: 'Size',
        attribute3: 'Small',
      },
    ] as ItemImport[];

    const { batchAttributeSets, newAttributeSets, updatedAttributeSets } = await importService.getAttributeSetsForBatch(
      items,
    );

    expect(batchAttributeSets.size).toEqual(3);
    expect(newAttributeSets.length).toEqual(2);
    expect(updatedAttributeSets.length).toEqual(1);
  });

  it('should parse attriburteSet and attribute from import item', () => {
    const item = {
      attributeSet1: 'Color',
      attribute1: 'green',
      attributeSet2: 'Size',
      attribute2: 'Small',
    };

    const res = importService.parseAttributeSetsAndAttributes(item as ItemImport);
    expect(res).toEqual([
      {
        attributeSet: item.attributeSet1,
        attribute: item.attribute1,
      },
      {
        attributeSet: item.attributeSet2,
        attribute: item.attribute2,
      },
    ]);
  });

  it('should check for items with duplicate attributeSets', () => {
    const item1 = {
      name: 'item1',
      attributeSet1: 'Color',
      attribute1: 'blue',
    } as ItemImport;

    const item2 = {
      name: 'item2',
      attributeSet1: 'Color',
      attribute1: 'blue',
    } as ItemImport;

    expect(importService.hasDuplicateAttributes(item1, item2)).toBe(true);
    expect(importService.hasDuplicateAttributes(item1, {} as ItemImport)).toBe(false);
    expect(importService.hasDuplicateAttributes(item1, { ...item2, attribute1: 'green' })).toBe(false);
  });

  it('should compare existing and new attribute values', () => {
    const existingValues = ['small', 'green'];
    const newValues = ['yellow', 'small'];

    expect(importService.diffExistingAndNewValues(existingValues, newValues)).toEqual(['yellow']);
  });

  it('should check if import item has attribute set fields', () => {
    const withAttributes = {
      attributeSet1: 'Color',
    } as ItemImport;

    expect(importService.hasAttributeSet(withAttributes)).toEqual(true);
    expect(importService.hasAttributeSet({} as ItemImport)).toEqual(false);
  });

  it('should group single and parent items', () => {
    const items = [
      {
        name: 't-shirt',
        price: 5,
      },
      {
        name: 't-shirt',
        attributeSet1: 'Color',
        attribute1: 'red',
        price: 6,
      },
      {
        name: 't-shirt',
        attributeSet1: 'Color',
        attribute1: 'yellow',
        price: 7,
      },
      {
        name: 'candle',
        price: 4,
      },
    ] as ItemImport[];

    const { singleItems, parentItems } = importService.groupSingleAndParentItems(items);

    expect(Array.from(singleItems.values())).toEqual([
      {
        name: 'candle',
        price: 4,
      },
    ]);
    expect(parentItems.get('t-shirt')?.length).toEqual(3);
  });

  test('should assign sku if autoSku is enabled', async () => {
    const entityUuid = v4();
    jest
      .spyOn(catalogEntitySettingsService, 'getSettings')
      .mockResolvedValue({ autoSkuEnabled: true, entityUuid } as any);
    const { createdItems } = await importService.batchCreateItemsAndCategories([
      {
        entityUuid,
        categories: ['a'],
        name: 'name',
        description: 'description',
        price: 1000,
        gst: 'Y',
        orderIndex: 1,
        enableForInvoice: 'Y',
        type: CatalogItemType.SINGLE,
      } as any,
    ]);
    expect(createdItems).toBeDefined();
    expect(createdItems![0].sku).toBeDefined();
  });

  it('should assign attribute set relation to parent Item', () => {
    const entityUuid = v4();
    const itemId = v4();
    const attributeSetId = v4();
    const parentItem = {
      id: itemId,
      attributes: [
        {
          id: v4(),
          entityUuid,
          catalogItemId: itemId,
          catalogAttributeSetId: attributeSetId,
          ordinal: 1,
        },
      ],
    } as CatalogItemParent;
    const attributeSets = [
      {
        id: v4(),
      },
      {
        id: attributeSetId,
        entityUuid,
        name: 'testSet',
      },
    ] as CatalogAttributeSet[];

    importService.addAttributeSetRelationToParentItem(parentItem, attributeSets);
    expect(parentItem.attributes!.at(0)!.catalogAttributeSet).toEqual(attributeSets[1]);
  });

  describe('remove duplicates', () => {
    it('should mark items as duplicate if name,sku and attributeSets matches with other item in import data', async () => {
      const entityUuid = v4();
      const DUPLICATE_NAME = 'duplicate';

      const duplicateRawItemImportData = {
        id: v4(),
        entityUuid,
        jobUuid: v4(),
        categories: ['a'],
        name: DUPLICATE_NAME,
        description: v4(),
        price: 10,
        gst: 'Y',
        orderIndex: 1,
        sku: v4(),
        attributeSet1: 'Color',
        attribute1: 'green',
      } as any;
      // act
      const { failedItems, distinctItems } = await importService.removeDuplicateItems({
        entityUuid,
        items: [duplicateRawItemImportData, duplicateRawItemImportData],
      });

      // verify
      expect(failedItems).toHaveLength(2);
      failedItems.forEach((item) => expect(item.importFailureReason).toEqual(ImportFailureReason.DUPLICATE_ITEM_ERROR));
      expect(distinctItems).toHaveLength(0);
    });

    it('should mark items as duplicate if name matches with other item in import data and one of sku is null', async () => {
      const entityUuid = v4();
      const DUPLICATE_NAME = 'duplicate';

      const duplicateRawItemImportData = {
        id: v4(),
        entityUuid,
        jobUuid: v4(),
        categories: ['a'],
        name: DUPLICATE_NAME,
        description: v4(),
        price: 20,
        gst: 'Y',
        orderIndex: 1,
        sku: v4(),
      } as any;
      // act
      const { failedItems, distinctItems } = await importService.removeDuplicateItems({
        entityUuid,
        items: [duplicateRawItemImportData, { ...duplicateRawItemImportData, sku: null }],
      });

      // verify
      expect(failedItems).toHaveLength(2);
      failedItems.forEach((item) => expect(item.importFailureReason).toEqual(ImportFailureReason.DUPLICATE_ITEM_ERROR));
      expect(distinctItems).toHaveLength(0);
    });

    it('should mark items as duplicate if name matches with other item in import data and both of sku are null', async () => {
      const entityUuid = v4();
      const DUPLICATE_NAME = 'duplicate';

      const duplicateRawItemImportData = {
        id: v4(),
        entityUuid,
        jobUuid: v4(),
        categories: ['a'],
        name: DUPLICATE_NAME,
        description: v4(),
        price: 20,
        gst: 'Y',
        orderIndex: 1,
        sku: null,
      } as any;
      // act
      const { failedItems, distinctItems } = await importService.removeDuplicateItems({
        entityUuid,
        items: [duplicateRawItemImportData, duplicateRawItemImportData],
      });

      // verify
      expect(failedItems).toHaveLength(2);
      failedItems.forEach((item) => expect(item.importFailureReason).toEqual(ImportFailureReason.DUPLICATE_ITEM_ERROR));
      expect(distinctItems).toHaveLength(0);
    });

    it('should not mark as duplicate if item name matches in db but item is a VARIANT', async () => {
      const entityUuid = v4();
      const DUPLICATE_NAME = 'duplicate';
      const DUPLICATE_SKU = '1234';

      const category = new CatalogCategory();
      category.name = 'APPLE';
      category.entityUuid = entityUuid;
      category.id = v4();
      category.color = 'white';

      const item1 = new CatalogItemParent();
      item1.categories = [category];
      item1.entityUuid = entityUuid;
      item1.type = CatalogItemType.PARENT;
      item1.price = 20;
      item1.name = DUPLICATE_NAME;
      item1.description = v4();
      item1.sku = DUPLICATE_SKU;
      item1.id = v4();

      await dataSource.getRepository(CatalogItem).save([item1]);

      const duplicateNameVariantImportData = {
        id: v4(),
        entityUuid,
        jobUuid: v4(),
        categories: ['a'],
        name: DUPLICATE_NAME,
        description: v4(),
        price: 30,
        sku: '5678',
        orderIndex: 1,
        type: CatalogItemType.VARIANT,
        attributeSet1: 'Size',
        attribute1: 'small',
      } as any;

      const duplicateSkuVariantImportData = {
        id: v4(),
        entityUuid,
        jobUuid: v4(),
        categories: ['a'],
        name: DUPLICATE_NAME,
        description: v4(),
        price: 30,
        sku: DUPLICATE_SKU,
        orderIndex: 1,
        type: CatalogItemType.VARIANT,
        attributeSet1: 'Size',
        attribute1: 'medium',
      } as any;

      const duplicateNameParentImportData = {
        id: v4(),
        entityUuid,
        jobUuid: v4(),
        categories: ['a'],
        name: DUPLICATE_NAME,
        description: v4(),
        price: 30,
        sku: '5677',
        orderIndex: 1,
        type: CatalogItemType.PARENT,
      } as any;

      const duplicateSkuSingleItemImportData = {
        id: v4(),
        entityUuid,
        jobUuid: v4(),
        categories: ['a'],
        name: v4(),
        description: v4(),
        price: 30,
        sku: DUPLICATE_SKU,
        orderIndex: 1,
        type: CatalogItemType.SINGLE,
      } as any;

      const { distinctItems, failedItems } = await importService.removeDuplicateItems({
        entityUuid,
        items: [
          duplicateSkuSingleItemImportData,
          duplicateNameParentImportData,
          duplicateSkuVariantImportData,
          duplicateNameVariantImportData,
        ],
      });

      expect(failedItems).toHaveLength(3);
      expect(failedItems[0].id).toBe(duplicateSkuSingleItemImportData.id);
      expect(failedItems[1].id).toBe(duplicateNameParentImportData.id);
      expect(failedItems[2].id).toBe(duplicateSkuVariantImportData.id);
      expect(distinctItems).toHaveLength(1);
      expect(distinctItems[0].id).toBe(duplicateNameVariantImportData.id);
    });

    it('should mark items as duplicate if name or sku matches the item in DB', async () => {
      const entityUuid = v4();
      const DUPLICATE_NAME = 'duplicate';
      const DUPLICATE_SKU = '1234';

      const category = new CatalogCategory();
      category.name = 'APPLE';
      category.entityUuid = entityUuid;
      category.id = v4();
      category.color = 'white';

      const item1 = new CatalogItemSingle();
      item1.categories = [category];
      item1.entityUuid = entityUuid;
      item1.type = CatalogItemType.SINGLE;
      item1.price = 20;
      item1.name = DUPLICATE_NAME;
      item1.description = v4();
      item1.sku = DUPLICATE_SKU;
      item1.id = v4();

      await dataSource.getRepository(CatalogItem).save([item1]);

      const duplicateRawItemImportData = {
        id: v4(),
        entityUuid,
        jobUuid: v4(),
        categories: ['a'],
        name: DUPLICATE_NAME,
        description: v4(),
        price: 30,
        sku: DUPLICATE_SKU,
        orderIndex: 1,
      } as any;
      // act
      const { failedItems, distinctItems } = await importService.removeDuplicateItems({
        entityUuid,
        items: [
          duplicateRawItemImportData,
          { ...duplicateRawItemImportData, name: 'not duplicate', id: v4(), sku: '345' },
        ],
      });

      // verify
      expect(failedItems).toHaveLength(1);
      expect(failedItems[0].name).toEqual(DUPLICATE_NAME);
      expect(distinctItems).toHaveLength(1);
      expect(distinctItems[0].name).toEqual('not duplicate');
      expect(distinctItems[0].sku).toEqual('345');
    });

    it('should mark identical item names as duplicates', () => {
      const itemA = {
        name: 'name',
      } as ItemImport;
      const itemB = {
        name: 'name',
      } as ItemImport;

      expect(importService.isDuplicate(itemA, itemB)).toBe(true);
    });

    it('should mark identical item skus as duplicates', () => {
      const itemA = {
        name: 'name1',
        sku: 'PJH1NAMW',
      } as ItemImport;
      const itemB = {
        name: 'name2',
        sku: 'PJH1NAMW',
      } as ItemImport;

      expect(importService.isDuplicate(itemA, itemB)).toBe(true);
    });

    it('should mark distinct item names as non duplicates', () => {
      const itemA = {
        name: 'name1',
      } as ItemImport;
      const itemB = {
        name: 'name2',
      } as ItemImport;

      expect(importService.isDuplicate(itemA, itemB)).toBeFalsy();
    });

    it('should mark distinct items as non duplicates', () => {
      const itemA = {
        name: 'name1',
        sku: 'PJH1NAMW',
      } as ItemImport;
      const itemB = {
        name: 'name2',
        sku: 'S1E0J5R0',
      } as ItemImport;

      expect(importService.isDuplicate(itemA, itemB)).toBeFalsy();
    });

    it('should mark same item names with distinct attributeSets as non duplicates', () => {
      const itemA = {
        name: 'name1',
        attributeSet1: 'Size',
        attribute1: 'small',
      } as ItemImport;
      const itemB = {
        name: 'name2',
        attributeSet1: 'Size',
        attribute1: 'medium',
      } as ItemImport;

      expect(importService.isDuplicate(itemA, itemB)).toBeFalsy();
    });

    it('should mark distinct item names with same attributesets as non duplicates', () => {
      const itemA = {
        name: 'name1',
        attributeSet1: 'Size',
        attribute1: 'small',
      } as ItemImport;
      const itemB = {
        name: 'name2',
        attributeSet1: 'Size',
        attribute1: 'small',
      } as ItemImport;

      expect(importService.isDuplicate(itemA, itemB)).toBeFalsy();
    });
  });

  describe('batch update items', () => {
    const entityUuid = v4();
    const itemImports: ItemImport[] = [];
    let parentItem1Import: ItemImport;
    let parentItem1Variant: ItemImport;
    let parentItem2Import: ItemImport;
    let singleItemImport: ItemImport;
    const parentItem1 = new CatalogItemParent();
    const parentItem2 = new CatalogItemParent();
    const singleItem = new CatalogItemSingle();
    const categories: CatalogCategory[] = [];
    const attributeSets = new Map<string, CatalogAttributeSet>();
    const singleItemNewSiteId = v4();

    beforeAll(async () => {
      const unixTime = Math.floor(new Date().getTime() / 1000);
      const category = new CatalogCategory();
      category.name = 'tshirt';
      category.entityUuid = entityUuid;
      category.id = v4();
      category.color = 'white';
      categories.push(category);

      const attributeSet1 = new CatalogAttributeSet();
      attributeSet1.id = v4();
      attributeSet1.entityUuid = entityUuid;
      attributeSet1.name = 'Size';
      attributeSet1.status = ItemStatus.ACTIVE;
      attributeSet1.createdTime = unixTime;
      attributeSet1.updatedTime = unixTime;

      const attributeValue1 = new CatalogAttributeValue();
      attributeValue1.id = v4();
      attributeValue1.catalogAttributeSetId = attributeSet1.id;
      attributeValue1.catalogAttributeSet = attributeSet1;
      attributeValue1.entityUuid = entityUuid;
      attributeValue1.ordinal = 1;
      attributeValue1.value = 'small';
      attributeValue1.status = ItemStatus.ACTIVE;
      attributeValue1.createdTime = unixTime;

      attributeSet1.values = [attributeValue1];
      await dataSource.getRepository(CatalogAttributeSet).save(attributeSet1);
      await dataSource.getRepository(CatalogCategory).save(category);
      attributeSets.set(attributeSet1.name, attributeSet1);

      parentItem1.id = v4();
      parentItem1.name = `Parent Item 1 ${v4()}`;
      parentItem1.price = 5;
      parentItem1.entityUuid = entityUuid;
      parentItem1.categories = [category];
      parentItem1.reportingCategoryUuid = category.id;
      parentItem1.status = ItemStatus.ACTIVE;
      parentItem1.createdTime = unixTime;
      parentItem1.referenceNumber = '0001';

      const itemAttribute = new CatalogItemAttribute();
      itemAttribute.id = v4();
      itemAttribute.entityUuid = entityUuid;
      itemAttribute.catalogItemId = parentItem1.id;
      itemAttribute.catalogAttributeSetId = attributeSet1.id;
      itemAttribute.catalogAttributeValueIds = [attributeSet1.values[0].id];
      itemAttribute.ordinal = 1;
      itemAttribute.createdTime = unixTime;

      const itemVariant = new CatalogItemVariant();
      itemVariant.id = v4();
      itemVariant.entityUuid = entityUuid;
      itemVariant.parentId = parentItem1.id;
      itemVariant.name = `small`;
      itemVariant.price = 4;
      itemVariant.attributeValueIds = [attributeSet1.values[0].id];
      itemVariant.attributeCompositeId = [attributeSet1.values[0].id].join('#');
      itemVariant.ordinal = 1;
      itemVariant.createdTime = unixTime;
      itemVariant.referenceNumber = 'ghfkl';

      parentItem1.attributes = [itemAttribute];
      parentItem1.variants = [itemVariant];

      parentItem2.id = v4();
      parentItem2.name = `Parent Item 2 ${v4()}`;
      parentItem2.price = 7;
      parentItem2.entityUuid = entityUuid;
      parentItem2.categories = [category];
      parentItem2.reportingCategoryUuid = category.id;
      parentItem2.status = ItemStatus.ACTIVE;
      parentItem2.createdTime = unixTime;
      parentItem2.referenceNumber = '0002';
      parentItem2.variants = [];
      parentItem2.attributes = [];

      const existingSiteUuid = v4();
      singleItem.id = v4();
      singleItem.entityUuid = entityUuid;
      singleItem.name = `Single item ${v4()}`;
      singleItem.price = 10;
      singleItem.categories = [category];
      singleItem.reportingCategoryUuid = category.id;
      singleItem.status = ItemStatus.ACTIVE;
      singleItem.createdTime = unixTime;
      singleItem.referenceNumber = '0003';
      singleItem.siteSettings = [
        ...new ItemSiteSettingsModel().create(
          { sites: [existingSiteUuid], entityUuid: singleItem.entityUuid } as any,
          singleItem,
        ),
      ];

      await dataSource.getRepository(CatalogItemParent).save(parentItem1);
      await dataSource.getRepository(CatalogItemParent).save(parentItem2);
      await dataSource.getRepository(CatalogItemSingle).save(singleItem);

      const jobUuid = v4();
      parentItem1Import = {
        id: v4(),
        entityUuid,
        jobUuid,
        categories: ['tshirt'],
        name: `Item 1 Update Name`,
        price: 3,
        gst: 'Y',
        sku: v4(),
        referenceNumber: parentItem1.referenceNumber,
      } as ItemImport;
      parentItem1Variant = {
        id: v4(),
        entityUuid,
        jobUuid,
        name: `medium`,
        price: 3,
        gst: 'Y',
        sku: v4(),
        attributeSet1: 'Size',
        attribute1: 'medium',
        attributeSet2: 'Colour',
        attribute2: 'red',
        referenceNumber: parentItem1.variants[0].referenceNumber,
      } as ItemImport;

      parentItem2Import = {
        id: v4(),
        entityUuid,
        jobUuid,
        categories: ['tshirt'],
        name: `Item 2 Update Name`,
        price: 3,
        gst: 'Y',
        orderIndex: 1,
        sku: v4(),
        referenceNumber: parentItem2.referenceNumber,
      } as ItemImport;

      singleItemImport = {
        id: v4(),
        entityUuid,
        jobUuid,
        categories: ['tshirt'],
        name: `Single item Update Name`,
        price: 15,
        gst: 'N',
        orderIndex: 1,
        sku: v4(),
        referenceNumber: singleItem.referenceNumber,
        sites: {
          [existingSiteUuid]: 'N',
          [singleItemNewSiteId]: 'Y',
        },
      } as ItemImport;

      itemImports.push(parentItem1Import);
      itemImports.push(parentItem1Variant);
      itemImports.push(parentItem2Import);
      itemImports.push(singleItemImport);
    });

    it('should get items by reference number', async () => {
      const res = await importService.getItemsByReferenceNumber(entityUuid, [
        parentItem1.referenceNumber,
        parentItem1.variants[0].referenceNumber,
        parentItem2.referenceNumber,
        singleItem.referenceNumber,
      ]);

      expect(res.parentsWithVariantsUpdate).toHaveLength(1);
      expect(res.parentsWithoutVariantsUpdate).toHaveLength(1);
      expect(res.singleItems).toHaveLength(1);
    });

    it('should group single and parent item updates', async () => {
      const res = await importService.groupSingleAndParentUpdates(
        [
          parentItem1.referenceNumber,
          parentItem1.variants[0].referenceNumber,
          parentItem2.referenceNumber,
          singleItem.referenceNumber,
        ],
        itemImports,
      );

      expect(res.singleRecords).toHaveLength(1);
      expect(res.parentRecords).toHaveLength(2);
      expect(res.singleRecords[0].existingRecord.id).toBe(singleItem.id);

      res.parentRecords.forEach((record) => {
        const parentItem = [parentItem1, parentItem2].find((item) => item.id === record.existingRecord.id);
        expect(parentItem).toBeTruthy();
        expect(record?.importVariantRecords).toHaveLength(parentItem?.variants.length as number);
      });
    });

    it('should handle single item updates', async () => {
      const { singleRecords } = await importService.groupSingleAndParentUpdates(
        [
          parentItem1.referenceNumber,
          parentItem1.variants[0].referenceNumber,
          parentItem2.referenceNumber,
          singleItem.referenceNumber,
        ],
        itemImports,
      );

      const updatedItem = importService.handleSingleItemUpdate(
        singleRecords[0].importRecord,
        singleRecords[0].existingRecord,
        categories,
        attributeSets,
      );

      console.log(itemImports[3]);

      expect(updatedItem.type).toEqual(CatalogItemType.SINGLE);
      expect(updatedItem.name).toEqual(singleItemImport.name);
      expect(updatedItem.price).toEqual(singleItemImport.price);
      expect(updatedItem.siteSettings).toHaveLength(1);
      expect(updatedItem.siteSettings![0].siteUuid).toEqual(singleItemNewSiteId);
    });

    it('should update a single item to parent', async () => {
      const { singleRecords } = await importService.groupSingleAndParentUpdates(
        [
          parentItem1.referenceNumber,
          parentItem1.variants[0].referenceNumber,
          parentItem2.referenceNumber,
          singleItem.referenceNumber,
        ],
        itemImports,
      );

      singleRecords[0].importRecord = {
        ...singleRecords[0].importRecord,
        attributeSet1: 'Size',
        attribute1: 'small',
      };

      const updatedItem = (await importService.handleSingleItemUpdate(
        singleRecords[0].importRecord,
        singleRecords[0].existingRecord,
        categories,
        attributeSets,
      )) as CatalogItemParent;

      expect(updatedItem.type).toBe(CatalogItemType.PARENT);
      expect(updatedItem.variants[0].name).toEqual('small');
      expect(updatedItem.variants[0].price).toEqual(singleRecords[0].importRecord.price);
      expect(updatedItem.variants[0].parentId).toEqual(updatedItem.id);
      expect(updatedItem.variants[0].attributeValueIds).toEqual([attributeSets.get('Size')?.values[0].id]);
    });

    it('should handle parent item update', async () => {
      const { parentRecords } = await importService.groupSingleAndParentUpdates(
        [
          parentItem1.referenceNumber,
          parentItem1.variants[0].referenceNumber,
          parentItem2.referenceNumber,
          singleItem.referenceNumber,
        ],
        itemImports,
      );

      const { batchAttributeSets } = await importService.getAttributeSetsForBatch(itemImports);

      const withoutVariants = !parentRecords[0].importVariantRecords?.length ? parentRecords[0] : parentRecords[1];
      const withVariants = parentRecords[0].importVariantRecords?.length ? parentRecords[0] : parentRecords[1];

      const { parentItem: updatedWithoutVariants } = await importService.handleParentItemUpdate(
        withoutVariants,
        categories,
        batchAttributeSets,
      );
      expect(updatedWithoutVariants?.name).toEqual(parentItem2Import.name);
      expect(updatedWithoutVariants?.price).toEqual(parentItem2Import.price);

      const { parentItem: updatedWithVariants } = await importService.handleParentItemUpdate(
        withVariants,
        categories,
        batchAttributeSets,
      );

      expect(updatedWithVariants?.attributes![0].catalogAttributeValueIds).toHaveLength(2);
      expect(updatedWithVariants?.attributes![1].catalogAttributeValueIds).toHaveLength(1);
      expect(updatedWithVariants?.variants![0].attributeValueIds).toHaveLength(2);
      expect(updatedWithVariants?.variants![0].name).toBe('medium, red');
      expect(updatedWithVariants?.variants[0].price).toBe(parentItem1Variant.price);
    });

    it('update for just the variant records', async () => {
      const { batchAttributeSets } = await importService.getAttributeSetsForBatch(itemImports);

      const { parentRecords } = await importService.groupSingleAndParentUpdates(
        [parentItem1.variants[0].referenceNumber],
        [itemImports[1]],
      );

      const { parentItem, failedItems } = await importService.handleParentItemUpdate(
        parentRecords[0],
        categories,
        batchAttributeSets,
      );

      expect(parentItem).toBeDefined();
      expect(parentItem?.variants![0].attributeValueIds).toHaveLength(2);
      expect(failedItems).toHaveLength(0);
    });
  });
});
