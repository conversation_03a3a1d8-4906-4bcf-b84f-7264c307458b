import { SkuGenerator } from '@npco/bff-fe-common/src';
import { debug, info } from '@npco/component-bff-core/dist/utils/logger';
import { generateShortId } from '@npco/component-bff-core/dist/utils/shortId';
import type { ItemTax } from '@npco/component-dto-catalog/dist';
import { ItemStatus } from '@npco/component-dto-catalog/dist';
import { ISO4217 } from '@npco/component-dto-core/dist';

import { Injectable } from '@nestjs/common';
import { In, Not } from 'typeorm';
import { v4 } from 'uuid';

import type {
  CatalogAttributeSet,
  CatalogAttributeValue,
  CatalogCategory,
  CatalogItem,
  GstImportValueType,
  ItemImport,
} from '../../../../domain';
import {
  BaseModel,
  CatalogAttributeSetModel,
  CatalogItemAttribute,
  CatalogItemParent,
  CatalogItemSingle,
  CatalogItemType,
  CatalogItemVariant,
  CategoryModel,
} from '../../../../domain';
import { ItemSiteSettingsModel } from '../../../../domain/models/items/itemSiteSettingsModel';
import { BaseService } from '../../../base';
import { CatalogApiV1 } from '../../catalogApi/versionMap/v1';
import { CatalogAttributeRepository } from '../../catalogAttribute';
import { CategoryRepository } from '../../catalogCategory/categoryRepository';
import { CatalogEntitySettingsService } from '../../catalogEntitySettings';
import { ItemRepository } from '../../catalogItem/itemRepository';
import { ImportFailureReason } from '../types';

@Injectable()
export class BaseImportService extends BaseService {
  currentVersion = CatalogApiV1.versionName;

  private readonly skuGenerator: SkuGenerator;

  constructor(
    private readonly itemDb: ItemRepository,
    private readonly categoryDb: CategoryRepository,
    private readonly catalogSettingsService: CatalogEntitySettingsService,
    private readonly attributeSetDb: CatalogAttributeRepository,
  ) {
    super();
    this.skuGenerator = new SkuGenerator();
  }

  readonly getCategoriesForBatch = async (
    importRecords: ItemImport[],
  ): Promise<{
    batchCategories: CatalogCategory[];
    newCategories: CatalogCategory[];
  }> => {
    const entityUuid = importRecords[0].entityUuid;
    const uniqueCategoryNames = [
      ...new Set(importRecords.map((item) => item.categories).reduce((acc, categories) => acc.concat(categories), [])),
    ];
    debug(`unique category names ${JSON.stringify(uniqueCategoryNames)}`);
    const existingCategories = await this.categoryDb.getExistingCategoriesByNames(entityUuid, uniqueCategoryNames);

    let batchCategories: CatalogCategory[] = [];
    batchCategories = batchCategories.concat(existingCategories);
    const newCategories: CatalogCategory[] = [];
    const categoryModel = new CategoryModel();
    uniqueCategoryNames.forEach((categoryName) => {
      if (
        !existingCategories
          .concat(newCategories)
          .find((category) => category.name.toLocaleLowerCase().trim() === categoryName.toLocaleLowerCase().trim())
      ) {
        // then add to the new categories list
        newCategories.push(
          categoryModel.fromImport({
            entityUuid,
            id: v4(),
            name: categoryName.trim(),
          }),
        );
      }
    });
    if (newCategories.length > 0) {
      const entities = newCategories.map((entity) =>
        this.categoryDb.getRepository().create({
          ...entity,
        }),
      );
      debug(`saving the category ${JSON.stringify(entities)}`);
      await this.categoryDb.getRepository().save(entities);
    }

    batchCategories = batchCategories.concat(newCategories);

    return {
      batchCategories,
      newCategories,
    };
  };

  readonly parseAttributeSetsAndAttributes = (item: ItemImport) => {
    const attributeSets: { attributeSet: string; attribute: string }[] = [];

    if (item.attributeSet1 && item.attribute1) {
      attributeSets.push({
        attributeSet: item.attributeSet1,
        attribute: item.attribute1,
      });
    }

    if (item.attributeSet2 && item.attribute2) {
      attributeSets.push({
        attributeSet: item.attributeSet2,
        attribute: item.attribute2,
      });
    }

    if (item.attributeSet3 && item.attribute3) {
      attributeSets.push({
        attributeSet: item.attributeSet3,
        attribute: item.attribute3,
      });
    }

    return attributeSets;
  };

  readonly getAttributeSetsForBatch = async (
    importRecords: ItemImport[],
  ): Promise<{
    batchAttributeSets: Map<string, CatalogAttributeSet>;
    newAttributeSets: CatalogAttributeSet[];
    updatedAttributeSets: CatalogAttributeSet[];
  }> => {
    const attributeSets = new Map<string, string[]>();
    const entityUuid = importRecords[0].entityUuid;

    // Build attributeSet Map
    importRecords.forEach((item) => {
      const attribs = this.parseAttributeSetsAndAttributes(item);

      attribs.forEach((a) => {
        if (!attributeSets.has(a.attributeSet)) {
          attributeSets.set(a.attributeSet, []);
        }
        if (!attributeSets.get(a.attributeSet)?.find((val) => val === a.attribute)) {
          attributeSets.get(a.attributeSet)?.push(a.attribute);
        }
      });
    });

    debug(`attributeSets ${JSON.stringify(attributeSets)}`);
    const existingAttributesSets = await this.attributeSetDb
      .getRepository()
      .createQueryBuilder('attributeSet')
      .leftJoinAndSelect('attributeSet.values', 'values')
      .where(
        'LOWER(attributeSet.name) ILIKE ANY(:uniqueAttributeSetNames) AND attributeSet.entityUuid = :entityUuid AND attributeSet.status <> :status',
        {
          uniqueAttributeSetNames: Array.from(attributeSets.keys()).map((name) => name.trim()),
          entityUuid,
          status: ItemStatus.DELETED,
        },
      )
      .getMany();

    const batchAttributeSets = new Map<string, CatalogAttributeSet>();
    const newAttributeSets: CatalogAttributeSet[] = [];
    const updatedAttributeSets: CatalogAttributeSet[] = [];
    const attributeSetModel = new CatalogAttributeSetModel();

    attributeSets.forEach((attributeValues, attributeSetName) => {
      const existingAttributeSet = existingAttributesSets.find(
        (attributeSet: any) =>
          attributeSet.name.toLocaleLowerCase().trim() === attributeSetName.toLocaleLowerCase().trim(),
      );

      // Add missing attribute set
      if (!existingAttributeSet) {
        newAttributeSets.push(
          attributeSetModel.attributeSetFromImport({
            entityUuid,
            name: attributeSetName.trim(),
            values: attributeValues,
          }),
        );
      }
      // Add missing attribute values
      else {
        const existingValues = existingAttributeSet.values?.map((attributeValue: any) => attributeValue.value) ?? [];
        const newValues = this.diffExistingAndNewValues(existingValues, attributeValues);

        if (newValues.length) {
          const inputValues = newValues.map((value, index) => ({
            value,
            ordinal: existingValues.length + index + 1,
            entityUuid,
            attributeSetId: existingAttributeSet.id,
          }));

          updatedAttributeSets.push(
            attributeSetModel.updateAttributeSetValuesFromImport(existingAttributeSet, inputValues),
          );
        }
      }
    });
    if (newAttributeSets.length > 0 || updatedAttributeSets.length > 0) {
      const entities = newAttributeSets.concat(updatedAttributeSets).map((entity) =>
        this.attributeSetDb.getRepository().create({
          ...entity,
        }),
      );
      debug(`saving the attribute sets ${JSON.stringify(entities)}`);
      await this.attributeSetDb.getRepository().save(entities);
    }

    existingAttributesSets.concat(newAttributeSets).forEach((attributeSet) => {
      batchAttributeSets.set(attributeSet.name, attributeSet);
    });

    updatedAttributeSets.forEach((attributeSet) => {
      batchAttributeSets.set(attributeSet.name, attributeSet);
    });

    return { batchAttributeSets, newAttributeSets, updatedAttributeSets };
  };

  readonly diffExistingAndNewValues = (existingValues: string[], newValues: string[]): string[] => {
    const v1 = new Set(existingValues);
    const v2 = new Set(newValues);

    const diff = [...v2].filter((element) => !v1.has(element));

    return diff;
  };

  readonly getCategoriesForItem = (
    categoryNames: string[],
    categories: Partial<CatalogCategory>[],
  ): CatalogCategory[] => {
    const result: CatalogCategory[] = [];
    categoryNames.forEach((categoryName) => {
      result.push(
        categories.find(
          (c) => c.name?.toLocaleLowerCase().trim() === categoryName.toLocaleLowerCase().trim(),
        ) as CatalogCategory,
      );
    });
    return result;
  };

  readonly getGstSettingsFromImportData = (input: GstImportValueType): ItemTax => ({
    name: 'GST',
    enabled: input === 'Y',
  });

  readonly getAssignedSiteUuids = (input: { [key: string]: string }): string[] => {
    return Object.keys(input).filter((key) => input[key] === 'Y');
  };

  readonly getUnassignedSiteUuids = (input: { [key: string]: string }): string[] => {
    return Object.keys(input).filter((key) => input[key] === 'N');
  };

  readonly hasAttributeSet = (item: ItemImport): boolean => {
    if (item.attributeSet1 || item.attributeSet2 || item.attributeSet3) {
      return true;
    }

    return false;
  };

  readonly findAttributeValueIdByValue = (values: CatalogAttributeValue[], searchValue: string) => {
    return values.find((value) => value.value.trim() === searchValue.trim())?.id as string;
  };

  readonly findAttributeIndexById = (catalogAttributes: CatalogItemAttribute[], attributeSetId: string) => {
    return catalogAttributes.findIndex((a) => a.catalogAttributeSetId === attributeSetId);
  };

  /*
        Creates two maps with lists one with all single itemTypes and one with parent type with all variants.
        Varients are decided by items with same name.
      */
  readonly groupSingleAndParentItems = (items: ItemImport[]) => {
    const singleItems = new Map<string, ItemImport>();
    const parentItems = new Map<string, ItemImport[]>();

    items.forEach((item) => {
      const itemName = item.name.trim();
      if (!this.hasAttributeSet(item) && !parentItems.get(itemName)) {
        singleItems.set(itemName, item);
      } else if (this.hasAttributeSet(item) && !parentItems.get(itemName)) {
        parentItems.set(itemName, [item]);
        // remove the item from single item if it had one with same name but with no attributes
        if (singleItems.has(itemName)) {
          parentItems.get(itemName)?.push(singleItems.get(itemName) as ItemImport);
          singleItems.delete(itemName);
        }
      } else if (parentItems.get(itemName)) {
        parentItems.get(itemName)?.push(item);
      }
    });

    return { singleItems, parentItems };
  };

  readonly batchCreateItemsAndCategories = async (importRecords: ItemImport[]) => {
    const createdItems: CatalogItem[] = [];
    const failedToCreateItemsMap = new Map<string, ItemImport>();
    const updatedItems: CatalogItem[] = [];

    const { batchCategories, newCategories } = await this.getCategoriesForBatch(importRecords).catch((e: any) => {
      this.handleBatchImportErrors(e, importRecords, failedToCreateItemsMap, 'Saving categories in db');
      return { batchCategories: [], newCategories: [] };
    });

    const { batchAttributeSets, newAttributeSets, updatedAttributeSets } = await this.getAttributeSetsForBatch(
      importRecords,
    ).catch((e: any) => {
      this.handleBatchImportErrors(e, importRecords, failedToCreateItemsMap, 'Saving attributeSets in db');
      return {
        batchAttributeSets: new Map<string, CatalogAttributeSet>(),
        newAttributeSets: [],
        updatedAttributeSets: [],
      };
    });

    const isAutoSkuEnabled = await this.catalogSettingsService
      .getSettings(importRecords[0].entityUuid)
      .then((settings) => settings?.autoSkuEnabled ?? false);

    try {
      // SingleItem
      const singleItems = importRecords.filter((item) => item.type === CatalogItemType.SINGLE);
      const newSingleItems = this.buildSingleItems(singleItems, batchCategories, isAutoSkuEnabled);

      // ParentItem
      const parentItems = importRecords.filter((item) => item.type === CatalogItemType.PARENT);
      const newParentItems = this.buildParentItems(parentItems, batchCategories, isAutoSkuEnabled);

      // VariantItem
      const variantItems = importRecords.filter((item) => item.type === CatalogItemType.VARIANT);
      const {
        createdParentItems: createdParentsWithVariants,
        updateParentItems: updatedParentsWithVariants,
        failedImportItems,
      } = await this.buildVariantItems(variantItems, batchCategories, batchAttributeSets, isAutoSkuEnabled);

      // Failed variant items
      if (failedImportItems.length) {
        this.handleBatchImportErrors(
          { message: `duplicate variant` },
          failedImportItems,
          failedToCreateItemsMap,
          'build variant items',
        );
      }

      await this.itemDb.batchCreate([...newSingleItems, ...newParentItems, ...createdParentsWithVariants]);
      const updatedParentItems = await this.itemDb.batchUpdate(updatedParentsWithVariants);
      updatedParentsWithVariants.forEach((item) =>
        this.addAttributeSetRelationToParentItem(item, Array.from(batchAttributeSets.values())),
      );
      createdParentsWithVariants.forEach((item) =>
        this.addAttributeSetRelationToParentItem(item, Array.from(batchAttributeSets.values())),
      );
      if (updatedParentItems?.length) {
        updatedItems?.push(...updatedParentsWithVariants);
      }
      createdItems.push(...newSingleItems, ...newParentItems, ...createdParentsWithVariants);
      info(`items created: ${createdItems?.length}}`);
    } catch (e: any) {
      this.handleBatchImportErrors(e, importRecords, failedToCreateItemsMap, 'Saving items in db');
    }

    return {
      createdItems,
      updatedItems,
      createdCategories: newCategories,
      createdAttributeSets: newAttributeSets,
      updatedAttributeSets,
      failedToCreateItems: Array.from(failedToCreateItemsMap.values()),
    };
  };

  readonly getParentItem = (entityUuid: string, name: string) => {
    return this.itemDb.getRepository().findOne({
      where: {
        entityUuid,
        name,
        type: CatalogItemType.PARENT,
        status: ItemStatus.ACTIVE,
      },
      relations: ['attributes', 'variants', 'siteSettings', 'categories'],
    });
  };

  readonly buildSingleItems = (
    singleItems: ItemImport[],
    batchCategories: CatalogCategory[],
    isAutoSkuEnabled = false,
  ): CatalogItemSingle[] => {
    return singleItems?.map((item) => {
      const categories = this.getCategoriesForItem(item.categories, batchCategories);
      const enableSiteUuid = this.getAssignedSiteUuids(item.sites ?? {});
      const siteSettingsModel = new ItemSiteSettingsModel();
      const reportingCategoryUuid = categories[0].id;
      const newItem = new CatalogItemSingle();
      newItem.entityUuid = item.entityUuid;
      newItem.name = item.name;
      newItem.amount = this.getItemPrice(item);
      newItem.price = newItem.amount;
      newItem.currency = ISO4217.AUD;
      newItem.description = item.description;
      newItem.sku = item.sku ?? (isAutoSkuEnabled ? this.skuGenerator.generateId(8).toUpperCase() : item.sku);
      newItem.createdTime = new BaseModel().getTimeUnix();
      newItem.categories = categories;
      newItem.taxes = [this.getGstSettingsFromImportData(item.gst as GstImportValueType)];
      newItem.reportingCategoryUuid = reportingCategoryUuid;
      newItem.invoicesEnabled = item.enableForInvoice === 'Y';
      newItem.available = item.available === 'Y';
      newItem.gtin = item.gtin?.toString();
      newItem.modifiersEnabled = false;
      newItem.attributeSetsEnabled = false;
      newItem.siteSettings = siteSettingsModel.create(
        { sites: enableSiteUuid, entityUuid: item.entityUuid } as any,
        newItem,
      );
      newItem.status = ItemStatus.ACTIVE;
      return newItem;
    });
  };

  readonly buildParentItems = (
    parentItems: ItemImport[],
    batchCategories: CatalogCategory[],
    isAutoSkuEnabled = false,
  ) => {
    return parentItems?.map((parentItem) => {
      const siteSettingsModel = new ItemSiteSettingsModel();
      const siteUuids: string[] = this.getAssignedSiteUuids(parentItem.sites ?? {});
      const categories = this.getCategoriesForItem(parentItem.categories, batchCategories);
      const reportingCategoryId = categories[0].id;
      const newParentItem = new CatalogItemParent();
      newParentItem.id = v4();
      newParentItem.entityUuid = parentItem.entityUuid;
      newParentItem.name = parentItem.name;
      newParentItem.createdTime = new BaseModel().getTimeUnix();
      newParentItem.taxes = [this.getGstSettingsFromImportData(parentItem.gst as GstImportValueType)];
      newParentItem.amount = this.getItemPrice(parentItem);
      newParentItem.price = newParentItem.amount;
      newParentItem.currency = ISO4217.AUD;
      newParentItem.description = parentItem.description;
      newParentItem.invoicesEnabled = (parentItem.enableForInvoice ?? 'Y') !== 'N';
      newParentItem.modifiersEnabled = true;
      newParentItem.attributeSetsEnabled = true;
      newParentItem.available = parentItem.available === 'Y';
      newParentItem.attributes = [];
      newParentItem.variants = [];
      newParentItem.status = ItemStatus.ACTIVE;
      newParentItem.gtin = parentItem.gtin?.toString();
      newParentItem.sku =
        parentItem.sku ?? (isAutoSkuEnabled ? this.skuGenerator.generateId(8).toUpperCase() : parentItem.sku);
      newParentItem.categories = categories;
      newParentItem.reportingCategoryUuid = reportingCategoryId;
      newParentItem.siteSettings = siteSettingsModel.create(
        { sites: Array.from(new Set(siteUuids)), entityUuid: newParentItem.entityUuid } as any,
        newParentItem,
      );

      return newParentItem;
    });
  };

  readonly buildVariantItems = async (
    variantItems: ItemImport[],
    batchCategories: CatalogCategory[],
    attributeSets: Map<string, CatalogAttributeSet>,
    isAutoSkuEnabled = false,
  ): Promise<{
    createdParentItems: CatalogItemParent[];
    updateParentItems: CatalogItemParent[];
    failedImportItems: ItemImport[];
  }> => {
    const createdParentItems: CatalogItemParent[] = [];
    const updateParentItems: CatalogItemParent[] = [];
    const failedImportItems: ItemImport[] = [];
    // Create a map to group variant items by their name
    const variantItemsMap = variantItems?.reduce((map, variantItem) => {
      if (!map.has(variantItem.name)) {
        map.set(variantItem.name, []);
      }
      map.get(variantItem.name)?.push(variantItem);
      return map;
    }, new Map<string, ItemImport[]>());

    const createdParentItemsMap = new Map<string, CatalogItemParent>();
    const updatedParentItemsMap = new Map<string, CatalogItemParent>();

    for (const [key, value] of Array.from(variantItemsMap.entries())) {
      const parentItem = await this.getParentItem(value[0].entityUuid, key);
      if (parentItem) {
        updatedParentItemsMap.set(key, parentItem as CatalogItemParent);
      } else {
        const parentItemImportRecord = {
          ...value[0],
          sku: undefined,
          gtin: undefined,
        };
        const newParentItem = this.buildParentItems([parentItemImportRecord], batchCategories, isAutoSkuEnabled)[0];
        createdParentItemsMap.set(key, newParentItem);
      }
    }

    // Process each group of variant items
    Array.from(variantItemsMap.values()).forEach((importVariantItems) => {
      let siteUuids: string[] = [];
      const categoriesMap = new Map<string, CatalogCategory>();
      const [firstVariantItem] = importVariantItems;

      // Retrieve the parent item
      const parentItem =
        updatedParentItemsMap.get(firstVariantItem.name) ?? createdParentItemsMap.get(firstVariantItem.name)!;

      // Process each variant item
      importVariantItems.forEach((variantItem, index) => {
        const categories = this.getCategoriesForItem(variantItem.categories, batchCategories);
        const variantValueUuids: string[] = [];
        const unixTime = new BaseModel().getTimeUnix();
        const attributeNames: string[] = [];

        // build variant name
        this.parseAttributeSetsAndAttributes(variantItem).forEach((setDetail) =>
          attributeNames.push(setDetail.attribute),
        );
        const variantName = attributeNames.join(', ');

        if (parentItem.variants.find((v) => v.name === variantName)) {
          info(`variant creation failed duplicate variant ${variantName} : ${JSON.stringify(variantItem, null, 2)}`);
          failedImportItems.push(variantItem);
          return;
        }
        // Add categories to the map
        categories.forEach((category) => {
          if (!categoriesMap.has(category.name)) {
            categoriesMap.set(category.name, category);
          }
        });

        // Process attributes and build the variant name
        this.parseAttributeSetsAndAttributes(variantItem).forEach((setDetail, indx) => {
          const attribute = attributeSets.get(setDetail.attributeSet.trim())!;
          const valueId = this.findAttributeValueIdByValue(attribute.values, setDetail.attribute);
          const attributeIndex = this.findAttributeIndexById(parentItem.attributes!, attribute.id);

          if (attributeIndex !== -1) {
            const existingAttributeSet = parentItem.attributes![attributeIndex];
            existingAttributeSet.catalogAttributeValueIds = Array.from(
              new Set([...existingAttributeSet.catalogAttributeValueIds, valueId]),
            );
            variantValueUuids.push(valueId);
          } else {
            // Create and add a new attribute set
            const newAttributeSet = new CatalogItemAttribute();
            newAttributeSet.id = v4();
            newAttributeSet.entityUuid = variantItem.entityUuid;
            newAttributeSet.catalogItemId = variantItem.id;
            newAttributeSet.catalogAttributeSetId = attribute.id;
            newAttributeSet.catalogAttributeValueIds = [valueId];
            newAttributeSet.ordinal = indx + 1;
            newAttributeSet.updatedTime = unixTime;
            newAttributeSet.createdTime = unixTime;
            variantValueUuids.push(valueId);
            parentItem.attributes!.push(newAttributeSet);
          }
        });

        const attributeValueIds = Array.from(new Set(variantValueUuids));

        if (attributeValueIds.length) {
          // Build the item variant
          const itemVariant = new CatalogItemVariant();
          itemVariant.id = v4();
          itemVariant.entityUuid = variantItem.entityUuid;
          itemVariant.parentId = parentItem.id;
          itemVariant.name = variantName;
          itemVariant.sku =
            variantItem.sku ?? (isAutoSkuEnabled ? this.skuGenerator.generateId(8).toUpperCase() : undefined);
          itemVariant.gtin = variantItem.gtin?.toString();
          itemVariant.amount = this.getItemPrice(variantItem);
          itemVariant.price = itemVariant.amount;
          itemVariant.currency = ISO4217.AUD;
          itemVariant.attributeValueIds = attributeValueIds;
          itemVariant.attributeCompositeId = attributeValueIds.join('#');
          itemVariant.ordinal = index + 1;
          itemVariant.updatedTime = unixTime;
          itemVariant.createdTime = unixTime;
          itemVariant.referenceNumber = generateShortId(6);
          itemVariant.status = ItemStatus.ACTIVE;
          itemVariant.available = variantItem.available === 'Y';
          parentItem.variants.push(itemVariant);
        }

        if (variantItem.sites) {
          siteUuids = siteUuids.concat(this.getAssignedSiteUuids(variantItem.sites ?? {}));
        }
      });

      if (categoriesMap.size > 0) {
        const categories = Array.from(categoriesMap.values());
        categories.forEach((category) => {
          if (!parentItem.categories?.find((cat) => cat.id === category.id)) {
            parentItem.categories?.push(category);
          }
        });
      }

      if (siteUuids.length > 0) {
        const sitesSet = Array.from(new Set(siteUuids));
        sitesSet.forEach((siteUuid) => {
          if (!parentItem.siteSettings?.find((siteSetting) => siteSetting.siteUuid === siteUuid)) {
            parentItem.siteSettings?.push(
              ...new ItemSiteSettingsModel().create(
                { sites: [siteUuid], entityUuid: parentItem.entityUuid } as any,
                parentItem,
              ),
            );
          }
        });
      }

      if (createdParentItemsMap.has(parentItem.name)) {
        createdParentItems?.push(parentItem);
      } else {
        updateParentItems?.push(parentItem);
      }
    });

    return { createdParentItems, updateParentItems, failedImportItems };
  };

  readonly addAttributeSetRelationToParentItem = (item: CatalogItemParent, attributeSets: CatalogAttributeSet[]) => {
    const parent = item;
    parent.attributes?.forEach((attribute, index) => {
      parent.attributes!.at(index)!.catalogAttributeSet = attributeSets.find(
        (attributeSet) => attributeSet.id === attribute.catalogAttributeSetId,
      ) as CatalogAttributeSet;
    });
  };

  readonly checkDuplicateSkuForUpdateBatch = async (
    items: ItemImport[],
  ): Promise<{
    failedItems?: ItemImport[];
    itemsToUpdate: ItemImport[];
  }> => {
    let itemsToUpdate: ItemImport[] = [];
    const failedItems: ItemImport[] = [];
    if (items.length === 0) {
      return {
        itemsToUpdate,
        failedItems,
      };
    }
    const rawItemsWithSku = items.filter((item) => item.sku);
    const skus = rawItemsWithSku.map((item) => item.sku!);

    // do not allow setting the sku of an item to match another item's sku
    const itemsWithExistingSku = await this.itemDb.getRepository().find({
      where: {
        entityUuid: items[0].entityUuid,
        sku: In(skus),
        status: Not(ItemStatus.DELETED),
      },
    });

    if (skus.length > 0) {
      rawItemsWithSku.forEach((rawItem) => {
        const item = itemsWithExistingSku.find((itm) => itm.sku === rawItem.sku);
        if (item && item.referenceNumber !== rawItem.referenceNumber) {
          failedItems.push({
            ...rawItem,
            importFailureReason: `${ImportFailureReason.DUPLICATE_SKU_ERROR}`,
          });
        }
      });
      itemsToUpdate = items.filter((item) => !failedItems.find((itm) => itm.orderIndex === item.orderIndex));
    } else {
      itemsToUpdate = items;
    }
    return {
      failedItems,
      itemsToUpdate,
    };
  };

  readonly checkInvalidReferenceNumbersInUpdateBatch = async (input: {
    items: ItemImport[];
    entityUuid: string;
  }): Promise<{
    failedItems?: ItemImport[];
    itemsToUpdate: ItemImport[];
  }> => {
    const { items } = input;
    const rawItemsWithReferenceNumbers = items;
    const referenceNumbers = rawItemsWithReferenceNumbers.map((item) => item.referenceNumber);
    const failedItems: ItemImport[] = [];

    // check if same reference number exists more than once
    referenceNumbers.forEach((referenceNumber) => {
      const count = referenceNumbers.filter((refNum) => refNum === referenceNumber).length;
      if (count > 1) {
        const duplicateRefNums = rawItemsWithReferenceNumbers.filter((rI) => rI.referenceNumber === referenceNumber);
        duplicateRefNums.forEach((itm) => {
          if (!failedItems.find((item) => item.id === itm.id)) {
            const importFailureReason = `${ImportFailureReason.REFNUM_ERROR}`;
            failedItems.push({ ...itm, importFailureReason });
          }
        });
      }
    });

    const itemsToUpdate = items.filter(
      (item) => !failedItems.find((itm) => itm.referenceNumber === item.referenceNumber),
    );
    return {
      failedItems,
      itemsToUpdate,
    };
  };

  validateBatchUpdateRawItems = async (input: {
    items: ItemImport[];
    entityUuid: string;
  }): Promise<{ itemsToUpdate: ItemImport[]; failedItems: ItemImport[] }> => {
    const { itemsToUpdate: validRefNumItems, failedItems: invalidRefNumItems = [] } =
      await this.checkInvalidReferenceNumbersInUpdateBatch(input);

    const { itemsToUpdate, failedItems: invalidSkuItems = [] } = await this.checkDuplicateSkuForUpdateBatch(
      validRefNumItems,
    );

    const failedItems = invalidRefNumItems.concat(invalidSkuItems);

    return { itemsToUpdate, failedItems };
  };

  readonly batchUpdateItemsAndCategories = async (importRecords: ItemImport[]) => {
    const updatedItems = [];
    const failedToUpdateItemsMap = new Map<string, ItemImport>();

    const { batchCategories, newCategories } = await this.getCategoriesForBatch(importRecords).catch((e: any) => {
      this.handleBatchImportErrors(e, importRecords, failedToUpdateItemsMap, 'Saving categories in db');
      return { batchCategories: [], newCategories: [] };
    });
    const { batchAttributeSets, newAttributeSets, updatedAttributeSets } = await this.getAttributeSetsForBatch(
      importRecords,
    ).catch((e: any) => {
      this.handleBatchImportErrors(e, importRecords, failedToUpdateItemsMap, 'Saving attributeSets in db');
      return {
        batchAttributeSets: new Map<string, CatalogAttributeSet>(),
        newAttributeSets: [],
        updatedAttributeSets: [],
      };
    });
    debug('categories retreived');
    const rawItemsWithReferenceNumbers = importRecords;
    const referenceNumbers = rawItemsWithReferenceNumbers.map((item) => item.referenceNumber) as string[];

    try {
      const { singleRecords, parentRecords } = await this.groupSingleAndParentUpdates(referenceNumbers, importRecords);

      const parentUpdates: CatalogItemParent[] = [];
      // parentItem updates
      parentRecords.forEach((record) => {
        const res = this.handleParentItemUpdate(record, batchCategories, batchAttributeSets);
        parentUpdates.push(res.parentItem);

        if (res.failedItems) {
          this.handleBatchImportErrors(
            { message: 'attributes already exist' },
            res.failedItems,
            failedToUpdateItemsMap,
            'updating items in db',
          );
        }
      });

      // singleItem updates
      const singleUpdates = singleRecords.map((record) => {
        return this.handleSingleItemUpdate(
          record.importRecord,
          record.existingRecord,
          batchCategories,
          batchAttributeSets,
        );
      });
      debug('running batch update');
      await this.itemDb.batchUpdate(singleUpdates.concat(parentUpdates));
      // add attribute set relation for materialization.
      parentUpdates.forEach((item) =>
        this.addAttributeSetRelationToParentItem(item, Array.from(batchAttributeSets.values())),
      );
      updatedItems.push(...singleUpdates, ...parentUpdates);
      info(`items updated: ${updatedItems?.length}}`);
    } catch (e: any) {
      this.handleBatchImportErrors(e, importRecords, failedToUpdateItemsMap, 'updating items in db');
    }

    return {
      updatedItems,
      createdCategories: newCategories,
      createdAttributeSets: newAttributeSets,
      updatedAttributeSets,
      failedToUpdateItems: Array.from(failedToUpdateItemsMap.values()),
    };
  };

  readonly groupSingleAndParentUpdates = async (referenceNumbers: string[], importRecords: ItemImport[]) => {
    const parentRecords = new Map<
      string,
      {
        existingRecord: CatalogItemParent;
        importParentRecord?: ItemImport;
        importVariantRecords?: ItemImport[];
      }
    >();
    const singleRecords: {
      existingRecord: CatalogItemSingle;
      importRecord: ItemImport;
    }[] = [];

    const existingItems = await this.getItemsByReferenceNumber(importRecords[0].entityUuid, referenceNumbers);

    importRecords.forEach((importItem) => {
      const existingSingleItem = existingItems.singleItems.find(
        (i) => i.referenceNumber === importItem.referenceNumber,
      ) as CatalogItem;
      // singleItem update
      if (existingSingleItem) {
        singleRecords.push({ existingRecord: existingSingleItem, importRecord: importItem });
        return;
      }

      const existingParentItem = existingItems.parentsWithoutVariantsUpdate.find(
        (i) => i.referenceNumber === importItem.referenceNumber,
      );
      // parent update without variant
      if (existingParentItem && !parentRecords.has(existingParentItem.id)) {
        parentRecords.set(existingParentItem.id, {
          existingRecord: existingParentItem,
          importParentRecord: importItem,
          importVariantRecords: [],
        });
        return;
      }

      // parent update with variants
      let parentWithVariantsUpdate = existingItems.parentsWithVariantsUpdate.find(
        (i) => i.referenceNumber === importItem.referenceNumber,
      );

      if (parentWithVariantsUpdate && !parentRecords.has(parentWithVariantsUpdate.id)) {
        parentRecords.set(parentWithVariantsUpdate.id, {
          existingRecord: parentWithVariantsUpdate,
          importParentRecord: importItem,
          importVariantRecords: [],
        });
        return;
      }

      if (parentWithVariantsUpdate && parentRecords.has(parentWithVariantsUpdate.id)) {
        parentRecords.set(parentWithVariantsUpdate.id, {
          ...(parentRecords.get(parentWithVariantsUpdate.id) as any),
          importParentRecord: importItem,
        });
        return;
      }

      parentWithVariantsUpdate = existingItems.parentsWithVariantsUpdate.find((i) => {
        return i.variants.find((variant) => variant.referenceNumber === importItem.referenceNumber);
      });

      if (parentWithVariantsUpdate && !parentRecords.has(parentWithVariantsUpdate.id)) {
        parentRecords.set(parentWithVariantsUpdate.id, {
          existingRecord: parentWithVariantsUpdate,
          importVariantRecords: [importItem],
        });

        return;
      }

      if (parentWithVariantsUpdate && parentRecords.has(parentWithVariantsUpdate.id)) {
        parentRecords.set(parentWithVariantsUpdate.id, {
          ...(parentRecords.get(parentWithVariantsUpdate.id) as any),
          importVariantRecords: parentRecords
            .get(parentWithVariantsUpdate.id)
            ?.importVariantRecords?.concat(importItem),
        });
      }
    });

    return {
      singleRecords,
      parentRecords: Array.from(parentRecords.values()),
    };
  };

  readonly getItemsByReferenceNumber = async (entityUuid: string, referenceNumbers: string[]) => {
    const variants = await this.itemDb
      .getRepository()
      .createQueryBuilder('item')
      .select(['item.parentId'])
      .where({
        entityUuid,
        referenceNumber: In(referenceNumbers),
        status: Not(ItemStatus.DELETED),
        type: CatalogItemType.VARIANT,
      })
      .getRawMany();

    const variantParentIds = variants.map((v) => v.item_parentId).filter(Boolean);

    // Parent items with variant updates in import
    const parentsWithVariantsUpdate = (await this.itemDb
      .getRepository()
      .createQueryBuilder('item')
      .leftJoinAndSelect('item.variants', 'variants')
      .leftJoinAndSelect('item.attributes', 'attributes')
      .leftJoinAndSelect('item.siteSettings', 'siteSettings')
      .leftJoinAndSelect('item.images', 'images')
      .leftJoinAndSelect('item.categories', 'categories')
      .where({
        id: In(variantParentIds),
        status: Not(ItemStatus.DELETED),
        type: CatalogItemType.PARENT,
      })
      .getMany()) as CatalogItemParent[];

    const filteredReferenceNumbers = referenceNumbers.filter(
      (refNum) => !parentsWithVariantsUpdate.find((parent) => parent.referenceNumber === refNum),
    );

    // Single items in import
    const singleItemsProm = this.itemDb
      .getRepository()
      .createQueryBuilder('item')
      .leftJoinAndSelect('item.variants', 'variants')
      .leftJoinAndSelect('item.attributes', 'attributes')
      .leftJoinAndSelect('item.siteSettings', 'siteSettings')
      .leftJoinAndSelect('item.images', 'images')
      .leftJoinAndSelect('item.categories', 'categories')
      .where({
        entityUuid,
        referenceNumber: In(filteredReferenceNumbers),
        status: Not(ItemStatus.DELETED),
        type: CatalogItemType.SINGLE,
      })
      .getMany();

    // Parent items without variant updates in import
    const parentsProm = this.itemDb
      .getRepository()
      .createQueryBuilder('item')
      .leftJoinAndSelect('item.variants', 'variants')
      .leftJoinAndSelect('item.attributes', 'attributes')
      .leftJoinAndSelect('item.siteSettings', 'siteSettings')
      .leftJoinAndSelect('item.images', 'images')
      .leftJoinAndSelect('item.categories', 'categories')
      .where({
        entityUuid,
        referenceNumber: In(filteredReferenceNumbers),
        status: Not(ItemStatus.DELETED),
        type: CatalogItemType.PARENT,
      })
      .getMany() as Promise<CatalogItemParent[]>;

    const [singleItems, parentsWithoutVariantsUpdate] = await Promise.all([singleItemsProm, parentsProm]);

    return {
      parentsWithVariantsUpdate,
      parentsWithoutVariantsUpdate,
      singleItems,
    };
  };

  readonly updateItemCore = (
    itemImport: ItemImport,
    dbRecord: CatalogItem,
    categories: CatalogCategory[],
    updateType: CatalogItemType,
  ) => {
    const assignedSiteIds = this.getAssignedSiteUuids(itemImport.sites ?? {});
    const unAssignedSiteIds = this.getUnassignedSiteUuids(itemImport.sites ?? {});
    const unixTime = new BaseModel().getTimeUnix();
    const updateItem = { ...dbRecord };
    const taxes = [this.getGstSettingsFromImportData(itemImport.gst as GstImportValueType)];
    updateItem.name = itemImport.name;
    updateItem.amount = this.getItemPrice(itemImport);
    updateItem.price = updateItem.amount;
    updateItem.currency = ISO4217.AUD;
    updateItem.type = updateType;
    updateItem.description = itemImport.description ?? dbRecord.description;
    updateItem.sku = itemImport.sku ?? dbRecord.sku;
    updateItem.taxes = taxes.length ? taxes : dbRecord.taxes;
    updateItem.invoicesEnabled = itemImport.enableForInvoice
      ? itemImport.enableForInvoice === 'Y'
      : dbRecord.invoicesEnabled;
    updateItem.gtin = itemImport.gtin?.toString() ?? dbRecord.gtin;
    updateItem.available = itemImport.available ? itemImport.available === 'Y' : dbRecord.available;
    updateItem.updatedTime = unixTime;

    itemImport.categories?.forEach((importCategory) => {
      const category = categories.find((cat) => cat.name.trim().toLowerCase() === importCategory?.trim().toLowerCase());
      if (category && !updateItem.categories?.find((cat) => cat.id === category.id)) {
        updateItem.categories?.push(category);
      }
    });
    updateItem.reportingCategoryUuid = updateItem.categories?.[0].id;
    assignedSiteIds.forEach((siteUuid) => {
      if (!updateItem.siteSettings?.find((siteSetting) => siteSetting.siteUuid === siteUuid)) {
        updateItem.siteSettings?.push(
          ...new ItemSiteSettingsModel().create(
            { sites: [siteUuid], entityUuid: updateItem.entityUuid } as any,
            updateItem,
          ),
        );
      }
    });

    unAssignedSiteIds.forEach((siteUuid) => {
      const index = updateItem.siteSettings?.findIndex((siteSetting) => siteSetting.siteUuid === siteUuid) as number;
      if (index !== -1) {
        updateItem.siteSettings?.splice(index, 1);
      }
    });

    return updateItem;
  };

  readonly handleParentItemUpdate = (
    parentUpdate: {
      existingRecord: CatalogItemParent;
      importParentRecord?: ItemImport;
      importVariantRecords?: ItemImport[];
    },
    categories: CatalogCategory[],
    attributeSets: Map<string, CatalogAttributeSet>,
  ) => {
    const { existingRecord, importParentRecord, importVariantRecords } = parentUpdate;

    if (importParentRecord && !importVariantRecords?.length) {
      const parentItem = this.updateItemCore(
        importParentRecord,
        existingRecord,
        categories,
        CatalogItemType.PARENT,
      ) as CatalogItemParent;
      parentItem.attributes = existingRecord.attributes || [];
      parentItem.variants = existingRecord.variants || [];
      return { parentItem };
    }

    if (importParentRecord && importVariantRecords?.length) {
      const parentItem = this.updateItemCore(
        importParentRecord,
        existingRecord,
        categories,
        CatalogItemType.PARENT,
      ) as CatalogItemParent;
      parentItem.attributes = existingRecord.attributes;
      parentItem.variants = existingRecord.variants;

      // update variants and attributeSets
      const failedItems = this.updateVariantsAttributes(
        parentItem,
        existingRecord,
        importVariantRecords,
        attributeSets,
      );
      return { parentItem, failedItems };
    }

    const parentItem = {
      ...existingRecord,
    };

    const failedItems = this.updateVariantsAttributes(parentItem, existingRecord, importVariantRecords!, attributeSets);
    return { parentItem, failedItems };
  };

  // update variants and attributesSets of parentItem in place
  readonly updateVariantsAttributes = (
    parentItem: CatalogItemParent,
    existingRecord: CatalogItemParent,
    importItems: ItemImport[],
    attributeSets: Map<string, CatalogAttributeSet>,
  ): ItemImport[] => {
    const unixTime = new BaseModel().getTimeUnix();
    const failedImportItems: ItemImport[] = [];
    // update variants and attributeSets
    importItems.forEach((importVariant) => {
      const variantRecordIndex = parentItem.variants.findIndex(
        (variant) => variant.referenceNumber === importVariant.referenceNumber,
      );
      const dbVariantRecord = parentItem.variants[variantRecordIndex];
      const variantValueUuids: string[] = [];
      const attributeNames: any[] = [];

      this.parseAttributeSetsAndAttributes(importVariant).forEach((setDetail, indx) => {
        attributeNames.push(setDetail.attribute);
        const catalogAttributeSet = attributeSets.get(setDetail.attributeSet) as CatalogAttributeSet;
        const valueId = this.findAttributeValueIdByValue(catalogAttributeSet.values, setDetail.attribute);
        const attributeIndex = this.findAttributeIndexById(parentItem.attributes!, catalogAttributeSet.id);
        // append value to existing attributeSet
        if (attributeIndex !== -1) {
          const updatedValueIds = [
            ...new Set([...(parentItem.attributes?.at(attributeIndex)?.catalogAttributeValueIds as string[]), valueId]),
          ];
          // eslint-disable-next-line no-param-reassign
          parentItem.attributes!.at(attributeIndex)!.catalogAttributeValueIds = updatedValueIds;
          variantValueUuids.push(valueId);
          return;
        }

        // assign new attributeSet to Item
        const itemAttributeSet = new CatalogItemAttribute();
        itemAttributeSet.id = v4();
        itemAttributeSet.entityUuid = existingRecord.entityUuid;
        itemAttributeSet.catalogItemId = existingRecord.id;
        itemAttributeSet.catalogAttributeSetId = catalogAttributeSet.id;
        itemAttributeSet.catalogAttributeValueIds = [valueId as unknown as string];
        itemAttributeSet.ordinal = indx + 1;
        itemAttributeSet.updatedTime = unixTime;
        itemAttributeSet.createdTime = unixTime;
        variantValueUuids.push(valueId as unknown as string);
        parentItem.attributes?.push(itemAttributeSet);
      });

      let updatedVariant = new CatalogItemVariant();
      const attributeValueIds = [...new Set(variantValueUuids)];
      const amount = this.getItemPrice(importVariant);
      updatedVariant = {
        ...dbVariantRecord,
        name: attributeNames.join(', '),
        amount,
        price: amount,
        currency: ISO4217.AUD,
        description: importVariant.description,
        updatedTime: unixTime,
        gtin: importVariant.gtin?.toString() ?? dbVariantRecord.gtin,
        available: importVariant.available ? importVariant.available === 'Y' : dbVariantRecord.available,
        sku: importVariant.sku ?? dbVariantRecord.sku,
        attributeValueIds,
        attributeCompositeId: attributeValueIds.join('#'),
      };

      if (
        !this.matchAttributeIds(dbVariantRecord.attributeValueIds, updatedVariant.attributeValueIds) &&
        this.variantExistsInParent(parentItem, updatedVariant)
      ) {
        info(`varient creation failed duplicate variant ${updatedVariant.name}`);
        failedImportItems.push(importVariant);
        return;
      }

      // update variant in place
      // eslint-disable-next-line no-param-reassign
      parentItem.variants[variantRecordIndex] = updatedVariant;
    });

    return failedImportItems;
  };

  readonly matchAttributeIds = (ids1: string[], ids2: string[]) => {
    return ids1.every((id) => ids2.includes(id));
  };

  readonly variantExistsInParent = (parentItem: CatalogItemParent, variant: CatalogItemVariant) => {
    return parentItem.variants.find((v) => {
      return v.attributeValueIds.every((id) => variant.attributeValueIds.includes(id));
    });
  };

  readonly handleSingleItemUpdate = (
    item: ItemImport,
    dbRecord: CatalogItem,
    categories: CatalogCategory[],
    attributeSets: Map<string, CatalogAttributeSet>,
  ) => {
    if (!this.hasAttributeSet(item)) {
      return this.singleItemUpdate(item, dbRecord, categories);
    }

    return this.singleItemToParentUpdate(item, dbRecord, categories, attributeSets);
  };

  readonly singleItemUpdate = (item: ItemImport, dbRecord: CatalogItem, categories: CatalogCategory[]) => {
    return this.updateItemCore(item, dbRecord, categories, CatalogItemType.SINGLE);
  };

  readonly singleItemToParentUpdate = (
    item: ItemImport,
    dbRecord: CatalogItem,
    categories: CatalogCategory[],
    attributeSets: Map<string, CatalogAttributeSet>,
  ) => {
    const attributeValueUuids: string[] = [];
    const unixTime = new BaseModel().getTimeUnix();
    const newItem = this.updateItemCore(item, dbRecord, categories, CatalogItemType.PARENT) as CatalogItemParent;
    newItem.attributes = [];
    newItem.variants = [];
    const attributeNames: any[] = [];

    this.parseAttributeSetsAndAttributes(item).forEach((setDetail, indx) => {
      // Set attribute set
      attributeNames.push(setDetail.attribute);
      const attribute = attributeSets.get(setDetail.attributeSet.trim()) as CatalogAttributeSet;
      const valueId = this.findAttributeValueIdByValue(attribute.values, setDetail.attribute);
      // assign new attributeSet to Item
      const itemAttributeSet = new CatalogItemAttribute();
      itemAttributeSet.id = v4();
      itemAttributeSet.entityUuid = item.entityUuid;
      itemAttributeSet.catalogItemId = item.id;
      itemAttributeSet.catalogAttributeSetId = attribute.id;
      itemAttributeSet.catalogAttributeValueIds = [valueId as unknown as string];
      itemAttributeSet.ordinal = indx + 1;
      itemAttributeSet.updatedTime = unixTime;
      itemAttributeSet.createdTime = unixTime;
      attributeValueUuids.push(valueId as unknown as string);
      newItem.attributes?.push(itemAttributeSet);
    });

    // Build Variants
    const itemVariant = new CatalogItemVariant();
    itemVariant.id = v4();
    itemVariant.entityUuid = dbRecord.entityUuid;
    itemVariant.parentId = dbRecord.id;
    itemVariant.name = attributeNames.join(', ');
    itemVariant.sku = this.skuGenerator.generateId(8).toUpperCase();
    itemVariant.gtin = item.gtin?.toString();
    itemVariant.amount = this.getItemPrice(item);
    itemVariant.price = itemVariant.amount;
    itemVariant.currency = ISO4217.AUD;
    itemVariant.attributeValueIds = attributeValueUuids;
    itemVariant.attributeCompositeId = attributeValueUuids.join('#');
    itemVariant.ordinal = 1;
    itemVariant.updatedTime = unixTime;
    itemVariant.referenceNumber = generateShortId(6);
    itemVariant.createdTime = unixTime;
    itemVariant.status = ItemStatus.ACTIVE;
    itemVariant.available = item.available === 'Y';
    newItem.variants.push(itemVariant);

    return newItem;
  };

  /**
     a) IF an item's name, description, price, SKU and attributeSets fully matches any of the other items provided in the import file, both marked as duplicate.

     b) IF an item's name, description, price, attributeSets matches any of the existing items in db, but SKU is NOT matching, it is NOT MARKED AS DUPLICATE and 2 items are created.

     c) IF an item's name, description, price, attributeSets matches any of the other items provided in the import file, and SKU is NULL for one item, both marked as duplicate.
  
     d) IF an item's name, description, price, attributeSets fully matches any of the other items provided in the import file, and SKU is NULL for both, both marked as duplicate.
     */
  readonly isDuplicate = (a: ItemImport, b: ItemImport) => {
    return (a.name === b.name || (a.sku && b.sku && a.sku === b.sku)) && this.hasDuplicateAttributes(a, b);
  };

  // Check if two items have exactly same attributes.
  readonly hasDuplicateAttributes = (itemA: ItemImport, itemB: ItemImport) => {
    const attribA = this.parseAttributeSetsAndAttributes(itemA);
    const attribB = this.parseAttributeSetsAndAttributes(itemB);

    if (attribA.length !== attribB.length) {
      return false;
    }

    return (
      attribA.filter((a) => attribB.find((b) => a.attributeSet === b.attributeSet && a.attribute === b.attribute))
        .length === attribA.length
    );
  };

  readonly checkDuplicatesInImportData = (items: ItemImport[]) => {
    const failedItems: ItemImport[] = [];
    const distinctItems: ItemImport[] = [];
    items.forEach((item) => {
      const duplicated = items.filter((rawItem) => this.isDuplicate(rawItem, item)).length > 1;

      if (duplicated) {
        failedItems.push({
          ...item,
          importFailureReason: ImportFailureReason.DUPLICATE_ITEM_ERROR,
        });
      } else {
        distinctItems.push(item);
      }
    });
    return { failedItems, distinctItems };
  };

  removeDuplicateItems = async (input: {
    items: ItemImport[];
    entityUuid: string;
  }): Promise<{
    failedItems: ItemImport[];
    distinctItems: ItemImport[];
  }> => {
    const { entityUuid, items } = input;
    let failedItems: ItemImport[] = [];
    let distinctItems: ItemImport[] = [];
    // remove duplicates from import data
    const { distinctItems: itemImportDistinct, failedItems: itemImportDuplicates } =
      this.checkDuplicatesInImportData(items);
    failedItems = failedItems.concat(itemImportDuplicates);

    if (itemImportDistinct.length > 0) {
      const distinctNames = itemImportDistinct.map((item) => item.name);
      const distinctSkus = itemImportDistinct.filter((item) => item.sku).map((i) => i.sku);
      let dbNameMatch: CatalogItem[] = [];
      let dbSkuMatch: CatalogItem[] = [];

      if (distinctNames.length) {
        dbNameMatch = await this.itemDb
          .getRepository()
          .createQueryBuilder('items')
          .where('items.entityUuid = :entityUuid', { entityUuid })
          .andWhere('items.status <> :status', { status: ItemStatus.DELETED })
          .andWhere('items.name IN (:...names)', {
            names: distinctNames,
          })
          .getMany();
      }

      if (distinctSkus.length) {
        dbSkuMatch = await this.itemDb
          .getRepository()
          .createQueryBuilder('items')
          .where('items.entityUuid = :entityUuid', { entityUuid })
          .andWhere('items.status <> :status', { status: ItemStatus.DELETED })
          .andWhere('items.sku IN (:...skus)', {
            names: distinctNames,
            skus: distinctSkus,
          })
          .getMany();
      }

      // check if batch contains duplicates in DB
      if (dbNameMatch.concat(dbSkuMatch).length > 0) {
        itemImportDistinct.forEach((item) => {
          const duplicateItemName = dbNameMatch.find((duplicateItem) => duplicateItem.name === item.name);
          const duplicateItemSku = dbSkuMatch.find((duplicateItem) => duplicateItem.sku === item.sku);
          if (
            (duplicateItemName &&
              !(duplicateItemName.type === CatalogItemType.PARENT && item.type === CatalogItemType.VARIANT)) ||
            duplicateItemSku
          ) {
            failedItems.push({
              ...item,
              importFailureReason: ImportFailureReason.DUPLICATE_ITEM_ERROR,
            });
          } else {
            distinctItems.push(item);
          }
        });
      } else {
        distinctItems = itemImportDistinct;
      }
    }
    return { failedItems, distinctItems };
  };

  readonly getItemPrice = (item: ItemImport) => {
    if (item.gst === 'Y') {
      return Number(Math.round(item.price / 1.1).toFixed(0));
    }
    return Number(item.price);
  };

  private readonly handleBatchImportErrors = (
    error: any,
    records: ItemImport[],
    failedToCreateItemsMap: Map<string, ItemImport>,
    context: string,
  ) => {
    const importFailureReason = error.message ? `${context} - ${error.message}` : `Error occurred while ${context}`;
    records.forEach((item) => {
      if (!failedToCreateItemsMap.has(item.id)) {
        failedToCreateItemsMap.set(item.id, { ...item, importFailureReason });
      }
    });
  };
}
