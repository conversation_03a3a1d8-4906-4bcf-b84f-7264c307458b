import { debug, info } from '@npco/component-bff-core/dist/utils/logger';
import { ItemStatus } from '@npco/component-dto-catalog/dist';

import { Injectable } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import type { <PERSON><PERSON>tyManager, QueryR<PERSON>ner, FindOptionsWhere } from 'typeorm';
import { v4 } from 'uuid';

import type { CatalogItemParent, CatalogItemSingle } from '../../../domain/entities';
import {
  CatalogItemVariant,
  CatalogItem,
  CatalogItemType,
  CatalogModifierSetSiteSettings,
  CatalogItemSiteSettings,
  CatalogItemAttribute,
  CatalogItemModifier,
  CatalogItemImage,
} from '../../../domain/entities';
import { NotFoundError } from '../../../domain/error';
import { ItemModel } from '../../../domain/models/itemModel';
import type { CreateItemInput, UpdatedItem } from '../../../domain/types';
import { BaseRepository } from '../../base/baseRepository';
import type { DeleteItemIdInput, GetItemInput } from '../../types';
import { ReferenceSettingsService } from '../referenceSettings/referenceSettingsService';

@Injectable()
export class ItemRepository extends BaseRepository<CatalogItem> {
  static readonly FULL_RELATIONS = ['variants', 'modifiers', 'attributes', 'images', 'siteSettings'];

  readonly relationNameToEntityMap: { [key: string]: { entity: any; relations: string[]; itemKeyName: string } } = {
    variants: { entity: CatalogItemVariant, relations: ['images'], itemKeyName: 'parentId' },
    modifiers: {
      entity: CatalogItemModifier,
      relations: ['catalogModifierSet', 'catalogModifierSet.modifiers'],
      itemKeyName: 'catalogItemId',
    },
    attributes: {
      entity: CatalogItemAttribute,
      relations: ['catalogAttributeSet', 'catalogAttributeSet.values'],
      itemKeyName: 'catalogItemId',
    },
    images: { entity: CatalogItemImage, relations: [], itemKeyName: 'parentId' },
    siteSettings: { entity: CatalogItemSiteSettings, relations: [], itemKeyName: 'catalogItemId' },
  };

  constructor(
    private readonly ds: DataSource,
    private readonly itemModel: ItemModel,
    private readonly referenceSettings: ReferenceSettingsService,
  ) {
    super('item', ds);
  }

  getRepository = () => this.ds.getRepository(CatalogItem);

  getRelationForItemIds = async (ids: string[], relation: string) => {
    const { entity, relations, itemKeyName } = this.relationNameToEntityMap[relation];

    const res = await this.ds.getRepository(entity).find({
      where: { [itemKeyName]: In(ids) },
      relations,
    });

    return { result: res, itemKeyName, relationName: relation };
  };

  getItemsWithRelations = async (ids: string[], where: FindOptionsWhere<CatalogItem>, relations?: string[]) => {
    const getItemProm = this.getRepository().find({
      where,
      // join only CatalogCategories table
      relations: ['categories'],
    });
    // query other relations separately
    const relationsProm = (relations ?? []).map((relation) => this.getRelationForItemIds(ids, relation));

    const results = await Promise.all([getItemProm, ...relationsProm]);
    const [itemsWithoutRelations, ...relationResults] = results;

    const items = itemsWithoutRelations.map((item) => {
      const itemWithRelations = { ...item } as { [key: string]: any };
      relationResults.forEach((relationResult) => {
        const result = relationResult.result.filter((relation) => relation[relationResult.itemKeyName] === item.id);
        itemWithRelations[relationResult.relationName] = result;
      });
      return itemWithRelations as CatalogItemSingle & CatalogItemParent & CatalogItemVariant;
    });
    debug(items);
    return items;
  };

  getItems = async (ids: string[], relations?: string[]) => this.getItemsWithRelations(ids, { id: In(ids) }, relations);

  getItemsWithReportingCategory = (entityUuid: string, reportingCategoryUuid: string) => {
    // only loading category relation
    return this.getRepository().find({
      where: {
        entityUuid,
        reportingCategoryUuid,
        status: ItemStatus.ACTIVE,
      },
      relations: ['categories'],
    });
  };

  findItemByReferenceNumberOrThrowNotFound = async (referenceNumber: string, entityUuid: string) => {
    const item = await this.getRepository().findOne({
      where: {
        referenceNumber,
        entityUuid,
        status: ItemStatus.ACTIVE,
      },
    });
    if (!item) {
      throw new NotFoundError('Item not found');
    }
    return item;
  };

  getItem = async (input: GetItemInput, relations = ItemRepository.FULL_RELATIONS) => {
    const id = input.id;
    const items = await this.getItemsWithRelations([id!], { id, entityUuid: input.entityUuid }, relations);
    return items[0];
  };

  createItem = async (item: CreateItemInput) => {
    // create item typeORM model
    const newItem = await this.itemModel.create(item);
    newItem.modifiers = await this.findAndAssignCatalogItemAndModifiersMutualSites(newItem);
    info(`Creating item ${JSON.stringify(newItem)}`);
    // save typeORM model in db
    const createdItem = await this.retry<CatalogItem>(
      () => this.captureTransaction('create', '', this.transactionCreateItem, [newItem]),
      this.RETRY_LIMIT,
      2000,
    );
    // fetch item with relations
    return this.getItem({ entityUuid: createdItem.entityUuid, id: createdItem.id }, ItemRepository.FULL_RELATIONS);
  };

  getItemOrThrowNotFound = async (input: GetItemInput) => {
    const catalogItem = await this.getItem(input);
    if (!catalogItem) {
      return Promise.reject(new NotFoundError('Item not found'));
    }
    return catalogItem;
  };

  editItem = async (updateInput: CatalogItem): Promise<UpdatedItem> => {
    const aggregateId = updateInput.id;
    const originalItem = await this.getItemOrThrowNotFound({ entityUuid: updateInput.entityUuid, id: aggregateId });
    const updateResult = await this.itemModel.edit(originalItem, updateInput);
    info(`updateResult   ====>> ${JSON.stringify(updateResult)}`);

    await this.retry<CatalogItem>(
      () => this.captureTransaction('updateItem', aggregateId, this.transactionEditItem, [originalItem, updateResult]),
      this.RETRY_LIMIT,
      2000,
    );

    return updateResult;
  };

  editItemType = async (manager: EntityManager, item: Pick<CatalogItem, 'id' | 'type'>) => {
    await manager.createQueryBuilder().update(CatalogItem).set({ type: item.type }).where({ id: item.id }).execute();
  };

  batchCreate = async (itemsData: CatalogItemSingle[]) =>
    this.captureTransaction<CatalogItem[]>('batchCreate', '', this.transactionBatchCreateItem, [itemsData]);

  batchUpdate = async (itemsData: CatalogItemSingle[]) =>
    this.captureTransaction<CatalogItem[]>('batchUpdate', '', this.transactionBatchUpdateItem, [itemsData]);

  deleteItem = async (input: DeleteItemIdInput) => {
    const originalItem = await this.getItemOrThrowNotFound({ id: input.itemUuid, ...input });
    const updateResult = await this.itemModel.delete(originalItem);

    await this.captureTransaction(
      'deleteItem',
      originalItem.id,
      async (queryRunner: QueryRunner) => {
        await queryRunner.manager.getRepository(CatalogItem).save(updateResult);

        if (originalItem.type === CatalogItemType.PARENT) {
          await this.deleteCatalogItemVariants(queryRunner.manager, (originalItem as CatalogItemParent).variants);
        }
      },
      [],
    );

    return updateResult;
  };

  deleteAllItemsTransaction = async (
    entityUuid: string,
    deleteCategoryRelation: boolean,
    callBack?: any,
  ): Promise<void> => {
    await this.captureTransaction(
      'deleteAllItems',
      entityUuid,
      async (queryRunner: QueryRunner) => {
        const promises = [];
        promises.push(this.deleteAllItems(queryRunner, entityUuid, deleteCategoryRelation));
        if (callBack) {
          promises.push(callBack());
        }
        await Promise.all(promises);
      },
      [],
    );
  };

  deleteAllItems = async (
    queryRunner: QueryRunner,
    entityUuid: string,
    deleteCategoryRelation: boolean,
  ): Promise<void> => {
    const manager = queryRunner.manager;
    const promises = [];
    const items = await manager
      .getRepository(CatalogItem)
      .find({ select: ['id'], where: { entityUuid, status: ItemStatus.ACTIVE } });
    if (deleteCategoryRelation) {
      promises.push(this.deleteCatalogItemsRelation(manager, items));
    }
    promises.push(this.deleteCatalogItemsInEntity(manager, entityUuid));
    promises.push(this.deleteCatalogItemsSiteRelation(manager, items));
    promises.push(this.deleteCatalogItemsModifierSetsRelation(manager, items));
    promises.push(this.deleteCatalogItemsAttributesRelation(manager, items));
    await Promise.all(promises);
  };

  findAndAssignCatalogItemAndModifiersMutualSites = async (
    catalogItem: CatalogItemSingle,
  ): Promise<CatalogItemModifier[] | undefined> => {
    if (!catalogItem.modifiers || !catalogItem.siteSettings) {
      return catalogItem.modifiers;
    }

    const catalogItemSiteUuids = catalogItem.siteSettings.map((siteSettings) => siteSettings.siteUuid);
    const catalogItemModifierSetUuids = catalogItem.modifiers.map((modifier) => modifier.modifierSetId);

    const mutualCatalogItemModifierSetSiteSettings = await this.dataSource
      .getRepository(CatalogModifierSetSiteSettings)
      .find({
        where: {
          entityUuid: catalogItem.entityUuid,
          catalogModifierSetUuid: In(catalogItemModifierSetUuids),
          siteUuid: In(catalogItemSiteUuids),
        },
        select: ['catalogModifierSetUuid', 'siteUuid'],
      });

    if (!mutualCatalogItemModifierSetSiteSettings) {
      return catalogItem.modifiers;
    }

    /**
     * Merge mutualCatalogItemModifierSetSiteSettings.siteUuid into catalogItem.modifiers[].sites where the modifierSetUuid matches between both
     */
    const catalogItemModifiersWithSites = catalogItem.modifiers.map((modifier) => {
      const mutualCatalogItemModifierSetSiteUuids = mutualCatalogItemModifierSetSiteSettings
        .filter((siteSettings) => siteSettings.catalogModifierSetUuid === modifier.modifierSetId)
        .map((siteSettings) => siteSettings.siteUuid);

      if (!mutualCatalogItemModifierSetSiteUuids) {
        return modifier;
      }

      return {
        ...modifier,
        siteUuids: mutualCatalogItemModifierSetSiteUuids,
      };
    });

    return catalogItemModifiersWithSites;
  };

  private readonly transactionCreateItem = async (queryRunner: QueryRunner, itemData: any) => {
    const { entityUuid } = itemData;
    const manager = queryRunner.manager;
    const referenceNumber = await this.referenceSettings.getNextReferenceNumberWithLock(entityUuid, manager);
    const data = manager.create(CatalogItem, { ...itemData, referenceNumber });
    debug(`saving item ${JSON.stringify(data, null, 2)}`);
    await manager.save(data);
    await this.referenceSettings.incrementReferenceNumber(entityUuid, manager);
    return itemData;
  };

  private readonly transactionEditItem = async (
    queryRunner: QueryRunner,
    originalItem: CatalogItem,
    updateResult: UpdatedItem,
  ) => {
    if (originalItem.type !== updateResult.newItem.type) {
      await this.editItemType(queryRunner.manager, updateResult.newItem);
    }

    debug(`updating item ${JSON.stringify(updateResult.newItem, null, 2)}`);
    await queryRunner.manager.getRepository(CatalogItem).save(updateResult.newItem);

    if (originalItem.type === CatalogItemType.PARENT) {
      const removedVariants = (originalItem as CatalogItemParent).variants.filter(
        (variant) => !(updateResult.newItem as CatalogItemParent).variants.find((v) => v.id === variant.id),
      );
      await this.deleteCatalogItemVariants(queryRunner.manager, removedVariants);
    }
  };

  private readonly transactionBatchCreateItem = async (queryRunner: QueryRunner, itemsData: CatalogItem[]) => {
    let createdItems: CatalogItem[] = [];
    const manager = queryRunner.manager;
    const items = [];
    if (itemsData.length > 0) {
      const { entityUuid } = itemsData[0];
      const startRefNum = await this.referenceSettings.getNextReferenceNumberWithLock(entityUuid, manager);
      for (let i = 0; i < itemsData.length; i += 1) {
        const item = itemsData[i];
        item.referenceNumber = this.referenceSettings.incrementStringReferenceNumberBy(startRefNum, i);
        item.id = item.id ?? v4();
        items.push(manager.create(CatalogItem, item));
      }
      createdItems = await manager.save(items);
      await this.referenceSettings.incrementReferenceNumber(entityUuid, manager, items.length);
    }
    return createdItems;
  };

  private readonly transactionBatchUpdateItem = async (queryRunner: QueryRunner, itemsData: CatalogItem[]) => {
    let updateItems: CatalogItem[] = [];
    const manager = queryRunner.manager;
    const items = [];
    if (itemsData.length > 0) {
      for (const item of itemsData) {
        items.push(manager.create(CatalogItem, item));
      }
      updateItems = await manager.save(items);
    }
    return updateItems;
  };

  private readonly deleteCatalogItemsInEntity = async (manager: EntityManager, entityUuid: string) => {
    await manager
      .createQueryBuilder()
      .update(CatalogItem)
      .set({ status: ItemStatus.DELETED })
      .where({ entityUuid })
      .execute();
  };

  private readonly deleteCatalogItemsVariantsRelation = async (manager: EntityManager, items: CatalogItem[]) => {
    await manager
      .createQueryBuilder()
      .delete()
      .from('CatalogItems')
      .where({ parentId: In(items.map((item) => item.id)) })
      .execute();
  };

  private readonly deleteCatalogItemsAttributesRelation = async (manager: EntityManager, items: CatalogItem[]) => {
    await manager
      .createQueryBuilder()
      .delete()
      .from('CatalogItemAttributes')
      .where({ catalogItemId: In(items.map((item) => item.id)) })
      .execute();
  };

  private readonly deleteCatalogItemsModifierSetsRelation = async (manager: EntityManager, items: CatalogItem[]) => {
    await manager
      .createQueryBuilder()
      .delete()
      .from('CatalogItemModifiers')
      .where({ catalogItemId: In(items.map((item) => item.id)) })
      .execute();
  };

  private readonly deleteCatalogItemsSiteRelation = async (manager: EntityManager, items: CatalogItem[]) => {
    await manager
      .createQueryBuilder()
      .delete()
      .from('CatalogItemSiteSettings')
      .where({ catalogItemId: In(items.map((item) => item.id)) })
      .execute();
  };

  private readonly deleteCatalogItemsRelation = async (manager: EntityManager, items: CatalogItem[]) => {
    await manager
      .createQueryBuilder()
      .delete()
      .from('CatalogItemsCategories')
      .where({ catalogItemsId: In(items.map((item) => item.id)) })
      .execute();
  };

  private readonly deleteCatalogItemVariants = async (manager: EntityManager, itemVariants: CatalogItemVariant[]) => {
    await manager
      .getRepository(CatalogItemVariant)
      .save(
        itemVariants.map((variant) => ({ id: variant.id, parentId: null, images: [], status: ItemStatus.DELETED })),
      );
  };
}
