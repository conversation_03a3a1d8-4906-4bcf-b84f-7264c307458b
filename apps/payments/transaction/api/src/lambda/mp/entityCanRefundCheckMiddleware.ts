import { ForbiddenError, InvalidRequest } from '@npco/component-bff-core/dist/error';
import { info, warn } from '@npco/component-bff-core/dist/utils/logger';
import { SessionService } from '@npco/component-dbs-mp-common/dist/session/sessionService';
import type { NestAppEntityContext } from '@npco/component-dbs-mp-common/dist/types';
import { OnboardingStatus } from '@npco/component-dto-entity/dist/types';

import type { Context } from 'aws-lambda';

const ACTION_IS_NOT_ALLOW = 'Action is not allowed';

const allowedRefundOnboardingStatus = [OnboardingStatus.ONBOARDED, OnboardingStatus.RC_ONBOARDED];

export const entityCanRefundCheckMiddleware = async (
  event: any,
  context: Context | NestAppEntityContext,
  next: any,
) => {
  const { app, entityUuid } = context as NestAppEntityContext;

  const entity = await app.get(SessionService).getEntityStatuses(entityUuid);

  info(`get entity statues: ${JSON.stringify(entity)}`, entityUuid);

  if (!entity.onboardingStatus || !allowedRefundOnboardingStatus.includes(entity.onboardingStatus)) {
    warn(
      `Refund request denied: entity onboarding status ${entity.onboardingStatus} does not permit refunds`,
      entityUuid,
    );
    return Promise.reject(new InvalidRequest('Company not setup to accept payments')); // NOSONAR
  }

  if (!entity.accountStatus?.canRefund) {
    warn('Refund request denied: entity account status does not permit refunds', entityUuid);
    return Promise.reject(new ForbiddenError(ACTION_IS_NOT_ALLOW)); // NOSONAR
  }

  return next(event, context);
};
