import { error, info } from '@npco/component-bff-core/dist/utils/logger';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';
import { queryCustomerSiteUuids } from '@npco/component-dbs-mp-common/dist/customer';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb';
import { ReportServiceBase } from '@npco/component-dbs-mp-common/dist/transaction/reportServiceBase';
import type { QueryIndex } from '@npco/component-dbs-mp-common/dist/types';
import { findTimeoffsetFromRequest } from '@npco/component-dbs-mp-common/dist/utils/pdfExportUtils';
import { TransactionType } from '@npco/component-dto-core/dist/types';
import { TransactionStatus, TransactionTotalsType } from '@npco/component-dto-transaction/dist/types';
import type { Transaction, TransactionQueryInput, TransactionReport } from '@npco/component-dto-transaction/dist/types';

import { Injectable } from '@nestjs/common';

import { TransactionModel } from './transactionModel';
import { TransactionService } from './transactionService';
import { mergeTaxAmounts } from './transactionUtils';

@Injectable()
export class TransactionReportService extends ReportServiceBase<Transaction> {
  constructor(
    private readonly envService: EnvironmentService,
    private readonly dynamodbService: DynamodbService,
    private readonly transactionService: TransactionService,
  ) {
    super();
  }

  splitToSubQueries = async (event: TransactionQueryInput, entityUuid: string, customerAllowedSiteUuids: string[]) => {
    const length = TransactionModel.TOTAL_PARTITION_COUNT;
    const proms = [];
    for (let i = 0; i < length; i += 1) {
      proms.push(
        this.getAllTransactions(event, `${entityUuid}_${i}`, customerAllowedSiteUuids, {
          indexName: this.envService.entityTransactionTotalGsi,
          keyName: 'entityTransactionTotal200',
        }),
      );
    }
    try {
      const allTransactions = await Promise.all(proms);
      const results: Transaction[] = [];
      allTransactions.forEach((transactions) => transactions.forEach((e) => results.push(e)));
      return results;
    } catch (err: any) {
      error(`failed to get all transactions ${err.message}`);
    }
    return [];
  };

  getTransactionTotals = async (
    event: TransactionQueryInput,
    totalsType: TransactionTotalsType,
    entityUuid: string,
    managerCustomerUuid?: string,
  ): Promise<TransactionReport[]> => {
    const customerAllowedSiteUuids = await queryCustomerSiteUuids(
      this.dynamodbService,
      entityUuid,
      managerCustomerUuid,
    );
    let allTransactions: Transaction[] = [];
    if (managerCustomerUuid && customerAllowedSiteUuids.length === 0) {
      info(`customer ${managerCustomerUuid} doesnt have a site.`);
    } else {
      info(`get transaction report ${entityUuid} ${JSON.stringify(event, null, 2)} ${managerCustomerUuid}`);
      allTransactions = await this.splitToSubQueries(event, entityUuid, customerAllowedSiteUuids);
    }
    info(`calculate transaction # ${allTransactions.length} for reports.`);
    const periodTransactions = this.getPeriods(allTransactions, totalsType, findTimeoffsetFromRequest(event));
    const reports = this.mapTransactionTotals(periodTransactions, totalsType);
    return this.removeReportNan(reports);
  };

  isEmptyTotal = (total: TransactionReport) =>
    !total.countPurchases &&
    !total.countRefunds &&
    !total.totalAmount &&
    !total.totalAmountMinusFees &&
    !total.saleAmount &&
    !total.purchaseAmount &&
    !total.refundAmount &&
    !total.tipAmount &&
    !total.surchargeAmount &&
    !total.declinedAmount &&
    !total.noResponseAmount &&
    !total.cancelledAmount &&
    total.taxAmounts.length === 0;

  private readonly getTransactionReportInitialValues = (
    period: string,
    totalsType: TransactionTotalsType,
  ): TransactionReport => {
    const defaultInitial = {
      countPurchases: 0,
      countRefunds: 0,
      saleAmount: 0,
      totalAmount: 0,
      totalAmountMinusFees: 0,
      purchaseAmount: 0,
      refundAmount: 0,
      tipAmount: 0,
      surchargeAmount: 0,
      declinedAmount: 0,
      noResponseAmount: 0,
      cancelledAmount: 0,
      taxAmounts: [],
      feeAmount: 0,
      totalsType,
    };

    return totalsType === TransactionTotalsType.HOURLY_SUMMARY || totalsType === TransactionTotalsType.DAILY_SUMMARY
      ? { ...defaultInitial, periodLabel: period }
      : { ...defaultInitial, period };
  };

  private readonly getAllTransactions = async (
    event: TransactionQueryInput,
    pkValue: string,
    customerAllowedSiteUuids: string[],
    queryIndex?: QueryIndex,
  ) =>
    (await this.transactionService.queryTransactions(event, pkValue, customerAllowedSiteUuids, queryIndex, false))
      .transactions;

  private readonly mapTransactionTotals = (periodTransactions: any, totalsType: TransactionTotalsType) =>
    Object.keys(periodTransactions)
      .map((period) => {
        const txns = periodTransactions[period];
        return txns.reduce((accum: TransactionReport, txn: Transaction) => {
          /* eslint-disable no-param-reassign */
          if (txn.status === TransactionStatus.APPROVED) {
            if (txn.type === TransactionType.PURCHASE) {
              accum.countPurchases += 1;
              accum.purchaseAmount += txn.amount;
              accum.surchargeAmount += txn.surchargeAmount;
              accum.tipAmount += txn.tipAmount;
              accum.feeAmount += txn.feeAmount ?? 0;
              mergeTaxAmounts(accum.taxAmounts, txn.taxAmounts);
            }
            if (txn.type === TransactionType.REFUND) {
              accum.countRefunds += 1;
              accum.refundAmount += txn.amount;
              accum.feeAmount -= txn.feeAmount ?? 0;
            }
          } else if (txn.status === TransactionStatus.DECLINED) {
            accum.declinedAmount += txn.amount;
          }
          if (txn.status === TransactionStatus.CANCELLED) {
            accum.cancelledAmount += txn.amount;
          }
          if (txn.status === TransactionStatus.NORESPONSE) {
            accum.noResponseAmount += txn.amount;
          }
          accum.saleAmount = accum.purchaseAmount - accum.surchargeAmount - accum.tipAmount;
          accum.totalAmount = accum.purchaseAmount - accum.refundAmount;
          accum.totalAmountMinusFees = accum.totalAmount - accum.feeAmount;

          return accum;
        }, this.getTransactionReportInitialValues(period, totalsType));
      })
      .filter((total: TransactionReport) => !this.isEmptyTotal(total))
      .sort((a: TransactionReport, b: TransactionReport) => {
        if (a.period && b.period) {
          return new Date(a.period).getTime() - new Date(b.period).getTime();
        }
        return 0;
      });
}
