import { CardholderService } from '@npco/component-dbs-mp-common/dist/cardholder';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb';
import { LambdaService } from '@npco/component-dbs-mp-common/dist/lambda';
import { CommonService } from '@npco/component-dbs-mp-common/dist/module/commonService';
import { SessionService } from '@npco/component-dbs-mp-common/dist/session';
import { DbRecordType } from '@npco/component-dto-core/dist/types';
import type { InitiateTransactionCommandDto } from '@npco/component-dto-transaction/dist/initiateTransactionCommandDto';

import axios from 'axios';
import { instance, mock } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import { createTransactionRequestDto } from './testcases/utils';
import { TransactionDb } from './transactionDb';
import { TransactionService } from './transactionService';

jest.mock('axios');
jest.mock('aws-xray-sdk-core');
jest.mock('@npco/component-dbs-mp-common/dist/dynamodb/dynamodbService');
jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

jest.mock('@npco/component-dbs-mp-common/dist/devices/getDeviceSettingsDbItem', () => {
  return {
    getDeviceSettingsDbItemOrThrow: jest.fn().mockResolvedValue({}),
    getDeviceSettingsDbItem: jest.fn().mockResolvedValue({
      deviceName: 'not found',
      model: 'NEW9220',
    }),
  };
});
jest.mock('@npco/component-dbs-mp-common/dist/site/getSiteName', () => {
  return {
    getSiteName: jest.fn(() => Promise.resolve(uuidv4().slice(0, 10))),
  };
});
const mockQueryCustomerSiteUuids = jest.fn();
jest.mock('@npco/component-bff-core/dist/middleware/queryCustomerSiteUuids', () => ({
  queryCustomerSiteUuids: () => mockQueryCustomerSiteUuids(),
}));

describe('TransactionService pagination test suite', () => {
  let txnService: TransactionService;
  const mockEnvService: EnvironmentService = mock(EnvironmentService);
  const mockLambdaService: LambdaService = mock(LambdaService);
  const mockSessionService = mock(SessionService);

  let allTransactions = [];
  let entityUuid: string;
  let siteUuid: string;
  let siteUuid2: string;

  const sleep = (time = 100) =>
    new Promise((resolve) => {
      setTimeout(() => resolve(null), time);
    });

  const env = {
    componentTableName: 'Entities',
    entityGsi: 'entityGsi',
    typeGsi: 'typeGsi',
    cardholderGsi: 'cardholderGsi',
    deviceGsi: 'deviceGsi',
    siteGsi: 'siteGsi',
    entityTransactionTotalGsi: 'entityTransactionTotal200Gsi',
  } as any;
  const dynamodbSrv = new DynamodbService(env);

  // The terminal does not provide id or entityUuid
  // Compatiblity with old FE and APK
  const getTerminalTxnNextToken = (nextToken: any) => (nextToken?.type ? { type: nextToken.type } : nextToken);

  const createTransactionService = () => {
    const service = new TransactionService(
      new CommonService(dynamodbSrv, instance(mockEnvService), instance(mockLambdaService)),
      new CardholderService(dynamodbSrv, env),
      instance(mockSessionService),
    );
    service.transactionDb = new TransactionDb(env, dynamodbSrv);
    return service;
  };

  const createContactCardholder = (contactUuid: string, cardholderUuid: string) => ({
    id: `cardholder#${cardholderUuid}`,
    type: `contact#${contactUuid}`,
    entityUuid,
    contactUuid,
    cardholderUuid,
  });
  const saveDbItem = async (item: any) => {
    return dynamodbSrv.put({
      TableName: 'Entities',
      Item: item,
    });
  };

  let contactUuid1: string;
  let contactUuid2: string;
  let cardholderUuid1: string;
  let cardholderUuid2: string;
  let cardholderUuid3: string;
  let deviceUuid1: string;
  let deviceUuid2: string;
  beforeAll(async () => {
    contactUuid1 = uuidv4();
    contactUuid2 = uuidv4();
    cardholderUuid1 = uuidv4();
    cardholderUuid2 = uuidv4();
    cardholderUuid3 = uuidv4();
    deviceUuid1 = 'deviceUuid1';
    deviceUuid2 = 'deviceUuid2';
    txnService = createTransactionService();
    (axios as any).post.mockResolvedValue({});
    entityUuid = uuidv4();
    siteUuid = uuidv4();
    siteUuid2 = uuidv4();
    allTransactions = [];
    mockQueryCustomerSiteUuids.mockResolvedValue([siteUuid, siteUuid2]);
    const txnSrv = createTransactionService();
    for (let i = 0; i < 100; i += 1) {
      const initiate = createTransactionRequestDto();
      initiate.timestampUtc = new Date(new Date().getTime() + i).toISOString();
      initiate.transactionUuid = uuidv4();
      initiate.entityUuid = entityUuid;
      initiate.siteUuid = i > 50 ? siteUuid2 : siteUuid;
      initiate.deviceUuid = i > 50 ? deviceUuid1 : deviceUuid2;
      initiate.cardholderUuid = i % 2 === 0 ? cardholderUuid2 : cardholderUuid1;
      initiate.cardholderUuid = i % 5 === 0 ? cardholderUuid3 : cardholderUuid1;
      await txnSrv.initiateTransactionProjection(initiate);
      allTransactions.push(initiate);
    }
    // Add a deleted transaction which should never appear
    const initiate: InitiateTransactionCommandDto = createTransactionRequestDto();
    initiate.timestampUtc = new Date(new Date().getTime() + 101).toISOString();
    initiate.transactionUuid = uuidv4();
    initiate.entityUuid = entityUuid;
    initiate.siteUuid = siteUuid;
    await txnSrv.initiateTransactionProjection(initiate);
    await txnService.transactionDb.updateTransaction(entityUuid, initiate.transactionUuid, {
      status: 'DELETED',
    });
    await sleep(200);
  });

  /**
   * for old FE and APK
   */
  it('should be able to query transaction with next token for type only', async () => {
    const txnSrv = createTransactionService();
    let response = await txnSrv.getTransactions({ limit: 10 }, entityUuid);
    expect(response.transactions.length).toBe(10);
    expect(response.nextToken?.id).not.toBeNull();
    expect(response.nextToken?.entityUuid).toBe(entityUuid);
    expect(response.nextToken?.type.startsWith(DbRecordType.TRANSACTION)).toBeTruthy();

    console.log('get next token:', response.nextToken);

    response = await txnSrv.getTransactions(
      { limit: 10, nextToken: getTerminalTxnNextToken(response.nextToken) },
      entityUuid,
    );
    expect(response.transactions.length).toBe(10);
    expect(response.nextToken?.id).not.toBeNull();
    expect(response.nextToken?.entityUuid).toBe(entityUuid);
    expect(response.nextToken?.type.startsWith(DbRecordType.TRANSACTION)).toBeTruthy();

    response = await txnSrv.getTransactions(
      { limit: allTransactions.length, nextToken: getTerminalTxnNextToken(response.nextToken) },
      entityUuid,
    );
    expect(response.transactions.length).toBe(allTransactions.length - 20);
    expect(response.nextToken).toBeUndefined();
  });

  it('should be able to query transaction with next token for all fields in next token', async () => {
    const txnSrv = createTransactionService();
    let response = await txnSrv.getTransactions({ limit: 10 }, entityUuid);
    let txns = response.transactions;
    expect(response.transactions.length).toBe(10);
    expect(response.nextToken?.id).not.toBeNull();
    expect(response.nextToken?.entityUuid).toBe(entityUuid);
    expect(response.nextToken?.type.startsWith(DbRecordType.TRANSACTION)).toBeTruthy();

    response = await txnSrv.getTransactions({ limit: 10, nextToken: response.nextToken }, entityUuid);

    txns = txns.concat(response.transactions);
    expect(response.transactions.length).toBe(10);
    expect(response.nextToken?.id).not.toBeNull();
    expect(response.nextToken?.entityUuid).toBe(entityUuid);
    expect(response.nextToken?.type.startsWith(DbRecordType.TRANSACTION)).toBeTruthy();
    response = await txnSrv.getTransactions({ limit: 10, nextToken: response.nextToken }, entityUuid);

    txns = txns.concat(response.transactions);
    expect(response.transactions.length).toBe(10);
    txns.forEach((txn: any, i) => {
      const prev = i > 0 ? i - 1 : 0;
      expect(new Date(txn.timestamp).getTime()).toBeLessThanOrEqual(new Date(txns[prev].timestamp).getTime());
    });
    response = await txnSrv.getTransactions(
      { limit: allTransactions.length, nextToken: response.nextToken },
      entityUuid,
    );

    expect(response.transactions.length).toBe(allTransactions.length - 30);
    expect(response.nextToken).toBeUndefined();
  });

  it('should not response next token if query all transactions', async () => {
    const txnSrv = createTransactionService();
    let response = await txnSrv.getTransactions({ limit: allTransactions.length + 1 }, entityUuid);
    expect(response.transactions.length).toBe(allTransactions.length);
    expect(response.nextToken).toBeUndefined();
    response = await txnSrv.getTransactions({ limit: 200 }, entityUuid);
    expect(response.transactions.length).toBe(allTransactions.length);
    expect(response.nextToken).toBeUndefined();
  }, 10000);

  it('should be able to get transactions with siteUuids', async () => {
    const txnSrv = createTransactionService();
    mockQueryCustomerSiteUuids.mockResolvedValue([siteUuid, siteUuid2]);
    let response = await txnSrv.getTransactions({}, entityUuid, '');
    expect(response.transactions.length).toBe(allTransactions.length);
    mockQueryCustomerSiteUuids.mockResolvedValue([siteUuid2]);

    response = await txnSrv.getTransactions({}, entityUuid, 'customerUuid');
    expect(response.transactions.length).toBe(49);
    mockQueryCustomerSiteUuids.mockResolvedValue([siteUuid, siteUuid2]);

    response = await txnSrv.getTransactions({}, entityUuid, 'customerUuid');
    expect(response.transactions.length).toBe(allTransactions.length);
  });

  describe('transaction by contact cardholders', () => {
    it('should be able to get transactions paginated with cardholderUuid', async () => {
      await saveDbItem(createContactCardholder(contactUuid2, cardholderUuid3));

      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions({ contactUuids: [contactUuid2], limit: 10 }, entityUuid);
      expect(response.transactions.length).toBe(10);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(10);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { contactUuids: [contactUuid2], nextToken: response.nextToken, limit: 20 },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(10);
      expect(duplicates.size).toBe(20);
      console.log('cardholderGsi', response.nextToken);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { contactUuids: [contactUuid2], nextToken: getTerminalTxnNextToken(response.nextToken), limit: 20 },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(0);
      expect(duplicates.size).toBe(20);
    });

    it('should be able to get transactions paginated with cardholderUuids', async () => {
      await saveDbItem(createContactCardholder(contactUuid1, cardholderUuid1));
      await saveDbItem(createContactCardholder(contactUuid1, cardholderUuid2));

      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions(
        { contactUuids: [contactUuid1, contactUuid2], limit: 50 },
        entityUuid,
      );
      expect(response.transactions.length).toBe(50);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(50);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        {
          contactUuids: [contactUuid1, contactUuid2],
          nextToken: response.nextToken,
          limit: 30,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(30);
      expect(duplicates.size).toBe(80);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        {
          contactUuids: [contactUuid1, contactUuid2],
          nextToken: response.nextToken,
          limit: 22,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(20);
      expect(duplicates.size).toBe(100);
      response = await txnSrv.getTransactions(
        {
          contactUuids: [contactUuid1, contactUuid2],
          nextToken: response.nextToken,
          limit: 20,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.nextToken).toBeUndefined();
      expect(response.transactions.length).toBe(0);
      expect(duplicates.size).toBe(100);
    });

    it('should be able to get transactions with cardholderUuids', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);

      let response = await txnSrv.getTransactions({ contactUuids: ['contact01'] }, entityUuid);
      expect(response.transactions.length).toBe(0);
      response = await txnSrv.getTransactions({ contactUuids: [contactUuid1] }, entityUuid);
      expect(response.transactions.length).toBe(80);
      response = await txnSrv.getTransactions({ contactUuids: [contactUuid2] }, entityUuid);
      expect(response.transactions.length).toBe(20);
      response = await txnSrv.getTransactions({ contactUuids: [contactUuid1, contactUuid2] }, entityUuid);
      expect(response.transactions.length).toBe(100);
      const duplicates = new Set();
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(100);
    });

    it('should be able to filter exclude cardholderUuid for get transactions with contactUuds', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);

      let response = await txnSrv.getTransactions(
        { contactUuids: ['contact01'], cardholderUuid: JSON.stringify({ eq: cardholderUuid2 }) },
        entityUuid,
      );
      expect(response.transactions.length).toBe(0);
      response = await txnSrv.getTransactions(
        { contactUuids: [contactUuid1, contactUuid2], cardholderUuid: JSON.stringify({ eq: cardholderUuid3 }) },
        entityUuid,
      );
      expect(response.transactions.length).toBe(80);
    });
  });

  describe('transactions by deviceUuidss deviceGsi', () => {
    it('should be able to get transactions paginated with deviceUuid', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions({ deviceUuids: [deviceUuid1], limit: 10 }, entityUuid);
      expect(response.transactions.length).toBe(10);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(10);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        {
          deviceUuids: [deviceUuid1],
          nextToken: response.nextToken,
          limit: 20,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(20);
      expect(duplicates.size).toBe(30);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        {
          deviceUuids: [deviceUuid1],
          nextToken: response.nextToken,
          limit: 25,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(19);
      expect(duplicates.size).toBe(49);
      response = await txnSrv.getTransactions(
        {
          deviceUuids: [deviceUuid1],
          limit: 200,
        },
        entityUuid,
      );
      expect(response.transactions.length).toBe(49);
    });

    it('should be able to get transactions paginated with deviceUuid for terminal token', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions({ deviceUuids: [deviceUuid1], limit: 10 }, entityUuid);
      expect(response.transactions.length).toBe(10);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(10);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        {
          deviceUuids: [deviceUuid1],
          nextToken: getTerminalTxnNextToken(response.nextToken), // compatiblity with old FE and APK
          limit: 20,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(20);
      expect(duplicates.size).toBe(30);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        {
          deviceUuids: [deviceUuid1],
          nextToken: getTerminalTxnNextToken(response.nextToken),
          limit: 25,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(19);
      expect(duplicates.size).toBe(49);
      expect(response.nextToken).toBeDefined();

      response = await txnSrv.getTransactions(
        {
          deviceUuids: [deviceUuid1],
          nextToken: getTerminalTxnNextToken(response.nextToken),
          limit: 25,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(0);
      expect(response.nextToken).toBeUndefined();
      expect(duplicates.size).toBe(49);
      response = await txnSrv.getTransactions(
        {
          deviceUuids: [deviceUuid1],
          limit: 200,
        },
        entityUuid,
      );
      expect(response.transactions.length).toBe(49);
    });

    it('should be able to get transactions paginated with deviceUuids', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions({ deviceUuids: [deviceUuid1, deviceUuid2], limit: 50 }, entityUuid);
      expect(response.transactions.length).toBe(50);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(50);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { deviceUuids: [deviceUuid1, deviceUuid2], nextToken: response.nextToken, limit: 30 },
        entityUuid,
      );
      expect(response.nextToken).toBeDefined();
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(30);
      expect(duplicates.size).toBe(80);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { deviceUuids: [deviceUuid1, deviceUuid2], nextToken: response.nextToken, limit: 20 },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(20);
      expect(duplicates.size).toBe(100);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { deviceUuids: [deviceUuid1, deviceUuid2], nextToken: response.nextToken, limit: 20 },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(0);
      expect(duplicates.size).toBe(100);
    });

    it('should be able to get transactions with deviceUuids', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);

      let response = await txnSrv.getTransactions({ deviceUuids: ['contact01'] }, entityUuid);
      expect(response.transactions.length).toBe(0);
      response = await txnSrv.getTransactions({ deviceUuids: [deviceUuid1] }, entityUuid);
      expect(response.transactions.length).toBe(49);
      response = await txnSrv.getTransactions({ deviceUuids: [deviceUuid2] }, entityUuid);
      expect(response.transactions.length).toBe(51);
      response = await txnSrv.getTransactions({ deviceUuids: [deviceUuid1, deviceUuid2] }, entityUuid);
      expect(response.transactions.length).toBe(100);
      const duplicates = new Set();
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(100);
    });
  });

  describe('transactions by siteUuids siteGsi', () => {
    it('should be able to get transactions paginated with siteUuid', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions({ siteUuids: [siteUuid], limit: 10 }, entityUuid);
      expect(response.transactions.length).toBe(10);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(10);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        {
          siteUuids: [siteUuid],
          nextToken: response.nextToken,
          limit: 20,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(20);
      expect(duplicates.size).toBe(30);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        {
          siteUuids: [siteUuid],
          nextToken: getTerminalTxnNextToken(response.nextToken), // compatiblity with old FE and APK
          limit: 25,
        },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(21);
      expect(response.nextToken).toBeDefined();
      expect(duplicates.size).toBe(51);
      response = await txnSrv.getTransactions(
        {
          siteUuids: [siteUuid],
          limit: 200,
        },
        entityUuid,
      );
      expect(response.transactions.length).toBe(51);
      response = await txnSrv.getTransactions(
        {
          siteUuids: [siteUuid],
          limit: 200,
          nextToken: { type: JSON.stringify(['transaction.notfound00']) },
        },
        entityUuid,
      );
      expect(response.transactions.length).toBe(51);
    });

    it('should be able to get transactions paginated with siteUuids', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions({ siteUuids: [siteUuid, siteUuid2], limit: 50 }, entityUuid);
      expect(response.transactions.length).toBe(50);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(50);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid, siteUuid2], nextToken: response.nextToken, limit: 30 },
        entityUuid,
      );
      expect(response.nextToken).toBeDefined();
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(30);
      expect(duplicates.size).toBe(80);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid, siteUuid2], nextToken: response.nextToken, limit: 20 },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(20);
      expect(duplicates.size).toBe(100);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid, siteUuid2], nextToken: response.nextToken, limit: 20 },
        entityUuid,
      );
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(response.transactions.length).toBe(0);
      expect(response.nextToken).toBeUndefined();
      expect(duplicates.size).toBe(100);
    });

    it('should be able to get transactions with siteUuids', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);

      let response = await txnSrv.getTransactions({ siteUuids: ['contact01'] }, entityUuid);
      expect(response.transactions.length).toBe(0);
      response = await txnSrv.getTransactions({ siteUuids: [siteUuid] }, entityUuid);
      expect(response.transactions.length).toBe(51);
      response = await txnSrv.getTransactions({ siteUuids: [siteUuid2] }, entityUuid);
      expect(response.transactions.length).toBe(49);
      response = await txnSrv.getTransactions({ siteUuids: [siteUuid, siteUuid2] }, entityUuid);
      expect(response.transactions.length).toBe(100);
      const duplicates = new Set();
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      expect(duplicates.size).toBe(100);
    });

    it('should be able to get transactions with multi uuids across pagination with dashboard token', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions({ siteUuids: [siteUuid, siteUuid2], limit: 25 }, entityUuid);
      expect(response.transactions.length).toBe(25);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      let txns = response.transactions;
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid, siteUuid2], limit: 25, nextToken: response.nextToken },
        entityUuid,
      );
      expect(response.transactions.length).toBe(25);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      txns = txns.concat(response.transactions);
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid, siteUuid2], limit: 25, nextToken: response.nextToken },
        entityUuid,
      );
      expect(response.transactions.length).toBe(25);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      txns = txns.concat(response.transactions);
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid, siteUuid2], limit: 25, nextToken: response.nextToken },
        entityUuid,
      );
      expect(response.transactions.length).toBe(25);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      txns = txns.concat(response.transactions);
      txns.forEach((txn: any, i) => {
        const prev = i > 0 ? i - 1 : 0;
        expect(new Date(txn.timestamp).getTime()).toBeLessThanOrEqual(new Date(txns[prev].timestamp).getTime());
      });
      expect(duplicates.size).toBe(100);
      expect(response.nextToken).toBeDefined();
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid, siteUuid2], limit: 25, nextToken: response.nextToken },
        entityUuid,
      );
      expect(response.transactions.length).toBe(0);
      expect(response.nextToken).toBeUndefined();
    });

    it('should be able to get transactions with single siteUuid across pagination with terminal token', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([]);
      const duplicates = new Set();
      let response = await txnSrv.getTransactions({ siteUuids: [siteUuid], limit: 25 }, entityUuid);
      expect(response.transactions.length).toBe(25);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      let txns = response.transactions;
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid], limit: 25, nextToken: getTerminalTxnNextToken(response.nextToken) },
        entityUuid,
      );
      expect(response.transactions.length).toBe(25);
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      txns = txns.concat(response.transactions);
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid], limit: 25, nextToken: getTerminalTxnNextToken(response.nextToken) },
        entityUuid,
      );
      expect(response.transactions.length).toBe(1);
      expect(response.nextToken).toBeDefined();
      response.transactions.forEach((txn) => duplicates.add(txn.id));
      txns = txns.concat(response.transactions);
      txns.forEach((txn: any, i) => {
        const prev = i > 0 ? i - 1 : 0;
        expect(new Date(txn.timestamp).getTime()).toBeLessThanOrEqual(new Date(txns[prev].timestamp).getTime());
      });
      expect(duplicates.size).toBe(51);
      response = await txnSrv.getTransactions(
        { siteUuids: [siteUuid], limit: 25, nextToken: getTerminalTxnNextToken(response.nextToken) },
        entityUuid,
      );
      expect(response.transactions.length).toBe(0);
      expect(response.nextToken).toBeUndefined();
    });

    it('should not be able to query unassigned siteUuid', async () => {
      const txnSrv = createTransactionService();
      mockQueryCustomerSiteUuids.mockResolvedValue([siteUuid]);
      let response = await txnSrv.getTransactions({ siteUuids: [siteUuid] }, entityUuid);
      expect(response.transactions.length).toBe(51);
      response = await txnSrv.getTransactions({ siteUuids: [siteUuid, siteUuid2] }, entityUuid);
      expect(response.transactions.length).toBe(51);
      response = await txnSrv.getTransactions({ siteUuids: [siteUuid2] }, entityUuid);
      expect(response.transactions.length).toBe(0);
    });
  });
});
