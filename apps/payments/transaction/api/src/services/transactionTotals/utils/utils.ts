import { warn } from '@npco/component-bff-core/dist/utils/logger';
import type { MoneyNumber, TaxAmount } from '@npco/component-dbs-mp-common/dist/types';
import type { Money } from '@npco/component-dto-core/dist/types';

import { formatInTimeZone } from 'date-fns-tz';

import type {
  AmPmHour,
  ISO8601Date,
  ISO8601DateTime,
  ISO8601FloatingDateTime,
  ISO8601Time,
  LocalisedValidTransaction,
  ReportingDate,
  ReportingYearMonth,
  SiteDateTimeRange,
  TimeZoneDesignator,
  UnvalidatedTransactionModel,
  ValidatedTransactionModel,
} from '../types';
import { DayOfWeek } from '../types';

export const chunkArray = <T>(array: T[], chunkSize: number): T[][] => {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

export const getRecalculationStartDateAndHour = (
  now: Date,
  offset: number,
): { startDate: ReportingDate; startHour: string } => {
  const recalculateStartTime = new Date(now.getTime() - offset);
  return {
    startDate: recalculateStartTime.toISOString().slice(0, 10) as ReportingDate,
    startHour: recalculateStartTime.toISOString().slice(11, 13),
  };
};

export function getHourFromTimestamp(timestamp: ISO8601DateTime): HourString {
  return timestamp.slice(11, 13) as HourString;
}

export type HourString = `${number}${number}`;
export const convertHourToAmPm = (hour: HourString): AmPmHour => {
  const hourInt = parseInt(hour, 10);
  if (hourInt === 0) {
    return '12am';
  }
  if (hourInt < 12) {
    return `${hourInt}am`;
  }
  if (hourInt === 12) {
    return '12pm';
  }
  return `${hourInt - 12}pm`;
};
export const convertAmPmToHour = (hour: AmPmHour): number => {
  if (hour === '12am') {
    return 0;
  }
  if (hour === '12pm') {
    return 12;
  }
  if (hour.includes('am')) {
    return parseInt(hour.replace('am', ''), 10);
  }
  return parseInt(hour.replace('pm', ''), 10) + 12;
};

export const getCurrentHourToAmPmString = (timestampLocal: ISO8601DateTime): AmPmHour => {
  return convertHourToAmPm(getHourFromTimestamp(timestampLocal));
};

export const getDayInWeek = (timestamp: ReportingDate) => {
  const daysOfWeek = Object.values(DayOfWeek) as DayOfWeek[];
  return daysOfWeek[new Date(`${timestamp.split('T')[0]}T00:00:00.000Z`).getDay()];
};

export function reportingDateToReportingYearMonth(date: ReportingDate): ReportingYearMonth {
  return date.slice(0, 7) as ReportingYearMonth;
}

export const validateTransactionModel = (
  transaction: UnvalidatedTransactionModel,
): ValidatedTransactionModel | null => {
  if (!transaction) {
    return null;
  }
  const { entityUuid, siteUuid, timestampLocal, timestampUtc, source, transactionType, amount } = transaction;
  if (!entityUuid || !siteUuid || !timestampLocal || !timestampUtc || !source || !transactionType || !amount) {
    warn('Transaction is missing required fields', transaction.id);
    return null;
  }
  return transaction as ValidatedTransactionModel;
};

export const appendLocalDateTimeToTransaction = (
  transaction: ValidatedTransactionModel,
  siteTimezone?: string,
): LocalisedValidTransaction => {
  if (!siteTimezone) {
    return { ...transaction, localDateTime: transaction.timestampLocal };
  }
  const formattedDateTime = formatInTimeZone(
    new Date(transaction.timestampUtc),
    siteTimezone,
    'yyyy-MM-dd HH:mm:ss.SSSxxx',
  );
  const datePart = formattedDateTime.split(' ')[0] as ISO8601Date;
  const timePart = formattedDateTime.split(' ')[1] as `${ISO8601Time}${TimeZoneDesignator}`;

  return { ...transaction, localDateTime: `${datePart}T${timePart}` };
};

export const parseAmountValue = (amount?: MoneyNumber | Money): number => {
  if (!amount) {
    return 0;
  }
  if (typeof amount.value === 'string') {
    return parseInt(amount.value, 10);
  }
  return amount.value;
};

export const parseAmountValueToBigInt = (amount?: MoneyNumber | Money): bigint => {
  if (!amount) {
    return BigInt(0);
  }
  return BigInt(amount.value);
};

export const mergeTaxAmountsToTotals = (
  taxAmountFromTotals: Record<string, bigint>,
  updatedTaxAmounts?: TaxAmount[],
) => {
  const totalsCopy = { ...taxAmountFromTotals };
  if (updatedTaxAmounts) {
    updatedTaxAmounts.forEach((updatedTaxAmount: TaxAmount) => {
      totalsCopy[updatedTaxAmount.name] =
        (totalsCopy[updatedTaxAmount.name] ?? BigInt(0)) + parseAmountValueToBigInt(updatedTaxAmount.amount);
    });
  }
  return totalsCopy;
};

export const mergeTaxAmountsToTotalsByTotals = (
  taxAmountFromTotals: Record<string, bigint>,
  updatedTaxAmounts?: Record<string, bigint>,
) => {
  const totalsCopy = { ...taxAmountFromTotals };
  if (updatedTaxAmounts) {
    Object.entries(updatedTaxAmounts).forEach(([taxName, taxAmount]) => {
      totalsCopy[taxName] = (totalsCopy[taxName] ?? BigInt(0)) + taxAmount;
    });
  }
  return totalsCopy;
};

export const unmergeTaxAmountToTotals = (
  taxAmountFromTotals: Record<string, bigint>,
  updatedTaxAmounts?: TaxAmount[],
) => {
  const totalsCopy = { ...taxAmountFromTotals };
  if (updatedTaxAmounts) {
    updatedTaxAmounts.forEach((updatedTaxAmount: TaxAmount) => {
      totalsCopy[updatedTaxAmount.name] =
        (totalsCopy[updatedTaxAmount.name] ?? BigInt(0)) - parseAmountValueToBigInt(updatedTaxAmount.amount);
    });
  }
  return totalsCopy;
};

/**
 * Add n days to a date
 * @param date `YYYY-MM-DD`
 * @param days number of days to add
 * @returns `YYYY-MM-DD`
 */
export const addNDaysToDate = (date: ISO8601Date, days: number): ISO8601Date => {
  const newDate = new Date(date);
  newDate.setDate(newDate.getDate() + days);
  return newDate.toISOString().slice(0, 10) as ISO8601Date;
};

export const isWithinTimeRange = (inputDateTime: ISO8601FloatingDateTime, range: SiteDateTimeRange) => {
  const inputTime = new Date(inputDateTime).getTime();
  const startTime = new Date(range.startDateTime).getTime();
  const endTime = new Date(range.endDateTime).getTime();
  return inputTime >= startTime && inputTime <= endTime;
};

export function getRandomSleepTime(staticBaseTimeMilliseconds = 1000) {
  const randomSleepTimeSeed = `${new Date(Date.now()).getMilliseconds()}`.padStart(3, '0');
  const randomSleepTime = Number.parseInt(
    `${randomSleepTimeSeed[2]}${randomSleepTimeSeed[1]}${randomSleepTimeSeed[0]}`,
    10,
  );
  return randomSleepTime + staticBaseTimeMilliseconds;
}

export const deepDiff = (
  left: Record<string, any>,
  right: Record<string, any>,
): Record<string, any> | 'No Difference' | undefined => {
  if (typeof left === 'undefined' && typeof right !== 'undefined') {
    return right;
  }
  if (typeof left !== 'undefined' && typeof right === 'undefined') {
    return left;
  }

  const diff: Record<string, any> = {} as Record<string, any>;
  let anyDifference = false;

  const uniqueUnionOfKeysList = Array.from(new Set([...Object.keys(left), ...Object.keys(right)]));

  uniqueUnionOfKeysList.forEach((key) => {
    if (typeof left[key] === 'object' && typeof right[key] === 'object') {
      const deepDiffResult = deepDiff(left[key], right[key]);

      if (deepDiffResult !== 'No Difference' && typeof deepDiffResult !== 'undefined') {
        diff[key] = deepDiffResult;
        anyDifference = true;
      }

      return;
    }

    if (typeof left[key] === 'undefined' && typeof right[key] !== 'undefined') {
      diff[key] = right[key];
      anyDifference = true;
      return;
    }
    if (typeof left[key] !== 'undefined' && typeof right[key] === 'undefined') {
      diff[key] = left[key];
      anyDifference = true;
      return;
    }

    if (left[key] !== right[key]) {
      diff[key] = `${left[key]} -> ${right[key]}`;

      anyDifference = true;
    }
  });

  if (!anyDifference) {
    return 'No Difference';
  }
  return diff;
};

export const formatCentsToDollars = (cents: string): string => {
  if (!/^-?\d+$/.test(cents)) {
    throw new Error('Invalid input: must be an integer string (positive or negative).');
  }

  const bigIntCents = BigInt(cents);
  const isNegative = bigIntCents < 0n;
  const absoluteCents = bigIntCents < 0n ? -bigIntCents : bigIntCents;

  const dollars = absoluteCents / 100n;
  const centsPart = absoluteCents % 100n;
  const formattedCents = centsPart.toString().padStart(2, '0');

  return `${isNegative ? '$-' : '$'}${dollars}.${formattedCents}`;
};
