import { ISO4217 } from '@npco/component-dto-core/dist/types';

import type { ISO8601DateTime, UnvalidatedTransactionModel, ValidatedTransactionModel } from '../types';

import { calculateReportingDate } from './calculateReportingDate';
import {
  addNDaysToDate,
  appendLocalDateTimeToTransaction,
  chunkArray,
  formatCentsToDollars,
  getCurrentHourToAmPmString,
  getRandomSleepTime,
  getRecalculationStartDateAndHour,
  isWithinTimeRange,
  parseAmountValue,
  validateTransactionModel,
} from './utils';

describe('Transaction Totals Utils', () => {
  describe('validateTransactionModel', () => {
    it('should return null when required field is missing from transaction', () => {
      const transaction = {
        id: '1',
        siteUuid: 'site-uuid',
        timestampLocal: '2025-01-01T11:00:00.000+11:00',
        timestampUtc: '2025-01-01T00:00:00.000Z',
        source: 'source',
        transactionType: 'transactionType',
        amount: { value: '100', currency: ISO4217.AUD },
      } as unknown as UnvalidatedTransactionModel;

      const result = validateTransactionModel(transaction);
      expect(result).toBeNull();
    });
  });

  describe('getRecalculationStartDateAndHour', () => {
    it('should get recalculation getRecalculationStartDateAndHour date correctly', () => {
      const THIRTY_EIGHT_HOUR = 38 * 60 * 60 * 1000;
      expect(getRecalculationStartDateAndHour(new Date('2025-01-02T00:00:00.000Z'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-12-31',
        startHour: '10',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-01-02T10:59:59.999Z'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-12-31',
        startHour: '20',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-01-03T00:00:00.000Z'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-01-01',
        startHour: '10',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-01-03T13:59:59.999Z'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-01-01',
        startHour: '23',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-01-03T14:00:00.000Z'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-01-02',
        startHour: '00',
      });

      expect(getRecalculationStartDateAndHour(new Date('2025-01-02T13:59:59.999+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-12-31',
        startHour: '09',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-01-02T14:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-12-31',
        startHour: '10',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-01-03T13:59:59.999+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-01-01',
        startHour: '09',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-01-03T14:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-01-01',
        startHour: '10',
      });

      expect(getRecalculationStartDateAndHour(new Date('2025-02-03T13:59:59.999+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-02-01',
        startHour: '09',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-02-03T14:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-02-01',
        startHour: '10',
      });

      expect(getRecalculationStartDateAndHour(new Date('2025-02-04T05:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-02-02',
        startHour: '01',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-02-04T13:59:59.999+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-02-02',
        startHour: '09',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-02-04T14:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-02-02',
        startHour: '10',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-02-04T15:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-02-02',
        startHour: '11',
      });
      expect(getRecalculationStartDateAndHour(new Date('2025-02-04T15:59:59.999+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2025-02-02',
        startHour: '11',
      });
    });

    it('should get recalculation start date correctly for leap year', () => {
      const THIRTY_EIGHT_HOUR = 38 * 60 * 60 * 1000;
      expect(getRecalculationStartDateAndHour(new Date('2024-03-01T14:00:00.000Z'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-02-29',
        startHour: '00',
      });
      expect(getRecalculationStartDateAndHour(new Date('2024-02-29T13:59:59.999+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-02-27',
        startHour: '09',
      });
      expect(getRecalculationStartDateAndHour(new Date('2024-02-29T14:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-02-27',
        startHour: '10',
      });
      expect(getRecalculationStartDateAndHour(new Date('2024-03-01T14:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-02-28',
        startHour: '10',
      });
      expect(getRecalculationStartDateAndHour(new Date('2024-03-02T14:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-02-29',
        startHour: '10',
      });
      expect(getRecalculationStartDateAndHour(new Date('2024-03-03T14:00:00.000+14:00'), THIRTY_EIGHT_HOUR)).toEqual({
        startDate: '2024-03-01',
        startHour: '10',
      });
    });
  });

  describe('appendLocalDateTimeToTransaction', () => {
    it('should append local date time to transaction correctly', () => {
      const result = appendLocalDateTimeToTransaction(
        { timestampUtc: '2025-01-01T00:00:00.000Z' } as unknown as ValidatedTransactionModel,
        'Australia/Sydney',
      );
      expect(result).toEqual({
        timestampUtc: '2025-01-01T00:00:00.000Z',
        localDateTime: '2025-01-01T11:00:00.000+11:00',
      });
    });

    it('should append local date time to transaction correctly', () => {
      const result = appendLocalDateTimeToTransaction(
        { timestampUtc: '2025-01-01T00:00:00.000Z' } as unknown as ValidatedTransactionModel,
        'Australia/Perth',
      );
      expect(result).toEqual({
        timestampUtc: '2025-01-01T00:00:00.000Z',
        localDateTime: '2025-01-01T08:00:00.000+08:00',
      });
    });

    it('should fallback to timestampLocal when timezone is not set', () => {
      const result = appendLocalDateTimeToTransaction({
        timestampUtc: '2025-01-01T00:00:00.000Z',
        timestampLocal: '2025-01-01T11:00:00.000+11:00',
      } as unknown as ValidatedTransactionModel);
      expect(result).toEqual({
        timestampUtc: '2025-01-01T00:00:00.000Z',
        timestampLocal: '2025-01-01T11:00:00.000+11:00',
        localDateTime: '2025-01-01T11:00:00.000+11:00',
      });
    });
  });

  describe('getCurrentHourToAmPmString test suite', () => {
    test.each([
      { date: '2025-02-10T00:00:00+11:00' as ISO8601DateTime, expected: '12am' },
      { date: '2025-02-10T00:00:00+10:00' as ISO8601DateTime, expected: '12am' },
      { date: '2025-02-10T11:00:00+11:00' as ISO8601DateTime, expected: '11am' },
      { date: '2025-02-10T11:30:00+11:00' as ISO8601DateTime, expected: '11am' },
      { date: '2025-02-10T12:00:00+11:00' as ISO8601DateTime, expected: '12pm' },
      { date: '2025-02-10T15:00:00+11:00' as ISO8601DateTime, expected: '3pm' },
      { date: '2025-02-10T23:00:00+11:00' as ISO8601DateTime, expected: '11pm' },
    ])('should convert hour to AM/PM format for date %p', ({ date, expected }) => {
      console.log(date);
      const result = getCurrentHourToAmPmString(date);
      expect(result).toBe(expected);
    });
  });

  describe('calculateReportingDate', () => {
    it('should calculate reporting date correctly', () => {
      expect(calculateReportingDate('2025-01-02T00:00:00.000Z', '0')).toBe('2025-01-02');
      expect(calculateReportingDate('2025-01-02T03:01:01.000Z', '3')).toBe('2025-01-02');
      expect(calculateReportingDate('2025-01-02T04:00:00.000Z', '3')).toBe('2025-01-02');
      expect(calculateReportingDate('2025-01-03T02:00:00.000Z', '3')).toBe('2025-01-02');
      expect(calculateReportingDate('2025-01-03T02:59:59.000Z', '3')).toBe('2025-01-02');
      expect(calculateReportingDate('2025-01-03T03:00:00.000Z', '3')).toBe('2025-01-03');
      expect(calculateReportingDate('2025-02-10T00:30:00+11:00', '0')).toBe('2025-02-10');
      expect(calculateReportingDate('2025-02-10T01:30:00+10:00', '3')).toBe('2025-02-09');
      expect(calculateReportingDate('2025-02-10T01:30:00+11:00', '3')).toBe('2025-02-09');
      expect(calculateReportingDate('2025-02-10T03:00:00+11:00', '3')).toBe('2025-02-10');
      expect(calculateReportingDate('2025-02-10T03:05:00+11:00', '3')).toBe('2025-02-10');
    });
  });

  describe('chunkArray', () => {
    it('should chunk array correctly', () => {
      expect(chunkArray([1, 2, 3, 4, 5], 3)).toEqual([
        [1, 2, 3],
        [4, 5],
      ]);
      expect(chunkArray([1, 2, 3, 4, 5], 1)).toEqual([[1], [2], [3], [4], [5]]);
      expect(chunkArray([1, 2, 3, 4, 5], 5)).toEqual([[1, 2, 3, 4, 5]]);
      expect(chunkArray([1, 2, 3, 4, 5], 6)).toEqual([[1, 2, 3, 4, 5]]);
    });
  });

  describe('parseAmountValue test suite', () => {
    test.each([
      { amount: undefined, expected: 0 },
      { amount: { value: '100', currency: ISO4217.AUD }, expected: 100 },
      { amount: { value: 200, currency: ISO4217.AUD }, expected: 200 },
    ])('should parse amount value correctly for %p', ({ amount, expected }) => {
      const result = parseAmountValue(amount);
      expect(result).toBe(expected);
    });
  });

  describe('addNDaysToDate test suite', () => {
    it('should add n days to date correctly', () => {
      expect(addNDaysToDate('2025-01-01', 1)).toBe('2025-01-02');
      expect(addNDaysToDate('2024-12-31', 1)).toBe('2025-01-01');
      expect(addNDaysToDate('2024-02-28', 1)).toBe('2024-02-29');
      expect(addNDaysToDate('2024-02-29', 1)).toBe('2024-03-01');
    });
  });

  describe('isWithinTimeRange', () => {
    it('should return true if timestamp is within time range', () => {
      expect(
        isWithinTimeRange('2025-01-01T12:00:00.000', {
          startDateTime: '2025-01-01T00:00:00.000',
          endDateTime: '2025-01-02T00:00:00.000',
        }),
      ).toBe(true);
      expect(
        isWithinTimeRange('2025-01-03T00:00:00.000', {
          startDateTime: '2025-01-01T00:00:00.000',
          endDateTime: '2025-01-02T00:00:00.000',
        }),
      ).toBe(false);
      expect(
        isWithinTimeRange('2025-01-01T00:00:00.999', {
          startDateTime: '2025-01-01T00:00:00.000',
          endDateTime: '2025-01-01T00:00:01.000',
        }),
      ).toBe(true);
    });
  });
  describe('getRandomSleepTime test suite', () => {
    it('should return a number greater than or equal to staticBaseTimeMilliseconds', () => {
      const staticBaseTimeMilliseconds = 1000;
      const sleepTime = getRandomSleepTime(staticBaseTimeMilliseconds);
      expect(sleepTime).toBeGreaterThanOrEqual(staticBaseTimeMilliseconds);
      expect(sleepTime).toBeLessThan(2000);
    });
  });

  describe('formatCentsToDollars', () => {
    it('should format a very large amount correctly', () => {
      expect(formatCentsToDollars('987654321000000000')).toBe('$9876543210000000.00');
    });
    it('should format zero correctly', () => {
      expect(formatCentsToDollars('0')).toBe('$0.00');
    });
    it('should format a small amount correctly', () => {
      expect(formatCentsToDollars('5')).toBe('$0.05');
    });
    it('should format an exact dollar amount correctly', () => {
      expect(formatCentsToDollars('100')).toBe('$1.00');
    });
    it('should format an amount with single-digit cents correctly', () => {
      expect(formatCentsToDollars('109')).toBe('$1.09');
    });
    it('should format an amount with double-digit cents correctly', () => {
      expect(formatCentsToDollars('1234')).toBe('$12.34');
    });
    it('should handle string input that looks like a number', () => {
      expect(formatCentsToDollars('00100')).toBe('$1.00');
    });
    it('should handle negative values', () => {
      expect(formatCentsToDollars('-987654321000000000')).toBe('$-9876543210000000.00');
      expect(formatCentsToDollars('-100')).toBe('$-1.00');
      expect(formatCentsToDollars('-5')).toBe('$-0.05');
    });
    it('should throw an error for non-numeric strings', () => {
      expect(() => formatCentsToDollars('abc')).toThrow();
      expect(() => formatCentsToDollars('')).toThrow();
    });
    it('should throw an error for decimal values', () => {
      expect(() => formatCentsToDollars('10.5')).toThrow();
    });
    it('should throw an error for inputs with spaces', () => {
      expect(() => formatCentsToDollars(' 100 ')).toThrow();
    });
  });
});
