import { createLambdaDataResolver, loadTemplateFile } from '@npco/component-bff-serverless';

export const resolvers = [
  {
    lambda: 'getTransactionTotalsV2',
    name: 'getTransactionTotalsV2',
    appsync: {
      fieldName: 'getTransactionTotalsV2',
      typeName: 'Query',
    },
  },
  {
    lambda: 'getTransactionTotalsMultiEntityV2',
    name: 'getTransactionTotalsMultiEntityV2',
    appsync: {
      fieldName: 'getTransactionTotalsMultiEntityV2',
      typeName: 'Query',
    },
  },
  {
    lambda: 'getTransactionTotalsMultiEntityExport',
    name: 'getTransactionTotalsMultiEntityExport',
    appsync: {
      fieldName: 'getTransactionTotalsMultiEntityExport',
      typeName: 'Query',
    },
  },
].reduce(
  (res, resolver) => ({
    ...res,
    ...createLambdaDataResolver(resolver.name, resolver.lambda, resolver.appsync, true),
  }),
  {},
);

export const additionalResolvers = [
  {
    lambda: '${self:custom.getEntityLambda}',
    name: 'getEntityRegionalOptions',
    appsync: {
      fieldName: 'regionalOptions',
      typeName: 'TransactionTotalsV2',
      template: {
        request: loadTemplateFile('resources/mp/template/getEntityRegionalOptionsRequest.vtl'),
        response: loadTemplateFile('resources/mp/template/getEntityRegionalOptionsResponse.vtl'),
      },
    },
  },
].reduce(
  (res, resolver) => ({
    ...res,
    ...createLambdaDataResolver(resolver.name, resolver.lambda, resolver.appsync, false),
  }),
  {},
);;
