#if ($util.isNullOrEmpty($context.source.entityUuid))
  #return
#end
{
    "version" : "2018-05-29",
    "operation": "Invoke",
    "payload": {
        "args": { "entityUuid": $util.toJson($context.source.entityUuid) },
        "identity": $util.toJson($context.identity),
        "request": $util.toJson($context.request),
        "authType": $util.toJson($util.authType()),
        "source": $util.toJson($context.source),
        "info": $util.toJson($context.info)
    }
}
