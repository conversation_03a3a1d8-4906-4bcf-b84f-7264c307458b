import { ApiAppServerlessStack } from '@npco/component-bff-serverless/dist/serverless';
import { MpApiAppEnvConfig, esbuild, pluginsApp } from './resources/mp/common';
import { lambdas } from './resources/mp/transactionTotals/lambdas';
import { resolvers, additionalResolvers } from './resources/mp/transactionTotals/resolvers';

export const envConfig = new MpApiAppEnvConfig('resources/mp/config/', true);

const sls = new ApiAppServerlessStack('txn-totals', envConfig, {
  plugins: pluginsApp,
  environment: {
    COMPONENT_TABLE: envConfig.componentTableName,
    TRANSACTION_TOTALS_TABLE: '${self:custom.transactionTotalsTableName}',
    OPENID_ISSUER_URL: envConfig.auth0IssuerUrl,
    AUTH0_CLIENT_ID: envConfig.auth0ClientId,
    AUTH0_CLIENT_SECRET: envConfig.auth0ClientSecret,
    AUTH0_TENANT: envConfig.auth0Tenant,
    IS_RBAC_ENFORCED: 'true',
    IS_RBAC_ENFORCE_ROLE: 'true',
    SESSION_CACHE_TABLE: envConfig.sessionCacheTableName,
  },
  functions: {
    ...lambdas,
  },
  package: {
    individually: true,
    patterns: ['!node_modules/**', 'src/fonts/*'],
  },

  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    esbuild,
    typeGsi: '${env:TYPE_GSI}',
    siteGsi: '${env:SITE_GSI}',
    secondaryGsiV1: '${env:SECONDARY_GSI_V1}',
    totalUpdateHandlerSqsQueueARN:
      'arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.service}-txn-totalsUpdateHandler.fifo',
    transactionTotalsUpdateSqsQueueUrl:
      'https://sqs.${self:provider.region}.amazonaws.com/${self:custom.accountId}/${self:custom.service}-txn-totalsUpdateHandler.fifo',
    recalculateFullSiteTotalsSqsQueueARN:
      'arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.service}-txn-recalculateFullSiteTotals.fifo',
    transactionTotalsTableName: '${self:custom.dynamodbStackName}-Totals',
    recalculateEntityDayTotalsQueueArn:
      'arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.service}-txn-recalculateEntityDayTotals.fifo',
    recalculateEntityDayTotalsQueueUrl:
      'https://sqs.${self:provider.region}.amazonaws.com/${self:custom.accountId}/${self:custom.service}-txn-recalculateEntityDayTotals.fifo',
    recalculateFullSiteTotalsQueueArn:
      'arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.service}-txn-recalculateFullSiteTotals.fifo',
    recalculateFullSiteTotalsQueueUrl:
      'https://sqs.${self:provider.region}.amazonaws.com/${self:custom.accountId}/${self:custom.service}-txn-recalculateFullSiteTotals.fifo',
    globalEventBusArn:
      'arn:aws:events:${self:provider.region}:${self:custom.accountId}:event-bus/${env:GLOBAL_EVENT_BUS_NAME}',
    transactionTotalsExportBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-txn-de-bucket}',
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
    getEntityLambda: '${self:custom.service}-entity-getEntityHandler',
  },
  resources: {
    ...resolvers,
    ...additionalResolvers,
  },
});

module.exports = sls.build();
