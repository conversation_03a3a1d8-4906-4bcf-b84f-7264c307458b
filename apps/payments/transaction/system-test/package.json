{"name": "bff-transaction-system-test", "version": "1.0.0", "main": "index.js", "author": "<PERSON>", "license": "MIT", "scripts": {"build": "rm -fr dist && yarn tsc --build tsconfig.json", "system:test": "jest --runInBand --forceExit", "lint": "eslint src", "run-audit": "echo no audit"}, "dependencies": {"bff-transaction-api": "workspace:*", "crms-engine-system-test": "workspace:*", "graphql-tag": "^2.11.0", "uuid": "^8.3.2"}, "devDependencies": {"@aws-sdk/client-cloudformation": "3.435.0", "@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-dynamodb-streams": "3.435.0", "@aws-sdk/client-eventbridge": "3.435.0", "@aws-sdk/client-s3": "3.435.0", "@aws-sdk/client-secrets-manager": "3.435.0", "@aws-sdk/client-sqs": "3.435.0", "@aws-sdk/client-ssm": "3.435.0", "@aws-sdk/lib-dynamodb": "3.435.0", "@npco/bff-systemtest-utils": "workspace:*", "@npco/component-bff-core": "workspace:*", "@npco/component-dbs-mp-common": "workspace:*", "@npco/component-domain-events": "12.1.53", "@npco/component-dto-cnp": "workspace:*", "@npco/component-dto-core": "workspace:*", "@npco/component-dto-cpoc": "workspace:*", "@npco/component-dto-customer": "workspace:*", "@npco/component-dto-deposit": "workspace:*", "@npco/component-dto-device": "workspace:*", "@npco/component-dto-entity": "workspace:*", "@npco/component-dto-site": "workspace:*", "@npco/component-dto-stlmts": "workspace:*", "@npco/component-dto-transaction": "workspace:*", "@npco/eslint-config-backend": "^1.0.13", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@types/graphql": "^14.5.0", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.6", "@types/mocha": "^8.2.0", "@types/node": "^20.6.2", "@types/uuid": "^8.3.0", "@types/ws": "^8.5.3", "@typescript-eslint/eslint-plugin": "^6.18.0", "aws-appsync": "^4.1.9", "axios": "^1.7.4", "crms-engine-system-test": "workspace:*", "eslint": "^8.56.0", "exceljs": "^4.3.0", "graphql": "^16.7.1", "isomorphic-fetch": "^3.0.0", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "jsonwebtoken": "^9.0.2", "ts-jest": "^29.1.1", "typescript": "^5.2.2", "ws": "^8.17.1"}, "prettier": "@npco/eslint-config-backend/prettier"}