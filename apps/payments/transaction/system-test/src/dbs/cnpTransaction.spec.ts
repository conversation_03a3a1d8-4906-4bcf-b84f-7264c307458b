import { DynamodbClient, retry } from '@npco/bff-systemtest-utils/dist';
import { ComponentClients } from '@npco/bff-systemtest-utils/dist/helper';
import { CnpTransactionRequestedDto } from '@npco/component-dto-cnp/dist';
import type {
  CnpTransactionApprovedDto,
  CnpTransactionDeclinedDto,
  ForcedTransactionRequestedDto,
} from '@npco/component-dto-cnp/dist';
import {
  TransactionType,
  Source,
  ISO4217,
  TransactionCancelReason,
  TransactionResponseCode,
  Channel,
  IsoProcessingCode,
  DbRecordType,
} from '@npco/component-dto-core/dist/types';
import {
  CardMedia,
  type CancelTransactionCommandDto,
  CardScheme,
  TransactionStatus,
  ThreeDSAuthStatus,
  ThreeDSOutcome,
} from '@npco/component-dto-transaction/dist';
import { TransactionTotalsType } from '@npco/component-dto-transaction/dist/types';

import gql from 'graphql-tag';
import { v4 as uuidv4 } from 'uuid';

import { ApiTestHelper } from '../apiTestHelper';
import { region } from '../globalVariables';
import { testIf } from '../testIf';

import { createTransactionRequestDto } from './transactionUtils';

describe('device backend service ', () => {
  const apiTestHelper = new ApiTestHelper(ComponentClients.DeviceBackend);
  const deviceTable = apiTestHelper.getComponentTableName();
  const dynamodb = new DynamodbClient({ region });
  const transactions: any[] = [];

  const createCnpTransactionRequestDto = (entityUuid: string, siteUuid: string, updates = {}) => {
    const input: CnpTransactionRequestedDto = {
      entityUuid,
      siteUuid,
      transactionUuid: uuidv4(),
      timestampLocal: new Date().toISOString(),
      timezoneLocal: 'Melbourne/Australia',
      ecommerceUuid: uuidv4(),
      sessionUuid: uuidv4(),
      type: TransactionType.PURCHASE,
      timestampUtc: new Date().toISOString(),
      amount: { value: '30', currency: ISO4217.AUD },
      taxAmounts: [
        {
          name: uuidv4(),
          amount: { value: '10', currency: ISO4217.AUD },
        },
      ],
      surchargeAmount: { value: '10', currency: ISO4217.AUD },
      cardMedia: CardMedia.CNP,
      location: {
        location: uuidv4(),
        accuracy: 0,
        timestampLocal: new Date().toISOString(),
      },
      source: Source.STANDALONE,
      channel: Channel.PAY_MYZELLER,
      ...updates,
    };
    return input;
  };

  const createCnpPartialRefundTransactionRequestDto = (purchaseTxn: any) => {
    return new CnpTransactionRequestedDto({
      transactionUuid: uuidv4(),
      originalTransactionUuid: purchaseTxn.transactionUuid,
      entityUuid: purchaseTxn.entityUuid,
      siteUuid: purchaseTxn.siteUuid,
      ecommerceUuid: purchaseTxn.ecommerceUuid,
      externalReference: purchaseTxn.externalReference,
      amount: {
        value: '10',
        currency: ISO4217.AUD,
      },
      type: TransactionType.REFUND,
      cardMedia: purchaseTxn.cardMedia,
      source: Source.DASHBOARD,
      timestampUtc: new Date().toISOString(),
      timestampLocal: new Date().toISOString(),
      timezoneLocal: 'Melbourne/Australia',
      sessionUuid: uuidv4(),
    });
  };

  const createForcedTransactionRequestDto = (entityUuid: string, siteUuid: string) => {
    const input: ForcedTransactionRequestedDto = {
      entityUuid,
      siteUuid,
      transactionUuid: uuidv4(),
      timestampLocal: new Date().toISOString(),
      timezoneLocal: 'Melbourne/Australia',
      ecommerceUuid: uuidv4(),
      externalReference: uuidv4(),
      type: TransactionType.PURCHASE,
      timestampUtc: new Date().toISOString(),
      amount: { value: '30', currency: ISO4217.AUD },
      taxAmounts: [
        {
          name: uuidv4(),
          amount: { value: '10', currency: ISO4217.AUD },
        },
      ],
      surchargeAmount: { value: '10', currency: ISO4217.AUD },
      cardMedia: CardMedia.CNP,
      location: {
        location: uuidv4(),
        accuracy: 0,
        timestampLocal: new Date().toISOString(),
      },
      source: Source.XERO_INVOICE,
    };
    return input;
  };

  const create3DSEvent = (
    transactionUuid: string,
    status: ThreeDSAuthStatus = ThreeDSAuthStatus.Y,
    timestampUtc: string = new Date().toISOString(),
  ) => {
    return {
      transactionUuid,
      timestampUtc,
      externalData: {
        transStatus: status,
      },
    };
  };

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
    await apiTestHelper.dbClient.update({
      TableName: deviceTable,
      Key: {
        id: apiTestHelper.getCustomerUuid(),
        type: DbRecordType.CUSTOMER,
      },
      UpdateExpression: 'set #sites = :sites',
      ExpressionAttributeNames: { '#sites': 'sites' },
      ExpressionAttributeValues: {
        ':sites': [apiTestHelper.getSiteUuid()],
      },
    });
    transactions.push({
      id: uuidv4(),
      type: `transaction.${new Date().getTime()}`,
      source: 'ZELLER_INVOICE',
      entityUuid: apiTestHelper.getEntityUuid(),
      siteUuid: apiTestHelper.getSiteUuid(),
      status: 'APPROVED',
      transactionType: 'PURCHASE',
      entityTransactionTotal200: `${apiTestHelper.getEntityUuid()}_1`,
      amount: {
        value: 100,
        currency: 'AUD',
      },
    });
    transactions.push({
      id: uuidv4(),
      type: `transaction.${new Date().getTime() + 10}`,
      source: 'XERO_INVOICE',
      transactionType: 'PURCHASE',
      status: 'APPROVED',
      amount: {
        value: 200,
        currency: 'AUD',
      },
    });
    transactions.push({
      id: uuidv4(),
      type: `transaction.${new Date().getTime() + 20}`,
      source: 'LINKLY',
      transactionType: 'PURCHASE',
      status: 'APPROVED',
      entityTransactionTotal200: `${apiTestHelper.getEntityUuid()}_1`,
      amount: {
        value: 300,
        currency: 'AUD',
      },
    });
    transactions.push({
      id: uuidv4(),
      type: `transaction.${new Date().getTime() + 30}`,
      source: 'STANDALONE',
      transactionType: 'REFUND',
      entityTransactionTotal200: `${apiTestHelper.getEntityUuid()}_1`,
      status: 'APPROVED',
      amount: {
        value: 400,
        currency: 'AUD',
      },
    });
    transactions.push({
      id: uuidv4(),
      type: `transaction.${new Date().getTime() + 40}`,
      status: 'DECLINED',
      entityTransactionTotal200: `${apiTestHelper.getEntityUuid()}_1`,
      transactionType: 'PURCHASE',
      amount: {
        value: 500,
        currency: 'AUD',
      },
    });
    for (let i = 0; i < transactions.length; i += 1) {
      transactions[i].entityUuid = apiTestHelper.getEntityUuid();
      transactions[i].siteUuid = apiTestHelper.getSiteUuid();
      transactions[i].entityTransactionTotal200 = `${apiTestHelper.getEntityUuid()}_1`;
      transactions[i].timestamp = `${new Date(parseInt(transactions[i].type.split('.')[1], 10)).toISOString()}`;
      transactions[i].scheme = CardScheme.AMEX;
      transactions[i].saleAmount = 10;
      transactions[i].maskedPan = uuidv4();
      transactions[i].panTOken = uuidv4();
      transactions[i].taxAmounts = [];
      transactions[i].tipAmount = {
        currency: 'AUD',
        value: 0,
      };
      transactions[i].surchargeAmount = {
        currency: 'AUD',
        value: 0,
      };
      await dynamodb.put({
        TableName: deviceTable,
        Item: transactions[i],
      });
    }
  });

  const queryTransactions = async (filter?: any): Promise<any> => {
    const output: any = await (
      await apiTestHelper.getOpenIdClient()
    ).query({
      query: gql`
        query getTransactions($limit: Int!, $filter: TransactionFilterInput) {
          getTransactions(limit: $limit, filter: $filter) {
            transactions {
              id
              source
            }
          }
        }
      `,
      variables: {
        limit: 10,
        filter,
      },
    });
    console.log('output:', output);
    return output.data.getTransactions.transactions;
  };

  const queryTransaction = async (uuid: string): Promise<any> => {
    // console.log('query transaction:', uuid);
    const output: any = await (
      await apiTestHelper.getOpenIdClient()
    ).query({
      query: gql`
        query getTransaction($uuid: ID!) {
          getTransaction(transactionUuid: $uuid) {
            id
            status
            entityUuid
            deviceName
            siteUuid
            siteName
            timestamp
            amount
            saleAmount
            tipAmount
            feeAmount
            feeCharged
            taxAmounts {
              name
              amount
            }
            feeAmount
            feeCharged
            surchargeAmount
            refundedAmount
            scheme
            status
            channel
            deposited
            depositUuid
            depositDate
            maskedPan
            reference
            review
            refunded
            reversed
            cardholderUuid
            responseCode
            responseDescription
            type
            notes
            issuer
            refundedTransactionUuid
            refundedTransaction {
              id
              entityUuid
              deviceUuid
            }
            refundTransactions {
              id
              refundedTransactionUuid
            }
            customerUuid
            customerName
            threeDSOutcome
          }
        }
      `,
      variables: {
        uuid,
      },
    });
    return output.data.getTransaction;
  };
  const queryTransactionReport = async (filter: any): Promise<any> => {
    const output: any = await (
      await apiTestHelper.getOpenIdClient()
    ).query({
      query: gql`
        query getTransactionTotalsBigInt($filter: TransactionFilterInput!, $totalsType: TransactionTotalsType!) {
          getTransactionTotalsBigInt(filter: $filter, totalsType: $totalsType) {
            countPurchases
            countRefunds
            totalAmount
            totalAmountMinusFees
            purchaseAmount
            refundAmount
            saleAmount
            surchargeAmount
            tipAmount
            taxAmounts {
              name
              amount
            }
            declinedAmount
            noResponseAmount
            cancelledAmount
            totalsType
            period
            periodLabel
          }
        }
      `,
      variables: {
        filter,
        totalsType: TransactionTotalsType.DAILY,
      },
    });
    console.log('output:', output);
    return output.data.getTransactionTotalsBigInt;
  };

  it('should filter invoice transaction for dbs', async () => {
    let txns = await queryTransactions({ status: { eq: 'APPROVED' } });
    expect(txns.length).toBe(2);
    expect(txns[0].id).toBe(transactions[3].id);
    expect(txns[1].id).toBe(transactions[2].id);
    txns = await queryTransactions({ status: { eq: 'DECLINED' } });
    expect(txns.length).toBe(1);
    expect(txns[0].id).toBe(transactions[4].id);
    txns = await queryTransactions({ amount: { gt: 90 } });
    expect(txns.length).toBe(3);
    expect(txns[0].id).toBe(transactions[4].id);
    expect(txns[1].id).toBe(transactions[3].id);
    expect(txns[2].id).toBe(transactions[2].id);

    txns = await queryTransactions({ amount: { gt: 90 }, type: { eq: 'REFUND' } });
    expect(txns.length).toBe(1);
    expect(txns[0].id).toBe(transactions[3].id);

    txns = await queryTransactions();
    expect(txns.length).toBe(3);
    expect(txns[0].id).toBe(transactions[4].id);
    expect(txns[1].id).toBe(transactions[3].id);
    expect(txns[2].id).toBe(transactions[2].id);
  });

  it('should filter invoice transaction for dbs reports', async () => {
    const report = await queryTransactionReport({
      timestamp: {
        between: [
          new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString(),
          new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString(),
        ],
      },
    });
    console.log('get totals:', report);
    delete report[0].period;
    expect(report[0]).toEqual({
      cancelledAmount: '0',
      countPurchases: '1',
      countRefunds: '1',
      declinedAmount: '500',
      noResponseAmount: '0',
      purchaseAmount: '300',
      refundAmount: '400',
      saleAmount: '300',
      surchargeAmount: '0',
      tipAmount: '0',
      totalAmount: '-100',
      totalAmountMinusFees: '-100',
      taxAmounts: [],
      totalsType: TransactionTotalsType.DAILY,
      periodLabel: null,
    });
  });

  testIf(apiTestHelper.getStage() === 'dev', 'should not receive invoice subscription', async () => {
    const client = await apiTestHelper.getOpenIdClient();
    const nextMock = jest.fn();
    console.log('subscribe on ', apiTestHelper.getEntityUuid());
    const subscribe = client
      .subscribe({
        query: gql`
          subscription onTransactionUpdate($entityUuid: ID!) {
            onTransactionUpdate(entityUuid: $entityUuid) {
              id
              timestamp
            }
          }
        `,
        variables: {
          entityUuid: transactions[0].entityUuid,
        },
      })
      .subscribe({
        next: ({ data }: any) => {
          console.log('receive updated event:', data);
          nextMock();
          expect(data.onTransactionUpdate.id).toBe(transactions[2].id);
        },
      });
    await apiTestHelper.sleep();
    await dynamodb.put({
      TableName: deviceTable,
      Item: { ...transactions[0], saleAmount: 20 },
    });
    await dynamodb.put({
      TableName: deviceTable,
      Item: { ...transactions[1], saleAmount: 20 },
    });
    await dynamodb.put({
      TableName: deviceTable,
      Item: { ...transactions[2], saleAmount: 20 },
    });
    console.log(deviceTable);
    await apiTestHelper.sleep(20000);
    subscribe.unsubscribe();
    expect(nextMock).toBeCalled();
  });

  describe('cnp transaction projection test suite', () => {
    let transaction: any;

    const sendTxn = async () => {
      transaction = createCnpTransactionRequestDto(apiTestHelper.getEntityUuid(), apiTestHelper.getSiteUuid());

      await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.Requested', transaction);
    };

    it('should invoke projection transaction lambda on transaction requested event', async () => {
      await sendTxn();

      await retry(async () => {
        const txn = await queryTransaction(transaction.transactionUuid);

        expect(txn.id).toBe(transaction.transactionUuid);
        expect(txn.status).toBe(TransactionStatus.PROCESSING);
        expect(txn.entityUuid).toBe(transaction.entityUuid);
        expect(txn.scheme).toBe(CardScheme.OTHER);
        expect(txn.channel).toBe(transaction.channel);
      }, 120);
    });

    it('should save entityTransactionTotal200/entityTransactionDate field when materialising requested event', async () => {
      const itemOuptut = await apiTestHelper.getDbItem(transaction.transactionUuid);

      const timestamp = parseInt(itemOuptut?.type.split('.')[1], 10);
      expect(itemOuptut?.entityTransactionTotal200).toBe(`${transaction.entityUuid}_${timestamp % 200}`);
      const date = new Date(timestamp);
      expect(itemOuptut?.entityTransactionDate).toBe(
        `${transaction.entityUuid}_${date.getFullYear()}-${date.getUTCMonth() + 1}-${date.getUTCDate()}`,
      );
    });

    it('should be able to project initiated transaction', async () => {
      transaction = createCnpTransactionRequestDto(apiTestHelper.getEntityUuid(), apiTestHelper.getSiteUuid());

      const cardholderUuid = uuidv4();
      const initiated = {
        ...transaction,
        scheme: CardScheme.VISA,
        bin: uuidv4(),
        cardholderUuid,
        isoProcessingCode: IsoProcessingCode.CODE_003000,
      };
      await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.Initiated', initiated);

      await retry(async () => {
        const txn = await queryTransaction(transaction.transactionUuid);
        expect(txn.status).toBe(TransactionStatus.PROCESSING);
        expect(txn.scheme).toBe(CardScheme.VISA);
        expect(txn.cardholderUuid).toBe(initiated.cardholderUuid);
      }, 120);

      const itemOuptut = await apiTestHelper.getDbItem(transaction.transactionUuid);

      expect(itemOuptut?.isoProcessingCode).toBe(initiated.isoProcessingCode);
    });

    it('should save entityTransactionTotal200/entityTransactionDate field when materialising initiated event', async () => {
      const itemOuptut = await apiTestHelper.getDbItem(transaction.transactionUuid);

      const timestamp = parseInt(itemOuptut?.type.split('.')[1], 10);
      expect(itemOuptut?.entityTransactionTotal200).toBe(`${transaction.entityUuid}_${timestamp % 200}`);
      const date = new Date(timestamp);
      expect(itemOuptut?.entityTransactionDate).toBe(
        `${transaction.entityUuid}_${date.getFullYear()}-${date.getUTCMonth() + 1}-${date.getUTCDate()}`,
      );
    });

    it('should be able to project approved transaction', async () => {
      await sendTxn();

      const approvedEvent: CnpTransactionApprovedDto = {
        transactionUuid: transaction.transactionUuid,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        cardholderUuid: uuidv4(),
        type: TransactionType.PURCHASE,
      };
      await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.Approved', approvedEvent);

      await retry(async () => {
        const txn = await queryTransaction(approvedEvent.transactionUuid);
        expect(txn.status).toBe(TransactionStatus.APPROVED);
      }, 120);
    });

    it('should save entityTransactionTotal200/entityTransactionDate field when materialising approved event', async () => {
      const itemOuptut = await apiTestHelper.getDbItem(transaction.transactionUuid);

      const timestamp = parseInt(itemOuptut?.type.split('.')[1], 10);
      expect(itemOuptut?.entityTransactionTotal200).toBe(`${transaction.entityUuid}_${timestamp % 200}`);
      const date = new Date(timestamp);
      expect(itemOuptut?.entityTransactionDate).toBe(
        `${transaction.entityUuid}_${date.getFullYear()}-${date.getUTCMonth() + 1}-${date.getUTCDate()}`,
      );
    });

    it('should be able to project declined transaction', async () => {
      await sendTxn();

      const declinedEvent: CnpTransactionDeclinedDto = {
        transactionUuid: transaction.transactionUuid,
        responseCode: '00',
        responseDescription: uuidv4(),
        rrn: uuidv4(),
        cardholderUuid: transaction.cardholderUuid,
        type: TransactionType.PURCHASE,
      };
      await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.Declined', declinedEvent);

      await retry(async () => {
        const txn = await queryTransaction(transaction.transactionUuid);
        console.log(txn.id, txn.status);
        expect(txn.status).toBe(TransactionStatus.DECLINED);
      }, 120);
    });

    it('should save entityTransactionTotal200/entityTransactionDate field when materialising declined event', async () => {
      const itemOuptut = await apiTestHelper.getDbItem(transaction.transactionUuid);

      const timestamp = parseInt(itemOuptut?.type.split('.')[1], 10);
      expect(itemOuptut?.entityTransactionTotal200).toBe(`${transaction.entityUuid}_${timestamp % 200}`);
      const date = new Date(timestamp);
      expect(itemOuptut?.entityTransactionDate).toBe(
        `${transaction.entityUuid}_${date.getFullYear()}-${date.getUTCMonth() + 1}-${date.getUTCDate()}`,
      );
    });

    it('should be able to project cancelled transaction', async () => {
      await sendTxn();

      const declinedEvent: CancelTransactionCommandDto = {
        transactionUuid: transaction.transactionUuid,
        entityUuid: apiTestHelper.getEntityUuid(),
        reason: TransactionCancelReason.USER_CANCELLED,
        transactionDetails: transaction,
        responseCode: TransactionResponseCode.USER_CANCELLED,
      };
      await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.Cancelled', declinedEvent);

      await retry(async () => {
        const txn = await queryTransaction(transaction.transactionUuid);
        console.log(txn.id, txn.status);
        expect(txn.status).toBe(TransactionStatus.DECLINED);
        expect(txn.type).toBe(TransactionType.PURCHASE);
        expect(txn.responseDescription).toBe(declinedEvent.reason);
      }, 120);
    });

    it('should project forced transaction requested event', async () => {
      const forcedTxn = createForcedTransactionRequestDto(apiTestHelper.getEntityUuid(), apiTestHelper.getSiteUuid());
      console.log('save transaction projection:', forcedTxn.transactionUuid, ',transaction:', forcedTxn);
      await apiTestHelper.sendTxnProjectionEvent('dbs.ForcedTransaction.Requested', forcedTxn);
      await apiTestHelper.sleep();

      console.log('query transaction ', forcedTxn.transactionUuid);
      await retry(async () => {
        const txn = await queryTransaction(forcedTxn.transactionUuid);
        expect(txn.id).toBe(forcedTxn.transactionUuid);
        expect(txn.status).toBe(TransactionStatus.PROCESSING);
        expect(txn.entityUuid).toBe(forcedTxn.entityUuid);
        expect(txn.scheme).toBe(CardScheme.OTHER);
        expect(txn.customerUuid).toBeNull();
        expect(txn.customerName).toBe('Zeller');
      });
    });

    it('should project forced transaction cancelled event', async () => {
      const forcedTxn = createForcedTransactionRequestDto(apiTestHelper.getEntityUuid(), apiTestHelper.getSiteUuid());
      console.log('save transaction projection:', forcedTxn.transactionUuid, ',transaction:', forcedTxn);
      await apiTestHelper.sendTxnProjectionEvent('dbs.ForcedTransaction.Requested', forcedTxn);
      await apiTestHelper.sleep();

      const cancelEvent: CancelTransactionCommandDto = {
        transactionUuid: forcedTxn.transactionUuid,
        entityUuid: apiTestHelper.getEntityUuid(),
        reason: TransactionCancelReason.COMMUNICATIONS_FAILURE,
        responseCode: TransactionResponseCode.COMMUNICATIONS_FAILURE,
      };
      await apiTestHelper.sendTxnProjectionEvent('dbs.ForcedTransaction.Cancelled', cancelEvent);
      await apiTestHelper.sleep();

      console.log('query transaction ', forcedTxn.transactionUuid);
      await retry(async () => {
        const txn = await queryTransaction(forcedTxn.transactionUuid);
        expect(txn.id).toBe(forcedTxn.transactionUuid);
        expect(txn.status).toBe(TransactionStatus.DECLINED);
        expect(txn.entityUuid).toBe(forcedTxn.entityUuid);
        expect(txn.responseCode).toBe(`${cancelEvent.responseCode}`);
        expect(txn.responseDescription).toBe(cancelEvent.reason);
        expect(txn.customerName).toBe('Zeller');
      });
    });
  });

  describe('cnp partial refund transaction test suite', () => {
    let purchaseTxnRequestedDto: any;
    let partialRefundTxnRequestedDto1: CnpTransactionRequestedDto;
    let partialRefundTxnRequestedDto2: CnpTransactionRequestedDto;
    let partialRefundTxnRequestedDto3: CnpTransactionRequestedDto;

    const createApprovedCnpTxn = async (dto: CnpTransactionRequestedDto) => {
      await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.Requested', dto);

      const approvedDto: CnpTransactionApprovedDto = {
        transactionUuid: dto.transactionUuid,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        cardholderUuid: uuidv4(),
        type: dto.type,
      };

      await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.Approved', approvedDto);

      await retry(
        async () => {
          const res = await queryTransaction(dto.transactionUuid);
          console.log('query approved purchase transaction', res);
          expect(res.id).toBe(dto.transactionUuid);
          expect(res.status).toBe(TransactionStatus.APPROVED);
        },
        30,
        3000,
      );
    };

    beforeAll(async () => {
      purchaseTxnRequestedDto = createTransactionRequestDto(
        apiTestHelper.getEntityUuid(),
        apiTestHelper.getSiteUuid(),
        apiTestHelper.getDeviceUuid(),
        apiTestHelper.getCustomerUuid(),
      );

      await createApprovedCnpTxn(purchaseTxnRequestedDto);
    });

    it('should be able to project three partial refunds for the purchase transaction', async () => {
      partialRefundTxnRequestedDto1 = createCnpPartialRefundTransactionRequestDto(purchaseTxnRequestedDto);
      partialRefundTxnRequestedDto2 = createCnpPartialRefundTransactionRequestDto(purchaseTxnRequestedDto);
      partialRefundTxnRequestedDto3 = createCnpPartialRefundTransactionRequestDto(purchaseTxnRequestedDto);

      await Promise.all([
        createApprovedCnpTxn(partialRefundTxnRequestedDto1),
        createApprovedCnpTxn(partialRefundTxnRequestedDto2),
        createApprovedCnpTxn(partialRefundTxnRequestedDto3),
      ]);
    });

    it('should be able to query partial refund transactions for the purchase transaction via appsync', async () => {
      await retry(
        async () => {
          const res = await queryTransaction(purchaseTxnRequestedDto.transactionUuid);
          console.log('query refunded purchase transaction', res);
          expect(res.id).toBe(purchaseTxnRequestedDto.transactionUuid);
          expect(res.refunded).toBe(true);
          expect(res.refundedAmount).toBe(
            Number(partialRefundTxnRequestedDto1.amount.value) +
              Number(partialRefundTxnRequestedDto2.amount.value) +
              Number(partialRefundTxnRequestedDto3.amount.value),
          );
          expect(res.refundTransactions).toEqual(
            expect.arrayContaining([
              expect.objectContaining({
                id: partialRefundTxnRequestedDto1.transactionUuid,
              }),
              expect.objectContaining({
                id: partialRefundTxnRequestedDto2.transactionUuid,
              }),
              expect.objectContaining({
                id: partialRefundTxnRequestedDto3.transactionUuid,
              }),
            ]),
          );
        },
        30,
        3000,
      );
    });
  });

  describe('3ds transaction test suite', () => {
    const testData: [
      s: string,
      data: {
        transactionUuid: string;
        aggregateName: string;
        status: ThreeDSAuthStatus;
        threeDSOutcome: ThreeDSOutcome;
        timestampUtc: string;
      },
    ][] = [
      [
        'ThreeDSAuthExecuted Y',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthExecuted',
          status: ThreeDSAuthStatus.Y,
          threeDSOutcome: ThreeDSOutcome.PASSED,
        },
      ],
      [
        'ThreeDSAuthExecuted A',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthExecuted',
          status: ThreeDSAuthStatus.A,
          threeDSOutcome: ThreeDSOutcome.PASSED,
        },
      ],
      [
        'ThreeDSAuthExecuted D',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthExecuted',
          status: ThreeDSAuthStatus.D,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
      [
        'ThreeDSAuthExecuted I',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthExecuted',
          status: ThreeDSAuthStatus.I,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
      [
        'ThreeDSAuthExecuted N',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthExecuted',
          status: ThreeDSAuthStatus.N,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
      [
        'ThreeDSAuthExecuted U',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthExecuted',
          status: ThreeDSAuthStatus.U,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
      [
        'ThreeDSAuthResultFinalized Y',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthResultFinalized',
          status: ThreeDSAuthStatus.Y,
          threeDSOutcome: ThreeDSOutcome.PASSED,
        },
      ],
      [
        'ThreeDSAuthResultFinalized A',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthResultFinalized',
          status: ThreeDSAuthStatus.A,
          threeDSOutcome: ThreeDSOutcome.PASSED,
        },
      ],
      [
        'ThreeDSAuthResultFinalized D',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthResultFinalized',
          status: ThreeDSAuthStatus.D,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
      [
        'ThreeDSAuthResultFinalized I',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthResultFinalized',
          status: ThreeDSAuthStatus.I,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
      [
        'ThreeDSAuthResultFinalized N',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthResultFinalized',
          status: ThreeDSAuthStatus.N,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
      [
        'ThreeDSAuthResultFinalized R',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthResultFinalized',
          status: ThreeDSAuthStatus.R,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
      [
        'ThreeDSAuthResultFinalized U',
        {
          transactionUuid: uuidv4(),
          timestampUtc: new Date().toISOString(),
          aggregateName: 'ThreeDSAuthResultFinalized',
          status: ThreeDSAuthStatus.U,
          threeDSOutcome: ThreeDSOutcome.FAILED,
        },
      ],
    ];
    it.each(testData)('should be able to project %s event', async (_s, data) => {
      const transaction = create3DSEvent(data.transactionUuid, data.status, data.timestampUtc);

      await apiTestHelper.sendTxnProjectionEvent(`dbs.CnpTransaction.${data.aggregateName}`, transaction);
      await apiTestHelper.sleep(2000);
      await retry(
        async () => {
          const record = await apiTestHelper.dbClient.getDbItem(
            apiTestHelper.getComponentTableName(),
            transaction.transactionUuid,
          );

          expect(record).toEqual({
            id: transaction.transactionUuid,
            type: `transaction.${new Date(transaction.timestampUtc).getTime()}`,
            threeDSOutcome: data.threeDSOutcome,
            timestamp: transaction.timestampUtc,
            timestampUtc: transaction.timestampUtc,
            transactionUuid: transaction.transactionUuid,
            version: 0,
          });
          await expect(queryTransaction(transaction.transactionUuid)).rejects.toThrow(
            `GraphQL error: Transaction not found ${transaction.transactionUuid}`,
          );
        },
        10,
        1000,
      );
    });

    it('should ignore 3ds event if challenge', async () => {
      const transaction = create3DSEvent(uuidv4(), ThreeDSAuthStatus.C);

      await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.ThreeDSAuthExecuted', transaction);
      await apiTestHelper.sleep(2000);

      const record = await apiTestHelper.dbClient.getDbItem(
        apiTestHelper.getComponentTableName(),
        transaction.transactionUuid,
      );
      expect(record).toBeUndefined();
    });

    it.each(testData)(
      'should be able to project on 3ds txn %s',
      async (_s, data: { transactionUuid: string; timestampUtc: string; threeDSOutcome: ThreeDSOutcome }) => {
        const request = createCnpTransactionRequestDto(apiTestHelper.getEntityUuid(), apiTestHelper.getSiteUuid(), {
          timestampUtc: data.timestampUtc,
          transactionUuid: data.transactionUuid,
        });
        console.log('save transaction projection on device:', request, ',transaction:', request);
        await apiTestHelper.sendTxnProjectionEvent('dbs.CnpTransaction.Requested', request);
        await apiTestHelper.sleep(2000);
        await retry(
          async () => {
            const result = await queryTransaction(data.transactionUuid);
            expect(result.id).toBe(data.transactionUuid);
            console.log(result.threeDSOutcome);
            expect(result.threeDSOutcome).toBe(data.threeDSOutcome);
          },
          10,
          1000,
        );
      },
    );
  });
});
