import { DynamodbClient } from '@npco/bff-systemtest-utils/dist';
import { ComponentClients } from '@npco/bff-systemtest-utils/dist/helper';

import { ISOCurrencyCode } from 'bff-transaction-api/src/services/types';
import gql from 'graphql-tag';
import { v4 as uuidv4 } from 'uuid';

import { ApiTestHelper } from '../apiTestHelper';
import { region } from '../globalVariables';

const queryTransaction = async (apiTestHelper: ApiTestHelper, uuid: string): Promise<any> => {
  const output: any = await (
    await apiTestHelper.getOpenIdClient()
  ).query({
    query: gql`
      query getTransaction($uuid: ID!) {
        getTransaction(transactionUuid: $uuid) {
          id
        }
      }
    `,
    variables: {
      uuid,
    },
  });
  return output.data.getTransaction;
};

const transaction = {
  id: uuidv4(),
  iso8583: 'dummy message',
  ksn: 'FFFF9876543210E00001',
  stan: '000001',
  catid: '12345678',
  caid: '123456789012345',
  type: 'PURCHASE',
  source: 'STANDALONE',
  timestamp: new Date().toISOString(),
  timestampUtc: new Date().toISOString(),
  isoMessageType: '200',
  isoProcessingCode: '003000',
  isoPosEntryMode: '003',
  isoPosConditionCode: '00',
  isoCurrencyCode: ISOCurrencyCode.AUD,
  iso8583Length: 149,
  amount: 1200,
  tipAmount: 100,
  taxAmounts: [
    {
      name: 'GST',
      amount: 10,
    },
  ],
  cashAmount: 0,
  adjustAmount: 0,
  surchargeAmount: 100,
  lineItems: [1000],
  scheme: 'MC',
  bin: '500000',
  panMasked: '0009',
  panToken: '44291b2ea9bdc2b09bd09df53b740a9694837df317f65f5fa1109d08c8eae627',
  par: '',
  cardMedia: 'MSR',
  cardFallback: 'ICC_MSR',
  cardholderName: 'A. GENERIC CARDHOLDER',
  offlineApproved: true,
  commsFallback: true,
  pinBypassed: true,
  location: '-37.810344,144.969165',
  locationAccuracy: 5 as any,
  locationTimestamp: '2020-08-27T00:11:13.004+10:00',
  altitude: uuidv4(),
  bearing: uuidv4(),
  speed: uuidv4(),
  speedAccuracy: uuidv4(),
  temperatureDevice: uuidv4(),
  temperatureAmbient: uuidv4(),
  humidity: uuidv4(),
  pressure: uuidv4(),
  light: uuidv4(),
  emvTerminalType: '22',
  emvTerminalCapabilities: 'E0F8C8',
  emvAdditionalTerminalCapabilities: 'FF00F0F001',
  emvTvrRequest: '0000400000',
  emvTsiRequest: 'E000',
  emvAid: 'A0000000031010',
  emvCid: '80',
  emvCvmResult: '420300',
  emvAuthResponseCode: '',
  emvTranCurrencyCode: '036',
  emvTerminalCountryCode: '036',
  emvAppCurrencyCode: '036',
  emvIssuerCurrencyCode: '036',
  emv: [
    {
      tag: uuidv4(),
      length: 10,
      value: uuidv4(),
    },
  ],
};

const requestTransaction = async (apiTestHelper: ApiTestHelper) => {
  const output: any = await (
    await apiTestHelper.getOpenIdClient()
  ).mutate({
    mutation: gql`
      mutation requestTransaction($transaction: TransactionRequestInput!) {
        requestTransaction(transaction: $transaction) {
          approvalCode
          cardholderUuid
          responseCode
          id
          par
          panToken
          panMasked
          approvalCode
        }
      }
    `,
    variables: {
      transaction,
    },
  });
  return output.data.requestTransaction;
};

const stage = String(process.env.STAGE);

const getStage = () => (stage.startsWith('st') && stage !== 'staging' ? 'dev' : stage);

describe('unauthorised api tests', () => {
  const apiTestHelper = new ApiTestHelper(ComponentClients.DeviceBackend);
  const documentClient = new DynamodbClient({ region });

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
  });

  it('should response unauthorized error when device is not activate', async () => {
    console.log('update device status inactive', apiTestHelper.getDeviceUuid());
    await documentClient.update({
      TableName: `${getStage()}-dbs-api-dynamodb-SessionCache`,
      Key: { id: apiTestHelper.getDeviceUuid(), type: 'deviceUuid' },
      UpdateExpression: 'SET #status = :status',
      ExpressionAttributeNames: {
        '#status': 'status',
      },
      ExpressionAttributeValues: {
        ':status': 'DELETED',
      },
    });
    try {
      await requestTransaction(apiTestHelper);
      expect(1).toBe(2);
    } catch (err: any) {
      expect(err.graphQLErrors[0].errorType).toBe('UNAUTHORIZED_ERROR');
      expect(err.graphQLErrors[0].message).toBe('Device is disabled or inactive.');
    }
  });

  it('should response unauthorized error when access token not found', async () => {
    console.log('delete device ', apiTestHelper.getDeviceUuid());
    const output = await documentClient.delete({
      TableName: `${getStage()}-dbs-api-dynamodb-SessionCache`,
      Key: {
        id: apiTestHelper.getDeviceUuid(),
        type: 'deviceUuid',
      },
    });
    console.log('delete output:', output);
    try {
      await queryTransaction(apiTestHelper, 'xxx');
      expect(1).toBe(2);
    } catch (err: any) {
      expect(err.graphQLErrors[0].errorType).toBe('UNAUTHORIZED_ERROR');
      expect(err.graphQLErrors[0].message).toBe('Invalid access token - Device not found.');
    }
  });
});
