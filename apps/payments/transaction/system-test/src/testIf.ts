export const describeIf = (condition: boolean, ...args: Parameters<typeof describe>) => {
  return condition ? describe(...args) : describe.skip(...args);
};

export const testIf = (condition: boolean, ...args: Parameters<typeof test>) => {
  return condition ? test(...args) : test.skip(...args);
};

export const testEachIf = (condition: boolean, testcases: Array<any>) => {
  return condition ? test.each(testcases) : test.skip.each(testcases);
};
