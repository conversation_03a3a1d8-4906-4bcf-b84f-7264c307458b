import { invokeSync<PERSON><PERSON>b<PERSON> } from '@npco/bff-systemtest-utils/dist';
import { AwsRegions } from '@npco/component-bff-core/dist/utils/domicile';
import { TransactionRequestType } from '@npco/component-domain-events/dist/commonTypes/transaction';
import { TransactionResponseType, TransactionType, DbRecordType } from '@npco/component-dto-core/dist/types';
import { TransactionStatus } from '@npco/component-dto-transaction/dist/types';

import { getAuthenticatedClient } from 'crms-engine-system-test/src/utils/app-sync/getAuthenticatedClient';
import { bootstrapHubspotOauthToken } from 'crms-engine-system-test/src/utils/hubspot/bootstrapHubspotOauthToken';
import { retry } from 'crms-engine-system-test/src/utils/retry';
import { createCompany } from 'crms-engine-system-test/src/utils/simplified-services/createCompany';
import { sleep } from 'crms-engine-system-test/src/utils/sleep';
import { getCqrsSqs } from 'crms-engine-system-test/src/utils/sqs/getCqrsSqs';
import { sendCqrsSqsMessage } from 'crms-engine-system-test/src/utils/sqs/sendCqrsSqsMessage';
import { getEngineStackName } from 'crms-engine-system-test/src/utils/stacks/getEngineStackName';
import { v4 as uuidv4 } from 'uuid';

import { describeIf, testEachIf } from '../testIf';

import {
  createTransactionRequestEvent,
  queryTransactionFromDB,
  createOfflineTransactionEvent,
  queryMetricsFromDB,
  queryDailyMetricsFromDB,
  queryTransactionFromAppsync,
  queryStandInMetricsFromAppsync,
  queryGetStandInTransactionTotalsFromAppsync,
} from './transaction';

beforeAll(async () => {
  await bootstrapHubspotOauthToken();
});

const sendSqsTransactionEvent = async (eventUri: string, payload: any) =>
  sendCqrsSqsMessage(
    JSON.stringify({
      'detail-type': eventUri,
      detail: {
        aggregateId: payload.transactionUuid,
        payload,
      },
    }),
  );

const { STAGE: stage } = process.env;
describeIf(stage === 'dev', 'transaction test suite', () => {
  const entityUuid = uuidv4();
  const siteUuid = uuidv4();
  const deviceUuid = uuidv4();
  const customerUuid = uuidv4();

  let appsyncClient: any;

  const queryTransaction = async (transactionUuid: string): Promise<any> =>
    queryTransactionFromAppsync(appsyncClient, entityUuid, transactionUuid);

  beforeAll(async () => {
    await getCqrsSqs();
    await createCompany(entityUuid);

    appsyncClient = await getAuthenticatedClient();
  });

  describe('decline/approve out of order test', () => {
    let transaction: any;

    beforeAll(async () => {
      transaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);
      await sendSqsTransactionEvent('crms.Transaction.Requested', transaction);
      await sleep(3000);
    });

    it('should not approve a declined transaction', async () => {
      const reversalApprovedEvent = {
        transactionUuid: uuidv4(),
        entityUuid: transaction.entityUuid,
        originalTransactionUuid: transaction.transactionUuid,
        timestampUtc: transaction.timestampUtc,
        type: TransactionType.REVERSAL,
        responseType: TransactionResponseType.APPROVED,
      };
      await sendSqsTransactionEvent('crms.Transaction.Approved', reversalApprovedEvent);
      console.log('send approved:', reversalApprovedEvent);
      let txn;
      await retry(async () => {
        txn = await queryTransaction(transaction.transactionUuid);
        expect(txn.status).toBe(TransactionStatus.DECLINED);
      });
      const approvedEvent = {
        transactionUuid: transaction.transactionUuid,
        timestampUtc: transaction.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: transaction.deviceUuid,
        entityUuid: transaction.entityUuid,
        siteUuid: transaction.siteUuid,
        type: TransactionType.PURCHASE,
        responseType: TransactionResponseType.APPROVED,
      };

      await sendSqsTransactionEvent('crms.Transaction.Approved', approvedEvent);
      await retry(async () => {
        txn = await queryTransaction(transaction.transactionUuid);
        expect(txn.status).toBe(TransactionStatus.DECLINED);
        expect(txn.responseDescription).not.toBe(approvedEvent.responseDescription);
      });
    });
  });

  describe('purchase advice transaction test suite', () => {
    let transaction: any;

    beforeAll(async () => {
      transaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);
      await sendSqsTransactionEvent('crms.Transaction.Requested', transaction);
      await sleep(3000);
    });

    it('should be able to materialise purchase advice requested event', async () => {
      const paTxn = { ...transaction };
      paTxn.originalTransactionUuid = transaction.transactionUuid;
      paTxn.transactionUuid = uuidv4();
      paTxn.type = TransactionType.PURCHASEADVICE;
      await sendSqsTransactionEvent('crms.Transaction.Requested', paTxn);
      await retry(async () => {
        const txn = await queryTransactionFromDB(transaction.transactionUuid);
        expect((txn || [{}])[0].transactionType).toBe(TransactionType.PURCHASE);
      });
    });

    it('should be able to materialise purchase advice initiated event', async () => {
      const paTxn = { ...transaction };
      paTxn.originalTransactionUuid = transaction.transactionUuid;
      paTxn.transactionUuid = uuidv4();
      paTxn.type = TransactionType.PURCHASEADVICE;
      await sendSqsTransactionEvent('crms.Transaction.Initiated', paTxn);
      await retry(async () => {
        const txn = await queryTransaction(transaction.transactionUuid);
        expect(txn.type).toBe(TransactionType.PURCHASE);
      });
    });

    it('should be able to materialise purchase advice approved event', async () => {
      const approvedEvent = {
        transactionUuid: uuidv4(),
        originalTransactionUuid: transaction.transactionUuid,
        timestampUtc: transaction.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: transaction.deviceUuid,
        entityUuid: transaction.entityUuid,
        siteUuid: transaction.siteUuid,
        type: TransactionType.PURCHASEADVICE,
        responseType: TransactionResponseType.APPROVED,
      };
      await sendSqsTransactionEvent('crms.Transaction.Approved', approvedEvent);
      await retry(async () => {
        const txn = await queryTransaction(transaction.transactionUuid);
        expect(txn.type).toBe(TransactionType.PURCHASE);
      });
    });
  });

  describe('stand in transaction metric test suite', () => {
    let dailyTransaction: any;
    let day30Transaction: any;
    const dailyType = `${DbRecordType.STAND_IN}day.${new Date().toISOString().split('T')[0]}`;
    const day30Type = `${DbRecordType.STAND_IN}30day`;

    beforeAll(async () => {
      dailyTransaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

      await sendSqsTransactionEvent('crms.Transaction.Requested', dailyTransaction);

      day30Transaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

      await sendSqsTransactionEvent('crms.Transaction.Requested', day30Transaction);

      await sleep(3000);
    });

    it('should be able to project offline transaction metric daily totals', async () => {
      let originalMetric = {
        id: entityUuid,
        type: dailyType,
        outstandingAmount: 0,
        amount: 0,
        version: 0,
      };

      const metrics = await queryMetricsFromDB(entityUuid, dailyType);
      if (metrics && metrics.length > 0) {
        originalMetric = metrics[0] as any;
      }

      const offlineDtoEvent = createOfflineTransactionEvent(
        entityUuid,
        siteUuid,
        deviceUuid,
        customerUuid,
        dailyTransaction.transactionUuid,
      );

      await sendSqsTransactionEvent('crms.Transaction.OfflineApproved', offlineDtoEvent);

      await retry(
        async () => {
          const query = await queryMetricsFromDB(entityUuid, dailyType);
          expect(query?.length).toBe(1);
          expect(query![0]).toMatchObject({
            ...originalMetric,
            amount: originalMetric.amount + +dailyTransaction.amount.value,
            outstandingAmount: originalMetric.outstandingAmount + +dailyTransaction.amount.value,
            version: originalMetric.version + 1,
          });
        },
        20,
        1000,
        true,
      );
    });

    it('should be able to project purchase advice transaction metric daily', async () => {
      let originalMetric = {
        id: entityUuid,
        type: dailyType,
        outstandingAmount: 0,
        amount: 0,
        version: 0,
      };

      const metrics = await queryMetricsFromDB(entityUuid, dailyType);
      if (metrics && metrics.length > 0) {
        originalMetric = metrics[0] as any;
      }

      const approvedDtoEvent = {
        transactionUuid: uuidv4(),
        originalTransactionUuid: dailyTransaction.transactionUuid,
        timestampUtc: dailyTransaction.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: dailyTransaction.deviceUuid,
        entityUuid: dailyTransaction.entityUuid,
        siteUuid: dailyTransaction.siteUuid,
        type: TransactionType.PURCHASEADVICE,
        responseType: TransactionResponseType.APPROVED,
      };

      await sendSqsTransactionEvent('crms.Transaction.Approved', approvedDtoEvent);

      await retry(
        async () => {
          const query = await queryMetricsFromDB(entityUuid, dailyType);
          expect(query?.length).toBe(1);
          expect(query![0]).toMatchObject({
            ...originalMetric,
            outstandingAmount: originalMetric.outstandingAmount - +dailyTransaction.amount.value,
            version: originalMetric.version + 1,
          });
        },
        20,
        1000,
        true,
      );
    });

    it('should be able to project offline transaction metric 30 day totals', async () => {
      let originalMetric = {
        id: entityUuid,
        type: day30Type,
        outstandingAmount: 0,
        amount: 0,
        lifetimeOutstandingAmount: 0,
        lifetimeAmount: 0,
        lifetimeGpv: 0,
        averageValue: 0,
        version: 0,
      };

      const metrics = await queryMetricsFromDB(entityUuid, day30Type);
      if (metrics && metrics.length > 0) {
        originalMetric = metrics[0] as any;
      }

      const offlineDtoEvent = createOfflineTransactionEvent(
        entityUuid,
        siteUuid,
        deviceUuid,
        customerUuid,
        day30Transaction.transactionUuid,
      );

      await sendSqsTransactionEvent('crms.Transaction.OfflineApproved', offlineDtoEvent);

      await retry(
        async () => {
          const query = await queryMetricsFromDB(entityUuid, day30Type);
          expect(query?.length).toBe(1);
          expect(query![0]).toMatchObject({
            ...originalMetric,
            amount: originalMetric.amount + +day30Transaction.amount.value,
            outstandingAmount: originalMetric.outstandingAmount + +day30Transaction.amount.value,
            lifetimeAmount: originalMetric.lifetimeAmount + +day30Transaction.amount.value,
            lifetimeOutstandingAmount: originalMetric.lifetimeOutstandingAmount + +day30Transaction.amount.value,
            version: originalMetric.version + 1,
          });
        },
        20,
        1000,
        true,
      );
    });

    it('should be able to project purchase advice transaction metric 30 day totals', async () => {
      let originalMetric = {
        id: entityUuid,
        type: day30Type,
        outstandingAmount: 0,
        amount: 0,
        lifetimeOutstandingAmount: 0,
        lifetimeAmount: 0,
        lifetimeGpv: 0,
        averageValue: 0,
        version: 0,
      };

      const metrics = await queryMetricsFromDB(entityUuid, day30Type);
      if (metrics && metrics.length > 0) {
        originalMetric = metrics[0] as any;
      }

      const approvedDtoEvent = {
        transactionUuid: uuidv4(),
        originalTransactionUuid: day30Transaction.transactionUuid,
        timestampUtc: day30Transaction.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: day30Transaction.deviceUuid,
        entityUuid: day30Transaction.entityUuid,
        siteUuid: day30Transaction.siteUuid,
        type: TransactionType.PURCHASEADVICE,
        responseType: TransactionResponseType.APPROVED,
      };

      await sendSqsTransactionEvent('crms.Transaction.Approved', approvedDtoEvent);

      await retry(
        async () => {
          const query = await queryMetricsFromDB(entityUuid, day30Type);
          expect(query?.length).toBe(1);
          expect(query![0]).toMatchObject({
            ...originalMetric,
            outstandingAmount: originalMetric.outstandingAmount - +day30Transaction.amount.value,
            lifetimeOutstandingAmount: originalMetric.lifetimeOutstandingAmount - +day30Transaction.amount.value,
            version: originalMetric.version + 1,
          });
        },
        20,
        1000,
        true,
      );
    });

    it('standIn metrics total should equal 30 daily metrics', async () => {
      const query30DayDate = new Date().setDate(new Date().getDate() - 30);

      const query30Day = await queryMetricsFromDB(entityUuid, day30Type);
      const queryDaily30 = await queryDailyMetricsFromDB(
        entityUuid,
        new Date(query30DayDate).toISOString().split('T')[0],
      );

      expect(query30Day![0].amount).toEqual(
        queryDaily30?.reduce((acc: number, metric: Record<string, any>) => acc + metric.amount, 0),
      );
      expect(query30Day![0].outstandingAmount).toEqual(
        queryDaily30?.reduce((acc: number, metric: Record<string, any>) => acc + metric.outstandingAmount, 0),
      );
    });

    it('should not add offline transaction to metric on event replay', async () => {
      const original30DayMetric = await queryMetricsFromDB(entityUuid, day30Type);
      const originalDailyMetric = await queryDailyMetricsFromDB(entityUuid, new Date().toISOString().split('T')[0]);

      const offlineDtoEvent = createOfflineTransactionEvent(
        entityUuid,
        siteUuid,
        deviceUuid,
        customerUuid,
        day30Transaction.transactionUuid,
      );

      await sendSqsTransactionEvent('crms.Transaction.OfflineApproved', offlineDtoEvent);
      await sendSqsTransactionEvent('crms.Transaction.OfflineApproved', offlineDtoEvent);

      console.log('send offline:', offlineDtoEvent);

      await retry(
        async () => {
          const updated30DayMetric = await queryMetricsFromDB(entityUuid, day30Type);

          expect(updated30DayMetric?.length).toBe(1);
          expect(updated30DayMetric![0]).toMatchObject({
            ...original30DayMetric![0],
            amount: original30DayMetric![0].amount + +offlineDtoEvent.amount.value,
            lifetimeAmount: original30DayMetric![0].lifetimeAmount + +offlineDtoEvent.amount.value,
            outstandingAmount: original30DayMetric![0].outstandingAmount + +offlineDtoEvent.amount.value,
            lifetimeOutstandingAmount:
              original30DayMetric![0].lifetimeOutstandingAmount + +day30Transaction.amount.value,
            version: original30DayMetric![0].version + 1,
          });

          const updatedDailyMetric = await queryDailyMetricsFromDB(entityUuid, new Date().toISOString().split('T')[0]);

          expect(updatedDailyMetric?.length).toBe(1);
          expect(updatedDailyMetric![0]).toMatchObject({
            ...originalDailyMetric![0],
            amount: originalDailyMetric![0].amount + +offlineDtoEvent.amount.value,
            outstandingAmount: originalDailyMetric![0].outstandingAmount + +offlineDtoEvent.amount.value,
            version: originalDailyMetric![0].version + 1,
          });
        },
        20,
        1000,
        true,
      );
    });

    it('should not add approved offline transaction to metric on event replay', async () => {
      const original30DayMetric = await queryMetricsFromDB(entityUuid, day30Type);
      const originalDailyMetric = await queryDailyMetricsFromDB(entityUuid, new Date().toISOString().split('T')[0]);

      const transaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

      await sendSqsTransactionEvent('crms.Transaction.Requested', transaction);

      await sleep(3000);

      const approvedDtoEvent = {
        transactionUuid: uuidv4(),
        originalTransactionUuid: transaction.transactionUuid,
        timestampUtc: transaction.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: transaction.deviceUuid,
        entityUuid: transaction.entityUuid,
        siteUuid: transaction.siteUuid,
        type: TransactionType.PURCHASEADVICE,
        responseType: TransactionResponseType.APPROVED,
      };

      await sendSqsTransactionEvent('crms.Transaction.Approved', approvedDtoEvent);
      await sendSqsTransactionEvent('crms.Transaction.Approved', approvedDtoEvent);

      console.log('send approved:', approvedDtoEvent);
      await retry(
        async () => {
          const updated30DayMetric = await queryMetricsFromDB(entityUuid, day30Type);
          expect(updated30DayMetric?.length).toBe(1);
          expect(updated30DayMetric![0]).toMatchObject({
            ...original30DayMetric![0],
            outstandingAmount: original30DayMetric![0].outstandingAmount - +transaction.amount.value,
            lifetimeOutstandingAmount: original30DayMetric![0].lifetimeOutstandingAmount - +transaction.amount.value,
            version: expect.any(Number),
          });

          const updatedDailyMetric = await queryDailyMetricsFromDB(entityUuid, new Date().toISOString().split('T')[0]);

          expect(updatedDailyMetric?.length).toBe(1);
          expect(updatedDailyMetric![0]).toMatchObject({
            ...originalDailyMetric![0],
            outstandingAmount: originalDailyMetric![0].outstandingAmount - +transaction.amount.value,
            version: expect.any(Number),
          });
        },
        20,
        1000,
        true,
      );
    });

    it('should reverse offline metrics on reversal', async () => {
      const original30DayMetric = await queryMetricsFromDB(entityUuid, day30Type);
      const originalDailyMetric = await queryDailyMetricsFromDB(entityUuid, new Date().toISOString().split('T')[0]);

      const offlineDtoEvent = createOfflineTransactionEvent(
        entityUuid,
        siteUuid,
        deviceUuid,
        customerUuid,
        day30Transaction.transactionUuid,
      );

      await sendSqsTransactionEvent('crms.Transaction.OfflineApproved', offlineDtoEvent);

      const reversalDtoEvent = {
        transactionUuid: uuidv4(),
        originalTransactionUuid: offlineDtoEvent.transactionUuid,
        timestampUtc: offlineDtoEvent.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: offlineDtoEvent.deviceUuid,
        entityUuid: offlineDtoEvent.entityUuid,
        siteUuid: offlineDtoEvent.siteUuid,
        type: TransactionType.REVERSAL,
        responseType: TransactionResponseType.APPROVED,
      };

      await sleep(3000);

      await sendSqsTransactionEvent('crms.Transaction.Approved', reversalDtoEvent);

      console.log('send reversal:', reversalDtoEvent);
      await retry(
        async () => {
          const updated30DayMetric = await queryMetricsFromDB(entityUuid, day30Type);

          expect(updated30DayMetric?.length).toBe(1);
          expect(updated30DayMetric![0]).toMatchObject({
            ...original30DayMetric![0],
            version: original30DayMetric![0].version + 2,
          });

          const updatedDailyMetric = await queryDailyMetricsFromDB(entityUuid, new Date().toISOString().split('T')[0]);

          expect(updatedDailyMetric?.length).toBe(1);
          expect(updatedDailyMetric![0]).toMatchObject({
            ...originalDailyMetric![0],
            version: original30DayMetric![0].version + 2,
          });
        },
        20,
        1000,
        true,
      );
    });

    it('should reverse full offline metrics on reversal', async () => {
      const original30DayMetric = await queryMetricsFromDB(entityUuid, day30Type);
      const originalDailyMetric = await queryDailyMetricsFromDB(entityUuid, new Date().toISOString().split('T')[0]);

      const offlineDtoEvent = createOfflineTransactionEvent(
        entityUuid,
        siteUuid,
        deviceUuid,
        customerUuid,
        day30Transaction.transactionUuid,
      );

      await sendSqsTransactionEvent('crms.Transaction.OfflineApproved', offlineDtoEvent);

      const approvedDtoEvent = {
        transactionUuid: uuidv4(),
        originalTransactionUuid: offlineDtoEvent.transactionUuid,
        timestampUtc: offlineDtoEvent.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: offlineDtoEvent.deviceUuid,
        entityUuid: offlineDtoEvent.entityUuid,
        siteUuid: offlineDtoEvent.siteUuid,
        type: TransactionType.PURCHASEADVICE,
        responseType: TransactionResponseType.APPROVED,
      };

      await sendSqsTransactionEvent('crms.Transaction.Approved', approvedDtoEvent);

      const reversalDtoEvent = {
        transactionUuid: uuidv4(),
        originalTransactionUuid: offlineDtoEvent.transactionUuid,
        timestampUtc: offlineDtoEvent.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: offlineDtoEvent.deviceUuid,
        entityUuid: offlineDtoEvent.entityUuid,
        siteUuid: offlineDtoEvent.siteUuid,
        type: TransactionType.REVERSAL,
        responseType: TransactionResponseType.APPROVED,
      };

      await sleep(3000);
      await sendSqsTransactionEvent('crms.Transaction.Approved', reversalDtoEvent);

      console.log('send offline:', offlineDtoEvent);
      await retry(
        async () => {
          const updated30DayMetric = await queryMetricsFromDB(entityUuid, day30Type);

          expect(updated30DayMetric?.length).toBe(1);
          expect(updated30DayMetric![0]).toMatchObject({
            ...original30DayMetric![0],
            version: original30DayMetric![0].version + 3,
          });

          const updatedDailyMetrics = await queryDailyMetricsFromDB(entityUuid, new Date().toISOString().split('T')[0]);

          expect(updatedDailyMetrics?.length).toBe(1);
          if (!updatedDailyMetrics || !updatedDailyMetrics[0]) {
            fail('missing updated daily metric');
            return;
          }
          const updatedDailyMetric = updatedDailyMetrics[0];

          expect(updatedDailyMetric).toMatchObject({
            ...originalDailyMetric![0],
            version: originalDailyMetric![0].version + 3,
          });
        },
        20,
        1000,
        true,
      );
    });

    it.each([
      ['ascending', { parameters: { ascending: true, limit: 10, orderBy: 'STAND_IN_30' } }],
      ['descending', { parameters: { ascending: false, limit: 10, orderBy: 'STAND_IN_30' } }],
      ['high limit', { parameters: { ascending: true, limit: 50, orderBy: 'STAND_IN_30' } }],
      ['order STAND_IN_30', { parameters: { ascending: true, limit: 10, orderBy: 'STAND_IN_30' } }],
      ['order OUTSTANDING_30', { parameters: { ascending: true, limit: 10, orderBy: 'OUTSTANDING_30' } }],
      ['order STAND_IN_LIFETIME', { parameters: { ascending: true, limit: 10, orderBy: 'STAND_IN_LIFETIME' } }],
      ['order OUTSTANDING_LIFETIME', { parameters: { ascending: true, limit: 10, orderBy: 'OUTSTANDING_LIFETIME' } }],
      [
        'order AVERAGE_TRANSACTION_LIFETIME',
        { parameters: { ascending: true, limit: 10, orderBy: 'AVERAGE_TRANSACTION_LIFETIME' } },
      ],
      ['order GPV_LIFETIME', { parameters: { ascending: true, limit: 10, orderBy: 'GPV_LIFETIME' } }],
    ])(
      'should be able to query GetStandInTransactionMetrics appsync with %s',
      async (_s: string, data: { parameters: { ascending: boolean; limit: number; orderBy: string } }) => {
        const response = await queryStandInMetricsFromAppsync(
          appsyncClient,
          data.parameters.ascending,
          data.parameters.limit,
          data.parameters.orderBy,
        );

        if (response.standInMetrics.length === data.parameters.limit) {
          expect(response.nextToken).not.toBeNull();
          expect(response.standInMetrics).toHaveLength(data.parameters.limit);
        }
        expect(response.standInMetrics[0]).toMatchObject({
          entityUuid: expect.any(String),
          entityName: expect.anything(),
          standIn30: expect.any(String),
          outstanding30: expect.any(String),
          averageTransactionLifetime: expect.any(String),
          standInLifeTime: expect.any(String),
          outstandingLifeTime: expect.any(String),
          gpvLifeTime: expect.any(String),
        });
      },
    );

    it('should be able to query GetStandInTransactionMetrics appsync with nextToken', async () => {
      const response = await queryStandInMetricsFromAppsync(appsyncClient, false, 2, 'STAND_IN_30');
      expect(response.nextToken).not.toBeNull();
      expect(response.standInMetrics).toHaveLength(2);
      expect(response.standInMetrics[0]).toMatchObject({
        entityUuid: expect.any(String),
        entityName: expect.anything(),
        standIn30: expect.any(String),
        outstanding30: expect.any(String),
        averageTransactionLifetime: expect.any(String),
        standInLifeTime: expect.any(String),
        outstandingLifeTime: expect.any(String),
        gpvLifeTime: expect.any(String),
      });

      const nextResponse = await queryStandInMetricsFromAppsync(
        appsyncClient,
        false,
        2,
        'STAND_IN_30',
        response.nextToken,
      );
      console.log('nextResponse', nextResponse);
      expect(nextResponse.nextToken).not.toBeNull();
      expect(nextResponse.standInMetrics).toHaveLength(2);
      expect(nextResponse.standInMetrics[0]).toMatchObject({
        entityUuid: expect.any(String),
        entityName: expect.any(String),
        standIn30: expect.any(String),
        outstanding30: expect.any(String),
        averageTransactionLifetime: expect.any(String),
        standInLifeTime: expect.any(String),
        outstandingLifeTime: expect.any(String),
        gpvLifeTime: expect.any(String),
      });

      expect(nextResponse.standInMetrics[0].entityUuid).not.toBe(response.standInMetrics[0].entityUuid);
    });

    // this is depending on the test data created in the integration test, enable it when UK integration test is enabled
    testEachIf(process.env.AWS_REGION === AwsRegions.SYDNEY, [
      [
        'minutely',
        {
          parameters: {
            filter: { timestamp: { gt: new Date().getTime() - 1000 * 60 * 60 * 24 * 7 } },
            totalsType: 'MINUTELY',
          },
        },
      ],
      [
        'hourly',
        {
          parameters: {
            filter: { timestamp: { gt: new Date().getTime() - 1000 * 60 * 60 * 24 * 7 } },
            totalsType: 'HOURLY',
          },
        },
      ],
      [
        'all standInStatus',
        {
          parameters: {
            filter: { timestamp: { gt: new Date().getTime() - 1000 * 60 * 60 * 24 * 7 }, standInStatus: 'ALL' },
            totalsType: 'HOURLY',
          },
        },
      ],
      [
        'APPROVED standInStatus',
        {
          parameters: {
            filter: { timestamp: { gt: new Date().getTime() - 1000 * 60 * 60 * 24 * 7 }, standInStatus: 'APPROVED' },
            totalsType: 'HOURLY',
          },
        },
      ],
      // Need a test case to generate this data
      // [
      //   'DECLINED standInStatus',
      //   {
      //     parameters: {
      //       filter: { timestamp: { gt: new Date().getTime() - 1000 * 60 * 60 * 24 * 7 }, standInStatus: 'DECLINED' },
      //       totalsType: 'HOURLY',
      //     },
      //   },
      // ],
    ])(
      'should be able to query GetStandInTransactionTotals appsync with %s',
      async (_s: string, data: { parameters: { filter: any; totalsType: string } }) => {
        const response = await queryGetStandInTransactionTotalsFromAppsync(
          appsyncClient,
          data.parameters.filter,
          data.parameters.totalsType,
        );
        expect(response.length).toBeGreaterThan(0);
        expect(response[0]).toMatchObject({
          timestamp: expect.any(String),
          amount: expect.any(String),
          count: expect.any(Number),
          approvedAmount: expect.any(String),
          approvedCount: expect.any(Number),
          declinedAmount: expect.any(String),
          declinedCount: expect.any(Number),
        });
      },
    );
  });

  describe('refund transaction test', () => {
    let transaction: any;
    let refundTransaction: any;

    beforeEach(async () => {
      transaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

      await sendSqsTransactionEvent('crms.Transaction.Requested', transaction);

      await sleep(3000);

      refundTransaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

      refundTransaction.originalTransactionUuid = transaction.transactionUuid;
      refundTransaction.type = TransactionType.REFUND;

      await sendSqsTransactionEvent('crms.Transaction.Requested', refundTransaction);
    });

    it('should be able to project refund transaction (lambda call)', async () => {
      await retry(async () => {
        const lambdaName = getEngineStackName('txn-getTransactionHandler');

        const refundTransactionPayload = await invokeSyncLambda(
          lambdaName,
          {
            args: {
              transactionUuid: refundTransaction.transactionUuid,
              entityUuid,
            },
          },
          true,
        );

        expect(refundTransactionPayload.id).toBe(refundTransaction.transactionUuid);
        expect(refundTransactionPayload.status).toBe(TransactionStatus.PROCESSING);
        expect(refundTransactionPayload.entityUuid).toBe(refundTransaction.entityUuid);
        expect(refundTransactionPayload.deviceUuid).toBe(refundTransaction.deviceUuid);
        expect(refundTransactionPayload.type).toBe(TransactionType.REFUND);
        expect(refundTransactionPayload.refundedTransactionUuid).toEqual(transaction.transactionUuid);
      });
    });

    it('should be able to project refund transaction', async () => {
      const txn = await queryTransaction(refundTransaction.transactionUuid);

      expect(txn.id).toBe(refundTransaction.transactionUuid);
      expect(txn.status).toBe(TransactionStatus.PROCESSING);
      expect(txn.entityUuid).toBe(refundTransaction.entityUuid);
      expect(txn.deviceUuid).toBe(refundTransaction.deviceUuid);
      expect(txn.type).toBe(TransactionType.REFUND);
      expect(txn.refundedTransactionUuid).toEqual(transaction.transactionUuid);
      expect(txn.refundedTransaction).toEqual({
        id: transaction.transactionUuid,
        entityUuid: transaction.entityUuid,
        deviceUuid: transaction.deviceUuid,
      });
    });

    it('should reflect the refunded status on the original transaction', async () => {
      const approvedDtoEvent = {
        transactionUuid: refundTransaction.transactionUuid,
        timestampUtc: refundTransaction.timestampUtc,
        responseCode: '00',
        responseDescription: uuidv4(),
        approvalCode: uuidv4(),
        rrn: uuidv4(),
        panMasked: uuidv4(),
        panToken: uuidv4(),
        par: uuidv4(),
        cardholderUuid: uuidv4(),
        deviceUuid: refundTransaction.deviceUuid,
        entityUuid: refundTransaction.entityUuid,
        siteUuid: refundTransaction.siteUuid,
        type: TransactionType.REFUND,
        responseType: TransactionResponseType.APPROVED,
      };

      await sendSqsTransactionEvent('crms.Transaction.Approved', approvedDtoEvent);

      await retry(async () => {
        const txn = await queryTransaction(transaction.transactionUuid);

        expect(txn.refunded).toBe(true);
        expect(txn.refundedAmount.toString()).toBe(refundTransaction.amount.value);
      });
    });

    it('should return null for refunded transaction for a non refund transaction', async () => {
      const txn = await queryTransaction(transaction.transactionUuid);

      expect(txn.refundedTransactionUuid).toBeNull();
      expect(txn.refundedTransaction).toBeNull();
    });

    it('should return an array of refunded transactions (lambda call)', async () => {
      const requestPromises = Array(2)
        .fill(1)
        .map(async () => {
          const refundTrans = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

          refundTrans.originalTransactionUuid = transaction.transactionUuid;
          refundTrans.type = TransactionRequestType.REFUND;

          await sendSqsTransactionEvent('crms.Transaction.Requested', refundTrans);

          return refundTrans;
        });

      const refundedTxns = await Promise.all(requestPromises);
      await sleep(3000);

      const approvePromises = refundedTxns.map(async (refundTxn) => {
        const approvedDtoEvent = {
          transactionUuid: refundTxn.transactionUuid,
          timestampUtc: refundTxn.timestampUtc,
          responseCode: '00',
          responseDescription: uuidv4(),
          approvalCode: uuidv4(),
          rrn: uuidv4(),
          panMasked: uuidv4(),
          panToken: uuidv4(),
          par: uuidv4(),
          cardholderUuid: uuidv4(),
          deviceUuid: refundTxn.deviceUuid,
          entityUuid: refundTxn.entityUuid,
          siteUuid: refundTxn.siteUuid,
          type: TransactionType.REFUND,
          responseType: TransactionResponseType.APPROVED,
        };

        await sendSqsTransactionEvent('crms.Transaction.Approved', approvedDtoEvent);

        return true;
      });

      await Promise.all(approvePromises);
      await sleep(3000);

      const lambdaName = getEngineStackName('txn-getRefundTransactionsHandler');

      const refundTransactionsPayload = await invokeSyncLambda(
        lambdaName,
        {
          args: {
            originalTransactionUuid: transaction.transactionUuid,
          },
        },
        true,
      );

      expect(refundTransactionsPayload.length).toBe(3);
      expect(
        refundTransactionsPayload.every(
          (refundedTransaction: any) =>
            refundedTransaction.originalTransactionUuid === transaction.transactionUuid &&
            refundTransaction.type === TransactionType.REFUND,
        ),
      ).toBe(true);
    });

    it('should return an empty array if transaction has no refunds (lambda call)', async () => {
      const noRefundTransaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

      await sendSqsTransactionEvent('crms.Transaction.Requested', noRefundTransaction);

      await retry(async () => {
        const lambdaName = getEngineStackName('txn-getRefundTransactionsHandler');

        const noRefundTransactionPayload = await invokeSyncLambda(
          lambdaName,
          {
            args: {
              originalTransactionUuid: noRefundTransaction.transactionUuid,
            },
          },
          true,
        );

        expect(noRefundTransactionPayload.length).toBe(0);
      });
    });

    it('should return an array of refunded transactions', async () => {
      const requestPromises = Array(2)
        .fill(1)
        .map(async () => {
          const refundTrans = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

          refundTrans.originalTransactionUuid = transaction.transactionUuid;
          refundTrans.type = TransactionRequestType.REFUND;

          await sendSqsTransactionEvent('crms.Transaction.Requested', refundTrans);

          return refundTrans;
        });

      const refundedTxns = await Promise.all(requestPromises);
      await sleep(3000);

      const approvePromises = refundedTxns.map(async (refundTxn) => {
        const approvedDtoEvent = {
          transactionUuid: refundTxn.transactionUuid,
          timestampUtc: refundTxn.timestampUtc,
          responseCode: '00',
          responseDescription: uuidv4(),
          approvalCode: uuidv4(),
          rrn: uuidv4(),
          panMasked: uuidv4(),
          panToken: uuidv4(),
          par: uuidv4(),
          cardholderUuid: uuidv4(),
          deviceUuid: refundTxn.deviceUuid,
          entityUuid: refundTxn.entityUuid,
          siteUuid: refundTxn.siteUuid,
          type: TransactionType.REFUND,
          responseType: TransactionResponseType.APPROVED,
        };

        await sendSqsTransactionEvent('crms.Transaction.Approved', approvedDtoEvent);

        return true;
      });

      await Promise.all(approvePromises);
      await sleep(3000);

      const txn = await queryTransaction(transaction.transactionUuid);

      expect(txn.refundTransactions.length).toBe(3);
      expect(
        txn.refundTransactions.every(
          (refundedTransaction: any) =>
            refundedTransaction.refundedTransactionUuid === transaction.transactionUuid &&
            refundTransaction.type === TransactionType.REFUND,
        ),
      ).toBe(true);
    });

    it('should return an empty array if transaction has no refunds', async () => {
      const noRefundTransaction = createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid);

      await sendSqsTransactionEvent('crms.Transaction.Requested', noRefundTransaction);

      await retry(async () => {
        const txn = await queryTransaction(noRefundTransaction.transactionUuid);

        expect(txn.refundTransactions.length).toBe(0);
      });
    });
  });
});
