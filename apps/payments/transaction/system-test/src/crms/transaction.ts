import { DynamodbClient } from '@npco/bff-systemtest-utils/dist';
import { TransactionRequestType } from '@npco/component-domain-events/dist/commonTypes/transaction';
import { TransactionApprovedEventPayload } from '@npco/component-domain-events/dist/customerRelationshipManagementService/crmsTransactionApprovedEvent';
import type { TransactionOfflineApprovedEventPayload } from '@npco/component-domain-events/dist/customerRelationshipManagementService/crmsTransactionOfflineApprovedEvent';
import { TransactionRequestedEventPayload } from '@npco/component-domain-events/dist/customerRelationshipManagementService/crmsTransactionRequestedEvent';
import type { TransactionFallbackApprovedBaseEventPayload } from '@npco/component-domain-events/dist/transaction';
import {
  AcquirerApprovedDetailReason,
  Channel,
  DbRecordType,
  IsoPosConditionCode,
  IsoPosEntryMode,
  IsoProcessingCode,
  Source,
  SplitPaymentType,
  TransactionType,
} from '@npco/component-dto-core/dist/types';
import { CardMedia, CardScheme, TransactionStatus, CardFallback } from '@npco/component-dto-transaction/dist/types';

import type { AWSAppSyncClient } from 'aws-appsync';
import { createRequestDtoFromDeviceInput } from 'bff-transaction-api/src/services/transactionUtils';
import type { TransactionRequestInput } from 'bff-transaction-api/src/services/types';
import { getEngineStackName } from 'crms-engine-system-test/src/utils/stacks/getEngineStackName';
import gql from 'graphql-tag';
import { v4 as uuidv4 } from 'uuid';

import { region } from '../globalVariables';

const documentClient = new DynamodbClient({ region });

export const createTransactionRequestEvent = (
  entityUuid: string,
  siteUuid: string,
  deviceUuid: string,
  customerUuid: string,
): TransactionRequestedEventPayload & { transactionUuid: string } => {
  const input: TransactionRequestInput = {
    id: uuidv4(),
    iso8583: uuidv4(),
    ksn: uuidv4(),
    stan: uuidv4(),
    catid: uuidv4(),
    caid: uuidv4(),
    type: TransactionType.PURCHASE,
    source: Source.STANDALONE as any,
    timestamp: new Date().toISOString(),
    timestampUtc: new Date().toISOString(),
    isoMessageType: uuidv4(),
    isoProcessingCode: IsoProcessingCode.CODE_001000,
    isoPosEntryMode: IsoPosEntryMode.CODE_000,
    isoPosConditionCode: IsoPosConditionCode.CODE_00,
    iso8583Length: 1,
    amount: 20,
    tipAmount: 30,
    lcr: true,
    taxAmounts: [
      {
        name: uuidv4(),
        amount: 10,
      },
    ],
    cashAmount: 10,
    adjustAmount: 40,
    surchargeAmount: 50,
    lineItems: [0],
    scheme: CardScheme.AMEX,
    bin: uuidv4(),
    panMasked: uuidv4(),
    panToken: uuidv4(),
    par: uuidv4(),
    cardMedia: CardMedia.ICC,
    cardFallback: CardFallback.ICC_MSR,
    cardholderName: uuidv4(),
    commsFallback: true,
    pinBypassed: true,
    location: uuidv4(),
    locationAccuracy: 0 as any,
    locationTimestamp: new Date().toISOString(),
    altitude: uuidv4(),
    bearing: uuidv4(),
    speed: uuidv4(),
    speedAccuracy: uuidv4(),
    temperatureDevice: uuidv4(),
    temperatureAmbient: uuidv4(),
    humidity: uuidv4(),
    pressure: uuidv4(),
    light: uuidv4(),
    posName: 'pos register name',
    externalReference: 'external reference',
    splitPayment: {
      id: uuidv4(),
      type: SplitPaymentType.PORTION,
      targetAmount: 2500,
      portions: 50,
    },
  };
  const requestInput = createRequestDtoFromDeviceInput(
    {
      deviceUuid,
      entityUuid,
      siteUuid,
      customerUuid,
      channel: Channel.TERMINAL,
    },
    input,
  );
  return {
    transactionUuid: input.id,
    ...new TransactionRequestedEventPayload(requestInput),
    emv: {
      terminalType: uuidv4(),
      terminalCapabilities: uuidv4(),
      additionalTerminalCapabilities: uuidv4(),
      tvrRequest: uuidv4(),
      tsiRequest: uuidv4(),
      aid: uuidv4(),
      cid: uuidv4(),
      cvmResult: uuidv4(),
      authResponseCode: uuidv4(),
      tranCurrencyCode: uuidv4(),
      terminalCountryCode: uuidv4(),
      appCurrencyCode: uuidv4(),
      issuerCurrencyCode: uuidv4(),
      additionalTags: [
        {
          tag: uuidv4(),
          length: 10,
          value: uuidv4(),
        },
      ],
    },
  };
};

export const createOfflineTransactionEvent = (
  entityUuid: string,
  siteUuid: string,
  deviceUuid: string,
  customerUuid: string,
  originalTransactionUuid?: string,
): TransactionOfflineApprovedEventPayload & { transactionUuid: string } => ({
  ...createTransactionRequestEvent(entityUuid, siteUuid, deviceUuid, customerUuid),
  originalTransactionUuid,
  responseCode: uuidv4(),
  responseDescription: uuidv4(),
  approvalCode: uuidv4(),
});

export const createTransactionResponseEvent = (
  update: any = {},
): TransactionApprovedEventPayload & { transactionUuid: string } => ({
  transactionUuid: update?.transactionUuid ?? uuidv4(),
  ...new TransactionApprovedEventPayload({
    timestampUtc: new Date().toString(),
    ksn: uuidv4(),
    responseCode: uuidv4(),
    responseDescription: uuidv4(),
    approvalCode: uuidv4(),
    rrn: uuidv4(),
    panMasked: uuidv4(),
    panToken: uuidv4(),
    par: uuidv4(),
    cardholderUuid: uuidv4(),
    cardholderEmail: uuidv4(),
    cardholderPhone: uuidv4(),
    deviceUuid: uuidv4(),
    siteUuid: uuidv4(),
    entityUuid: uuidv4(),
    type: TransactionRequestType.PURCHASE,
    ...update,
  }),
});

export const createFallbackApproveTransactionDto = (
  originalTransaction?: any,
): TransactionFallbackApprovedBaseEventPayload & { transactionUuid: string } => ({
  ...createTransactionResponseEvent(originalTransaction),
  ...(originalTransaction
    ? {
        transactionUuid: originalTransaction.transactionUuid,
        deviceUuid: originalTransaction.deviceUuid,
        siteUuid: originalTransaction.siteUuid,
        entityUuid: originalTransaction.entityUuid,
        timestampUtc: originalTransaction.timestampUtc,
      }
    : {}),
  isNotAcquirerApprovedDetails: {
    isNotAcquirerApproved: true,
    reason: AcquirerApprovedDetailReason.ACQUIRED_DECLINED,
    acquirerResponseCode: uuidv4(),
    reasonDetails: uuidv4(),
  },
});

export const queryTransactionFromDB = async (id: string) => {
  const output = await documentClient.query({
    TableName: getEngineStackName('Entities'),
    KeyConditionExpression: 'id = :id',
    ExpressionAttributeValues: {
      ':id': id,
    },
  });
  return output.Items;
};

export const queryMetricsFromDB = async (id: string, type: string) => {
  const output = await documentClient.query({
    TableName: getEngineStackName('Metrics'),
    KeyConditionExpression: '#id = :id AND #type = :type',
    ExpressionAttributeNames: {
      '#id': 'id',
      '#type': 'type',
    },
    ExpressionAttributeValues: {
      ':id': id,
      ':type': type,
    },
  });
  return output.Items;
};

export const queryDailyMetricsFromDB = async (id: string, from: string) => {
  const output = await documentClient.query({
    TableName: getEngineStackName('Metrics'),
    KeyConditionExpression: '#id = :id AND #type >= :type',
    ExpressionAttributeNames: {
      '#id': 'id',
      '#type': 'type',
    },
    ExpressionAttributeValues: {
      ':id': id,
      ':type': `${DbRecordType.STAND_IN}day.${from}`,
    },
  });
  return output.Items;
};

export const queryTransactionFromAppsync = async (
  client: AWSAppSyncClient<any>,
  entityUuid: string,
  transactionUuid: string,
  threeDS = false,
) => {
  const threeDSFiedls = `threeDSOutcome
  threeDSAuthStatus
  threeDSLiabilityShift
  threeDSSkippedReason
  threeDSNotSupport
  `;
  const output: any = await client.query({
    query: gql`
      query getTransaction($entityUuid: ID!, $transactionUuid: ID!) {
        getTransaction(entityUuid: $entityUuid, transactionUuid: $transactionUuid) {
          id
          entityUuid
          deviceUuid
          deviceName
          deviceModel
          siteUuid
          siteName
          customerUuid
          customerName
          timestamp
          source
          channel
          amount
          saleAmount
          tipAmount
          taxAmounts {
            name
            amount
          }
          feeAmount
          feeCharged
          surchargeAmount
          refundedAmount
          scheme
          type
          status
          deposited
          depositUuid
          depositShortId
          depositDate
          maskedPan
          reference
          review
          refunded
          reversed
          cardholderUuid
          cardholderEmail
          cardholderName
          cardholderPhone
          bin
          stan
          caid
          catid
          cardMedia
          emvAid
          emvTagsPrint
          emvAppName
          isoProcessingCode
          isoMessageType
          approvalCode
          responseCode
          responseDescription
          ksn
          isoPosEntryMode
          isoPosConditionCode
          panToken
          par
          cardFallback
          wasOfflineApproved
          wasCommsFallback
          wasPinBypassed
          originalTransactionUuid
          emv {
            tag
            length
            value
          }
          location {
            location
            timestampLocal
            accuracy
          }
          emvTerminalType
          emvTerminalCapabilities
          emvAdditionalTerminalCapabilities
          emvTvrRequest
          emvTsiRequest
          emvCid
          emvCvmResult
          emvAuthResponseCode
          emvTranCurrencyCode
          emvTerminalCountryCode
          emvAppCurrencyCode
          emvIssuerCountryCode
          posName
          externalReference
          externalReferenceUrl
          notes
          issuer
          standInStatus
          refundedTransactionUuid
          refundedTransaction {
            id
            entityUuid
            deviceUuid
          }
          refundTransactions {
            id
            refundedTransactionUuid
          }
          lcr
          splitPaymentUuid
          refundReason
          ${threeDS ? threeDSFiedls : ''}
          splitPayment {
            id
            type
            targetAmount
            portions
            transactions {
              id
              splitPaymentUuid
              splitPayment {
                id
                transactions {
                  id
                }
              }
            }
          }
        }
      }
    `,
    variables: {
      transactionUuid,
      entityUuid,
    },
  });
  console.log(output);
  return output.data.getTransaction;
};

export const queryTransactionsFromAppsync = async (
  client: AWSAppSyncClient<any>,
  entityUuid: string,
  filter: any = { filter: { type: { eq: 'PURCHASE' } } },
) => {
  const output: any = await client.query({
    query: gql`
      query getTransactions($entityUuid: ID!, $filter: TransactionFilterInput) {
        getTransactions(entityUuid: $entityUuid, limit: 50, filter: $filter) {
          transactions {
            id
            entityUuid
            deviceUuid
            deviceName
            siteUuid
            siteName
            timestamp
            amount
            saleAmount
            tipAmount
            taxAmounts {
              name
              amount
            }
            feeAmount
            surchargeAmount
            scheme
            type
            channel
            source
            sourceFilter
            status
            standInStatus
            depositUuid
            depositShortId
            depositDate
            maskedPan
            reference
            notes
            lcr
            splitPaymentUuid
            splitPayment {
              id
              type
              targetAmount
              portions
              transactions {
                id
                splitPaymentUuid
                splitPayment {
                  id
                  transactions {
                    id
                  }
                }
              }
            }
          }
        }
      }
    `,
    variables: {
      entityUuid,
      filter,
    },
  });
  return output.data.getTransactions;
};

export const queryStandInMetricsFromAppsync = async (
  client: AWSAppSyncClient<any>,
  ascending: boolean,
  limit: number,
  orderBy: string,
  nextToken?: string,
) => {
  const output: any = await client.query({
    query: gql`
      query getStandInTransactionMetrics(
        $ascending: Boolean!
        $limit: Int!
        $nextToken: String
        $orderBy: StandInMetricsOrderField!
      ) {
        getStandInTransactionMetrics(ascending: $ascending, limit: $limit, nextToken: $nextToken, orderBy: $orderBy) {
          standInMetrics {
            averageTransactionLifetime
            entityName
            entityUuid
            gpvLifeTime
            outstanding30
            outstandingLifeTime
            standIn30
            standInLifeTime
          }
          nextToken
        }
      }
    `,
    variables: {
      ascending,
      limit,
      nextToken,
      orderBy,
    },
  });
  return output.data.getStandInTransactionMetrics;
};

export const queryGetStandInTransactionTotalsFromAppsync = async (
  client: AWSAppSyncClient<any>,
  filter: { timestamp: string; standInStatus?: string },
  totalsType: any = 'HOURLY',
) => {
  const output: any = await client.query({
    query: gql`
      query getStandInTransactionTotals($filter: StandInTransactionFilterInput!, $totalsType: StandInType!) {
        getStandInTransactionTotals(filter: $filter, totalsType: $totalsType) {
          timestamp
          amount
          count
          approvedAmount
          approvedCount
          declinedAmount
          declinedCount
        }
      }
    `,
    variables: {
      filter,
      totalsType,
    },
  });
  return output.data.getStandInTransactionTotals;
};

export const expectedAppsyncView = (requested?: any, initiated?: any, response?: any, updates: any = {}) => {
  const getFirstField = (field: string) => requested?.[field] ?? initiated?.[field] ?? response?.[field] ?? null;
  const getFieldBoolString = (field: string) => {
    const value = getFirstField(field);
    if (value === null) {
      return null;
    }

    return value ? 'true' : 'false';
  };
  const getFirstEmvField = (field: string) => requested?.emv?.[field] ?? initiated?.emv?.[field] ?? null;

  const amount = +(requested?.amount?.value ?? initiated?.amount?.value ?? 0);
  const tipAmount = +(requested?.tipAmount?.value ?? initiated?.tipAmount?.value ?? 0);
  const surchargeAmount = +(requested?.surchargeAmount?.value ?? initiated?.surchargeAmount?.value ?? 0);
  const saleAmount = amount - tipAmount - surchargeAmount;
  const taxAmounts = requested?.taxAmounts ?? initiated?.taxAmounts ?? [];

  const splitPayment = getFirstField('splitPayment');

  return {
    id: getFirstField('transactionUuid'),
    entityUuid: getFirstField('entityUuid'),
    deviceUuid: getFirstField('deviceUuid'),
    deviceName: null,
    deviceModel: null,
    siteUuid: getFirstField('siteUuid'),
    siteName: null,
    customerUuid: getFirstField('customerUuid'),
    customerName: null,
    timestamp: getFirstField('timestampUtc'),
    source: getFirstField('source'),
    channel: getFirstField('channel'),
    amount,
    saleAmount,
    tipAmount,
    surchargeAmount,
    taxAmounts: taxAmounts.map((tax: any) => ({
      name: tax.name,
      amount: +tax.amount.value,
    })),
    feeAmount: null,
    feeCharged: false,
    refundedAmount: null,
    scheme: getFirstField('scheme') ?? 'OTHER',
    type: getFirstField('type'),
    status: response?.responseType ?? TransactionStatus.PROCESSING,
    deposited: false,
    depositUuid: null,
    depositShortId: null,
    depositDate: null,
    maskedPan: getFirstField('panMasked'),
    reference: response?.rrn ?? null,
    review: null,
    refunded: false,
    reversed: false,
    cardholderUuid: getFirstField('cardholderUuid'),
    cardholderEmail: null,
    cardholderName: getFirstField('cardholderName'),
    cardholderPhone: null,
    bin: getFirstField('bin'),
    stan: getFirstField('stan'),
    caid: getFirstField('caid'),
    catid: getFirstField('catid'),
    cardMedia: getFirstField('cardMedia'),
    emvAid: getFirstEmvField('aid'),
    emvTagsPrint: getFirstEmvField('tagsPrint'),
    emvAppName: getFirstEmvField('appName'),
    isoProcessingCode: getFirstField('isoProcessingCode'),
    isoMessageType: getFirstField('isoMessageType'),
    approvalCode: response?.approvalCode ?? null,
    responseCode: response?.responseCode ?? null,
    responseDescription: response?.responseDescription ?? null,
    ksn: getFirstField('ksn'),
    isoPosEntryMode: getFirstField('isoPosEntryMode'),
    isoPosConditionCode: getFirstField('isoPosConditionCode'),
    panToken: getFirstField('panToken'),
    par: getFirstField('par'),
    cardFallback: getFirstField('cardFallback'),
    wasOfflineApproved: getFieldBoolString('wasOfflineApproved'),
    wasCommsFallback: getFieldBoolString('wasCommsFallback'),
    wasPinBypassed: getFieldBoolString('wasPinBypassed'),
    originalTransactionUuid: getFirstField('originalTransactionUuid'),
    emv: getFirstEmvField('additionalTags'),
    location: getFirstField('location'),
    emvTerminalType: getFirstEmvField('terminalType'),
    emvTerminalCapabilities: getFirstEmvField('terminalCapabilities'),
    emvAdditionalTerminalCapabilities: getFirstEmvField('additionalTerminalCapabilities'),
    emvTvrRequest: getFirstEmvField('tvrRequest'),
    emvTsiRequest: getFirstEmvField('tsiRequest'),
    emvCid: getFirstEmvField('cid'),
    emvCvmResult: getFirstEmvField('cvmResult'),
    emvAuthResponseCode: getFirstEmvField('authResponseCode'),
    emvTranCurrencyCode: getFirstEmvField('tranCurrencyCode'),
    emvTerminalCountryCode: getFirstEmvField('terminalCountryCode'),
    emvAppCurrencyCode: getFirstEmvField('appCurrencyCode'),
    emvIssuerCountryCode: getFirstEmvField('issuerCountryCode'),
    posName: getFirstField('posName'),
    externalReference: getFirstField('externalReference'),
    externalReferenceUrl: getFirstField('externalReferenceUrl'),
    notes: null,
    issuer: response?.issuer ?? null,
    standInStatus: null,
    refundedTransactionUuid: requested?.type === 'REFUND' ? requested?.originalTransactionUuid : null,
    refundedTransaction:
      requested?.type === 'REFUND'
        ? {
            deviceUuid: requested.deviceUuid ?? null,
            entityUuid: requested.entityUuid,
            id: requested?.originalTransactionUuid,
          }
        : null,
    refundTransactions: [],
    lcr: getFirstField('lcr'),
    splitPaymentUuid: getFirstField('splitPaymentUuid'),
    splitPayment: splitPayment
      ? {
          ...splitPayment,
          targetAmount: +splitPayment.targetAmount.value,
          transactions: [],
        }
      : null,
    refundReason: null,
    ...updates,
  };
};
