import { retry } from '@npco/bff-systemtest-utils/src/utils/retry';

import type AWSAppSyncClient from 'aws-appsync';
import gql from 'graphql-tag';
import { v4 } from 'uuid';

import { SdkApiTestHelper } from './sdkApiTestHelper';

describe('RBAC SDK Devices API System Test Suite ', () => {
  const apiTestHelper = new SdkApiTestHelper();
  let openIdClient: AWSAppSyncClient<any>;
  let deviceUuid: string;
  const entityUuid = apiTestHelper.getEntityUuid();

  beforeAll(async () => {
    await apiTestHelper.before();
    await apiTestHelper.createTestCustomer();
    await apiTestHelper.createDomicileValue();
    openIdClient = await apiTestHelper.getOpenIdClient();
    await apiTestHelper.sleep(1000);
  });

  beforeEach(async () => {
    deviceUuid = v4();
    await apiTestHelper.createTestIdentityCache({
      deviceUuid,
      entityUuid,
      customerUuid: apiTestHelper.getCustomerUuid(),
    });

    await apiTestHelper.createTestDeviceCache({
      deviceUuid,
      entityUuid,
    });
  });

  describe.each(['ADMIN', 'MANAGER'])(`RBAC device tests`, (data) => {
    beforeAll(async () => {
      console.log('rbac-device: beforeAll for role =>', data);
      await apiTestHelper.setTestCustomerAsRole(data);

      retry(async () => {
        const createdCustomerEntity = await apiTestHelper.queryItemWithType(
          apiTestHelper.getCustomerUuid(),
          `customer.entity.${apiTestHelper.getEntityUuid()}`,
        );
        expect(createdCustomerEntity?.Items?.length).toBe(1);
        expect(createdCustomerEntity?.Items?.[0].role).toBe(data);
      });
    });

    it(`should be able to call assignDeviceToEntity with ${data} role`, async () => {
      try {
        await openIdClient.mutate({
          mutation: gql`
            mutation assignDeviceToEntity($deviceUuid: ID!, $entityUuid: ID!, $name: String!) {
              assignDeviceToEntity(deviceUuid: $deviceUuid, entityUuid: $entityUuid, name: $name)
            }
          `,
          variables: {
            deviceUuid,
            entityUuid,
            name: v4(),
          },
        });
      } catch (error: any) {
        console.error(`rbac: ${data}: assignDeviceToEntity error:`, error);
        expect(error.graphQLErrors[0].errorType).not.toBe('UNAUTHORIZED_ERROR');
        expect(error.graphQLErrors[0].message).not.toBe('Insufficient permissions to perform the action');
      }
    });

    it(`should be able to call removeDeviceFromEntity with ${data} role`, async () => {
      try {
        // expecting error response in system-tests as we will receive `ECONNREFUSED 127.0.0.1:3000` error for AMS api calls
        await openIdClient.mutate({
          mutation: gql`
            mutation removeDeviceFromEntity($deviceUuid: ID!, $entityUuid: ID!) {
              removeDeviceFromEntity(deviceUuid: $deviceUuid, entityUuid: $entityUuid)
            }
          `,
          variables: {
            entityUuid,
            deviceUuid,
          },
        });
      } catch (error: any) {
        console.error(`rbac: ${data}: removeDeviceFromEntity error:`, error);
        expect(error.graphQLErrors[0].errorType).not.toBe('UNAUTHORIZED_ERROR');
        expect(error.graphQLErrors[0].message).not.toBe('Insufficient permissions to perform the action');
      }
    });

    it(`should be able to call getDeviceSettings with ${data} role`, async () => {
      try {
        await openIdClient.query({
          query: gql`
            query getDeviceSettings($deviceUuid: ID!) {
              getDeviceSettings(deviceUuid: $deviceUuid) {
                id
                entityUuid
                model
                serial
              }
            }
          `,
          variables: {
            deviceUuid,
          },
        });
      } catch (error: any) {
        console.error(`rbac: ${data}: getDeviceSettings error:`, error);
        expect(error.graphQLErrors[0].errorType).not.toBe('UNAUTHORIZED_ERROR');
        expect(error.graphQLErrors[0].message).not.toBe('Insufficient permissions to perform the action');
      }
    });
  });
});
