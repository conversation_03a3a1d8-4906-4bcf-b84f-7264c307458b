import { retry } from '@npco/bff-systemtest-utils/src/utils/retry';
import { Source, StandInField, StandInOperation } from '@npco/component-dto-core';
import type { DeviceCreatedEventDto, SiteDeviceSettings } from '@npco/component-dto-device';
import { PosMethod, PosMode, Theme, ZellerPosFavouriteType } from '@npco/component-dto-device';
import { type SiteCreatedEventDto, SiteType } from '@npco/component-dto-site';
import { CardScheme } from '@npco/component-dto-transaction';

import type AWSAppSyncClient from 'aws-appsync';
import gql from 'graphql-tag';
import { v4 } from 'uuid';

import { testIf } from '../testIf';

import { SdkApiTestHelper } from './sdkApiTestHelper';

describe('SDK Devices API System Test Suite', () => {
  const apiTestHelper = new SdkApiTestHelper();
  let openIdClient: AWSAppSyncClient<any>;

  const deviceSettings: DeviceCreatedEventDto = {
    deviceUuid: apiTestHelper.getDeviceUuid(),
    entityUuid: apiTestHelper.getEntityUuid(),
    siteUuid: apiTestHelper.getSiteUuid(),
    name: 'zeller device name 1',
    model: 'POS_INTERFACE',
    entity: {
      canAcquire: true,
      canAcquireMoto: true,
      canAcquireMobile: true,
      canRefund: true,
      canStandIn: true,
      domicile: 'AUS',
      currency: 'AUD',
    },
    serial: 'serial',
    terminalConfig: 'terminalConfig',
    emvConfig: 'emvConfig',
    emvTables: 'emvTables',
    emvCaKeys: 'emvCaKeys',
    geofencing: 'geofencing',
    receipt: {
      merchantCopy: true,
      name: 'String',
      address1: 'String',
      address2: 'String',
      number: 'String',
      phone: 'String',
      email: 'String',
      message: 'String',
      returnsMessage: 'String',
      website: 'String',
      logoMonochrome: 'logoMonochrome',
      instagram: 'String',
      facebook: 'String',
      twitter: 'String',
      logo: 'String',
      printLogo: false,
      printSocials: false,
      printDeclinedTransaction: false,
    },
    moto: {
      enabled: true,
      defaultEntryMethod: true,
      requiresPin: true,
    },
    surchargesTaxes: {
      surchargeEnabled: true,
      surchargePercent: 1,
      feePercent: 1,
      gstEnabled: true,
      gstPercent: 10,
      taxes: [{ percent: 2, name: 'GST' }],
      feePercentMoto: 100,
      surchargeFullFees: false,
      surchargeFullFeesMoto: false,
      surchargePercentMoto: 100,
      feesSurchargeCpoc: {
        surchargePercent: 2,
        feePercent: 2,
        surchargeFullFees: true,
        surchargeEnabled: true,
        feeFixed: 2,
      },
      // feesSurchargeCp: {},
      // feesSurchargeMoto: {},
      feesSurchargeXinv: {
        surchargePercent: 3,
        feePercent: 3,
        surchargeFullFees: true,
        surchargeEnabled: true,
        feeFixed: 3,
        feeFixedIntl: 3,
        feePercentIntl: 3,
        // surchargePercentIntl: 3,
      },
      feesSurchargeZinv: {
        surchargePercent: 4,
        feePercent: 4,
        surchargeFullFees: true,
        surchargeEnabled: true,
        feeFixed: 4,
        feeFixedIntl: 4,
        feePercentIntl: 4,
        // surchargePercentIntl: 4,
      },
    } as any,
    tipping: {
      customTipAllowed: true,
      enabled: true,
      tipPercent1: 5,
      tipPercent2: 4,
      tipPercent3: 3,
    },
    schemes: [{ name: CardScheme.AMEX }],
    schemesMoto: [{ name: CardScheme.MC }],
    standInRules: [
      {
        operation: StandInOperation.BELOW,
        field: StandInField.OFFLINE_AMOUNT,
        value: v4(),
      },
      {
        operation: StandInOperation.ABOVE,
        field: StandInField.TRANSACTION_AMOUNT,
        value: v4(),
      },
    ],

    screen: {
      sleepEnabled: true,
      sleepTimer: 1,
      standbyEnabled: true,
      standbyTimer: 1,
      brightness: 1,
      pinEntryTimer: 1,
      inactivityTimer: 1,
      communicationsTimer: 1,
      notHibernateWhenPluggedIn: false,
      theme: Theme.DARK_THEME,
    },
    network: {
      wifiEnabled: true,
      wifiSsid: '',
      cellularEnabled: true,
      cellularNetwork: 'string',
      ethernetEnabled: true,
    },

    posSettings: {
      posSoftwareName: 'Square',
      mode: PosMode.PAY_AT_TABLE,
      connectionMethod: PosMethod.POS_CONNECTOR,
      active: Source.IMPOS,
      port: 1,
      posReceipt: false,
      ipAddress: 'ipAddress',
      posVenue: {
        id: v4(),
        name: v4(),
        locations: [{ id: v4(), name: v4(), number: v4() }],
      },
      zellerPosSettings: {
        favourites: [
          { id: v4(), type: ZellerPosFavouriteType.ITEM },
          { id: v4(), type: ZellerPosFavouriteType.CATEGORY },
        ],
      },
    },
    features: {
      declineSoundEnabled: true,
      splitPaymentEnabled: false,
      restrictReportAccessEnabled: true,
    },
  };

  const createSiteCreatedDto = (update = {}): SiteCreatedEventDto => {
    const dto: SiteCreatedEventDto = {
      entityUuid: apiTestHelper.getEntityUuid(),
      siteUuid: apiTestHelper.getSiteUuid(),
      name: v4(),
      pin: v4(),
      refundRequiresPin: true,
      address: {
        street: 'street1',
        suburb: 'suburb',
        postcode: 'postcode',
      },
      type: SiteType.FIXED,
      ...update,
      surchargesTaxes: undefined as any,
    };
    return dto;
  };

  beforeAll(async () => {
    await apiTestHelper.before();
    await apiTestHelper.createTestCustomer();
    await apiTestHelper.createDomicileValue();
    openIdClient = await apiTestHelper.getOpenIdClient();

    retry(async () => {
      const createdCustomerEntity = await apiTestHelper.queryItemWithType(
        apiTestHelper.getCustomerUuid(),
        `customer.entity.${apiTestHelper.getEntityUuid()}`,
      );
      expect(createdCustomerEntity?.Items?.length).toBe(1);
    });
  });

  it('should get unauthorized error when device is not assigned to entity', async () => {
    const model = 'POS_INTERFACE';
    const serial = v4();
    
    try {
      const deviceUuid = deviceSettings.deviceUuid;

      await apiTestHelper.createTestDeviceCacheByModel({
        deviceUuid,
        model,
        serial,
      });

      await openIdClient.query({
        query: gql`
          query getDeviceSettings($deviceUuid: ID!) {
            getDeviceSettings(deviceUuid: $deviceUuid) {
              id
              entityUuid
              model
              serial
              name
              site {
                siteUuid
                name
                type
                screensaver {
                  primaryColour
                  primaryLogoUrl
                }
              }
              entity {
                canAcquire
                canRefund
                domicile
                currency
              }
            }
          }
        `,
        variables: {
          deviceUuid,
        },
      });
    } catch (error: any) {
      console.error('error', error);
      expect(error.graphQLErrors[0].errorType).toBe('UNAUTHORIZED_ERROR');
      expect(error.graphQLErrors[0].message).toBe('Device not assigned to entity');
    }
  });

  it('should be able connect to assignDeviceToEntity lambda which calls ams create device api to fail with an expected error', async () => {
    const assignDeviceToEntityInput = {
      name: v4(),
      entityUuid: apiTestHelper.getEntityUuid(),
      deviceUuid: v4(),
    };

    // create device and identity session cache without assigning to the current entity
    await apiTestHelper.createTestIdentityCache({
      deviceUuid: assignDeviceToEntityInput.deviceUuid,
      entityUuid: v4(),
      customerUuid: apiTestHelper.getCustomerUuid(),
    });

    await apiTestHelper.createTestDeviceCache({
      deviceUuid: assignDeviceToEntityInput.deviceUuid,
      entityUuid: v4(),
    });

    try {
      // expecting error response in system-tests as we will receive `ECONNREFUSED 127.0.0.1:3000` error for AMS api calls
      await openIdClient.mutate({
        mutation: gql`
          mutation assignDeviceToEntity($deviceUuid: ID!, $entityUuid: ID!, $name: String!) {
            assignDeviceToEntity(deviceUuid: $deviceUuid, entityUuid: $entityUuid, name: $name)
          }
        `,
        variables: {
          deviceUuid: assignDeviceToEntityInput.deviceUuid,
          entityUuid: apiTestHelper.getEntityUuid(),
          name: v4(),
        },
      });
    } catch (error: any) {
      console.error(error);
      expect(['SERVER_ERROR', 'INVALID_REQUEST']).toContain(error.graphQLErrors[0].errorType);
      expect([
        `Error assigning device to entity for the deviceId: ${assignDeviceToEntityInput.deviceUuid}`,
        `[400] Cant find requested device id ${assignDeviceToEntityInput.deviceUuid}`,
      ]).toContain(error.graphQLErrors[0].message);
    }
  });

  it('should be able connect to assignDeviceToEntity lambda which calls ams api to update device name', async () => {
    const assignDeviceToEntityInput = {
      name: v4(),
      entityUuid: apiTestHelper.getEntityUuid(),
      deviceUuid: v4(),
    };

    // create session cache with current entityUuid
    await apiTestHelper.createTestIdentityCache({
      deviceUuid: assignDeviceToEntityInput.deviceUuid,
      entityUuid: assignDeviceToEntityInput.entityUuid,
      customerUuid: apiTestHelper.getCustomerUuid(),
    });

    await apiTestHelper.createTestDeviceCache({
      deviceUuid: assignDeviceToEntityInput.deviceUuid,
      entityUuid: assignDeviceToEntityInput.entityUuid,
    });

    try {
      (await openIdClient.mutate({
        mutation: gql`
          mutation assignDeviceToEntity($deviceUuid: ID!, $entityUuid: ID!, $name: String!) {
            assignDeviceToEntity(deviceUuid: $deviceUuid, entityUuid: $entityUuid, name: $name)
          }
        `,
        variables: {
          deviceUuid: assignDeviceToEntityInput.deviceUuid,
          entityUuid: assignDeviceToEntityInput.entityUuid,
          name: v4(),
        },
      })) as any;
    } catch (error: any) {
      console.error(error);
      expect(['SERVER_ERROR', 'INVALID_REQUEST']).toContain(error.graphQLErrors[0].errorType);
      expect([
        // expected error in st env
        `Failed to update "device" with id: "${assignDeviceToEntityInput.deviceUuid}"`,

        // expected error in dev env
        `[400] Devices's id ${assignDeviceToEntityInput.deviceUuid} doesnt exist.`,
      ]).toContain(error.graphQLErrors[0].message);
    }
  });

  it('should be able to call removeDeviceFromEntity lambda which calls ams api service and fails with an expected error', async () => {
    const createdDeviceUuid = v4();
    const entityUuid = apiTestHelper.getEntityUuid();
    // assign device to entity in the cache
    await apiTestHelper.createTestIdentityCache({
      deviceUuid: createdDeviceUuid,
      entityUuid,
      customerUuid: apiTestHelper.getCustomerUuid(),
    });

    await apiTestHelper.createTestDeviceCache({
      deviceUuid: createdDeviceUuid,
      entityUuid,
    });
    try {
      // expecting error response in system-tests as we will receive `ECONNREFUSED 127.0.0.1:3000` error for AMS api calls
      await openIdClient.mutate({
        mutation: gql`
          mutation removeDeviceFromEntity($deviceUuid: ID!, $entityUuid: ID!) {
            removeDeviceFromEntity(deviceUuid: $deviceUuid, entityUuid: $entityUuid)
          }
        `,
        variables: {
          entityUuid,
          deviceUuid: createdDeviceUuid,
        },
      });
    } catch (error: any) {
      console.error(error);
      // System test env throws server error
      // Dev env throws invalid request error as AMS api throwing validation error
      console.log('error.graphQLErrors[0].errorType', error.graphQLErrors[0].errorType);
      console.log('error.graphQLErrors[0].message', error.graphQLErrors[0].message);
      expect(['SERVER_ERROR', 'INVALID_REQUEST']).toContain(error.graphQLErrors[0].errorType);
      expect([
        `Failed to delete "device" with id: "${createdDeviceUuid}"`,
        `[400] Cant find requested device id ${createdDeviceUuid}`,
      ]).toContain(error.graphQLErrors[0].message);
    }
  });

  describe('getDeviceSettings and getDevicesBySite graphql lambda handler test suite', () => {
    const getDeviceSettingsInput = {
      siteUuid: apiTestHelper.getSiteUuid(),
      entityUuid: apiTestHelper.getEntityUuid(),
      deviceUuid: deviceSettings.deviceUuid,
    };
    let assignedSiteSettings: SiteDeviceSettings;

    beforeAll(async () => {
      await apiTestHelper.createSite(
        createSiteCreatedDto({
          name: 'mockSite',
          screensaver: {
            primaryColour: 'red',
            primaryLogoUuid: 'redUuid',
            primaryLogoUrl: 'redUrl',
            logos: [
              {
                logoUuid: 'mockLogoUuid',
                url: 'mockScreensaverUrl',
              },
            ],
            customColours: ['silver'],
          },
        }),
      );

      await retry(async () => {
        const createdSite = await apiTestHelper.getDbItem(apiTestHelper.getSiteUuid());
        expect(createdSite?.siteUuid).toBe(apiTestHelper.getSiteUuid());
        assignedSiteSettings = { ...createdSite, name: createdSite!.siteName } as SiteDeviceSettings;
      });

      await Promise.all([
        apiTestHelper.createDevice({
          ...deviceSettings,
          siteUuid: assignedSiteSettings.siteUuid,
          site: assignedSiteSettings,
        }),
        apiTestHelper.createTestIdentityCache({
          deviceUuid: getDeviceSettingsInput.deviceUuid,
          entityUuid: getDeviceSettingsInput.entityUuid,
          customerUuid: apiTestHelper.getCustomerUuid(),
        }),
      ]);
    });

    it('should be able to call getDeviceSettings lambda', async () => {
      const result = await openIdClient.query({
        query: gql`
          query getDeviceSettings($deviceUuid: ID!) {
            getDeviceSettings(deviceUuid: $deviceUuid) {
              id
              entityUuid
              model
              serial
              name
              site {
                siteUuid
                name
                type
                screensaver {
                  primaryColour
                  primaryLogoUrl
                }
              }
              entity {
                canAcquire
                canRefund
                domicile
                currency
              }
            }
          }
        `,
        variables: {
          deviceUuid: getDeviceSettingsInput.deviceUuid,
        },
      });
      console.log(result);
      expect(result.data.getDeviceSettings).toMatchObject({
        id: deviceSettings.deviceUuid,
        entityUuid: deviceSettings.entityUuid,
        model: deviceSettings.model,
        serial: deviceSettings.serial,
        name: deviceSettings.name,
        site: {
          siteUuid: assignedSiteSettings.siteUuid,
          name: assignedSiteSettings.name,
          screensaver: {
            primaryColour: assignedSiteSettings.screensaver?.primaryColour,
            primaryLogoUrl: assignedSiteSettings.screensaver?.primaryLogoUrl,
          },
        },
        entity: {
          canAcquire: deviceSettings.entity?.canAcquire,
          canRefund: deviceSettings.entity?.canRefund,
          domicile: deviceSettings.entity?.domicile,
          currency: deviceSettings.entity?.currency,
        },
      });
    });

    testIf(apiTestHelper.getStage() === 'dev', 'should be able to call getDevicesBySite lambda', async () => {
      await retry(async () => {
        const result = await openIdClient.query({
          query: gql`
            query getSites($deviceUuid: ID!, $limit: Int!) {
              getSites(deviceUuid: $deviceUuid, limit: $limit) {
                sites {
                  id
                  devices {
                    id
                    entityUuid
                    model
                    serial
                    name
                    site {
                      siteUuid
                      name
                      type
                      screensaver {
                        primaryColour
                        primaryLogoUrl
                      }
                    }
                    entity {
                      canAcquire
                      canRefund
                      domicile
                      currency
                    }
                  }
                }
              }
            }
          `,
          variables: {
            deviceUuid: getDeviceSettingsInput.deviceUuid,
            limit: 10,
          },
        });
        console.log(result);
        expect(result.data.getSites.sites[0]).toMatchObject({
          id: apiTestHelper.getSiteUuid(),
          devices: [
            {
              id: deviceSettings.deviceUuid,
              entityUuid: deviceSettings.entityUuid,
              model: deviceSettings.model,
              serial: deviceSettings.serial,
              name: deviceSettings.name,
              site: null,
              entity: {
                canAcquire: deviceSettings.entity?.canAcquire,
                canRefund: deviceSettings.entity?.canRefund,
                domicile: deviceSettings.entity?.domicile,
                currency: deviceSettings.entity?.currency,
              },
            },
          ],
        });
      });
    });
  });
});
