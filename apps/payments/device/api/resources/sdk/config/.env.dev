# Project
COMPONENT_NAME=sdk
PART_NAME=api

# Auth
IDENTITY_AUTH0_AUDIENCE=https://sdk.myzeller.com
IDENTITY_AUTH0_TENANT=https://zeller-dev.au.auth0.com
OPENID_ISSUER_URL=https://auth.myzeller.dev/

# Env
LOG_LEVEL=debug
NODE_JS_RUNTIME=nodejs18.x
NODE_RUNTIME=18
STATIC_ENV_NAME=dev

# Table
COMPONENT_TABLE=Entities
SESSION_CACHE_TABLE=SessionCache
CACHE_MODEL_SERIAL_GSI=modelSerialGsi
SITE_GSI=siteGsi
DEVICE_GSI=deviceGsi

# Lambda
LAMBDA_TIMEOUT_IN_SECONDS=30
LAMBDA_MEMORY_DEFAULT=512

# VPC
VPC_ENV_NAME=dev

MULTI_ENTITY_ENABLED=true

# External API
AMS_API_ENDPOINT_VERSION=v2
DOMICILE_LOOKUP_ENABLED=true

# RBAC
IS_RBAC_ENFORCED=true
IS_RBAC_ENFORCE_ROLE=true


