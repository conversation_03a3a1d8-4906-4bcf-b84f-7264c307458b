service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-identity

plugins:
  - serverless-dotenv-plugin
  - serverless-esbuild
  - serverless-pseudo-parameters
  - serverless-plugin-resource-tagging
  - serverless-prune-plugin

useDotenv: true
variablesResolutionMode: ********
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  stackName: ${self:service}
  accountId: '#{AWS::AccountId}'
  vpc:
    securityGroupIds:
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg'
    subnetIds:
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01'
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02'
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03'
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  timeout: ${env:LAMBDA_TIMEOUT_IN_SECONDS}

  deploymentBucket:
    name: ${cf:${self:provider.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  dynamodbStackName: ${self:custom.serviceName}-dynamodb
  appsyncStackName: ${self:custom.service}-appsync
  deviceTableName: ${self:provider.dynamodbStackName}-${env:COMPONENT_TABLE}
  smscodeTableName: ${self:provider.dynamodbStackName}-${env:SMSCODE_TABLE}
  dbsCqrsStackName: ${opt:stage}-dbs-cqrs
  cqrsStackName:
    dev: '${opt:stage}-dbs-cqrs'
    staging: '${opt:stage}-dbs-cqrs'
    prod: '${opt:stage}-dbs-cqrs'
    st: '${opt:stage}-dbs-device-cqrs'
  dbsCqrsEventBus: '${cf:${self:provider.cqrsStackName.${opt:stage}, "${self:provider.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionArn}'
  dbsCqrsCommandHandler: ${self:provider.dbsCqrsStackName}-commandHandlers-handler
  cacheTableName: ${self:provider.dynamodbStackName}-${env:SESSION_CACHE_TABLE}
  accessTokenGsi: ${env:ACCESS_TOKEN_GSI}
  entityCacheGsi: ${env:ENTITY_CACHE_GSI}
  auth0Tenant: ${env:IDENTITY_AUTH0_TENANT}
  entityGsi: ${env:ENTITY_GSI}
  siteGsi: ${env:SITE_GSI}
  auth0Audience: ${env:IDENTITY_AUTH0_AUDIENCE}
  auth0IssuerUrl: ${env:OPENID_ISSUER_URL}
  smscodeTtl: ${env:SMSCODE_TTL}
  staticEnv: ${env:STATIC_ENV_NAME}
  iamUserKey: ${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_KEY}
  iamUserSecret: ${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_SECRET}
  appSyncEndpoint: ${self:custom.appSyncAutoGeneratedEndpoint}
  appSyncApiId:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiId'
  amsApiEndpoint: ${ssm:${opt:stage}-ams-engine-api-endpoint, ''}
  amsApiEndpointVersion: ${env:AMS_API_ENDPOINT_VERSION}
  auth0ClientId: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_ID}
  auth0ClientSecret: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_SECRET}
  serviceName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}
  service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1
    IS_RBAC_ENFORCED: ${env:IS_RBAC_ENFORCED}
    IS_RBAC_ENFORCE_ROLE: ${env:IS_RBAC_ENFORCE_ROLE}
  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

package:
  individually: true
  exclude:
    - node_modules/**

custom:
  esbuild:
    bundle: true
    keepNames: true
    plugins: esbuild_plugin.js
    exclude:
      - 'cache-manager'
      - 'class-transformer'
      - 'class-validator'
      - '@nestjs/microservices'
      - '@nestjs/websockets/socket-module'
      - '@nestjs/platform-express'
  prune:
    automatic: true
    includeLayers: true
    number: 5
  serviceName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}
  service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  appsyncDataSourceRoleArn: ${cf:${self:provider.appsyncStackName}.DataSourceLambdaRole}
  dbsCqrsProjectionDLQArn: '${cf:${self:provider.cqrsStackName.${opt:stage}, "${self:provider.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionDLQArn}'
  messageMediaApiKeySsmName: ${env:STATIC_ENV_NAME}-messagemedia-api-key
  messageMediaApiSecretSsmName: ${env:STATIC_ENV_NAME}-messagemedia-api-secret
  lambdaPararameterExtensionAccountId: '${ssm:Parameters-and-Secrets-Lambda-Extension}'
  appSyncEndpoints:
    dev: https://devices.myzeller.${opt:stage}/graphql
    staging: https://devices.myzeller.show/graphql
    prod: https://devices.myzeller.com/graphql
    st: ${self:custom.appSyncAutoGeneratedEndpoint}
  appSyncAutoGeneratedEndpoint:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiUrl'

  permissionsTableReadRolePolicyArn:
    Fn::ImportValue: '${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}-permissionsTableReadRolePolicyArn'

  domicileLookupTableReadRolePolicyArn:
    Fn::ImportValue: '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn'

functions:
  - ${file(resources/dbs/identity/lambdas.yml)}

resources:
  - ${file(resources/dbs/identity/resolvers.yml)}
  - ${file(resources/dbs/identity/iam.yml)}
  - ${file(resources/dbs/identity/eventBridgeRule.yml)}
