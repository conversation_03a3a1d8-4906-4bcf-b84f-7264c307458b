import { ApiAppServerlessStack, vpcImport } from '@npco/component-bff-serverless';
import { lambdas } from './resources/mp/lambdas';
import {
  publishDeviceSettingsUpdateEventDataSource,
  publishDeviceSettingsUpdateEventResolver,
} from './resources/mp/resolvers';
import { MpApiAppEnvConfig, pluginsApp, createEventBridgeProjectionRuleAndPermission } from './resources/common';

export const envConfig = new MpApiAppEnvConfig('./resources/mp/config', true);

envConfig.appsyncStackName = `${envConfig.service}-appsync` as const;

const sls = new ApiAppServerlessStack('devices', envConfig, {
  plugins: [...pluginsApp, 'serverless-plugin-lambda-dead-letter'],
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    ...envConfig.getAuth0(),
    ...envConfig.getEsbuild(),
    mpCqrsCommandHandler: envConfig.cqrsCommandHandler,
    sessionCacheTableName: envConfig.sessionCacheTableName,
    entityModelSerialGsi: '${env:ENTITY_MODEL_SERIAL_GSI}',
    accessTokenGsi: envConfig.accessTokenGsi,
    auth0Audience: envConfig.auth0Audience,
    modelSerialGsi: envConfig.modelSerialGsi,
    siteGsi: envConfig.siteGsi,
    entityCacheGsi: envConfig.entityCacheGsi,
    entityGsi: envConfig.entityGsi,
    vpcImport,
    cqrsStackName: {
      dev: '${opt:stage}-mp-cqrs',
      staging: '${opt:stage}-mp-cqrs',
      prod: '${opt:stage}-mp-cqrs',
      st: '${opt:stage}-mp-device-cqrs',
    },
    cqrsSqsArn: '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-sqs.QueueARN}',
    cqrsSqsUrl: '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-sqs.QueueURL}',
    cqrsEventBus:
      '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionArn}',
    mpCqrsProjectionDLQArn:
      '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionDLQArn}',
    firebaseAdminPrivateKeySsmName:
      '/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY',
    firebaseAdminEmailSsmName:
      '/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/ZELLER_APP_FIREBASE_ADMIN_EMAIL',
    zellerAppAuth0ClientId: `\${ssm:/${envConfig.serviceName}/ZELLER_APP_AUTH0_CLIENT_ID}`,
    streamArn: {
      'Fn::ImportValue': '${self:custom.dynamodbStackName}-entityTableStreamArn',
    },
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
  },
  functions: lambdas,
  resources: {
    ...createEventBridgeProjectionRuleAndPermission(
      'deviceSettingsProjectionHandler',
      'DeviceSettingsProjectionHandlerLambdaFunction',
      ['mp.Device.Created', 'mp.Device.Updated', 'mp.Device.InformationUpdated'],
    ),
    publishDeviceSettingsUpdateEventDataSource,
    publishDeviceSettingsUpdateEventResolver,
  },
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    CQRS_COMMAND_HANDLER: '${self:custom.mpCqrsCommandHandler}',
    IS_ZELLER_SESSION_ID_ENABLED: '${env:IS_ZELLER_SESSION_ID_ENABLED}',
    SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    MULTI_ENTITY_ENABLED: '${env:MULTI_ENTITY_ENABLED}',
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}',
  },
});

console.log(sls.build().custom);

module.exports = { ...sls.build(), variablesResolutionMode: '********' };
