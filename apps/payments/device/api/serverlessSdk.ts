import { ApiAppServerlessStack, ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless';

import { pluginsApp } from './resources/common';
import { SdkApiAppEnvConfig } from './resources/common/sdkConfig';
import { getSdkLambdas } from './resources/sdk/lambdas';

export const envConfig = new SdkApiAppEnvConfig('./resources/sdk/config', true);

const sls = new ApiAppServerlessStack('device', envConfig, {
  plugins: pluginsApp,
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAppsync(),
    ...envConfig.getAmsApi(),
    vpcImport,
    dependsOn: {
      enabled: false,
      chains: 5,
    },
    multiEntityEnabled: '${env:MULTI_ENTITY_ENABLED}',
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
  },
  functions: getSdkLambdas(envConfig),
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
    SESSION_CACHE_TABLE: envConfig.sessionCacheTableName,
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
    AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}',
  },
});

const built = sls.build();
console.log('SLS Build Log', JSON.stringify(built, null, 2));

module.exports = built;
