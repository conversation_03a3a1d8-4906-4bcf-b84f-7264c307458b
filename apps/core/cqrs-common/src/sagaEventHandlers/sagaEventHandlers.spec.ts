import '../testcases/mocks';
import { unmarshall } from '@aws-sdk/util-dynamodb';
import type { EventBridgeEvent } from 'aws-lambda';
import { forceRewireUriMap } from 'events/forceUriRemapper';
import { v4 as uuidv4 } from 'uuid';

import { getCqrsTestEvents } from '../events/testcases/testHelpers';
import { getEvent } from '../testcases/utils';
import type { Payload } from '../types';

import { getAggregateId, sagaEventHandler, sagaEventHandlerService } from './lambdas';

jest.mock('../common/eventStoreService');

const ecstEmittedEvents: string[] = [
  'pgs.Transaction.Approved',
  'pgs.CnpTransaction.Approved',
  'pgs.CnpTransaction.Declined',
  'pgs.CpocTransaction.Approved',
  'banking.TransactionAccount.Closed',
  'pgs.IncompleteTransaction.MockDeclined',
  'fs2.Site.FeeRatesEffected',
  'fs2.Site.FeeRatesAssigned',
  'stlmts.Deposit.Created',
  'stlmts.Fee.AccumulatedUpdated',
  'ecst.stlmts.Fee.AccumulatedUpdated',
  'ecst.stlmts.Deposit.GpvUpdated',
  'stlmts.Fee.Created',
  'ecst.stlmts.Fee.AccumulatedUpdated',
  'stlmts.DepositBatch.Failed',
  'stlmts.DepositBatch.Completed',
  'stlmts.DepositBatch.Submitted',
  'stlmts.DepositBatch.SkippedFromSubmission',
  'stlmts.FeeBatch.Failed',
  'stlmts.FeeBatch.Completed',
  'stlmts.FeeBatch.Submitted',
  'stlmts.FeeBatch.SkippedFromSubmission',
  'stlmts.TransactionBatch.LinkedToDeposit',
];

/**
 * EntityMetric.Updated is ignored as it is a special case where the aggregateId is a combination of entityId and metrics
 * Contact.Linked and Contact.Unlinked are ignored as they use an internal aggregateId of the linking contact
 */
const ignoreEvents = [
  'EntityMetric.Updated',
  'TransactionBatch.FeeAdjusted',
  'Contact.Linked',
  'Contact.Unlinked',
  'stlmts.DepositBatch.Submitted',
  'stlmts.DepositBatch.Completed',
  'stlmts.DepositBatch.Failed',
  'stlmts.TransactionBatch.LinkedToDeposit',
];

const customEventTests = [
  'stlmts.DepositBatch.Submitted',
  'stlmts.DepositBatch.Failed',
  'stlmts.DepositBatch.Completed',
  'stlmts.TransactionBatch.LinkedToDeposit',
];

const baseEventBridgeEvent: EventBridgeEvent<string, Payload> = {
  version: '0',
  id: '',
  account: '',
  region: '',
  source: '',
  time: '',
  resources: [],
  'detail-type': '',
  detail: {} as Payload,
};

describe('saga lambda test suite', () => {
  let backupEnv: any;
  beforeAll(() => {
    backupEnv = process.env;
    process.env.COMPONENT_NAME = 'mp';
    process.env.ENVIRONMENT = 'test';
    process.env.API_RESPONSE_STACK_NAME = 'mp-api';
    process.env.AWS_REGION = 'ap-southeast-2';
  });

  beforeEach(() => {
    jest.resetModules();
  });

  afterAll(() => {
    process.env = backupEnv;
  });

  it('should get aggregateId', () => {
    expect(getAggregateId({ detail: { aggregateId: 'agg' } } as unknown as EventBridgeEvent<string, Payload>)).toEqual(
      'agg',
    );
  });

  describe.each(['mp'])('component %s', (component: any) => {
    const buildTests = () => {
      const testEventUris = getCqrsTestEvents('saga') as any[];
      return customEventTests
        .concat(testEventUris)
        .filter((uri) => !ignoreEvents.some((e) => uri.includes(e)) && !uri.startsWith('ecst.'));
    };

    test.each(buildTests())('should be able to handle event %p', async (uri: any) => {
      await new Promise((resolve, reject) => {
        const aggregateId = uuidv4();
        sagaEventHandlerService.componentName = component;
        const [source, aggregate, action] = uri.split('.');
        const sourceOrAms = source === component ? 'ams' : source;
        sagaEventHandler(
          {
            ...baseEventBridgeEvent,
            'detail-type': uri,
            source: sourceOrAms,
            detail: {
              aggregateId,
              payload: {
                entityUuid: uuidv4(),
              },
            },
          },
          {} as any,
          async (error: any) => {
            try {
              if (error?.message.includes('Domain event builder not found')) {
                return resolve(null);
              }
              expect(error).toBeNull();
              const eventStore = await getEvent(component, aggregateId);
              expect(eventStore).toBeDefined();
              const newV2Uri = forceRewireUriMap[source]?.[aggregate]?.[action];
              expect(unmarshall(eventStore)).toEqual({
                eventId: expect.any(String),
                aggregateId,
                sequenceNo: 1,
                payload: expect.any(Object),
                createdTimestamp: expect.any(Number),
                emitEcst: ecstEmittedEvents.includes(`${aggregate}.${action}`) || ecstEmittedEvents.includes(uri),
                name: action,
                version: expect.any(Number),
                uri: newV2Uri ? `${component}.${newV2Uri}` : `${component}.${aggregate}.${action}`,
                source: sourceOrAms,
                timestamp: expect.any(String),
                ...(['DocumentVerificationRequested', 'DocumentVerificationAndSafeHarbourRequested'].includes(action)
                  ? { ttl: expect.any(Number) }
                  : undefined),
                aggregate,
                sourceRegion: process.env.AWS_REGION,
              });
              return resolve(null);
            } catch (e) {
              console.error(uri, e);
              reject(e);
              throw e;
            }
          },
        );
      });
    });
  });
});
