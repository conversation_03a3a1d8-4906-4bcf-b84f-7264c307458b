import { debug, error } from '@npco/component-bff-core/dist/utils/logger';
import type { IPersistentEvent } from '@npco/component-domain-events/dist/persistentEvent.interface';
import { AwsEventBridgeEventProxy } from '@npco/component-events-proxy';

import type { EventBridgeEvent } from 'aws-lambda';

import { BaseCqrsEventHandler } from '../../common/baseCqrsEventHandler';
import { createDomainEventInstance } from '../../common/createDomainEventInstance';
import type { EventStoreService } from '../../common/eventStoreService';
import { forceRewireUriMap } from '../../events/forceUriRemapper';
import type { Payload } from '../../types';

export class SagaEventHandlerService extends BaseCqrsEventHandler<EventBridgeEvent<string, Payload>> {
  constructor(private readonly eventStore: EventStoreService) {
    super('sagaEvents');
  }

  handleEvent = async (eventBridgeEventData: EventBridgeEvent<string, Payload>): Promise<void> => {
    debug(`SagaEventHandler:event ${JSON.stringify(eventBridgeEventData)}}`);

    const { source } = eventBridgeEventData;

    const sagaDomainEvent = this.buildDomainEvent(eventBridgeEventData);

    if (source === this.componentName) {
      error(
        `Event source is the same as the component ${JSON.stringify(eventBridgeEventData)}`,
        sagaDomainEvent.aggregateId,
      );
      return;
    }

    const { cqrsEventConfig } = this.getCqrsDomainEventConfig(sagaDomainEvent.uri);
    if (!cqrsEventConfig) {
      error(`Cqrs event handler not found ${sagaDomainEvent.uri}`);
      throw new Error(`Cqrs event handler not found ${sagaDomainEvent.uri}`);
    }

    const dto = this.getDtoPayload(sagaDomainEvent, cqrsEventConfig);
    debug(`SagaEventHandler:dto ${JSON.stringify(dto)}`);

    const uri = this.localiseUriFromEvent(source, sagaDomainEvent);

    const domainEvent = createDomainEventInstance(
      dto,
      uri,
      cqrsEventConfig,
      sagaDomainEvent.metadata?.mutationAttribution,
    );
    debug(`sagaEventHandler:storeEvent ${JSON.stringify(domainEvent)}`);

    await this.eventStore.storeEvent({ ...domainEvent, uri, source });
  };

  private readonly buildDomainEvent = (eventBridgeEventData: EventBridgeEvent<string, Payload>) => {
    const awsEventBridgeEventProxy = new AwsEventBridgeEventProxy(eventBridgeEventData);
    const isEcst = eventBridgeEventData['detail-type'].startsWith('ecst');
    const domainEvent: IPersistentEvent<any> = isEcst
      ? awsEventBridgeEventProxy.toEcstEvent()
      : awsEventBridgeEventProxy.toDomainEvent();
    debug(`domain event:${JSON.stringify(domainEvent)}`, domainEvent.aggregateId);
    return domainEvent;
  };

  private readonly localiseUriFromEvent = (originalSource: string, sagaDomainEvent: IPersistentEvent<any>): string => {
    const { aggregate, name } = this.getUriFromEvent(sagaDomainEvent.uri);
    // Support v2 of events
    const rewiredUri = forceRewireUriMap[originalSource]?.[aggregate]?.[name];
    if (rewiredUri) {
      const [aggregateV2, nameV2] = rewiredUri.split('.');
      return `${this.componentName}.${aggregateV2}.${nameV2}`;
    }
    return `${this.componentName}.${aggregate}.${name}`;
  };
}
