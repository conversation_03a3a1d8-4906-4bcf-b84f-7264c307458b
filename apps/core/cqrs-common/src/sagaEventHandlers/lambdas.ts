import { loggerAggregateMetadataMiddleware } from '@npco/component-bff-core/dist/middleware/loggerAggregateMetadataMiddleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';

import type { EventBridgeEvent } from 'aws-lambda';

import { EventStoreService } from '../common/eventStoreService';
import type { Payload } from '../types';

import { ProjectionSagaEventHandlerService } from './services/projectionSagaEventHandlerService';
import { SagaEventHandlerService } from './services/sagaEventHandlerService';

export const getAggregateId = (event: EventBridgeEvent<string, Payload>) => event.detail.aggregateId;

export const sagaEventHandlerService = new SagaEventHandlerService(new EventStoreService());

export const sagaEventHandler = withMiddlewaresV2(
  { eventType: LambdaEventSource.INVOKED },
  (event: EventBridgeEvent<string, Payload>) => sagaEventHandlerService.handleEvent(event),
  [
    loggerAggregateMetadataMiddleware((event: any) => {
      const eventBridgeDomainEvent: EventBridgeEvent<string, Payload> = event as EventBridgeEvent<string, Payload>;

      const metadata: Record<string, any> = {
        eventUri: eventBridgeDomainEvent['detail-type'],
      };

      if (eventBridgeDomainEvent.detail?.payload?.entityUuid) {
        metadata.entityUuid = eventBridgeDomainEvent.detail.payload.entityUuid;
      }

      return {
        aggregateId: getAggregateId(eventBridgeDomainEvent),
        metadata,
      };
    }),
  ],
  {
    getAggregateId,
  },
);

const cmsProjectionSagaEventService = new ProjectionSagaEventHandlerService(new EventStoreService());

export const cmsProjectionSagaEventHandler = withMiddlewaresV2(
  { eventType: LambdaEventSource.INVOKED },
  (event: any) => cmsProjectionSagaEventService.handleProjectionEvent(event),
  [],
  {
    getAggregateId,
  },
);
