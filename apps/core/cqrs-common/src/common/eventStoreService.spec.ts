import AWSXray from 'aws-xray-sdk-core';

import { EventStoreService } from './eventStoreService';

jest.mock('aws-xray-sdk-core', () => ({
  captureAWSv3Client: jest.fn(),
}));

const mockStoreEvent = jest.fn();

jest.mock('@npco/component-domain-events/dist/eventStore/eventStore.dynamodb', () => {
  return {
    EventStore: jest.fn(() => ({
      storeEvent: () => mockStoreEvent(),
    })),
  };
});

describe('event store provider test suite', () => {
  it('should be able to create event store', async () => {
    process.env.LAMBDA_TASK_ROOT = undefined;
    const eventStoreProvider = new EventStoreService();
    expect(eventStoreProvider.eventStore).not.toBeUndefined();
    expect(AWSXray.captureAWSv3Client).toHaveBeenCalledTimes(1);
  });

  it('should be able to enable aws xray in lambda', async () => {
    process.env.LAMBDA_TASK_ROOT = 'LAMBDA';
    const eventStoreProvider = new EventStoreService();
    await eventStoreProvider.storeEvent({} as any);
    expect(eventStoreProvider.eventStore).not.toBeUndefined();
    expect(AWSXray.captureAWSv3Client).toHaveBeenCalledTimes(1);
    expect(mockStoreEvent).toHaveBeenCalledTimes(1);
  });

  it('should be able to handle error', async () => {
    mockStoreEvent.mockImplementation(() => {
      throw new Error('Test error');
    });
    const eventStoreProvider = new EventStoreService();
    await expect(eventStoreProvider.storeEvent({} as any)).rejects.toThrow('Test error');
  });
});
