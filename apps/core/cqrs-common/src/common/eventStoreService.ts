import { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { error } from '@npco/component-bff-core/dist/utils/logger';
import type { IEventStore } from '@npco/component-domain-events/dist/eventStore/eventStore.interface';

import { EventBridgeClient } from '@aws-sdk/client-eventbridge';
import { NodeHttpHandler } from '@smithy/node-http-handler';

import { EventStore } from './eventStore.dynamodb';

export class EventStoreService {
  eventStore: IEventStore;

  componentName: string;

  environment: string;

  isInLambda: boolean;

  eventBusName: string;

  constructor() {
    this.componentName = process.env.COMPONENT_NAME ?? '';
    this.environment = process.env.ENVIRONMENT ?? '';
    this.isInLambda = !!process.env.LAMBDA_TASK_ROOT;
    this.eventBusName = process.env.GLOBAL_EVENT_BUS_NAME ?? `${this.environment}-eventBus-global`;
    this.eventStore = new EventStore(
      this.environment,
      this.componentName,
      DynamodbService.getInstance(),
      new EventBridgeClient({
        requestHandler: new NodeHttpHandler({
          requestTimeout: 5000,
          connectionTimeout: 2000,
        }),
        maxAttempts: 3,
      }),
      this.getEventBusName(),
      {
        persistEcst: false,
        publishEcstViaDbstream: false,
      },
    );
  }

  async storeEvent<T>(event: any): Promise<void> {
    try {
      await this.eventStore.storeEvent<T>(event, true);
    } catch (err) {
      error(`EventStoreService:Failed to store event: ${JSON.stringify(event)}`, event.aggregateId);
      throw err;
    }
  }

  private readonly getEventBusName = () => this.eventBusName;
}
