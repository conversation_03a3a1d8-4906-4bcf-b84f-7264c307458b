{"name": "cqrs-common", "version": "1.0.0", "scripts": {"lint": "eslint src", "test": "jest --no-cache", "system:test": "jest --no-cache --config tests/jest.config.ts", "deploy": "bin/deploy.sh", "destroy": "bin/destroy.sh", "package": "serverless package --config $SLS_CONFIG --region ap-southeast-2 --stage", "deploy:stack": "serverless deploy --config $SLS_CONFIG --region ap-southeast-2 --stage", "destroy:stack": "serverless remove --config $SLS_CONFIG --region ap-southeast-2 --stage", "update:cqrs": "sh ./bin/update-cqrs.sh", "run-audit": "yarn npm audit --environment production"}, "dependencies": {"@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-eventbridge": "3.435.0", "@aws-sdk/client-kms": "3.435.0", "@aws-sdk/client-lambda": "3.435.0", "@aws-sdk/client-s3": "3.435.0", "@aws-sdk/client-secrets-manager": "3.435.0", "@aws-sdk/client-sqs": "3.435.0", "@aws-sdk/client-ssm": "3.435.0", "@aws-sdk/lib-dynamodb": "3.435.0", "@aws-sdk/util-dynamodb": "3.435.0", "@npco/component-bff-core": "workspace:*", "@npco/component-cms-events": "^1.1.9", "@npco/component-domain-events": "15.3.28", "@npco/component-domain-events-types": "^0.0.3", "@npco/component-dto-addressbook": "workspace:*", "@npco/component-dto-cnp": "workspace:*", "@npco/component-dto-core": "workspace:*", "@npco/component-dto-cpoc": "workspace:*", "@npco/component-dto-customer": "workspace:*", "@npco/component-dto-deposit": "workspace:*", "@npco/component-dto-device": "workspace:*", "@npco/component-dto-digital-wallet-token": "workspace:*", "@npco/component-dto-entity": "workspace:*", "@npco/component-dto-issuing-account": "workspace:*", "@npco/component-dto-issuing-card": "workspace:*", "@npco/component-dto-issuing-transaction": "workspace:*", "@npco/component-dto-issuing-transaction-statement": "workspace:*", "@npco/component-dto-merchant": "workspace:*", "@npco/component-dto-promotion": "workspace:*", "@npco/component-dto-scheduled-transfer": "workspace:*", "@npco/component-dto-site": "workspace:*", "@npco/component-dto-stlmts": "workspace:*", "@npco/component-dto-subscription": "workspace:*", "@npco/component-dto-transaction": "workspace:*", "@npco/component-events-proxy": "workspace:*", "@npco/component-fs2-events-v3": "3.0.1", "@npco/component-stlmts-events": "^4.0.7", "@npco/cqrs-interface": "workspace:*", "@smithy/node-http-handler": "^2.1.8", "ais-cqrs": "workspace:*", "ams-cqrs": "workspace:*", "att-cqrs": "workspace:*", "aws-xray-sdk-core": "^3.5.1", "ce-cqrs": "workspace:*", "cims-cqrs": "workspace:*", "cnp-cqrs": "workspace:*", "cpi-cqrs": "workspace:*", "crms-cqrs": "workspace:*", "dbs-cqrs": "workspace:*", "dotenv": "^16.0.3", "ers-cqrs": "workspace:*", "hlpos-cqrs": "workspace:*", "ims-cqrs": "workspace:*", "mp-cqrs": "workspace:*", "nms-cqrs": "workspace:*", "oraclepos-cqrs": "workspace:*", "os-cqrs": "workspace:*", "posconnector-cqrs": "workspace:*", "reps-cqrs": "workspace:*", "sdk-cqrs": "workspace:*", "sis-cqrs": "workspace:*", "sms-cqrs": "workspace:*", "uuid": "^8.3.2", "zpos-cqrs": "workspace:*"}, "devDependencies": {"@npco/component-bff-domain-events": "workspace:*", "@npco/component-events-ais": "workspace:*", "@npco/component-events-ams": "workspace:*", "@npco/component-events-cims": "workspace:*", "@npco/component-events-cnp": "workspace:*", "@npco/component-events-core": "workspace:*", "@npco/component-events-crms": "workspace:*", "@npco/component-events-dbs": "workspace:*", "@npco/component-events-ims": "workspace:*", "@npco/component-events-mp": "workspace:*", "@npco/component-events-sms": "workspace:*", "@npco/component-events-ss": "workspace:*", "@npco/eslint-config-backend": "^1.0.8", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.1", "@shelf/jest-dynamodb": "^3.4.2", "@smithy/protocol-http": "^3.0.4", "@smithy/types": "^2.3.2", "@swc/core": "^1.3.59", "@swc/jest": "^0.2.29", "@types/aws-lambda": "^8.10.115", "@types/jest": "^29.5.11", "@types/mocha": "^8.0.1", "@types/node": "^13.7.7", "@types/serverless": "^3.12.22", "@types/uuid": "^9.0.0", "esbuild": "^0.25.0", "eslint": "^8.56.0", "husky": "^4.2.5", "improved-yarn-audit": "^3.0.0", "jest": "^29.7.0", "jest-html-reporter": "^3.10.2", "jest-junit": "^16.0.0", "jest-sonar-reporter": "^2.0.0", "nx": "20.7.0", "serverless": "^3.39.0", "serverless-create-global-dynamodb-table-tags": "^1.0.2", "serverless-dotenv-plugin": "^4.0.2", "serverless-esbuild": "^1.52.1", "serverless-plugin-canary-deployments": "^0.8.0", "serverless-plugin-lambda-dead-letter": "^1.2.1", "serverless-plugin-resource-tagging": "^1.2.0", "serverless-plugin-scripts": "^1.0.2", "serverless-plugin-tracing": "^2.0.0", "serverless-prune-plugin": "^2.0.2", "serverless-stack-output": "^0.2.3", "ts-jest": "^29.1.1", "ts-mockito": "^2.6.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "jestSonar": {"reportPath": "dist", "reportFile": "test-reporter.xml", "indent": 2}, "prettier": "@npco/eslint-config-backend/prettier"}