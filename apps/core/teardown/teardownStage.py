#!/usr/bin/env python
import sys
from destroy import Destroy
from multiprocessing import Pool
import time
import re
import boto3
from component_list import components

cloudformation = boto3.client("cloudformation")

stackStatus = [
    "CREATE_FAILED",
    "CREATE_COMPLETE",
    "ROLLBACK_FAILED",
    "ROLLBACK_COMPLETE",
    "DELETE_FAILED",
    "UPDATE_COMPLETE",
    "UPDATE_FAILED",
    "UPDATE_ROLLBACK_FAILED",
    "UPDATE_ROLLBACK_COMPLETE",
]


# st1234
stEnvRegex = re.compile(r"^st[0-9]{4,5}-")


def execute_destroy(componentPart):
    result = componentPart.split("-")
    componentName = result[1]
    partName = result[2]
    stage = result[0]
    print("Destroying:", f"{stage}-{componentName}-{partName}")
    destroy = Destroy(stage, componentName, partName, False)
    destroy.destroy_common_api_engine()
    time.sleep(2)


def getStackName(stack):
    try:
        isSt = stEnvRegex.match(stack["StackName"])
        if not isSt:
            return None
        componentName = stack["StackName"].split("-")[1]
        if componentName in components:
            return stack["StackName"]
    except Exception as ex:
        print(ex)
        print("Error getting stack name:", stack["StackName"])
    return None


def listStacksAllForSystemTest():
    arr = []
    paginator = cloudformation.get_paginator("list_stacks")
    page_iterator = paginator.paginate(StackStatusFilter=stackStatus)
    for page in page_iterator:
        stackNames = page["StackSummaries"]
        for stack in stackNames:
            stackName = getStackName(stack)
            if stackName:
                arr.append(stackName)
    return arr


partNames = ["engine", "api", "cqrs"]


def getServiceNameForStack(stage, stackName):
    if not stackName.startswith("st"):
        return None

    componentParts = stackName.split("-")

    env = componentParts[0]
    if env != stage:
        return None

    componentName = componentParts[1]
    if componentName not in components:
        print("Unknown component:", componentName)
        return None

    if componentParts[2] in partNames:
        return f"{env}-{componentName}-{componentParts[2]}"
    elif componentParts[3] in partNames:
        return f"{env}-{componentName}-{componentParts[3]}"

    return None


def getComponents(stage, stacks):
    services = []
    for stackName in stacks:
        if (
            stackName.startswith("dev-")
            or stackName.startswith("staging-")
            or stackName.startswith("prod-")
        ):
            continue
        serviceName = getServiceNameForStack(stage, stackName)
        if serviceName:
            services.append(serviceName)

    return services


if __name__ == "__main__":

    if len(sys.argv) < 2:
        print("Environment argument is required.")
        sys.exit(1)

    environment = sys.argv[1]

    if environment in ["dev", "staging", "prod"]:
        sys.exit(1)

    print("Teardown for stage:", environment)

    stacks = listStacksAllForSystemTest()
    components = getComponents(environment, stacks)

    print("components to delete:", components)
    if not components:
        print("No components found for the specified environment.")
        sys.exit(0)
    with Pool(processes=4) as pool:  # Cloudformation starts to rate limit
        pool.map(execute_destroy, components)

    print("All components destroyed successfully.")
