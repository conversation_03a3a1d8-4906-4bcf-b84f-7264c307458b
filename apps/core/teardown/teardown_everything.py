#!/usr/bin/env python
import sys
from destroy import Destroy
from multiprocessing import Pool
import time
import re
from datetime import date, timedelta
import boto3
from component_list import components

cloudformation = boto3.client("cloudformation")

stackStatus = [
    "CREATE_FAILED",
    "CREATE_COMPLETE",
    "ROLLBACK_FAILED",
    "ROLLBACK_COMPLETE",
    "DELETE_FAILED",
    "UPDATE_COMPLETE",
    "UPDATE_FAILED",
    "UPDATE_ROLLBACK_FAILED",
    "UPDATE_ROLLBACK_COMPLETE",
]


DAYS_FROM_NOW = 14

# st1234
stEnvRegex = re.compile(r"^st[0-9]{4,5}-")


def execute_destroy(componentPart):
    result = componentPart.split("-")
    componentName = result[1]
    partName = result[2]
    stage = result[0]
    print("Destroying:", f"{stage}-{componentName}-{partName}")
    destroy = Destroy(stage, componentName, partName, False)
    destroy.destroy_common_api_engine()
    time.sleep(2)


def confirm(prompt_text):
    while True:
        answer = input(prompt_text).lower()
        if answer in ["y", "yes"]:
            return True
        elif answer in ["n", "no"]:
            return False
        else:
            print("Please enter 'y' or 'n'.")


def listStacksAllForSystemTest(dateCreatedBefore=None):
    today = date.today()
    if not dateCreatedBefore:
        day = today - timedelta(days=DAYS_FROM_NOW)
        dateCreatedBefore = day.isoformat()
    print("Date created before: " + dateCreatedBefore)
    arr = []
    paginator = cloudformation.get_paginator("list_stacks")
    page_iterator = paginator.paginate(StackStatusFilter=stackStatus)
    for page in page_iterator:
        stackNames = page["StackSummaries"]
        for stack in stackNames:
            try:
                isSt = stEnvRegex.match(stack["StackName"])
                creationTime = stack["CreationTime"].isoformat()
                componentName = stack["StackName"].split("-")[1]
                if (
                    isSt
                    and creationTime < dateCreatedBefore
                    and componentName in components
                ):
                    arr.append(stack["StackName"])
            except Exception as ex:
                print(ex)
                print("Error getting stack name:", stack["StackName"])
                continue
    return arr


def getComponentEnvironments(stacks):
    environments = []
    for stackName in stacks:
        if (
            stackName.startswith("dev-")
            or stackName.startswith("staging-")
            or stackName.startswith("prod-")
        ):
            continue
        if stackName.startswith("st"):
            componentParts = stackName.split("-")
            env = componentParts[0]
            componentName = componentParts[1]
            if componentName not in components:
                print("Unknown component:", componentName)
                continue
            partNameA = componentParts[2]
            partNameB = componentParts[3]
            if partNameA in ["engine", "api", "cqrs"]:
                partName = partNameA
            elif partNameB in ["engine", "api", "cqrs"]:
                partName = partNameB
            if partName:
                serviceName = f"{env}-{componentName}-{partName}"
                if serviceName not in environments:
                    environments.append(serviceName)


if __name__ == "__main__":
    dateFrom = sys.argv[1] if len(sys.argv) > 1 else None
    stacks = listStacksAllForSystemTest(dateFrom)
    environments = getComponentEnvironments(stacks)

    print("Environments to delete:", environments)

    if confirm("Delete envs? (y/n): "):
        print("Deleting envs...")
        with Pool(processes=4) as pool:  # Cloudformation starts to rate limit
            pool.map(execute_destroy, environments)

        print("Envs deleted.")
        sys.exit(0)
    else:
        print("Nothing deleted.")
