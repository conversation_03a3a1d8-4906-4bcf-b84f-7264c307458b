import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import { debug, error, info } from '@npco/component-bff-core/dist/utils/logger';
import type { DomainURIValue } from '@npco/component-dto-core';
import type {
  FundTransferInput,
  CreateTransferScheduleInput,
  ScheduledTransferCancelledDto,
  ScheduledTransferSkippedDto,
  ScheduledTransferUnSkippedDto,
  SkipScheduledTransferInput,
  UnSkipScheduledTransfersInput,
  UpdateTransferScheduleInput,
  TransferScheduleOutput,
  ScheduledTransferCreatedDto,
  ScheduledTransferUpdatedDto,
  TransferDetails,
  SkipScheduledTransferResponse,
  UnSkipScheduledTransferResponse,
  RetryScheduleTransferResponse,
  CancelScheduledTransferResponse,
  Bpay,
  NppOut,
  ScheduledTransferCompletedDto,
  RecurringInterval,
} from '@npco/component-dto-scheduled-transfer';
import {
  ScheduledTransferType,
  PayeeType,
  PayeeTypeToTransferTypeMapping,
  ScheduledTransferStatus,
} from '@npco/component-dto-scheduled-transfer';

import { Injectable } from '@nestjs/common';
import type { ProjectionDto } from 'services/projections/types';
import type { EntityManager, QueryRunner, Repository } from 'typeorm';
import { getRepository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';

import { EnvironmentService } from '../../config';
import {
  PaymentInstrumentBpay,
  PaymentInstrumentBsb,
  SkipUnskipTransferExecution,
  TransferExecutionStatus,
  TransferSchedule,
} from '../../entities';
import { BaseService } from '../base/baseService';
import { BadRequestError, NotFoundError, ServerError } from '../base/error';

import { CbsService } from './cbs/cbsService';
import type {
  CbsCreateScheduledTransferInput,
  CbsCreateScheduledTransferResponse,
  CbsUpdateScheduledTransferResponse,
} from './cbs/inputTypes';
import {
  buildBpayTransfer,
  buildExternalTransfer,
  buildInternalTransfer,
  buildResponse,
  insertTransfersSchedule,
  validateInput,
} from './common';

@Injectable()
export class ScheduledTransferService extends BaseService<TransferSchedule> {
  constructor(
    protected readonly envService: EnvironmentService,
    protected readonly lambdaService: LambdaService,
    protected readonly cbsService: CbsService,
  ) {
    super('TransferSchedule', 'scheduledTransferUuid', lambdaService, envService);
  }

  getRepository = (): Repository<TransferSchedule> => getRepository(TransferSchedule);

  getScheduledSkipExecutionRepository = (): Repository<SkipUnskipTransferExecution> =>
    getRepository(SkipUnskipTransferExecution);

  getPaymentInstrumentBsbRepository = (): Repository<PaymentInstrumentBsb> => getRepository(PaymentInstrumentBsb);

  getPaymentInstrumentBpayRepository = (): Repository<PaymentInstrumentBpay> => getRepository(PaymentInstrumentBpay);

  updateDBEntity = (mgr: EntityManager, aggregateField: any, object: any) =>
    mgr.update(TransferSchedule, aggregateField, object);

  isLockUpdateRequired = () => false;

  /**
   * Creates a scheduled transfer.
   * @param input - The input containing the transfer details.
   * @returns A promise that resolves to an object containing the created scheduled transfer details.
   */
  createScheduledTransfers = async (input: CreateTransferScheduleInput): Promise<TransferScheduleOutput> => {
    debug(`createScheduledTransfers: ${JSON.stringify(input)}`, input.entityUuid);

    validateInput(input, 'createScheduledTransfer');

    const { cbsInput, paymentInstrument } = await this.buildAndValidateTransfer(input);
    debug(`cbsInput: ${JSON.stringify(cbsInput)}`, input.entityUuid);
    try {
      const apiResponse = await this.cbsService.createScheduledTransfer(cbsInput);
      return await this.handleCbsResponse<TransferScheduleOutput>(
        apiResponse,
        async () => {
          await this.handleSuccessScheduledTransferCreation(
            apiResponse.data,
            paymentInstrument.contactUuid ?? undefined,
          );
          return this.buildResponse(apiResponse.data, input, paymentInstrument);
        },
        `Scheduled transfer`,
      );
    } catch (err: any) {
      error(`Error creating scheduled transfer: ${err.message}`, input.entityUuid);
      throw err;
    }
  };

  /**
   * Updates a scheduled transfer.
   * @param input - The input containing the updated transfer details.
   * @param scheduledTransferUuid - The UUID of the scheduled transfer to update.
   * @returns A promise that resolves to an object containing the updated scheduled transfer details.
   */
  updateScheduledTransfers = async (input: UpdateTransferScheduleInput, scheduledTransferUuid: string) => {
    debug(
      `updateScheduledTransfers: ${JSON.stringify(input)}, scheduledTransferUuid: ${scheduledTransferUuid}`,
      scheduledTransferUuid,
    );

    validateInput({ ...input, scheduledTransferUuid }, 'updateScheduledTransfer');

    const persistedScheduleTransfer = await this.findTransferSchedule(scheduledTransferUuid);

    const { cbsInput: updatedData, paymentInstrument } = await this.prepareCbsInput(input, persistedScheduleTransfer);

    debug(`cbsInput: ${JSON.stringify(updatedData, null, 2)}`);
    try {
      const apiResponse = await this.cbsService.updateScheduledTransfer(
        {
          transferType: updatedData.transferType,
          schedule: updatedData.schedule,
          transfer: updatedData.transfer,
        },
        scheduledTransferUuid,
      );

      return await this.handleCbsResponse<TransferScheduleOutput>(
        apiResponse,
        async () => {
          await this.handleSuccessScheduledTransferUpdation(
            apiResponse.data,
            updatedData,
            paymentInstrument?.contactUuid ?? undefined,
          );
          return this.buildResponse(apiResponse.data, input, paymentInstrument);
        },
        `Scheduled transfer with id ${scheduledTransferUuid}`,
      );
    } catch (err: any) {
      error(`Error updating scheduled transfer: ${err.message}`, scheduledTransferUuid);
      throw err;
    }
  };

  /**
   * Skips a scheduled transfer.
   * @param input - The input containing the scheduled transfer UUID and skip date/time.
   * @returns A promise that resolves to an object containing the scheduled transfer UUID, isSkipped status, and skip date/time.
   */
  skipScheduledTransfers = async (input: SkipScheduledTransferInput): Promise<SkipScheduledTransferResponse> => {
    debug(`skipScheduledTransfers: ${JSON.stringify(input)}`);
    const { scheduledTransferUuid, skipDateTime } = input;

    validateInput({ scheduledTransferUuid, skipDateTime }, 'skipScheduledTransfer');

    const persistedScheduleTransfer = await this.findTransferSchedule(scheduledTransferUuid);

    try {
      const apiResponse = await this.cbsService.skipScheduledTransfer(
        {
          date: skipDateTime,
        },
        scheduledTransferUuid,
      );

      return await this.handleCbsResponse<SkipScheduledTransferResponse>(
        apiResponse,
        async () => {
          await this.handleSuccessScheduledTransferSkip(input, persistedScheduleTransfer);
          return { scheduledTransferUuid, isSkipped: true, skipDateTime };
        },
        `Scheduled transfer with id ${scheduledTransferUuid}`,
      );
    } catch (err: any) {
      error(`Error skipping scheduled transfer: ${err.message}`, input.scheduledTransferUuid);
      throw err;
    }
  };

  /**
   * Unskips a scheduled transfer.
   * @param input - The input containing the scheduled transfer UUID and execution UUID.
   * @returns A promise that resolves to an object containing the scheduled transfer UUID, execution UUID, and isSkipped status.
   */
  unSkipScheduledTransfers = async (input: UnSkipScheduledTransfersInput): Promise<UnSkipScheduledTransferResponse> => {
    debug(`unSkipScheduledTransfers: ${JSON.stringify(input)}`);
    const { scheduledTransferUuid, scheduledTransferExecutionUuid } = input;

    validateInput({ scheduledTransferUuid, scheduledTransferExecutionUuid }, 'unSkipScheduledTransfer');

    const persistedTransferExecution = await this.findTransferExecution(scheduledTransferExecutionUuid);

    try {
      const apiResponse = await this.cbsService.unSkipScheduledTransfer(
        {
          scheduledTransferExecutionUuid,
        },
        scheduledTransferUuid,
      );
      return await this.handleCbsResponse<UnSkipScheduledTransferResponse>(
        apiResponse,
        async () => {
          await this.handleSuccessScheduledTransferUnSkip(input, persistedTransferExecution);
          return { scheduledTransferUuid, isSkipped: false, scheduledTransferExecutionUuid };
        },
        `Scheduled transfer with id ${scheduledTransferUuid}`,
      );
    } catch (err: any) {
      error(`Error unskipping scheduled transfer: ${err.message}`, input.scheduledTransferUuid);
      throw err;
    }
  };

  retryScheduledTransfers = async (input: {
    scheduledTransferExecutionUuid: string;
  }): Promise<RetryScheduleTransferResponse> => {
    debug(`retryScheduledTransfers: ${JSON.stringify(input)}`);

    const { scheduledTransferExecutionUuid } = input;
    const persistedTransferExecution = await this.findTransferExecution(scheduledTransferExecutionUuid);

    try {
      const apiResponse = await this.cbsService.retryScheduledTransfer(
        persistedTransferExecution.scheduledTransferUuid,
        scheduledTransferExecutionUuid,
      );
      return await this.handleCbsResponse<RetryScheduleTransferResponse>(
        apiResponse,
        async () => {
          return {
            scheduledTransferUuid: persistedTransferExecution.scheduledTransferUuid,
            scheduledTransferExecutionUuid,
            isSuccess: true,
          };
        },
        `Scheduled transfer with id ${persistedTransferExecution.scheduledTransferUuid}`,
      );
    } catch (err: any) {
      error(`Error retrying scheduled transfer: ${err.message}`, input.scheduledTransferExecutionUuid);
      throw err;
    }
  };

  /**
   * Cancels a scheduled transfer.
   * @param input - The input containing the scheduled transfer UUID.
   * @returns A promise that resolves to an object containing the scheduled transfer UUID and isCancelled status.
   */
  cancelScheduledTransfers = async (input: { scheduledTransferUuid: string }) => {
    debug(`cancelScheduledTransfers: ${JSON.stringify(input)}`);
    const { scheduledTransferUuid } = input;

    const persistedScheduleTransfer = await this.findTransferSchedule(scheduledTransferUuid);

    try {
      const apiResponse = await this.cbsService.cancelScheduledTransfer(scheduledTransferUuid);

      return await this.handleCbsResponse<CancelScheduledTransferResponse>(
        apiResponse,
        async () => {
          await this.handleSuccessScheduledTransferCancel(persistedScheduleTransfer);
          return { id: scheduledTransferUuid, isCancelled: true };
        },
        `Scheduled transfer with id ${scheduledTransferUuid}`,
      );
    } catch (err: any) {
      error(`Error cancelling scheduled transfer: ${err.message}`, input.scheduledTransferUuid);
      throw err;
    }
  };

  public async processScheduledTransferProjectionEvent(
    event: ProjectionDto<ScheduledTransferCompletedDto>,
  ): Promise<void> {
    debug(`processScheduledTransferEvent: ${JSON.stringify(event)}`);
    const [, , aggregateEvent] = event.uri.split('.');
    if (aggregateEvent === 'completed') {
      const eventTime = parseInt(event.createdTimestamp, 10);
      info(`event time: ${eventTime}`);
      await this.processScheduledTransferCompletedEvent(event);
    }
  }

  private async emitCommandEvent<T>(id: string, command: DomainURIValue, payload: T): Promise<void> {
    await this.callCommandHandler<T>(id, command, payload);
  }

  private readonly handleSuccessScheduledTransferCreation = async (
    cbsResponse: CbsCreateScheduledTransferResponse,
    contactUuid?: string,
  ) => {
    const { id, customerUuid, entityUuid, status, accountUuid, transferType, transfer, schedule } = cbsResponse;
    const timestamp = Date.now();
    try {
      const transferScheduleEntity = this.getRepository().create({
        scheduledTransferUuid: id,
        accountUuid,
        entityUuid,
        customerUuid,
        transferType,
        status,
        transfer,
        schedule,
        createdTime: `${Math.floor(timestamp / 1000)}`,
        updatedTime: `${Math.floor(timestamp / 1000)}`,
        domicile: this.getDomicile(),
      });
      await insertTransfersSchedule({
        ...transferScheduleEntity,
        repository: this.getRepository(),
        domicile: this.getDomicile(),
        captureCallback: this.captureCallback,
      });
    } catch (err: any) {
      error(`Error persisting scheduled transfer to DB: ${err.message}`);
      throw err;
    }

    await this.emitCommandEvent<ScheduledTransferCreatedDto>(
      cbsResponse.id,
      this.envService.cqrsCmds.ScheduledTransfer.Created,
      {
        ...cbsResponse,
        timestamp,
        nextTransferDate: cbsResponse.nextTransferDate,
        transferDetails: cbsResponse.transfer,
        customerUuid: cbsResponse.customerUuid as string,
        contactUuid,
      },
    );
  };

  private readonly handleSuccessScheduledTransferUpdation = async (
    cbsResponse: CbsUpdateScheduledTransferResponse,
    updatedData: Partial<TransferSchedule>,
    contactUuid?: string,
  ) => {
    const { id } = cbsResponse;
    try {
      const updateTransfersScheduleCallback = () =>
        this.getRepository()
          .save({
            ...updatedData,
            scheduledTransferUuid: id,
            domicile: this.getDomicile(),
            updatedTime: `${Math.floor(Date.now() / 1000)}`,
          })
          .catch((e) => {
            error(e);
            throw new ServerError(`Unable to save schedule transfers`);
          });
      await this.captureCallback('update-transfer-schedule', id, updateTransfersScheduleCallback);
    } catch (err: any) {
      error(`Error persisting scheduled transfer to DB: ${err.message}`);
      throw err;
    }

    this.emitCommandEvent<ScheduledTransferUpdatedDto>(id, this.envService.cqrsCmds.ScheduledTransfer.Updated, {
      ...cbsResponse,
      timestamp: Number(updatedData.createdTime as string) * 1000,
      transferDetails: cbsResponse.transfer,
      customerUuid: cbsResponse.customerUuid as string,
      description: updatedData.description,
      contactUuid,
    });
  };

  private readonly handleSuccessScheduledTransferSkip = async (
    input: SkipScheduledTransferInput,
    scheduleTransfer: TransferSchedule,
  ): Promise<void> => {
    const { scheduledTransferUuid, skipDateTime } = input;
    const id = uuidv4();
    try {
      const transferExecutionEntity = this.getScheduledSkipExecutionRepository().create({
        id,
        scheduledTransferUuid,
        accountUuid: scheduleTransfer.accountUuid,
        entityUuid: scheduleTransfer.entityUuid,
        status: TransferExecutionStatus.SKIPPED,
        scheduledTransferSkipDateTime: skipDateTime,
        createdTime: `${Math.floor(Date.now() / 1000)}`,
        updatedTime: `${Math.floor(Date.now() / 1000)}`,
        domicile: this.getDomicile(),
      });

      const insertTransfersScheduleCallback = () =>
        this.getScheduledSkipExecutionRepository()
          .insert({ ...transferExecutionEntity })
          .catch((e) => {
            error(e);
            throw new ServerError(`Unable to save transfers executions`);
          });
      await this.captureCallback(
        'update-transfer-schedule',
        transferExecutionEntity.id,
        insertTransfersScheduleCallback,
      );
    } catch (err: any) {
      error(`Error persisting scheduled transfer to DB: ${err.message}`);
      throw err;
    }

    await this.callCommandHandler<ScheduledTransferSkippedDto>(
      scheduledTransferUuid,
      this.envService.cqrsCmds.ScheduledTransfer.Skipped,
      {
        id: scheduledTransferUuid,
        entityUuid: scheduleTransfer.entityUuid,
        scheduledTransferSkipDateTime: skipDateTime,
      },
    );
  };

  private readonly handleSuccessScheduledTransferUnSkip = async (
    input: UnSkipScheduledTransfersInput,
    transferExecution: SkipUnskipTransferExecution,
  ): Promise<void> => {
    const { scheduledTransferUuid, scheduledTransferExecutionUuid } = input;
    try {
      const transferExecutionEntity = this.getScheduledSkipExecutionRepository().create({
        id: transferExecution.id,
        scheduledTransferUuid,
        scheduledTransferExecutionUuid,
        accountUuid: transferExecution.accountUuid,
        entityUuid: transferExecution.entityUuid,
        status: TransferExecutionStatus.UNSKIPPED,
        scheduledTransferSkipDateTime: transferExecution.scheduledTransferSkipDateTime,
        createdTime: transferExecution.createdTime,
        updatedTime: `${Math.floor(Date.now() / 1000)}`,
        domicile: this.getDomicile(),
      });

      const insertTransfersScheduleCallback = () =>
        this.getScheduledSkipExecutionRepository()
          .save({ ...transferExecutionEntity })
          .catch((e) => {
            error(e);
            throw new ServerError(`Unable to update transfers executions`);
          });
      await this.captureCallback(
        'update-transfer-schedule',
        transferExecutionEntity.id,
        insertTransfersScheduleCallback,
      );
    } catch (err: any) {
      error(`Error persisting scheduled transfer to DB: ${err.message}`);
      throw err;
    }

    await this.callCommandHandler<ScheduledTransferUnSkippedDto>(
      scheduledTransferUuid,
      this.envService.cqrsCmds.ScheduledTransfer.UnSkipped,
      {
        id: scheduledTransferUuid,
        entityUuid: transferExecution.entityUuid,
        scheduledTransferSkipDateTime: transferExecution.scheduledTransferSkipDateTime,
        scheduledTransferExecutionUuid: transferExecution.scheduledTransferExecutionUuid!,
      },
    );
  };

  private readonly handleSuccessScheduledTransferCancel = async (transferSchedule: TransferSchedule): Promise<void> => {
    try {
      const transferScheduleEntity = this.getRepository().create({
        ...transferSchedule,
        status: ScheduledTransferStatus.CANCELLED,
        updatedTime: `${Math.floor(Date.now() / 1000)}`,
        domicile: this.getDomicile(),
      });

      const insertTransfersScheduleCallback = () =>
        this.getRepository()
          .save({ ...transferScheduleEntity })
          .catch((e) => {
            error(e);
            throw new ServerError(`Unable to save transfers executions`);
          });
      await this.captureCallback(
        'cancel-transfer-schedule',
        transferSchedule.scheduledTransferUuid,
        insertTransfersScheduleCallback,
      );
    } catch (err: any) {
      error(`Error persisting scheduled transfer to DB: ${err.message}`);
      throw err;
    }

    await this.callCommandHandler<ScheduledTransferCancelledDto>(
      transferSchedule.scheduledTransferUuid,
      this.envService.cqrsCmds.ScheduledTransfer.Cancelled,
      {
        id: transferSchedule.scheduledTransferUuid,
        entityUuid: transferSchedule.entityUuid,
        status: ScheduledTransferStatus.CANCELLED,
      },
    );
  };

  private readonly buildResponse = async (
    cbsResponse: CbsCreateScheduledTransferResponse,
    input: Partial<CreateTransferScheduleInput>,
    paymentInstrument: PaymentInstrumentBpay | PaymentInstrumentBsb,
  ): Promise<TransferScheduleOutput> => {
    return buildResponse(cbsResponse, input, paymentInstrument);
  };

  private async findTransferSchedule(scheduledTransferUuid: string): Promise<TransferSchedule> {
    const transfer = await this.getRepository().findOne({
      scheduledTransferUuid,
      domicile: this.getDomicile(),
    });
    debug(`findTransferSchedule: ${JSON.stringify(transfer)}`, scheduledTransferUuid);
    if (!transfer) {
      throw new BadRequestError(`Scheduled transfer with id ${scheduledTransferUuid} not found in DB`);
    }
    return transfer;
  }

  private async findTransferExecution(scheduledTransferExecutionUuid: string): Promise<SkipUnskipTransferExecution> {
    const execution = await this.getScheduledSkipExecutionRepository().findOne({
      scheduledTransferExecutionUuid,
      domicile: this.getDomicile(),
    });
    debug(`findTransferExecution: ${JSON.stringify(execution)}`, scheduledTransferExecutionUuid);
    if (!execution) {
      throw new BadRequestError(
        `Scheduled transfer execution with id ${scheduledTransferExecutionUuid} not found in DB`,
      );
    }
    return execution;
  }

  private async findPaymentInstrumentBpay(
    payeeAccountUuid: string,
    entityUuid: string,
  ): Promise<PaymentInstrumentBpay> {
    const paymentInstrumentBpay = await this.getPaymentInstrumentBpayRepository().findOne({
      paymentInstrumentUuid: payeeAccountUuid,
      entityUuid,
      domicile: this.getDomicile(),
    });
    debug(`findPaymentInstrumentBpay: ${JSON.stringify(paymentInstrumentBpay)}`, payeeAccountUuid);
    if (!paymentInstrumentBpay) {
      throw new BadRequestError(`Payment instrument with id ${payeeAccountUuid} not found in DB`);
    }
    return paymentInstrumentBpay;
  }

  private async findPaymentInstrumentBsb(payeeAccountUuid: string, entityUuid: string): Promise<PaymentInstrumentBsb> {
    const paymentInstrumentBsb = await this.getPaymentInstrumentBsbRepository().findOne({
      paymentInstrumentUuid: payeeAccountUuid,
      entityUuid,
      domicile: this.getDomicile(),
    });
    debug(`findPaymentInstrumentBsb: ${JSON.stringify(paymentInstrumentBsb)}`, payeeAccountUuid);
    if (!paymentInstrumentBsb) {
      throw new BadRequestError(`Payment instrument with id ${payeeAccountUuid} not found in DB`);
    }
    return paymentInstrumentBsb;
  }

  private async handleCbsResponse<T>(
    apiResponse: any,
    successCallback: () => Promise<T>,
    errorMessage: string,
  ): Promise<T> {
    const { status, data } = apiResponse;
    switch (status) {
      case 200:
        return successCallback();
      case 404:
        error(`${errorMessage}: Not found in CBS`);
        throw new NotFoundError(`CBS Error: ${JSON.stringify(data)}`);
      case 400:
        error(`${errorMessage}: Bad request to CBS service: ${JSON.stringify(data)}`);
        throw new BadRequestError(`CBS Error: ${JSON.stringify(data)}`);
      default:
        error(`${errorMessage}: Unexpected status code from CBS service ${status}`, '', data);
        throw new ServerError(`CBS Error: ${JSON.stringify(data)}`);
    }
  }

  private async buildAndValidateTransfer(input: CreateTransferScheduleInput): Promise<{
    cbsInput: CbsCreateScheduledTransferInput;
    paymentInstrument: PaymentInstrumentBpay | PaymentInstrumentBsb;
  }> {
    const { entityUuid, payeeType, transfer: transferInput, schedule, customerUuid, description } = input;
    const intervalType = schedule.type as RecurringInterval;
    const paymentInstrument = await this.getPaymentInstrument(input);

    let transferDetails: { transfer: TransferDetails; transferType: ScheduledTransferType };
    switch (payeeType) {
      case PayeeType.INTERNAL:
        transferDetails = buildInternalTransfer(transferInput);
        break;
      case PayeeType.BPAY:
        transferDetails = buildBpayTransfer(
          transferInput,
          paymentInstrument as PaymentInstrumentBpay,
          intervalType,
          customerUuid,
        );
        break;
      case PayeeType.EXTERNAL:
        transferDetails = buildExternalTransfer(transferInput, paymentInstrument as PaymentInstrumentBsb);
        break;
      default:
        throw new BadRequestError(`Unsupported payeeType: ${payeeType}`);
    }

    return {
      cbsInput: {
        ...transferDetails,
        schedule,
        entityUuid,
        accountUuid: transferInput.payerAccountUuid,
        customerUuid,
        description,
      },
      paymentInstrument,
    };
  }

  private async prepareCbsInput(
    input: UpdateTransferScheduleInput,
    scheduleTransfer: TransferSchedule,
  ): Promise<{
    paymentInstrument: PaymentInstrumentBpay | PaymentInstrumentBsb;
    cbsInput: CbsCreateScheduledTransferInput;
  }> {
    const { entityUuid, transfer } = input;
    const isNewTransfer = !!transfer;
    const schedule = input.schedule ?? scheduleTransfer.schedule;
    const payeeType = input.payeeType ?? PayeeTypeToTransferTypeMapping.get(scheduleTransfer.transferType);
    if (!payeeType) {
      throw new Error(`Invalid transferType: ${scheduleTransfer.transferType}`);
    }

    // Fetch the payment instrument
    const paymentInstrument = isNewTransfer
      ? await this.getPaymentInstrument({ entityUuid, payeeType, transfer })
      : await this.getPaymentInstrumentByTransferType(scheduleTransfer);

    // Build the CBS input
    const cbsData = isNewTransfer
      ? await this.buildAndValidateTransfer({ ...input, payeeType, schedule } as CreateTransferScheduleInput) // NOSONAR
      : {
          cbsInput: {
            entityUuid: scheduleTransfer.entityUuid,
            accountUuid: scheduleTransfer.accountUuid,
            transferType: scheduleTransfer.transferType,
            schedule: input.schedule ?? scheduleTransfer.schedule,
            transfer: scheduleTransfer.transfer,
            customerUuid: scheduleTransfer.customerUuid,
            description: input.description ?? scheduleTransfer.description,
          },
        };

    return { paymentInstrument, cbsInput: cbsData.cbsInput };
  }

  private async getPaymentInstrument(input: {
    entityUuid: string;
    payeeType: PayeeType;
    transfer: FundTransferInput;
  }): Promise<PaymentInstrumentBpay | PaymentInstrumentBsb> {
    const { payeeType, transfer } = input;

    if (payeeType === PayeeType.BPAY) {
      return this.findPaymentInstrumentBpay(transfer.payeeAccountUuid, input.entityUuid);
    }
    if (payeeType === PayeeType.EXTERNAL) {
      return this.findPaymentInstrumentBsb(transfer.payeeAccountUuid, input.entityUuid);
    }
    return {} as PaymentInstrumentBpay | PaymentInstrumentBsb;
  }

  private async getPaymentInstrumentByTransferType(
    scheduleTransfer: TransferSchedule,
  ): Promise<PaymentInstrumentBpay | PaymentInstrumentBsb> {
    const { entityUuid, transferType } = scheduleTransfer;

    if (transferType === ScheduledTransferType.BPAY) {
      const transfer = scheduleTransfer.transfer as Bpay;
      return this.findPaymentInstrumentBpay(transfer.paymentInstrumentUuid, entityUuid);
    }
    if (transferType === ScheduledTransferType.NPP_OUT) {
      const transfer = scheduleTransfer.transfer as NppOut;
      return this.findPaymentInstrumentBsb(transfer.recipientUuid, entityUuid);
    }
    if (transferType === ScheduledTransferType.TRANSFER_OUT) {
      return {} as PaymentInstrumentBpay | PaymentInstrumentBsb;
    }
    throw new BadRequestError(`Unsupported transfer type: ${transferType}`);
  }

  private readonly processScheduledTransferCompletedEvent = async (
    event: ProjectionDto<ScheduledTransferCompletedDto>,
  ) => {
    const { payload } = event;
    const saveScheduleTransferCompleted = async (queryRunner: QueryRunner) => {
      debug(`saveScheduleTransferCompleted: ${JSON.stringify(payload)}`, payload.id);
      await queryRunner.manager.getRepository(TransferSchedule).update(
        { scheduledTransferUuid: payload.id, entityUuid: payload.entityUuid, domicile: this.getDomicile() },
        {
          status: payload.status,
          updatedTime: `${Math.floor(Date.now() / 1000)}`,
          domicile: this.getDomicile(),
        },
      );

      info(`emit Scheduled transfer completed event`, payload.id);
      this.emitCommandEvent<ScheduledTransferCompletedDto>(
        payload.id,
        this.envService.cqrsCmds.ScheduledTransfer.Completed,
        {
          id: payload.id,
          entityUuid: payload.entityUuid,
          status: payload.status,
        },
      );
    };

    await this.captureTransaction('save-transfer-schedule', payload.id, saveScheduleTransferCompleted);
  };
}
