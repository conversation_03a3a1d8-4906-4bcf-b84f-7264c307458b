import { MutationAttributionService } from '@npco/component-bff-core/dist/attribution/mutationAttributionService';
import { LambdaService } from '@npco/component-bff-core/dist/lambda';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import * as Logger from '@npco/component-bff-core/dist/utils/logger';
import {
  AmexAcquisitionStatus,
  CustomerRole,
  DomainURImap,
  EntityType,
  ISO4217,
  MutationAttributionPlatform,
  MutationAttributionTokenGrant,
  MutationAttributionUserRole,
  PaymentType,
  ScreeningRequestedType,
  ScreeningStatus,
  StandInField,
  StandInOperation,
  Status,
} from '@npco/component-dto-core';
import { CustomerDocumentVerificationResult } from '@npco/component-dto-customer';
import type { CustomerCreateRequestedEventDto } from '@npco/component-dto-customer';
import type {
  EntityAmexMerchantSubmissionRequestedDto,
  EntityOnboardingStatusUpdatedEventDto,
  EntityCreateRequestedEventDto,
} from '@npco/component-dto-entity';
import {
  EntityUpdatedEventDto,
  OnboardingStatus,
  RiskReviewResult,
  RiskReviewStatus,
  EntityGqlCreateRequestedEventDto,
} from '@npco/component-dto-entity';

import { mockDomicile } from 'lambdas/testcases/testUtils';
import { PosInterfaceCleanService } from 'services/posInterface/posInterfaceCleanService';
import { anything, instance, mock, when } from 'ts-mockito';
import type { Repository } from 'typeorm';
import { v4 } from 'uuid';

import { EnvironmentService } from '../../config';
import type { Customer, CustomerEntity, Entity, Referral } from '../../entities';
import { BadRequestError } from '../base/error';
import { ContactService } from '../contact/contactService';
import { CustomerService } from '../customer/customerService';
import * as productUtils from '../customer/productTourUtils';
import { CustomerEntityService } from '../customerEntity/customerEntityService';
import { DeviceService } from '../device/deviceService';
import { EventsEmitterService } from '../events/eventsEmitterService';
import { ReferralService } from '../referral/referralService';
import { SiteService } from '../site/siteService';
import { ThirdPartyBankAccountService } from '../thirdPartyAccount/thirdPartyBankAccountService';

import { CbsService } from './cbs/cbsService';
import type { UpdateEntityFlagsResponse } from './cbs/updateEntityFlagsResponse';
import { CanSettleDownstreamUpdate } from './downstreamUpdates/canSettleDownstreamUpdate';
import { EntityService } from './entityService';
import { MsService } from './ms/msService';
import { PgsEntityService } from './pgsEntity/pgsEntityService';
import { RiskRuleService } from './risk/riskRuleService';
import { postFinalisedOnboardingStatus } from './rules';
import type { AmsUpdateEntityOption } from './types';

jest.mock('@npco/component-bff-core/dist/utils/logger');
jest.mock('aws-xray-sdk');

const createQueryBuilder = jest.fn().mockReturnValue({
  where: jest.fn().mockReturnValue({ andWhere: jest.fn().mockReturnValue({ getCount: jest.fn() }) }),
});

const getRepositoryMock = jest.fn().mockReturnValue({ createQueryBuilder });
const getManagerMock = jest.fn();

jest.mock('typeorm', () => {
  const originalModule = jest.requireActual('typeorm');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
    getRepository: () => getRepositoryMock(),
    getManager: () => getManagerMock(),
  };
});

const generateAmexMerchantSubmissionDtoMock = jest.fn();
jest.mock('./lib/amex', () => {
  const originalModule = jest.requireActual('./lib/amex');
  return {
    __esModule: true,
    ...originalModule,
    generateAmexMerchantSubmissionDto: () => generateAmexMerchantSubmissionDtoMock(),
  };
});

jest.mock('./cbs/cbsService', () => {
  const service = {
    createEntity: jest.fn(),
    updateEntityFlags: jest.fn(),
  } as Partial<CbsService>;

  return { CbsService: jest.fn(() => service) };
});

jest.mock('../globalConfig/globalConfigService', () => {
  return {
    GlobalConfigService: jest.fn().mockImplementation(() => ({
      findAll: jest.fn().mockResolvedValue({}),
      getValue: jest.fn().mockReturnValue({ currency: 'AUD', domicile: Domicile.AU }),
    })),
  };
});

jest.mock('@npco/component-bff-core/dist/domicile', () => {
  return {
    DomicileLookupDb: jest.fn().mockImplementation(() => ({
      createDomicileRecord: jest.fn().mockResolvedValue({}),
      getDomicileByEntityId: jest.fn().mockReturnValue(Domicile.AU),
    })),
  };
});

describe('entity service test suite', () => {
  let entityService: EntityService;
  const mockEnvService = mock(EnvironmentService);
  const mockLambdaService = mock(LambdaService);
  const mockDeviceService = mock(DeviceService);
  const mockSiteService = mock(SiteService);
  const mockCustomerService = mock(CustomerService);
  const mock3rdAccountService = mock(ThirdPartyBankAccountService);
  const mockReferredService = mock(ReferralService);
  const mockCbsService = new CbsService(mockEnvService) as jest.Mocked<CbsService>;
  const mockMsService = mock(MsService);
  const mockPgsEntityService = mock(PgsEntityService);
  const mockContactService = mock(ContactService);
  const mockCustomerEntityService = mock(CustomerEntityService);
  const mockRiskRuleService = mock(RiskRuleService);

  const mockHlPosService = { disconnectByEntityUuid: jest.fn() } as any;
  const mockOraclePosService = { disconnectByEntityUuid: jest.fn() } as any;
  const mockImposPosService = { disconnectByEntityUuid: jest.fn() } as any;
  const mockTevalisPosService = { disconnectByEntityUuid: jest.fn() } as any;

  const posInterfaceCleanService = new PosInterfaceCleanService(
    mockHlPosService,
    mockImposPosService,
    mockOraclePosService,
    mockTevalisPosService,
  );

  let originalResetCbsFlags: EntityService['resetCbsFlags'];

  beforeAll(() => {
    mockDomicile();
  });

  beforeEach(() => {
    entityService = new EntityService(
      instance(mockEnvService),
      instance(mockLambdaService),
      instance(mockSiteService),
      instance(mockDeviceService),
      instance(mockCustomerService),
      instance(mockCustomerEntityService),
      instance(mock3rdAccountService),
      instance(mockReferredService),
      mockCbsService,
      instance(mockMsService),
      instance(mockPgsEntityService),
      instance(mockContactService),
      posInterfaceCleanService,
      instance(mockRiskRuleService),
    );

    when(mockSiteService.updateEntitySites).thenReturn(() => Promise.resolve());
    when(mockDeviceService.updateDevicesSettings).thenReturn(() => Promise.resolve());
    when(mockEnvService.cqrsCmds).thenReturn(DomainURImap);
    when(mockMsService.updateEntityCanSettle(anything(), anything())).thenResolve({
      status: 404,
      body: 'Mock not found',
    });
    when(mockPgsEntityService.updateEntityCanAcquireAmex(anything(), anything())).thenResolve();

    originalResetCbsFlags = entityService.resetCbsFlags;
    entityService.resetCbsFlags = jest.fn();
  });

  describe('isScreeningRequired test suite', () => {
    describe('isScreeningRequired should return false', () => {
      it('if failed to run rule engine', async () => {
        const required = await entityService.isScreeningRequired(undefined as any, undefined as any);
        expect(required).toBeFalsy();
      });

      describe('if the onboarding Status is not post-finalised onboardingStatus', () => {
        it('if onboardingStatus is undefined', async () => {
          const required = await entityService.isScreeningRequired(undefined as any, {} as any);
          expect(required).toBeFalsy();
        });

        test.each(
          Object.keys(OnboardingStatus).filter(
            (status) => !postFinalisedOnboardingStatus.includes(status as OnboardingStatus),
          ),
        )('if the onboarding status is %s', async (onboardingStatus) => {
          const required = await entityService.isScreeningRequired(
            { name: 'name' } as any,
            { onboardingStatus } as Entity,
          );
          expect(required).toBeFalsy();
        });
      });

      it('if the name or tradingName is undefined', async () => {
        const required = await entityService.isScreeningRequired(
          {} as any,
          { onboardingStatus: OnboardingStatus.REVIEW } as Entity,
        );
        expect(required).toBeFalsy();
      });

      describe('if the name is ', () => {
        it('null', async () => {
          const required = await entityService.isScreeningRequired(
            { name: null } as any,
            { onboardingStatus: OnboardingStatus.REVIEW } as Entity,
          );
          expect(required).toBeFalsy();
        });

        it('an empty string', async () => {
          const required = await entityService.isScreeningRequired(
            { name: '' } as any,
            { onboardingStatus: OnboardingStatus.REVIEW } as Entity,
          );
          expect(required).toBeFalsy();
        });

        it('same as the current name', async () => {
          const required = await entityService.isScreeningRequired(
            { name: 'Name ' } as any,
            { name: 'name', onboardingStatus: OnboardingStatus.REVIEW } as Entity,
          );
          expect(required).toBeFalsy();
        });

        it('same as the current trading name', async () => {
          const required = await entityService.isScreeningRequired(
            { name: 'Name ' } as any,
            {
              name: 'business name',
              tradingName: 'name',
              onboardingStatus: OnboardingStatus.REVIEW,
            } as Entity,
          );
          expect(required).toBeFalsy();
        });
      });
    });

    describe('if the trading name is ', () => {
      it('null', async () => {
        const required = await entityService.isScreeningRequired(
          { tradingName: null } as any,
          { onboardingStatus: OnboardingStatus.REVIEW } as Entity,
        );
        expect(required).toBeFalsy();
      });

      it('an empty string', async () => {
        const required = await entityService.isScreeningRequired(
          { tradingName: '' } as any,
          { onboardingStatus: OnboardingStatus.REVIEW } as Entity,
        );
        expect(required).toBeFalsy();
      });

      it('same as the current name', async () => {
        const required = await entityService.isScreeningRequired(
          { tradingName: 'Name ' } as any,
          { name: 'name', onboardingStatus: OnboardingStatus.REVIEW } as Entity,
        );
        expect(required).toBeFalsy();
      });

      it('same as the current trading name', async () => {
        const required = await entityService.isScreeningRequired(
          { tradingName: 'Name ' } as any,
          { name: 'business name', tradingName: 'name', onboardingStatus: OnboardingStatus.REVIEW } as Entity,
        );
        expect(required).toBeFalsy();
      });
    });

    describe('isScreeningRequired should return true', () => {
      it(' if the name is defined', async () => {
        const required = await entityService.isScreeningRequired(
          { name: 'name' } as any,
          { onboardingStatus: OnboardingStatus.REVIEW } as Entity,
        );
        expect(required).toBeTruthy();
      });

      it(' if the name is changed', async () => {
        const required = await entityService.isScreeningRequired(
          { name: 'name' } as any,
          {
            name: 'business name',
            tradingName: 'trading name',
            onboardingStatus: OnboardingStatus.REVIEW,
          } as Entity,
        );
        expect(required).toBeTruthy();
      });

      it('if the tradingName is defined and not empty', async () => {
        const required = await entityService.isScreeningRequired(
          { tradingName: 'tradingName' } as any,
          {
            name: 'business name',
            tradingName: 'trading name',
            onboardingStatus: OnboardingStatus.REVIEW,
          } as Entity,
        );
        expect(required).toBeTruthy();
      });

      describe('if the onboarding Status is post-finalised onboardingStatus and the name is updated', () => {
        test.each(postFinalisedOnboardingStatus)('if the onboarding status is ', async (onboardingStatus) => {
          const required = await entityService.isScreeningRequired(
            { name: 'name' } as any,
            { name: 'business name', tradingName: 'trading name', onboardingStatus } as Entity,
          );
          expect(required).toBeTruthy();
        });
      });
    });
  });

  it('isAmexSubmissionRequired should return false if failed to run rule engine', async () => {
    const required = await entityService.isAmexSubmissionRequired(undefined as any);
    expect(required).toBeFalsy();
  });

  describe('sendScreeningRequest test suite', () => {
    it('should perform nothing if there is no name / tradingName in the event dto', async () => {
      entityService.callCommandHandler = jest.fn();
      const spy = jest.spyOn(entityService, 'callCommandHandler');
      expect(spy).not.toHaveBeenCalled();
      await entityService.sendScreeningRequest({ entityUuid: '' });
      expect(spy).not.toHaveBeenCalled();
    });

    it('should throw error if failed to find entity when sending screening request', async () => {
      entityService.findOne = () => {
        return Promise.resolve(undefined);
      };
      await expect(entityService.sendScreeningRequest({ entityUuid: '', name: 'name' })).rejects.toThrowError();
    });

    describe('should generate the dto correctly', () => {
      it('able to handle the name', async () => {
        entityService.findOne = (id: string) => {
          return Promise.resolve({ entityUuid: id, type: EntityType.COMPANY, domicile: Domicile.AU });
        };
        entityService.callCommandHandler = jest.fn();
        const spy = jest.spyOn(entityService, 'callCommandHandler');
        expect(spy).not.toHaveBeenCalled();
        await entityService.sendScreeningRequest({ entityUuid: 'entityUuid', name: 'name' });
        expect(spy).toHaveBeenCalledWith('entityUuid', 'Entity.ScreeningRequested', {
          entityUuid: 'entityUuid',
          name: 'name',
        });
      });

      it('able to handle tradingName', async () => {
        entityService.findOne = (id: string) => {
          return Promise.resolve({ entityUuid: id, type: EntityType.COMPANY, domicile: Domicile.AU });
        };
        entityService.callCommandHandler = jest.fn();
        const spy = jest.spyOn(entityService, 'callCommandHandler');
        expect(spy).not.toHaveBeenCalled();
        await entityService.sendScreeningRequest({ entityUuid: 'entityUuid', tradingName: 'tradingName' });
        expect(spy).toHaveBeenCalledWith('entityUuid', 'Entity.ScreeningRequested', {
          entityUuid: 'entityUuid',
          name: 'tradingName',
        });
      });
    });
  });

  describe('Update Entity test', () => {
    const completedOnboardingEvents = [
      OnboardingStatus.ONBOARDED,
      OnboardingStatus.RC_ONBOARDED,
      OnboardingStatus.REVIEW,
      OnboardingStatus.RC_ABANDONED,
      OnboardingStatus.RC_REJECTED,
      OnboardingStatus.RC_DEPLATFORMED,
      OnboardingStatus.RC_REVIEW,
    ];
    const emitAmexSubmissionStatusList = [OnboardingStatus.ONBOARDED, OnboardingStatus.RC_ONBOARDED];

    beforeEach(() => {
      entityService.update = () => Promise.resolve({} as Entity);
      entityService.callCommandHandler = () => Promise.resolve();
      entityService.isScreeningRequired = () => Promise.resolve(false);
      getRepositoryMock.mockReturnValue({
        create: jest.fn().mockReturnValue({} as any),
        findOne: (id: string) => jest.fn().mockResolvedValue({ id }),
        createQueryBuilder,
      });
    });
    it('should not throw error if event.onboardingStatus is not define', async () => {
      await expect(entityService.updateEntity({ entityUuid: '' })).resolves.not.toThrowError();
    });

    it('should throw error if country of origin is invalid', async () => {
      await expect(entityService.updateEntity({ entityUuid: 'entityUuid', countryOfOrigin: 'AU' })).rejects.toThrow(
        'Not allow to change countryOfOrigin to AU',
      );
    });

    it('should be able to handle firstTransactionCreated', async () => {
      // check only call update when yetToMakeTransaction is true
      const spy = jest.spyOn(entityService, 'update');
      getRepositoryMock.mockReturnValue({
        createQueryBuilder,
        create: jest.fn().mockImplementation((data: any) => data),
        findOne: jest
          .fn()
          .mockResolvedValueOnce({ entityUuid: 'entityUuid' })
          .mockResolvedValueOnce({ entityUuid: 'entityUuid', transactionMetaData: { yetToMakeTransaction: false } })
          .mockResolvedValueOnce({ entityUuid: 'entityUuid', transactionMetaData: { yetToMakeTransaction: true } }),
      });
      await entityService.firstTransactionCreated('entityUuid', {
        transactionUuid: 'transactionUuid',
        domicile: Domicile.AU,
      });
      expect(spy).toHaveBeenCalledTimes(0);
      spy.mockClear();

      await entityService.firstTransactionCreated('entityUuid', {
        transactionUuid: 'transactionUuid',
        domicile: Domicile.AU,
      });
      expect(spy).toHaveBeenCalledTimes(0);
      spy.mockClear();

      await entityService.firstTransactionCreated('entityUuid', {
        transactionUuid: 'transactionUuid',
        timestampUtc: '2023-05-09T05:55:30.968Z',
        domicile: Domicile.AU,
      });
      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith(
        'entityUuid',
        {
          entityUuid: 'entityUuid',
          transactionMetaData: {
            firstTransactionUuid: 'transactionUuid',
            firstTransactionTimestamp: '2023-05-09T05:55:30.968Z',
            yetToMakeTransaction: false,
          },
        },
        {
          successCmd: 'Entity.FirstTransactionCreated',
          successEvent: {
            entityUuid: 'entityUuid',
            firstTransactionUuid: 'transactionUuid',
            firstTransactionTimestamp: '2023-05-09T05:55:30.968Z',
            yetToMakeTransaction: false,
          },
        },
        { useDomicile: true },
      );
    });

    describe('current entity onboardingStatus is onboarded', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({ onboardingStatus: OnboardingStatus.ONBOARDED } as Entity);
      });
      test.each([
        OnboardingStatus.RC_REJECTED,
        OnboardingStatus.ONBOARDED,
        OnboardingStatus.RC_DEPLATFORMED,
        OnboardingStatus.RC_REVIEW,
      ])('should not throw if the incoming event tries to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity(
            {
              entityUuid: '',
              onboardingStatus,
              abn: 'abn',
            },
            { allowFinaliseOnboardingStatus: true } as AmsUpdateEntityOption,
          ),
        ).resolves.not.toThrowError();
      });
      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) => !completedOnboardingEvents.includes(status as OnboardingStatus),
        ),
      )('should throw error if the incoming event ties to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).rejects.toThrowError(`Not allow to change onboardingStatus from ONBOARDED to ${onboardingStatus}`);
      });
    });

    describe('current entity onboardingStatus is rc_onboarded', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({ onboardingStatus: OnboardingStatus.RC_ONBOARDED } as Entity);
      });
      test.each([
        OnboardingStatus.RC_ONBOARDED,
        OnboardingStatus.RC_REJECTED,
        OnboardingStatus.RC_DEPLATFORMED,
        OnboardingStatus.RC_REVIEW,
      ])('should not throw if the incoming event tries to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity(
            {
              entityUuid: '',
              onboardingStatus,
            },
            { allowFinaliseOnboardingStatus: true } as AmsUpdateEntityOption,
          ),
        ).resolves.not.toThrowError();
      });
      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) =>
            status !== OnboardingStatus.RC_ONBOARDED &&
            status !== OnboardingStatus.RC_REJECTED &&
            status !== OnboardingStatus.RC_DEPLATFORMED &&
            status !== OnboardingStatus.RC_REVIEW,
        ),
      )('should throw error if the incoming event ties to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).rejects.toThrowError(`Not allow to change onboardingStatus from RC_ONBOARDED to ${onboardingStatus}`);
      });
    });

    describe(`current entity onboardingStatus is ${OnboardingStatus.RC_REVIEW}`, () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({ onboardingStatus: OnboardingStatus.RC_REVIEW } as Entity);
      });
      test.each([
        OnboardingStatus.RC_REVIEW,
        OnboardingStatus.RC_ABANDONED,
        OnboardingStatus.RC_REJECTED,
        OnboardingStatus.RC_ONBOARDED,
      ])('should not throw if the incoming event tries to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity(
            {
              entityUuid: '',
              onboardingStatus,
            },
            { allowFinaliseOnboardingStatus: true } as AmsUpdateEntityOption,
          ),
        ).resolves.not.toThrowError();
      });

      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) =>
            status !== OnboardingStatus.RC_REVIEW &&
            status !== OnboardingStatus.RC_ABANDONED &&
            status !== OnboardingStatus.RC_REJECTED &&
            status !== OnboardingStatus.RC_ONBOARDED,
        ),
      )('should throw error if the incoming event ties to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).rejects.toThrowError(
          `Not allow to change onboardingStatus from ${OnboardingStatus.RC_REVIEW} to ${onboardingStatus}`,
        );
      });
    });

    describe('current entity onboardingStatus is rc_rejected', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({ onboardingStatus: OnboardingStatus.RC_REJECTED } as Entity);
      });
      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) =>
            status !== OnboardingStatus.RC_ONBOARDED &&
            status !== OnboardingStatus.RC_REJECTED &&
            status !== OnboardingStatus.RC_REVIEW,
        ),
      )('should throw error if the incoming event ties to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).rejects.toThrowError(`Not allow to change onboardingStatus from RC_REJECTED to ${onboardingStatus}`);
      });
    });

    describe('current entity onboardingStatus is rc_abandoned', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({ onboardingStatus: OnboardingStatus.RC_ABANDONED } as Entity);
      });
      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) =>
            status !== OnboardingStatus.RC_ONBOARDED &&
            status !== OnboardingStatus.RC_REJECTED &&
            status !== OnboardingStatus.RC_ABANDONED &&
            status !== OnboardingStatus.RC_REVIEW,
        ),
      )('should throw error if the incoming event ties to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).rejects.toThrowError(`Not allow to change onboardingStatus from RC_ABANDONED to ${onboardingStatus}`);
      });
    });

    describe('current entity onboardingStatus is rc_deplatformed', () => {
      beforeEach(() => {
        entityService.getEntity = () =>
          Promise.resolve({ onboardingStatus: OnboardingStatus.RC_DEPLATFORMED } as Entity);
      });
      test.each([OnboardingStatus.RC_ONBOARDED, OnboardingStatus.RC_DEPLATFORMED, OnboardingStatus.RC_REVIEW])(
        'should not throw if the incoming event tries to set to %s',
        async (onboardingStatus) => {
          await expect(
            entityService.updateEntity(
              {
                entityUuid: '',
                onboardingStatus,
              },
              { allowFinaliseOnboardingStatus: true } as AmsUpdateEntityOption,
            ),
          ).resolves.not.toThrowError();
        },
      );
      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) =>
            status !== OnboardingStatus.RC_ONBOARDED &&
            status !== OnboardingStatus.RC_DEPLATFORMED &&
            status !== OnboardingStatus.RC_REVIEW,
        ),
      )('should throw error if the incoming event ties to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).rejects.toThrowError(`Not allow to change onboardingStatus from RC_DEPLATFORMED to ${onboardingStatus}`);
      });
    });

    describe('current entity onboardingStatus is review', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({ onboardingStatus: OnboardingStatus.REVIEW } as Entity);
      });
      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) => !postFinalisedOnboardingStatus.includes(status as OnboardingStatus),
        ),
      )('should throw error if the incoming event ties to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).rejects.toThrowError(`Not allow to change onboardingStatus from REVIEW to ${onboardingStatus}`);
      });
    });

    describe('current entity onboardingStatus is onboardingStatus', () => {
      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) => !postFinalisedOnboardingStatus.includes(status as OnboardingStatus),
        ),
      )('allow set to any other onboardingStatus if the current entity is %s', async (onboardingStatus) => {
        entityService.getEntity = () =>
          Promise.resolve({
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          } as Entity);

        for (const status of Object.keys(OnboardingStatus).filter(
          (s) => !postFinalisedOnboardingStatus.includes(s as OnboardingStatus),
        )) {
          await expect(
            entityService.updateEntity({
              entityUuid: '',
              onboardingStatus: OnboardingStatus[status as keyof typeof OnboardingStatus],
            }),
          ).resolves.not.toThrowError();
        }
      });
    });

    describe('current entity onboardingStatus is undefined', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({} as Entity);
      });
      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) => !postFinalisedOnboardingStatus.includes(status as OnboardingStatus),
        ),
      )('should not throw error if the incoming event ties to set to %s', async (onboardingStatus) => {
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).resolves.not.toThrowError();
      });
    });

    describe('able to emit the AMEX Merchant Submission request correctly', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({} as Entity);
        entityService.amexMerchantOnboardingRequest = jest.fn().mockImplementation(() => Promise.resolve());
      });
      test.each(emitAmexSubmissionStatusList)(
        'should call the amex merchant onboarding request if the status is %s',
        async (onboardingStatus) => {
          expect(entityService.amexMerchantOnboardingRequest).not.toHaveBeenCalled();
          await expect(
            entityService.updateEntity(
              {
                entityUuid: '',
                onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
              },
              { allowFinaliseOnboardingStatus: true },
            ),
          ).resolves.not.toThrowError();
          expect(entityService.amexMerchantOnboardingRequest).toHaveBeenCalledTimes(1);
        },
      );

      test.each(
        Object.keys(OnboardingStatus).filter(
          (status) =>
            !emitAmexSubmissionStatusList.includes(status as OnboardingStatus) &&
            !postFinalisedOnboardingStatus.includes(status as OnboardingStatus),
        ),
      )('should not call the amex merchant onboarding request if the status is %s', async (onboardingStatus) => {
        expect(entityService.amexMerchantOnboardingRequest).not.toHaveBeenCalled();
        await expect(
          entityService.updateEntity({
            entityUuid: '',
            onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
          }),
        ).resolves.not.toThrowError();
        expect(entityService.amexMerchantOnboardingRequest).not.toHaveBeenCalled();
      });
    });

    describe('Testing out the allowFinaliseOnboardingStatus when updatingEntity', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({ onboardingStatus: OnboardingStatus.REVIEW } as Entity);
      });
      test.each(postFinalisedOnboardingStatus.filter((status) => status !== OnboardingStatus.RC_REVIEW))(
        'should reject onboarding the when the onboarding status is already set for %s',
        async (onboardingStatus) => {
          await expect(
            entityService.updateEntity(
              {
                entityUuid: '',
                onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
              },
              { allowFinaliseOnboardingStatus: false },
            ),
          ).rejects.toThrowError('illegal onboarding status');
        },
      );

      test.each(postFinalisedOnboardingStatus.filter((status) => status !== OnboardingStatus.RC_REVIEW))(
        'should reject onboarding the when the onboarding status is already set for %s',
        async (onboardingStatus) => {
          await expect(
            entityService.updateEntity(
              {
                entityUuid: '',
                onboardingStatus: OnboardingStatus[onboardingStatus as keyof typeof OnboardingStatus],
              },
              { allowFinaliseOnboardingStatus: true },
            ),
          ).resolves.not.toThrowError();
        },
      );
    });

    describe('risk review will chain the onboardingStatus update', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({} as Entity);
        getRepositoryMock.mockReturnValue({
          create: jest.fn((x) => x),
          createQueryBuilder,
        });
        when(mockDeviceService.find(anything())).thenResolve([]);
        when(mockCustomerService.find(anything())).thenResolve([]);
        entityService.amexMerchantOnboardingRequest = jest.fn().mockImplementation(() => Promise.resolve());
      });
      it('no action if there is no risk review', async () => {
        const spy = jest.spyOn(entityService, 'update');
        expect(spy).not.toHaveBeenCalled();
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
        });
        expect(spy).toHaveBeenCalledTimes(1);
      });
      it('no action if there is risk review status is NOT_REQUIRED', async () => {
        const spy = jest.spyOn(entityService, 'update');
        expect(spy).not.toHaveBeenCalled();
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          riskReview: {
            status: RiskReviewStatus.NOT_REQUIRED,
          },
        });
        expect(spy).toHaveBeenCalledTimes(1);
      });
      it('no action if there is risk review status is REQUIRED', async () => {
        const spy = jest.spyOn(entityService, 'update');
        expect(spy).not.toHaveBeenCalled();
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          riskReview: {
            status: RiskReviewStatus.REQUIRED,
          },
        });
        expect(spy).toHaveBeenCalledTimes(1);
      });
      it('no action if there is risk review status is COMPLETED, but risk result is missing', async () => {
        const spy = jest.spyOn(entityService, 'update');
        expect(spy).not.toHaveBeenCalled();
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          riskReview: {
            status: RiskReviewStatus.COMPLETED,
          },
        });
        expect(spy).toHaveBeenCalledTimes(1);
      });
      it('send the RC_Onboarded if risk review result is accepted', async () => {
        const updateCommandSpy = jest.spyOn(entityService, 'update');
        const amexSubmissionSpy = jest.spyOn(entityService, 'amexMerchantOnboardingRequest');
        const commandHandlerSpy = jest.spyOn(entityService, 'callCommandHandler');
        expect(updateCommandSpy).not.toHaveBeenCalled();
        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        expect(commandHandlerSpy).not.toHaveBeenCalled();
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          riskReview: {
            status: RiskReviewStatus.COMPLETED,
            result: RiskReviewResult.ACCEPTED,
          },
        });

        const updatedData = {
          entityUuid: 'entityUuid',
          onboardingStatus: OnboardingStatus.RC_ONBOARDED,
        };

        expect(updateCommandSpy).toHaveBeenCalledTimes(2);
        expect(commandHandlerSpy).toHaveBeenCalledTimes(2);
        expect(amexSubmissionSpy).toHaveBeenCalledWith('entityUuid');
        expect(updateCommandSpy).toHaveBeenCalledWith(
          'entityUuid',
          {
            ...updatedData,
            updatedTime: expect.any(String),
          },
          {
            successCmd: 'Entity.Updated',
            successEvent: updatedData,
          },
          { useDomicile: true },
        );
        expect(commandHandlerSpy).toHaveBeenCalledWith('entityUuid', 'Entity.OnboardingStatusUpdated', updatedData);
      });
      it('send the RC_REJECTED if risk review result is rejected', async () => {
        const updateCommandSpy = jest.spyOn(entityService, 'update');
        const commandHandlerSpy = jest.spyOn(entityService, 'callCommandHandler');
        const amexSubmissionSpy = jest.spyOn(entityService, 'amexMerchantOnboardingRequest');
        expect(updateCommandSpy).not.toHaveBeenCalled();
        expect(commandHandlerSpy).not.toHaveBeenCalled();
        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        when(mockCustomerEntityService.findAllCustomers).thenReturn(() => Promise.resolve([] as CustomerEntity[]));
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          riskReview: {
            status: RiskReviewStatus.COMPLETED,
            result: RiskReviewResult.REJECTED,
            reason: '',
          },
        });

        const updatedData = {
          entityUuid: 'entityUuid',
          onboardingStatus: OnboardingStatus.RC_REJECTED,
        };

        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        expect(updateCommandSpy).toHaveBeenCalledTimes(2);
        expect(commandHandlerSpy).toHaveBeenCalledTimes(2);
        expect(updateCommandSpy).toHaveBeenCalledWith(
          'entityUuid',
          {
            ...updatedData,
            updatedTime: expect.any(String),
          },
          {
            successCmd: 'Entity.Updated',
            successEvent: updatedData,
          },
          { useDomicile: true },
        );
        expect(commandHandlerSpy).toHaveBeenCalledWith('entityUuid', 'Entity.OnboardingStatusUpdated', updatedData);
      });

      it('send the RC_ABANDONED if risk review result is abandoned ', async () => {
        const updateCommandSpy = jest.spyOn(entityService, 'update');
        const commandHandlerSpy = jest.spyOn(entityService, 'callCommandHandler');
        const amexSubmissionSpy = jest.spyOn(entityService, 'amexMerchantOnboardingRequest');
        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        expect(updateCommandSpy).not.toHaveBeenCalled();
        expect(commandHandlerSpy).not.toHaveBeenCalled();
        when(mockCustomerEntityService.findAllCustomers).thenReturn(() => Promise.resolve([] as CustomerEntity[]));
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          riskReview: {
            status: RiskReviewStatus.COMPLETED,
            result: RiskReviewResult.ABANDONED,
            reason: '',
          },
        });

        const updatedData = {
          entityUuid: 'entityUuid',
          onboardingStatus: OnboardingStatus.RC_ABANDONED,
        };

        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        expect(updateCommandSpy).toHaveBeenCalledTimes(2);
        expect(commandHandlerSpy).toHaveBeenCalledTimes(2);
        expect(updateCommandSpy).toHaveBeenCalledWith(
          'entityUuid',
          {
            ...updatedData,
            updatedTime: expect.any(String),
          },
          {
            successCmd: 'Entity.Updated',
            successEvent: updatedData,
          },
          { useDomicile: true },
        );
        expect(commandHandlerSpy).toHaveBeenCalledWith('entityUuid', 'Entity.OnboardingStatusUpdated', updatedData);
      });

      it('send the RC_DEPLATFORMED if risk review result is deplatformed ', async () => {
        const updateCommandSpy = jest.spyOn(entityService, 'update');
        const commandHandlerSpy = jest.spyOn(entityService, 'callCommandHandler');
        const amexSubmissionSpy = jest.spyOn(entityService, 'amexMerchantOnboardingRequest');
        expect(updateCommandSpy).not.toHaveBeenCalled();
        expect(commandHandlerSpy).not.toHaveBeenCalled();
        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          riskReview: {
            status: RiskReviewStatus.COMPLETED,
            result: RiskReviewResult.DEPLATFORMED,
            reason: '',
          },
        });

        const updatedData = {
          entityUuid: 'entityUuid',
          onboardingStatus: OnboardingStatus.RC_DEPLATFORMED,
        };
        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        expect(updateCommandSpy).toHaveBeenCalledTimes(2);
        expect(commandHandlerSpy).toHaveBeenCalledTimes(2);
        expect(updateCommandSpy).toHaveBeenCalledWith(
          'entityUuid',
          {
            ...updatedData,
            updatedTime: expect.any(String),
          },
          {
            successCmd: 'Entity.Updated',
            successEvent: updatedData,
          },
          { useDomicile: true },
        );
        expect(commandHandlerSpy).toHaveBeenCalledWith('entityUuid', 'Entity.OnboardingStatusUpdated', updatedData);
      });

      it(`send the ${OnboardingStatus.RC_REVIEW} if risk review result is ${RiskReviewResult.REVIEW}`, async () => {
        const updateCommandSpy = jest.spyOn(entityService, 'update');
        const commandHandlerSpy = jest.spyOn(entityService, 'callCommandHandler');
        const amexSubmissionSpy = jest.spyOn(entityService, 'amexMerchantOnboardingRequest');
        expect(updateCommandSpy).not.toHaveBeenCalled();
        expect(commandHandlerSpy).not.toHaveBeenCalled();
        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          riskReview: {
            status: RiskReviewStatus.COMPLETED,
            result: RiskReviewResult.REVIEW,
            reason: '',
          },
        });

        const updatedData = {
          entityUuid: 'entityUuid',
          onboardingStatus: OnboardingStatus.RC_REVIEW,
        };

        expect(amexSubmissionSpy).not.toHaveBeenCalled();
        expect(updateCommandSpy).toHaveBeenCalledTimes(2);
        expect(commandHandlerSpy).toHaveBeenCalledTimes(2);
        expect(updateCommandSpy).toHaveBeenCalledWith(
          'entityUuid',
          {
            ...updatedData,
            updatedTime: expect.any(String),
          },
          {
            successCmd: 'Entity.Updated',
            successEvent: updatedData,
          },
          { useDomicile: true },
        );
        expect(commandHandlerSpy).toHaveBeenCalledWith('entityUuid', 'Entity.OnboardingStatusUpdated', updatedData);
      });
    });

    describe('EntityNameUpdated event', () => {
      beforeEach(() => {
        entityService.getEntity = () => Promise.resolve({ name: 'John Wick' } as Entity);
      });
      it('should be able to process EntityNameUpdated event', async () => {
        await expect(
          entityService.updateEntity({
            entityUuid: v4(),
            name: 'John Wick',
          }),
        ).resolves.not.toThrowError();
      });
    });
  });

  describe('amexMerchantOnboardingRequest test suit', () => {
    const entityUuid = v4();
    let customerService: CustomerService;
    const mockDto: EntityAmexMerchantSubmissionRequestedDto = {
      entityUuid,
      recordNumber: '01',
      seller: {
        caid: 'caid',
        mcc: 'mcc',
        name: 'name',
        tradingName: 'tradingName',
        email: 'email',
        phone: '123456789',
        currency: ISO4217.AUD,
        address: {
          address_line_1: 'address_line_1',
          city_name: 'city_name',
          region_code: 'VIC',
          postal_code: '3000',
          country_code: 'AU',
        },
      },
      signer: {
        firstName: 'firstName',
        lastName: 'lastName',
        dob: '2000-01-01',
        address: {
          address_line_1: 'address_line_1',
          city_name: 'city_name',
          region_code: 'VIC',
          postal_code: '3000',
          country_code: 'AU',
        },
      },
    };

    beforeEach(() => {
      jest.clearAllMocks();
      customerService = new CustomerService(
        instance(mockEnvService),
        instance(mockLambdaService),
        {} as any,
        {} as any,
        {} as any,
        {} as any,
      );
      entityService = new EntityService(
        instance(mockEnvService),
        instance(mockLambdaService),
        instance(mockSiteService),
        instance(mockDeviceService),
        customerService,
        mockCustomerEntityService,
        {} as any,
        {} as any,
        instance(mockCbsService),
        instance(mockMsService),
        instance(mockPgsEntityService),
        instance(mockContactService),
        posInterfaceCleanService,
        mockRiskRuleService,
      );

      entityService.callCommandHandler = jest.fn().mockImplementation(() => Promise.resolve());
      generateAmexMerchantSubmissionDtoMock.mockResolvedValue(mockDto);
    });
    it('can call the cqrs with the correct data', async () => {
      (customerService.getRegisteringIndividualByEntityUuid as jest.Mock) = jest.fn().mockResolvedValue({});
      entityService.getEntity = jest.fn().mockResolvedValue({});
      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      await entityService.amexMerchantOnboardingRequest(entityUuid);
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.AmexMerchantSubmissionRequested',
        mockDto,
      );
    });

    it('display error message if registering individual is not existed (should not happened)', async () => {
      entityService.getEntity = jest.fn().mockResolvedValue({});
      (customerService.getRegisteringIndividualByEntityUuid as jest.Mock) = jest.fn().mockResolvedValue(undefined);
      const spy = jest.spyOn(Logger, 'error');
      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      expect(spy).not.toHaveBeenCalled();
      await entityService.amexMerchantOnboardingRequest(entityUuid);
      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      expect(spy).toHaveBeenCalledTimes(2);
    });

    it('display error message if entity is not existed (should not happened)', async () => {
      entityService.getEntity = jest
        .fn()
        .mockRejectedValue(new BadRequestError(`Cant find requested entity id ${entityUuid}`));
      const spy = jest.spyOn(Logger, 'error');
      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      expect(spy).not.toHaveBeenCalled();
      await entityService.amexMerchantOnboardingRequest(entityUuid);
      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      expect(spy).toHaveBeenCalledTimes(2);
    });
  });

  describe('updateRefundOverdraftSettings', () => {
    const entityUuid = v4();
    beforeEach(() => {
      jest.clearAllMocks();
      getRepositoryMock.mockReturnValue({
        create: jest.fn((x) => x),
        createQueryBuilder,
      });
      entityService.update = jest.fn().mockResolvedValue({} as Entity);
    });

    it('should update entity refund overdraft settings', async () => {
      const dto = { entityUuid, limit: 1, reason: 'reason' };
      await entityService.updateRefundOverdraftSettings(dto);
      expect(entityService.update).toHaveBeenCalledWith(
        entityUuid,
        {
          entityUuid,
          refundOverdraftSettings: { limit: 1, reason: 'reason' },
        },
        undefined,
        { useDomicile: true },
      );
    });
  });

  describe('amexMerchantOnboardingResponse test suit', () => {
    const entityUuid = v4();
    beforeEach(() => {
      jest.clearAllMocks();
      getRepositoryMock.mockReturnValue({
        create: jest.fn((x) => x),
        createQueryBuilder,
      });
      entityService.update = jest.fn().mockResolvedValue({} as Entity);
      entityService.callCommandHandler = jest.fn().mockImplementation(() => Promise.resolve());
    });
    it('can handle the amex merchant onboarding response - without result correctly', async () => {
      const mockDto = {
        entityUuid,
        status: AmexAcquisitionStatus.COMPLETED,
      };

      expect(entityService.update).not.toHaveBeenCalled();
      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      await entityService.amexMerchantOnboardingResponse(mockDto);
      expect(entityService.update).toHaveBeenCalledWith(
        entityUuid,
        {
          entityUuid,
          amexSubmission: {
            status: AmexAcquisitionStatus.COMPLETED,
          },
          updatedTime: expect.any(String),
        },
        undefined,
        {
          useDomicile: true,
        },
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.Updated', {
        entityUuid,
        amexSubmission: {
          status: AmexAcquisitionStatus.COMPLETED,
        },
      });
    });
    it('can handle the amex merchant onboarding response - with result correctly', async () => {
      const result = {
        errors: [{ err_msg: 'err_msg' }],
        warnings: [{ warning_msg: 'warning_msg' }],
      };
      const mockDto = {
        entityUuid,
        status: AmexAcquisitionStatus.ERROR,
        result,
      };

      expect(entityService.update).not.toHaveBeenCalled();
      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      await entityService.amexMerchantOnboardingResponse(mockDto);
      expect(entityService.update).toHaveBeenCalledWith(
        entityUuid,
        {
          entityUuid,
          amexSubmission: {
            status: AmexAcquisitionStatus.ERROR,
            result,
          },
          updatedTime: expect.any(String),
        },
        undefined,
        {
          useDomicile: true,
        },
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.Updated', {
        entityUuid,
        amexSubmission: {
          status: AmexAcquisitionStatus.ERROR,
          result,
        },
      });
    });
  });

  describe('idv sendDocumentScreeningRequest', () => {
    it('should send only 1 document screening request on medicare card', async () => {
      const spy = jest.spyOn(entityService, 'sendDocumentScreeningRequest');
      const customer: Customer = {
        customerUuid: 'customerUuid',
        screening: {
          status: ScreeningStatus.COMPLETED,
        },
        documents: {
          resultMedicareCard: CustomerDocumentVerificationResult.ACCEPTED,
          resultPassport: CustomerDocumentVerificationResult.NOT_VERIFIED,
          resultDriversLicence: CustomerDocumentVerificationResult.NOT_VERIFIED,
          medicareFirstName: 'medicareFirstName',
          medicareLastName: 'medicareLastName',
          medicareMiddleName: 'medicareMiddleName',
          dob: 'dob',
          firstName: 'firstName',
          lastName: 'lastName',
          middleName: 'middleName',
        },
        domicile: Domicile.AU,
      };
      await entityService.sendScreeningRequestsForDocumentVerification(customer);
      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith({
        customerUuid: 'customerUuid',
        entityType: EntityType.INDIVIDUAL,
        firstName: 'medicareFirstName',
        lastName: 'medicareLastName',
        middleName: 'medicareMiddleName',
        dob: 'dob',
        screeningType: ScreeningRequestedType.MEDICARE,
      });
    });
    it('should send not send a document screening request', async () => {
      const spy = jest.spyOn(entityService, 'sendDocumentScreeningRequest');
      const customer: Customer = {
        customerUuid: 'customerUuid',
        firstname: 'firstname',
        middlename: 'middlename',
        lastname: 'lastname',
        screening: {
          status: ScreeningStatus.COMPLETED,
        },
        documents: {
          firstName: 'fname',
          lastName: 'lname',
          middleName: 'mname',
          medicareFirstName: 'firstname',
          medicareLastName: 'Lastname',
          medicareMiddleName: 'Middlename',
          passportFirstName: 'firstname',
          passportLastName: 'Lastname',
          passportMiddleName: 'middlename',
          driversLicenceFirstName: 'Firstname',
          driversLicenceLastName: 'lastname',
          driversLicenceMiddleName: 'Middlename',
          dob: 'dob',
          resultMedicareCard: CustomerDocumentVerificationResult.ACCEPTED,
          resultPassport: CustomerDocumentVerificationResult.ACCEPTED,
          resultDriversLicence: CustomerDocumentVerificationResult.ACCEPTED,
        },
        domicile: Domicile.AU,
      };
      await entityService.sendScreeningRequestsForDocumentVerification(customer);
      expect(spy).toHaveBeenCalledTimes(0);
    });
    it('should send not send a document screening request if all fields are undefined', async () => {
      const spy = jest.spyOn(entityService, 'sendDocumentScreeningRequest');
      const customer: Customer = {
        customerUuid: 'customerUuid',
        screening: {
          status: ScreeningStatus.COMPLETED,
        },
        documents: {
          firstName: 'fname',
          lastName: 'lname',
          middleName: 'mname',
          dob: 'dob',
          resultMedicareCard: CustomerDocumentVerificationResult.ACCEPTED,
          resultPassport: CustomerDocumentVerificationResult.ACCEPTED,
          resultDriversLicence: CustomerDocumentVerificationResult.ACCEPTED,
        },
        domicile: Domicile.AU,
      };
      await entityService.sendScreeningRequestsForDocumentVerification(customer);
      expect(spy).toHaveBeenCalledTimes(0);
    });
    it('should send a document screening request for passport', async () => {
      const spy = jest.spyOn(entityService, 'sendDocumentScreeningRequest');
      const customer: Customer = {
        customerUuid: 'customerUuid',
        firstname: 'firstname',
        middlename: 'middlename',
        lastname: 'lastname',
        screening: {
          status: ScreeningStatus.COMPLETED,
        },
        documents: {
          firstName: 'fname',
          lastName: 'lname',
          middleName: 'mname',
          medicareFirstName: 'firstname',
          medicareLastName: 'Lastname',
          medicareMiddleName: 'Middlename',
          passportFirstName: 'passportFirstName',
          passportLastName: 'Lastname',
          passportMiddleName: 'middlename',
          driversLicenceFirstName: 'Firstname',
          driversLicenceLastName: 'lastname',
          driversLicenceMiddleName: 'Middlename',
          resultMedicareCard: CustomerDocumentVerificationResult.ACCEPTED,
          resultPassport: CustomerDocumentVerificationResult.ACCEPTED,
          resultDriversLicence: CustomerDocumentVerificationResult.ACCEPTED,
          dob: 'dob',
        },
        domicile: Domicile.AU,
      };
      await entityService.sendScreeningRequestsForDocumentVerification(customer);
      expect(spy).toHaveBeenCalledTimes(1);
      expect(spy).toHaveBeenCalledWith({
        customerUuid: 'customerUuid',
        entityType: EntityType.INDIVIDUAL,
        firstName: 'passportFirstName',
        lastName: 'Lastname',
        middleName: 'middlename',
        dob: 'dob',
        screeningType: ScreeningRequestedType.PASSPORT,
      });
    });
    it('should send out three document screening requests', async () => {
      const spy = jest.spyOn(entityService, 'sendDocumentScreeningRequest');
      const customer: Customer = {
        customerUuid: 'customerUuid',
        firstname: 'firstname',
        middlename: 'middlename',
        lastname: 'lastname',
        screening: {
          status: ScreeningStatus.COMPLETED,
        },
        documents: {
          resultMedicareCard: CustomerDocumentVerificationResult.ACCEPTED,
          resultPassport: CustomerDocumentVerificationResult.ACCEPTED,
          resultDriversLicence: CustomerDocumentVerificationResult.ACCEPTED,
          firstName: 'fname',
          lastName: 'lname',
          middleName: 'mname',
          medicareFirstName: 'medicareFirstName',
          medicareLastName: 'medicareLastName',
          medicareMiddleName: 'medicareMiddleName',
          passportFirstName: 'passportFirstName',
          passportLastName: 'passportLastName',
          passportMiddleName: 'passportMiddleName',
          driversLicenceFirstName: 'driversLicenceFirstName',
          driversLicenceLastName: 'driversLicenceLastName',
          driversLicenceMiddleName: 'driversLicenceMiddleName',
          dob: 'dob',
        },
        domicile: Domicile.AU,
      };
      await entityService.sendScreeningRequestsForDocumentVerification(customer);
      expect(spy).toHaveBeenCalledTimes(3);
    });
  });

  it('can handle the amex cron job call', async () => {
    const entityUuids = [{ entityUuid: 'entity_uuid_1' }, { entityUuid: 'entity_uuid_2' }];
    getManagerMock.mockReturnValue({
      query: jest.fn().mockResolvedValue(entityUuids),
    });
    entityService.amexMerchantOnboardingRequest = jest.fn();
    const spy = jest.spyOn(entityService, 'amexMerchantOnboardingRequest');
    expect(spy).not.toHaveBeenCalled();
    await entityService.amexMerchantOnboardingCronJob();
    expect(spy).toHaveBeenCalledTimes(2);
    entityUuids.forEach((entity) => expect(spy).toHaveBeenCalledWith(entity.entityUuid));
  });

  test.each([
    {
      field: 'registeredAddress',
      mergeReqruied: true,
    },
    {
      field: 'businessAddress',
      mergeReqruied: true,
    },
    {
      field: 'accountStatus',
      mergeReqruied: true,
    },
    {
      field: 'screening',
      mergeReqruied: true,
    },
    {
      field: 'initialSearchResult',
      mergeReqruied: true,
    },
    {
      field: 'fullSearchResult',
      mergeReqruied: true,
    },
    {
      field: 'regulatorBody',
      mergeReqruied: true,
    },
    {
      field: 'riskReview',
      mergeReqruied: true,
    },
    {
      field: 'riskRating',
      mergeReqruied: true,
    },
    {
      field: 'metrics',
      mergeReqruied: true,
    },
    {
      field: 'amexSubmission',
      mergeReqruied: true,
    },
    {
      field: 'name',
      mergeReqruied: true,
    },
  ])('should be able to find merge required field', (data) => {
    const dto: any = { [data.field]: {} };
    expect(entityService.isLockUpdateRequired(dto)).toBe(data.mergeReqruied);
  });

  describe('Onboarding API', () => {
    const entityUuid = 'entityUuid';
    const attributionService = MutationAttributionService.getInstance();
    const mockEmitterService = new EventsEmitterService(instance(mockLambdaService), instance(mockEnvService));
    attributionService.mutationAttribution = {
      userIdentifier: 'userIdentifier',
      tokenGrant: MutationAttributionTokenGrant.USER_PASS,
      userRole: MutationAttributionUserRole.MANAGER,
      platform: MutationAttributionPlatform.TERMINAL,
      createdTimestamp: new Date().getTime(),
      sessionKey: 'sessionId',
      reason: 'reason',
    };

    const referralService = new ReferralService(instance(mockEnvService), instance(mockLambdaService));
    const customerService = new CustomerService(
      instance(mockEnvService),
      instance(mockLambdaService),
      {} as any,
      {} as any,
      mockCustomerEntityService,
      mockEmitterService,
    );

    beforeEach(() => {
      jest.clearAllMocks();
      entityService = new EntityService(
        instance(mockEnvService),
        instance(mockLambdaService),
        instance(mockSiteService),
        instance(mockDeviceService),
        customerService,
        mockCustomerEntityService,
        {} as any,
        referralService,
        instance(mockCbsService),
        instance(mockMsService),
        instance(mockPgsEntityService),
        instance(mockContactService),
        posInterfaceCleanService,
        mockRiskRuleService,
      );

      getRepositoryMock.mockReturnValue({
        create: jest.fn((x) => x),
        createQueryBuilder,
        findOne: jest.fn(),
      });
      entityService.update = jest.fn().mockResolvedValue({} as Entity);
      entityService.findOne = (id: string) => Promise.resolve({ entityUuid: id } as Entity);
      entityService.getAllCustomers = jest.fn().mockResolvedValue([
        {
          customerUuid: v4(),
          entityUuid,
          registeringIndividual: true,
        },
      ]);
      entityService.callCommandHandler = jest.fn().mockImplementation(() => Promise.resolve());
      getManagerMock.mockReturnValue({
        query: jest.fn().mockResolvedValue([]),
      });
      referralService.triggerReferredEvent = jest.fn();
      mockEmitterService.emitEvents = jest.fn();

      customerService.getCustomer = (id: string) => {
        return Promise.resolve({
          customerUuid: id,
          name: 'name',
          tradingName: 'tradingName',
          domicile: Domicile.AU,
        });
      };

      mockCustomerEntityService.findCustomerEntity = (customerUuid: string) => {
        return Promise.resolve({
          customerEntityUuid: v4(),
          customerUuid,
          entityUuid,
          name: 'name',
          tradingName: 'tradingName',
          domicile: Domicile.AU,
        });
      };
    });

    it('Can update the entity and sending screenings', async () => {
      entityService.findOneWithOption = (option: any) => {
        return Promise.resolve({
          entityUuid: option.where.entityUuid,
          name: 'name',
          tradingName: 'tradingName',
          domicile: Domicile.AU,
        });
      };

      const dto: EntityUpdatedEventDto = {
        entityUuid,
        onboardingStatus: OnboardingStatus.REVIEW,
      };

      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      await entityService.evaluateOnboardingDetails(entityUuid, Domicile.AU);
      expect(entityService.callCommandHandler).toHaveBeenCalledTimes(4);
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.Updated',
        new EntityUpdatedEventDto({
          ...dto,
          riskReview: {
            requiredRiskReviewReason: expect.any(Array),
            riskReviewDetail: [expect.any(String)],
            status: expect.any(String),
          },
          termsOfService: {
            ezta: {
              accepted: true,
              acceptedAt: expect.any(String),
              customerUuid: expect.any(String),
            },
          },
        }),
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.OnboardingStatusUpdated',
        {
          entityUuid,
          onboardingStatus: dto.onboardingStatus,
        },
        attributionService.mutationAttribution,
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.ScreeningRequested', {
        entityUuid,
        name: 'name',
      });
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.ScreeningRequested', {
        entityUuid,
        name: 'tradingName',
      });
      expect(referralService.triggerReferredEvent).toHaveBeenCalledTimes(1);
    });

    test.each([ScreeningStatus.REQUIRED, ScreeningStatus.NOT_REQUIRED])(
      'Send the screening request if the Screening status is not Completed - %s',
      async (status) => {
        entityService.findOneWithOption = (option: any) => {
          return Promise.resolve({
            entityUuid: option.where.entityUuid,
            name: 'name',
            tradingName: 'tradingName',
            screening: { status },
            domicile: Domicile.AU,
          });
        };

        const dto: EntityUpdatedEventDto = {
          entityUuid,
          onboardingStatus: OnboardingStatus.REVIEW,
        };

        expect(entityService.callCommandHandler).not.toHaveBeenCalled();
        await entityService.evaluateOnboardingDetails(entityUuid, Domicile.AU);
        expect(entityService.callCommandHandler).toHaveBeenCalledTimes(4);
        expect(entityService.callCommandHandler).toHaveBeenCalledWith(
          entityUuid,
          'Entity.Updated',
          new EntityUpdatedEventDto({
            ...dto,
            riskReview: {
              requiredRiskReviewReason: expect.any(Array),
              riskReviewDetail: [expect.any(String)],
              status: expect.any(String),
            },
            termsOfService: {
              ezta: {
                accepted: true,
                acceptedAt: expect.any(String),
                customerUuid: expect.any(String),
              },
            },
          }),
        );
        expect(entityService.callCommandHandler).toHaveBeenCalledWith(
          entityUuid,
          'Entity.OnboardingStatusUpdated',
          {
            entityUuid,
            onboardingStatus: dto.onboardingStatus,
          },
          attributionService.mutationAttribution,
        );
        expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.ScreeningRequested', {
          entityUuid,
          name: 'name',
        });
        expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.ScreeningRequested', {
          entityUuid,
          name: 'tradingName',
        });
      },
    );

    it.each([
      [
        'screening status completed',
        {
          entity: {
            entityUuid: 'entityUuid',
            name: 'name',
            tradingName: 'tradingName',
            screening: { status: ScreeningStatus.COMPLETED },
          },
        },
      ],
      [
        'entity name and trading name is missing',
        {
          entity: {
            entityUuid: 'entityUuid',
          },
        },
      ],
    ])('Will not send screening request if %p', async (_, { entity }) => {
      entityService.findOneWithOption = (option: any) => {
        return Promise.resolve({
          ...entity,
          entityUuid: option.where.entityUuid,
          domicile: Domicile.AU,
        });
      };

      const dto: EntityUpdatedEventDto = {
        entityUuid,
        onboardingStatus: OnboardingStatus.REVIEW,
      };

      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      await entityService.evaluateOnboardingDetails(entityUuid, Domicile.AU);
      expect(entityService.callCommandHandler).toHaveBeenCalledTimes(2);
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.Updated',
        new EntityUpdatedEventDto({
          ...dto,
          riskReview: {
            requiredRiskReviewReason: expect.any(Array),
            riskReviewDetail: [expect.any(String)],
            status: expect.any(String),
          },
          termsOfService: {
            ezta: {
              accepted: true,
              acceptedAt: expect.any(String),
              customerUuid: expect.any(String),
            },
          },
        }),
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.OnboardingStatusUpdated',
        {
          entityUuid,
          onboardingStatus: dto.onboardingStatus,
        },
        attributionService.mutationAttribution,
      );
    });

    test.each([
      ['null', null],
      ['undefined', undefined],
      ['same as entity name', 'Name '],
    ])('only send one screening if the trading name is %s', async (_testCase, tradingName) => {
      entityService.findOneWithOption = (option: any) => {
        return Promise.resolve({ entityUuid: option.where.entityUuid, name: 'name', tradingName } as Entity);
      };

      const dto: EntityUpdatedEventDto = {
        entityUuid,
        onboardingStatus: OnboardingStatus.REVIEW,
      };

      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      await entityService.evaluateOnboardingDetails(entityUuid, Domicile.AU);
      expect(entityService.callCommandHandler).toHaveBeenCalledTimes(3);
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.Updated',
        new EntityUpdatedEventDto({
          ...dto,
          riskReview: {
            requiredRiskReviewReason: expect.any(Array),
            riskReviewDetail: [expect.any(String)],
            status: expect.any(String),
          },
          termsOfService: {
            ezta: {
              accepted: true,
              acceptedAt: expect.any(String),
              customerUuid: expect.any(String),
            },
          },
        }),
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.OnboardingStatusUpdated',
        {
          entityUuid,
          onboardingStatus: dto.onboardingStatus,
        },
        attributionService.mutationAttribution,
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.ScreeningRequested', {
        entityUuid,
        name: 'name',
      });
    });

    it('Will trigger the Customer Screening', async () => {
      entityService.findOneWithOption = (option: any) => {
        return Promise.resolve({
          entityUuid: option.where.entityUuid,
          name: 'name',
          screening: { status: ScreeningStatus.NOT_REQUIRED },
        } as Entity);
      };

      const customerUuids = [{ customerUuid: 'customer_uuid_1' }, { customerUuid: 'customer_uuid_2' }];

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      getManagerMock.mockReturnValue({
        query: jest.fn().mockResolvedValue(customerUuids),
      });

      // getCustomer() gets called (once in finaliseKyc() and twice during evaluateCustomersForScreening())
      (customerService.getCustomer as jest.Mock) = jest
        .fn()
        .mockResolvedValueOnce(<Customer>{
          customerUuid: 'customerUuid',
          name: 'name',
          tradingName: 'tradingName',
          domicile: Domicile.AU,
        })
        .mockResolvedValueOnce(<Customer>{
          customerUuid: 'customer_uuid_1',
          entityUuid,
          type: EntityType.INDIVIDUAL,
          firstname: 'customer_firstname',
          middlename: 'customer_middlename',
          lastname: 'customer_lastname',
          dob: '2000-01-01',
          domicile: Domicile.AU,
        })
        .mockResolvedValueOnce(<Customer>{
          customerUuid: 'customer_uuid_2',
          entityUuid,
          type: EntityType.COMPANY,
          companyTrustName: 'customer_companyTrustName',
          domicile: Domicile.AU,
        });

      const dto: EntityUpdatedEventDto = {
        entityUuid,
        onboardingStatus: OnboardingStatus.REVIEW,
      };

      expect(entityService.callCommandHandler).not.toHaveBeenCalled();
      await entityService.evaluateOnboardingDetails(entityUuid, Domicile.AU);
      expect(entityService.callCommandHandler).toHaveBeenCalledTimes(5);
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.Updated',
        new EntityUpdatedEventDto({
          ...dto,
          riskReview: {
            requiredRiskReviewReason: expect.any(Array),
            riskReviewDetail: [expect.any(String)],
            status: expect.any(String),
          },
          termsOfService: {
            ezta: {
              accepted: true,
              acceptedAt: expect.any(String),
              customerUuid: expect.any(String),
            },
          },
        }),
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(
        entityUuid,
        'Entity.OnboardingStatusUpdated',
        {
          entityUuid,
          onboardingStatus: dto.onboardingStatus,
        },
        attributionService.mutationAttribution,
      );
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.ScreeningRequested', {
        entityUuid,
        name: 'name',
      });
      expect(entityService.callCommandHandler).toHaveBeenCalledWith('customer_uuid_1', 'Customer.ScreeningRequested', {
        customerUuid: 'customer_uuid_1',
        entityType: EntityType.INDIVIDUAL,
        firstName: 'customer_firstname',
        middleName: 'customer_middlename',
        lastName: 'customer_lastname',
        dob: '2000-01-01',
        screeningType: ScreeningRequestedType.DEFAULT,
      });
      expect(entityService.callCommandHandler).toHaveBeenCalledWith('customer_uuid_2', 'Customer.ScreeningRequested', {
        customerUuid: 'customer_uuid_2',
        entityType: EntityType.COMPANY,
        companyTrustName: 'customer_companyTrustName',
        screeningType: ScreeningRequestedType.DEFAULT,
      });
    });
  });

  describe('StandInRules API', () => {
    const entityUuid = 'entityUuid';

    beforeEach(() => {
      getRepositoryMock.mockReturnValue({
        update: jest.fn((x) => x),
        createQueryBuilder,
      });
      entityService.update = jest.fn().mockImplementation(() => Promise.resolve());
      entityService.findOne = (id: string) => Promise.resolve({ entityUuid: id } as Entity);
      entityService.callCommandHandler = jest.fn().mockImplementation(() => Promise.resolve());
    });

    it('should set stand in rules', async () => {
      const standInRules = [
        {
          operation: StandInOperation.ABOVE,
          field: StandInField.OFFLINE_AMOUNT,
          value: 'value',
        },
      ];

      await entityService.updateStandInRules(entityUuid, standInRules);
      expect(entityService.update).toHaveBeenCalledTimes(1);
      expect(entityService.update).toHaveBeenCalledWith(entityUuid, { standInRules }, undefined, {
        useDomicile: true,
      });

      expect(entityService.callCommandHandler).toHaveBeenCalledTimes(1);
      expect(entityService.callCommandHandler).toHaveBeenCalledWith(entityUuid, 'Entity.StandInRulesUpdated', {
        entityUuid,
        standInRules,
      });
    });
  });

  describe('ReferredBy API', () => {
    const referralService = new ReferralService(instance(mockEnvService), instance(mockLambdaService));
    beforeEach(() => {
      jest.clearAllMocks();
      referralService.findOne = jest.fn();
      referralService.createReferral = jest.fn();
      referralService.triggerReferredEvent = jest.fn();
      entityService = new EntityService(
        instance(mockEnvService),
        instance(mockLambdaService),
        instance(mockSiteService),
        instance(mockDeviceService),
        instance(mockCustomerService),
        instance(mockCustomerEntityService),
        {} as any,
        referralService,
        instance(mockCbsService),
        instance(mockMsService),
        instance(mockPgsEntityService),
        instance(mockContactService),
        posInterfaceCleanService,
        mockRiskRuleService,
      );
    });

    it('should add referred by', async () => {
      entityService.getEntityUuidFromReferralCode = jest.fn().mockImplementation((x) => Promise.resolve(x));
      await entityService.addReferredBy(v4(), v4());
      expect(referralService.createReferral).toHaveBeenCalledTimes(1);
      expect(referralService.triggerReferredEvent).toHaveBeenCalledTimes(1);
    });

    it('should throw error if entity all ready been referred', async () => {
      const existingReferral = v4();
      referralService.findOne = jest.fn().mockResolvedValue({ entityUuid: existingReferral } as Referral);
      await expect(entityService.addReferredBy(v4(), v4())).rejects.toThrowError(
        `Entity it all ready referred by ${existingReferral}`,
      );
      expect(referralService.createReferral).toHaveBeenCalledTimes(0);
      expect(referralService.triggerReferredEvent).toHaveBeenCalledTimes(1);
    });
  });

  describe('account status flags', () => {
    beforeEach(() => {
      jest.clearAllMocks();
      entityService.update = jest.fn().mockImplementation(() => Promise.resolve({} as Entity));
      entityService.findOne = (id: string) => Promise.resolve({ entityUuid: id } as Entity);
      entityService.callCommandHandler = () => Promise.resolve();
      entityService.isScreeningRequired = () => Promise.resolve(false);
      getRepositoryMock.mockReturnValue({
        create: jest.fn().mockImplementation((input) => {
          return { ...input };
        }),
        createQueryBuilder: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({ andWhere: jest.fn().mockReturnValue({ getCount: jest.fn() }) }),
        }),
        findOne: (id: string) => jest.fn().mockResolvedValue({ id }),
      });
    });

    describe('when canAcquireAmex is updated to false', () => {
      describe('should update hasAmexPreviousCancelled to true', () => {
        it.each([OnboardingStatus.ONBOARDED, OnboardingStatus.RC_ONBOARDED])(
          'if the onboardingStatus is %s',
          async (onboardingStatus) => {
            const spiedUpdate = jest.spyOn(entityService, 'update').mockResolvedValue({} as any);
            const spiedCallCommandHandler = jest
              .spyOn(entityService, 'callCommandHandler')
              .mockResolvedValue({} as any);
            jest.spyOn(entityService, 'getEntity').mockImplementation((id: string) =>
              Promise.resolve({
                entityUuid: id,
                onboardingStatus,
                accountStatus: { canAcquireAmex: true, hasAmexPreviousCancelled: false },
              } as any),
            );
            getRepositoryMock.mockReturnValue({
              create: (obj: any) => obj,
              createQueryBuilder: jest.fn().mockReturnValue({
                where: jest.fn().mockReturnValue({ andWhere: jest.fn().mockReturnValue({ getCount: jest.fn() }) }),
              }),
            });

            await entityService.updateEntity({
              entityUuid: 'entityUuid',
              accountStatus: { canAcquireAmex: false } as any,
            });

            expect(spiedUpdate).toHaveBeenCalledWith(
              'entityUuid',
              {
                entityUuid: 'entityUuid',
                accountStatus: { canAcquireAmex: false, hasAmexPreviousCancelled: true },
                updatedTime: expect.any(String),
              },
              undefined,
              {
                useDomicile: true,
              },
            );
            expect(spiedCallCommandHandler).toHaveBeenCalledWith('entityUuid', 'Entity.Updated', {
              entityUuid: 'entityUuid',
              accountStatus: { canAcquireAmex: false } as any,
            });
          },
        );
      });

      describe('should not update hasAmexPreviousCancelled', () => {
        it.each(
          Object.keys(OnboardingStatus).filter(
            (status) =>
              ![OnboardingStatus.RC_ONBOARDED, OnboardingStatus.ONBOARDED].includes(status as OnboardingStatus),
          ),
        )('if the onboardingStatus is %s', async (onboardingStatus) => {
          const spiedUpdate = jest.spyOn(entityService, 'update').mockResolvedValue({} as any);
          const spiedCallCommandHandler = jest.spyOn(entityService, 'callCommandHandler').mockResolvedValue({} as any);
          jest.spyOn(entityService, 'getEntity').mockImplementation((id: string) =>
            Promise.resolve({
              entityUuid: id,
              onboardingStatus,
              accountStatus: { canAcquireAmex: true, hasAmexPreviousCancelled: false },
            } as any),
          );
          getRepositoryMock.mockReturnValue({
            create: (obj: any) => obj,
            createQueryBuilder: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnValue({ andWhere: jest.fn().mockReturnValue({ getCount: jest.fn() }) }),
            }),
          });
          entityService.findOne = (id: string) => Promise.resolve({ entityUuid: id, onboardingStatus } as Entity);

          await entityService.updateEntity({
            entityUuid: 'entityUuid',
            accountStatus: { canAcquireAmex: false } as any,
          });

          expect(spiedUpdate).toHaveBeenCalledWith(
            'entityUuid',
            {
              entityUuid: 'entityUuid',
              accountStatus: { canAcquireAmex: false },
              updatedTime: expect.any(String),
            },
            undefined,
            {
              useDomicile: true,
            },
          );
          expect(spiedCallCommandHandler).toHaveBeenCalledWith('entityUuid', 'Entity.Updated', {
            entityUuid: 'entityUuid',
            accountStatus: { canAcquireAmex: false } as any,
          });
        });
      });
    });

    it.each([OnboardingStatus.ONBOARDED, OnboardingStatus.RC_ONBOARDED])(
      'should call the amex merchant onboarding request if amex status code is changed - onboardingStatus: %s',
      async (onboardingStatus) => {
        const spy = jest.spyOn(entityService, 'amexMerchantOnboardingRequest');
        const mockUpdate = jest.spyOn(entityService, 'update').mockResolvedValue({} as any);
        entityService.findOneWithOption = (option: any) =>
          Promise.resolve({ entityUuid: option.id, onboardingStatus } as Entity);

        mockUpdate.mockResolvedValueOnce({
          accountStatus: { canAcquireAmex: true, hasAmexPreviousCancelled: false },
        } as any);

        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          accountStatus: { canAcquireAmex: true } as any,
        });
        expect(spy).toHaveBeenCalledTimes(0);

        mockUpdate.mockResolvedValueOnce({
          accountStatus: { canAcquireAmex: false, hasAmexPreviousCancelled: true },
        } as any);

        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          accountStatus: { canAcquireAmex: false } as any,
        });
        expect(spy).toHaveBeenCalledTimes(1);
      },
    );

    it.each(
      Object.keys(OnboardingStatus).filter(
        (status) => ![OnboardingStatus.RC_ONBOARDED, OnboardingStatus.ONBOARDED].includes(status as OnboardingStatus),
      ),
    )(
      'should call the amex merchant onboarding request if amex status code is changed - onboardingStatus: %s',
      async (onboardingStatus) => {
        const spy = jest.spyOn(entityService, 'amexMerchantOnboardingRequest');
        const mockUpdate = jest.spyOn(entityService, 'update').mockResolvedValue({} as any);
        entityService.findOne = (id: string) => Promise.resolve({ entityUuid: id, onboardingStatus } as Entity);

        mockUpdate.mockResolvedValueOnce({
          accountStatus: { canAcquireAmex: true, hasAmexPreviousCancelled: false },
        } as any);

        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          accountStatus: { canAcquireAmex: true } as any,
        });
        expect(spy).toHaveBeenCalledTimes(0);

        mockUpdate.mockResolvedValueOnce({
          accountStatus: { canAcquireAmex: false, hasAmexPreviousCancelled: true },
        } as any);

        await entityService.updateEntity({
          entityUuid: 'entityUuid',
          accountStatus: { canAcquireAmex: false } as any,
        });
        expect(spy).toHaveBeenCalledTimes(0);
      },
    );

    it('should rollback on throw from cbs', async () => {
      when(mockEnvService.enableCbsCreateEntity).thenReturn(true);
      when(mockEnvService.enableCbsEntityFlags).thenReturn(true);
      const entity = {
        entityUuid: v4(),
        accountStatus: {
          canTransferIn: true,
        },
      };

      mockCbsService.updateEntityFlags.mockImplementation(() => {
        throw new Error('this throw is expected');
      });

      await entityService.updateEntity(entity as any);
      expect(mockCbsService.updateEntityFlags).toHaveBeenCalledWith(entity.entityUuid, expect.any(Object));
    });

    it('should call merchant-service when there is a canSettle update', async () => {
      const entityUuid = 'test-entityuuid1';

      when(mockMsService.updateEntityCanSettle(entityUuid, false)).thenResolve({
        status: 200,
        body: 'success',
      });

      const entity = { accountStatus: {} } as Entity;
      const data = { entityUuid, accountStatus: { canSettle: false } } as Entity;
      await expect(
        new CanSettleDownstreamUpdate(instance(mockMsService)).tryPerformDownstreamUpdate(entity, data),
      ).resolves.toEqual({ isRollbackRequired: false });
    });

    it('should rollback when merchant-service rejects canSettle update', async () => {
      const entityUuid = 'test-entityuuid2';

      when(mockMsService.updateEntityCanSettle(entityUuid, false)).thenResolve({
        status: 500,
        body: 'Failed to update',
      });

      const entity = { accountStatus: { canSettle: true } } as Entity;
      const data = { entityUuid, accountStatus: { canSettle: false } } as Entity;
      await expect(
        new CanSettleDownstreamUpdate(instance(mockMsService)).tryPerformDownstreamUpdate(entity, data),
      ).resolves.toEqual({
        isRollbackRequired: true,
        errors: [
          {
            path: 'accountStatus.canSettle',
            errorCode: 'MERCHANT_SERVICE_REJECTED_UPDATE',
            errorMessage: 'merchant-service: [500] Failed to update',
          },
        ],
      });
    });

    it('should reset only CBS-related flags and preserve other flags', async () => {
      const entityUuid = v4();

      const existingAccountStatus = {
        canCreateAccount: false,
        canCreateCard: false,
        canPayByCard: false,
        canTransferIn: true,
        canTransferOut: false,
        hasChargeback: false,
        hasDirectDebitRequest: true,
      };

      entityService.getEntity = jest.fn().mockResolvedValue({
        entityUuid,
        accountStatus: existingAccountStatus,
      });

      (entityService.resetCbsFlags as jest.MockedFunction<EntityService['resetCbsFlags']>).mockImplementationOnce(
        originalResetCbsFlags,
      );
      await entityService.resetCbsFlags(entityUuid);
      expect(entityService.update).toHaveBeenCalledWith(
        entityUuid,
        {
          accountStatus: {
            ...existingAccountStatus,
            canCreateAccount: true,
            canCreateCard: true,
            canPayByCard: true,
            canTransferIn: true,
            canTransferOut: true,
          },
        },
        expect.any(Object),
        {
          useDomicile: true,
        },
      );
    });

    it('should not call external APIs if any feature flags are disabled', async () => {
      when(mockEnvService.enableCbsCreateEntity).thenReturn(false);
      when(mockEnvService.enableCbsEntityFlags).thenReturn(false);
      const entity = {
        entityUuid: v4(),
        domicile: Domicile.AU,
      };
      await entityService.initializeCbsFlags(entity);
      expect(entityService.resetCbsFlags).toHaveBeenCalledWith(entity.entityUuid);
      expect(mockCbsService.createEntity).not.toHaveBeenCalled();
    });

    it('should not call update flags if create entity call fails', async () => {
      when(mockEnvService.enableCbsCreateEntity).thenReturn(true);
      when(mockEnvService.enableCbsEntityFlags).thenReturn(true);
      const entity = {
        entityUuid: v4(),
        domicile: Domicile.AU,
      };
      mockCbsService.createEntity.mockRejectedValueOnce(new Error('oops'));
      await entityService.initializeCbsFlags(entity);
      expect(mockCbsService.createEntity).toHaveBeenCalledWith(entity.entityUuid);
      expect(entityService.resetCbsFlags).toHaveBeenCalledWith(entity.entityUuid);
      expect(mockCbsService.updateEntityFlags).not.toHaveBeenCalled();
    });

    it('should save updates to CBS flags if any flags defaulted', async () => {
      when(mockEnvService.enableCbsCreateEntity).thenReturn(true);
      when(mockEnvService.enableCbsEntityFlags).thenReturn(true);
      const entity: Entity = {
        entityUuid: v4(),
        accountStatus: {
          canCreateAccount: false,
          canCreateCard: false,
          canPayByCard: false,
        } as any,
        domicile: Domicile.AU,
      };
      mockCbsService.updateEntityFlags.mockRejectedValueOnce(new Error('ouch'));
      await entityService.initializeCbsFlags(entity);
      expect(mockCbsService.createEntity).toHaveBeenCalledWith(entity.entityUuid);
      expect(entityService.update).toHaveBeenCalledWith(
        entity.entityUuid,
        {
          accountStatus: {
            canCreateAccount: false,
            canCreateCard: false,
            canPayByCard: false,
            canTransferIn: true,
            canTransferOut: true,
          },
        },
        expect.any(Object),
        {
          useDomicile: true,
        },
      );
      expect(mockCbsService.updateEntityFlags).toHaveBeenCalledWith(entity.entityUuid, expect.any(Object));
    });

    it('should not save updates to CBS flags if all flags provided', async () => {
      when(mockEnvService.enableCbsCreateEntity).thenReturn(true);
      when(mockEnvService.enableCbsEntityFlags).thenReturn(true);
      const entity: Entity = {
        entityUuid: v4(),
        accountStatus: {
          canCreateAccount: false,
          canCreateCard: false,
          canPayByCard: false,
          canTransferIn: false,
          canTransferOut: false,
        } as any,
        domicile: Domicile.AU,
      };
      mockCbsService.updateEntityFlags.mockRejectedValueOnce(new Error('it hurts'));
      await entityService.initializeCbsFlags(entity);
      expect(entityService.update).not.toHaveBeenCalled();
      expect(mockCbsService.updateEntityFlags).toHaveBeenCalledWith(entity.entityUuid, entity.accountStatus);
    });

    it('should reset CBS flags if update flags call fails', async () => {
      when(mockEnvService.enableCbsCreateEntity).thenReturn(true);
      when(mockEnvService.enableCbsEntityFlags).thenReturn(true);
      const entity = {
        entityUuid: v4(),
        domicile: Domicile.AU,
      };
      mockCbsService.updateEntityFlags.mockRejectedValueOnce(new Error('my pizza'));
      await entityService.initializeCbsFlags(entity);
      expect(mockCbsService.updateEntityFlags).toHaveBeenCalledWith(entity.entityUuid, expect.any(Object));
      expect(entityService.resetCbsFlags).toHaveBeenCalledWith(entity.entityUuid);
    });

    it('should not call reset CBS flags if successfully updates CBS flags', async () => {
      when(mockEnvService.enableCbsCreateEntity).thenReturn(true);
      when(mockEnvService.enableCbsEntityFlags).thenReturn(true);
      const entity: Entity = {
        entityUuid: v4(),
        domicile: Domicile.AU,
      };
      mockCbsService.updateEntityFlags.mockResolvedValueOnce({
        isError: false,
      } as UpdateEntityFlagsResponse);
      await entityService.initializeCbsFlags(entity);
      expect(mockCbsService.updateEntityFlags).toHaveBeenCalledWith(entity.entityUuid, expect.any(Object));
      expect(entityService.resetCbsFlags).not.toHaveBeenCalled();
    });
  });

  describe('pos interface', () => {
    beforeEach(() => {
      entityService.getEntity = () => Promise.resolve({ onboardingStatus: OnboardingStatus.RC_ONBOARDED } as Entity);
      entityService.update = () => Promise.resolve({} as Entity);
      getRepositoryMock.mockReturnValue({
        create: jest.fn().mockResolvedValue({} as any),
        save: jest.fn().mockResolvedValue({} as any),
        findOne: (id: string) => jest.fn().mockResolvedValue({ id, onboardingStatus: OnboardingStatus.ONBOARDED }),
        createQueryBuilder: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({ andWhere: jest.fn().mockReturnValue({ getCount: jest.fn() }) }),
        }),
      });
      (mockHlPosService.disconnectByEntityUuid as jest.Mock).mockClear();
      (mockOraclePosService.disconnectByEntityUuid as jest.Mock).mockClear();
      (mockImposPosService.disconnectByEntityUuid as jest.Mock).mockClear();
      when(mockCustomerEntityService.findAllCustomers).thenReturn(() => Promise.resolve([] as CustomerEntity[]));
      when(mockDeviceService.find(anything())).thenResolve([]);
    });
    it('should disconnect pos interface when entity status is changed to disable', async () => {
      const entityUuid = 'entityUuid';
      const dto = {
        entityUuid,
        onboardingStatus: OnboardingStatus.RC_REJECTED,
        riskReview: {
          status: RiskReviewStatus.COMPLETED,
          result: RiskReviewResult.REJECTED,
        },
      };
      await entityService.updateEntity(dto, { allowFinaliseOnboardingStatus: true });
      expect(mockHlPosService.disconnectByEntityUuid).toHaveBeenCalledTimes(1);
      expect(mockOraclePosService.disconnectByEntityUuid).toHaveBeenCalledTimes(1);
      expect(mockImposPosService.disconnectByEntityUuid).toHaveBeenCalledTimes(1);
    });
  });

  describe('updatePaymentSettings', () => {
    const originalPaymentLimits = [
      {
        paymentType: PaymentType.MOTO,
        maximum: '100000',
        minimum: '100',
      },
      {
        paymentType: PaymentType.CP,
        maximum: '200000',
        minimum: '200',
      },
      {
        paymentType: PaymentType.CNP,
        maximum: '300000',
        minimum: '300',
      },
      {
        paymentType: PaymentType.CPOC,
        maximum: '400000',
        minimum: '400',
      },
    ];
    beforeEach(() => {
      entityService.getEntity = () => Promise.resolve({ paymentLimits: originalPaymentLimits } as Entity);
      entityService.update = () => Promise.resolve({} as Entity);
      getRepositoryMock.mockReturnValue({
        create: jest.fn().mockResolvedValue({} as any),
        findOne: (id: string) => jest.fn().mockResolvedValue({ id }),
        createQueryBuilder: jest.fn().mockReturnValue({
          where: jest.fn().mockReturnValue({ andWhere: jest.fn().mockReturnValue({ getCount: jest.fn() }) }),
        }),
      });
    });

    it('should be able to complete full payment limit update', async () => {
      const updateCommandSpy = jest.spyOn(entityService, 'update');
      const dto = {
        entityUuid: v4(),
        paymentLimits: [
          {
            paymentType: PaymentType.MOTO,
            maximum: '500000',
            minimum: '500',
          },
          {
            paymentType: PaymentType.CP,
            maximum: '600000',
            minimum: '600',
          },
          {
            paymentType: PaymentType.CNP,
            maximum: '700000',
            minimum: '700',
          },
          {
            paymentType: PaymentType.CPOC,
            maximum: '800000',
            minimum: '800',
          },
        ],
      };
      await entityService.updatePaymentSettings(dto);

      expect(updateCommandSpy).toHaveBeenCalledWith(
        dto.entityUuid,
        { paymentLimits: dto.paymentLimits },
        {
          successCmd: 'Entity.PaymentSettingsUpdated',
          successEvent: dto,
        },
        {
          useDomicile: true,
        },
      );
    });

    it.each([
      [
        'MOTO',
        {
          paymentType: PaymentType.MOTO,
          maximum: '500000',
          minimum: '500',
        },
      ],
      [
        'CP',
        {
          paymentType: PaymentType.CP,
          maximum: '600000',
          minimum: '600',
        },
      ],
      [
        'CPOC',
        {
          paymentType: PaymentType.CPOC,
          maximum: '700000',
          minimum: '700',
        },
      ],
      [
        'CNP',
        {
          paymentType: PaymentType.CNP,
          maximum: '800000',
          minimum: '800',
        },
      ],
    ])('should be able to complete a partial %s payment limit update', async (_, paymentLimit) => {
      const updateCommandSpy = jest.spyOn(entityService, 'update');
      const dto = {
        entityUuid: v4(),
        paymentLimits: [paymentLimit],
      };
      const allPaymentLimits = [...originalPaymentLimits];
      const paymentLimitIndex = originalPaymentLimits.findIndex(
        (limit) => limit.paymentType === paymentLimit.paymentType,
      );
      allPaymentLimits[paymentLimitIndex] = paymentLimit;
      await entityService.updatePaymentSettings(dto);

      expect(updateCommandSpy).toHaveBeenCalledWith(
        dto.entityUuid,
        { paymentLimits: allPaymentLimits },
        {
          successCmd: 'Entity.PaymentSettingsUpdated',
          successEvent: dto,
        },
        {
          useDomicile: true,
        },
      );
    });

    it('should be able to update payment limits if original is empty', async () => {
      entityService.getEntity = () => Promise.resolve({} as Entity);
      const updateCommandSpy = jest.spyOn(entityService, 'update');
      const dto = {
        entityUuid: v4(),
        paymentLimits: [
          {
            paymentType: PaymentType.MOTO,
            maximum: '500000',
            minimum: '500',
          },
          {
            paymentType: PaymentType.CP,
            maximum: '600000',
            minimum: '600',
          },
          {
            paymentType: PaymentType.CNP,
            maximum: '700000',
            minimum: '700',
          },
          {
            paymentType: PaymentType.CPOC,
            maximum: '800000',
            minimum: '800',
          },
        ],
      };
      await entityService.updatePaymentSettings(dto);

      expect(updateCommandSpy).toHaveBeenCalledWith(
        dto.entityUuid,
        { paymentLimits: dto.paymentLimits },
        {
          successCmd: 'Entity.PaymentSettingsUpdated',
          successEvent: dto,
        },
        {
          useDomicile: true,
        },
      );
    });
  });

  describe('onboardingStatusUpdatedProjection', () => {
    it.each([undefined, { hasEverOnboarded: true }])('should not update entity hasEverOnboarded', async (entity) => {
      when(mockCustomerEntityService.getRegisteringIndividualByEntityUuid).thenReturn(() =>
        Promise.resolve({} as any as Customer),
      );
      when(mockContactService.createSelfContactFromEntityAndCustomer).thenReturn(() => Promise.resolve());
      const spy = jest.spyOn(entityService, 'update');
      entityService.findOne = jest.fn().mockResolvedValue(entity);
      const dto = {
        onboardingStatus: OnboardingStatus.ONBOARDED,
      } as EntityOnboardingStatusUpdatedEventDto;

      await entityService.onboardingStatusUpdatedProjection(dto);

      expect(spy).not.toHaveBeenCalled();
    });

    it('should not update entity hasEverOnboarded when customer not found', async () => {
      when(mockCustomerEntityService.getRegisteringIndividualByEntityUuid).thenReturn(() => Promise.resolve(undefined));
      const spy = jest.spyOn(entityService, 'update');

      entityService.findOne = jest.fn().mockResolvedValue({ hasEverOnboarded: false });
      const dto = {
        onboardingStatus: OnboardingStatus.ONBOARDED,
      } as EntityOnboardingStatusUpdatedEventDto;

      await entityService.onboardingStatusUpdatedProjection(dto);

      expect(spy).not.toHaveBeenCalled();
    });
  });

  describe('checkIfAmexStatusCodeChanged', () => {
    describe('Non onboarded status', () => {
      it.each(
        Object.keys(OnboardingStatus).filter(
          (status) => ![OnboardingStatus.RC_ONBOARDED, OnboardingStatus.ONBOARDED].includes(status as OnboardingStatus),
        ),
      )('should return false if the current onboarding status is %s', async (onboardingStatus) => {
        const existingEntity = {
          onboardingStatus,
        } as any;

        const updatedEntity = {} as any;
        const result = entityService.checkIfAmexStatusCodeChanged(existingEntity, updatedEntity);
        expect(result).toBe(false);
      });
    });

    describe.each([OnboardingStatus.RC_ONBOARDED, OnboardingStatus.ONBOARDED])(
      'Onboarded status: %s',
      (onboardingStatus) => {
        it('should return true if the Amex status code has changed', () => {
          const existingEntity = {
            accountStatus: {
              canAcquireAmex: true,
              hasAmexPreviousCancelled: false,
            },
            onboardingStatus,
          } as any;
          const updatedEntity = {
            accountStatus: {
              canAcquireAmex: false,
              hasAmexPreviousCancelled: true,
            },
          } as any;

          const result = entityService.checkIfAmexStatusCodeChanged(existingEntity, updatedEntity);

          expect(result).toBe(true);
        });

        it('should return false if the Amex status code has not changed', () => {
          const existingEntity = {
            accountStatus: {
              canAcquireAmex: true,
              hasAmexPreviousCancelled: false,
            },
          } as any;
          const updatedEntity = {
            accountStatus: {
              canAcquireAmex: true,
              hasAmexPreviousCancelled: false,
            },
          } as any;
          const result = entityService.checkIfAmexStatusCodeChanged(existingEntity, updatedEntity);

          expect(result).toBe(false);
        });
      },
    );
  });

  describe('initializeEntityCanAcquireAmexFlag', () => {
    it('should update the entity account status and trigger the success event', async () => {
      const entityUuid = 'entityUuid';
      const canAcquireAmex = true;
      const updatingValue = { accountStatus: { canAcquireAmex, hasAmexPreviousCancelled: !canAcquireAmex } };
      const successEvent = { entityUuid, accountStatus: { canAcquireAmex } };

      const updateSpy = jest.spyOn(entityService, 'update').mockResolvedValueOnce({} as any);

      await entityService.initializeEntityCanAcquireAmexFlag(entityUuid, canAcquireAmex);

      expect(updateSpy).toHaveBeenCalledWith(
        entityUuid,
        updatingValue,
        {
          successCmd: 'Entity.Updated',
          successEvent,
        },
        {
          useDomicile: true,
        },
      );
    });
  });

  describe('attachEntityDomicileAndCurrency', () => {
    it('should update the entity domicile and currency and trigger the success event', async () => {
      const event = {
        entityUuid: 'entityUuid',
        domicile: 'AUS',
      };
      const successEvent = { entityUuid: event.entityUuid, domicile: event.domicile, currency: 'AUD' };

      const updateSpy = jest.spyOn(entityService, 'update').mockResolvedValueOnce({} as any);

      await entityService.attachEntityDomicileAndCurrency(event);

      expect(updateSpy).toHaveBeenCalledWith(
        event.entityUuid,
        { domicile: 'AUS', currency: 'AUD' },
        {
          successCmd: 'Entity.DomicileCurrencyAttached',
          successEvent,
        },
        {
          useDomicile: true,
        },
      );
    });
  });

  describe('updateEntity: [Entity.Updated]', () => {
    let spy: jest.SpyInstance;
    const entityUuid = 'entity-123';
    const setup = () => {
      entityService.callCommandHandler = jest.fn();
      spy = jest.spyOn(entityService, 'callCommandHandler');
    };

    beforeEach(() => {
      setup();
      getRepositoryMock.mockReturnValue({
        create: jest.fn().mockReturnValue({} as any),
        findOne: (id: string) => jest.fn().mockResolvedValue({ id, entityUuid }),
      });
    });

    it('should emit EntityHubspotObjectIdUpdated event when hubspotCompanyId is present', async () => {
      const payload: EntityUpdatedEventDto = {
        entityUuid,
        hubspotCompanyId: 'hubspot-123',
      };
      await entityService.updateEntity(payload);
      expect(spy).toHaveBeenCalledWith(
        entityUuid,
        expect.stringContaining('Entity.HubspotObjectIdUpdated'),
        expect.objectContaining({ entityUuid, hubspotCompanyId: 'hubspot-123' }),
      );
    });

    it('should emit EntityUpdated event if something to update apart from hubspotCompanyId', async () => {
      const payload: EntityUpdatedEventDto = {
        entityUuid,
        name: 'test-entity',
      };
      await entityService.updateEntity(payload);
      expect(spy).toHaveBeenCalledWith(
        entityUuid,
        expect.stringContaining('Entity.Updated'),
        expect.objectContaining({ entityUuid, name: 'test-entity' }),
      );
    });

    it('should not emit Entity.Updated when only entityUuid is present', async () => {
      const payload: EntityUpdatedEventDto = {
        entityUuid,
      };
      await entityService.updateEntity(payload);
      expect(spy).not.toHaveBeenCalledWith(
        entityUuid,
        expect.stringContaining('Entity.Updated'),
        expect.objectContaining({ entityUuid }),
      );
    });
  });

  describe('createEntity - extractEntityDetails', () => {
    let mockCustomerRepo: Partial<Repository<Customer>>;
    let mockCustomerEntityRepo: Partial<Repository<CustomerEntity>>;

    beforeEach(() => {
      mockCustomerRepo = { findOne: jest.fn() };
      mockCustomerEntityRepo = { findOne: jest.fn() };
      jest.spyOn(entityService, 'getCustomerRepository').mockReturnValue(mockCustomerRepo as Repository<Customer>);
      jest
        .spyOn(entityService, 'getCustomerEntityRepository')
        .mockReturnValue(mockCustomerEntityRepo as Repository<CustomerEntity>);
      jest
        .spyOn(entityService, 'getRegisteringIndividual')
        .mockResolvedValue({ customerUuid: 'uuid', domicile: 'AUS' } as any);
    });

    it('should extract details for EntityGqlCreateRequestedEventDto when valid', async () => {
      const mockEvent = new EntityGqlCreateRequestedEventDto({
        name: 'Test-Company',
        type: EntityType.INDIVIDUAL,
        categoryGroup: 'categoryGroup',
        category: 'category',
        estimatedAnnualRevenue: 100000,
        sourceIp: '127.0.0.1',
        registeringIndividual: 'cust-123',
        businessDetails: {
          ausBusiness: { abn: '123' },
        },
        country: 'AUS',
      });
      (mockCustomerRepo.findOne as jest.Mock).mockResolvedValue({ customerUuid: 'cust-123', domicile: 'AUS' });
      (mockCustomerEntityRepo.findOne as jest.Mock).mockResolvedValue(undefined);
      const result = await entityService.extractEntityDetails(mockEvent, Domicile.AU);
      expect(result.entityUuid).toBeDefined();
      expect(result.abn).toBe('123');
      expect(result.registeringIndividual.customerUuid).toBe('cust-123');
    });

    it('should throw error if customer not found', async () => {
      const mockEvent = new EntityGqlCreateRequestedEventDto({
        name: 'Test-Company',
        type: EntityType.INDIVIDUAL,
        categoryGroup: 'categoryGroup',
        category: 'category',
        estimatedAnnualRevenue: 100000,
        sourceIp: '127.0.0.1',
        registeringIndividual: 'missing-user',
        country: 'AUS',
      });
      (mockCustomerRepo.findOne as jest.Mock).mockResolvedValue(undefined);
      await expect(entityService.extractEntityDetails(mockEvent, Domicile.AU)).rejects.toThrow(
        `Customer not found for registeringIndividual: missing-user`,
      );
    });

    it('should throw error if customer is already associated with an entity', async () => {
      const mockEvent = new EntityGqlCreateRequestedEventDto({
        name: 'Test-Company',
        type: EntityType.INDIVIDUAL,
        categoryGroup: 'categoryGroup',
        category: 'category',
        estimatedAnnualRevenue: 100000,
        sourceIp: '127.0.0.1',
        registeringIndividual: 'cust-123',
        country: 'AUS',
      });
      (mockCustomerRepo.findOne as jest.Mock).mockResolvedValue({ customerUuid: 'cust-123', domicile: 'AUS' });
      (mockCustomerEntityRepo.findOne as jest.Mock).mockResolvedValue({
        entityUuid: 'ent-001',
      });
      await expect(entityService.extractEntityDetails(mockEvent, Domicile.AU)).rejects.toThrow(
        `RegisteringIndividual: cust-123 is already associated with another entity: ent-001`,
      );
    });

    it('should extract details for EntityCreateRequestedEventDto', async () => {
      const mockEvent: EntityCreateRequestedEventDto = {
        entityUuid: 'entity-uuid',
        name: 'Test-Company',
        type: EntityType.INDIVIDUAL,
        acn: 'acn-1',
        sourceIp: '127.0.0.1',
      };
      const result = await entityService.extractEntityDetails(mockEvent, Domicile.AU);
      expect(result.entityUuid).toBe('entity-uuid');
      expect(result.acn).toBe('acn-1');
      expect(result.registeringIndividual.customerUuid).toBe('uuid');
    });
  });

  describe('createEntity - updateCustomerWithChecks', () => {
    let mockCustomerRepo: Partial<Repository<any>>;
    let mockCallCommandHandler: jest.SpyInstance;

    beforeEach(() => {
      mockCustomerRepo = {
        findOne: jest.fn(),
        update: jest.fn(),
      };

      jest.spyOn(entityService, 'getCustomerRepository').mockReturnValue(mockCustomerRepo as Repository<any>);
      mockCallCommandHandler = jest.spyOn(entityService, 'callCommandHandler').mockResolvedValue(undefined);
      jest.spyOn(productUtils, 'createBaseProductTourStatus').mockReturnValue({ showAdminMerchantPortalWelcome: true });
    });

    it('should update customer with required statuses when not verified or completed', async () => {
      const customerUuid = 'cust-123';
      const entityUuid = 'entity-456';
      const attributionService = MutationAttributionService.getInstance();
      attributionService.mutationAttribution = {
        userIdentifier: 'userIdentifier',
        tokenGrant: MutationAttributionTokenGrant.USER_PASS,
        userRole: MutationAttributionUserRole.MANAGER,
        platform: MutationAttributionPlatform.TERMINAL,
        createdTimestamp: new Date().getTime(),
        sessionKey: 'sessionId',
        reason: 'reason',
      };

      (mockCustomerRepo.findOne as jest.Mock).mockResolvedValue({
        customerUuid,
        kyc: { status: 'NOT_VERIFIED' },
        idv: { status: 'PENDING' },
        safeharbour: { status: 'PENDING' },
        screening: { status: 'PENDING' },
        productTourStatus: {
          showAdminMerchantPortalWelcome: false,
        },
        defaultEntityUuid: null,
      });

      const input: CustomerCreateRequestedEventDto = {
        entityUuid,
        customerUuid,
        role: CustomerRole.ADMIN,
        registeringIndividual: true,
        status: Status.ACTIVE,
        createdTime: `${Date.now()}`,
      };

      await entityService.updateCustomerWithChecks(input, Domicile.AU);
      expect(mockCustomerRepo.update).toHaveBeenCalledWith(
        { customerUuid, domicile: Domicile.AU },
        expect.objectContaining({
          kyc: expect.objectContaining({ status: 'REQUIRED' }),
          idv: expect.objectContaining({ status: 'REQUIRED' }),
          safeharbour: expect.objectContaining({ status: 'REQUIRED' }),
          screening: expect.objectContaining({ status: 'REQUIRED' }),
          productTourStatus: expect.objectContaining({
            showAdminMerchantPortalWelcome: true,
          }),
          defaultEntityUuid: entityUuid,
        }),
      );

      expect(mockCallCommandHandler).toHaveBeenCalledWith(
        customerUuid,
        'Customer.Updated',
        {
          idv: { status: 'REQUIRED' },
          kyc: { status: 'REQUIRED' },
          productTourStatus: { showAdminMerchantPortalWelcome: true },
          safeharbour: { status: 'REQUIRED' },
          screening: { status: 'REQUIRED' },
          defaultEntityUuid: 'entity-456',
        },
        attributionService.mutationAttribution,
      );
    });

    it('should NOT update statuses if they are already verified/completed and product tours shown and defaultEntityUuid exists', async () => {
      const customerUuid = 'cust-456';
      const entityUuid = 'entity-789';

      (mockCustomerRepo.findOne as jest.Mock).mockResolvedValue({
        customerUuid,
        kyc: { status: 'VERIFIED' },
        idv: { status: 'COMPLETED' },
        safeharbour: { status: 'COMPLETED' },
        screening: { status: 'COMPLETED' },
        productTourStatus: {
          showAdminMerchantPortalWelcome: true,
        },
        defaultEntityUuid: 'existing-entity-uuid',
      });

      const input: CustomerCreateRequestedEventDto = {
        entityUuid,
        customerUuid,
        role: CustomerRole.ADMIN,
        registeringIndividual: true,
        status: Status.ACTIVE,
        createdTime: `${Date.now()}`,
      };

      await entityService.updateCustomerWithChecks(input, Domicile.AU);
      expect(mockCustomerRepo.update).not.toHaveBeenCalled();
      expect(mockCallCommandHandler).not.toHaveBeenCalled();
    });

    it('should throw ServerError on repository failure', async () => {
      const customerUuid = 'cust-999';
      const entityUuid = 'entity-999';

      (mockCustomerRepo.findOne as jest.Mock).mockRejectedValue(new Error('DB failure'));

      const input: CustomerCreateRequestedEventDto = {
        entityUuid,
        customerUuid,
        role: CustomerRole.ADMIN,
        registeringIndividual: true,
        status: Status.ACTIVE,
        createdTime: `${Date.now()}`,
      };
      await expect(entityService.updateCustomerWithChecks(input, Domicile.AU)).rejects.toThrow(
        'Failed to update customer with checks: Error: DB failure',
      );
    });
  });
});
