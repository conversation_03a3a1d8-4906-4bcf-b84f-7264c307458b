import type { ReportRange, ReportType } from './index.js';

export type NetAmount = {
  reportType: ReportType;
  range: ReportRange;
  timeZone: string;
  accountUuid?: string;
};

export type NetAmountLambdaInput = {
  date: string;
  reportType: ReportType;
  entityUuid: string;
  timeZone?: string;
  accountUuid?: string;
} & SortingAndLimitInput;

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum SortBy {
  TOTAL = 'TOTAL',
  AVERAGE = 'AVERAGE',
  NO_OF_TRANSACTION = 'NO_OF_TRANSACTION',
  AZ = 'AZ',
}

export type SortingAndLimitInput = {
  sortBy?: SortBy;
  sortOrder?: SortOrder;
  limit?: number;
};
