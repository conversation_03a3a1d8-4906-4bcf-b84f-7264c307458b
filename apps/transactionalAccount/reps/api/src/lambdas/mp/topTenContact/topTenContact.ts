import { appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware/sessionIdentityMiddleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { ReportType, ResolverHandler, TransactionDirection } from '../../../common/types/index.js';
import { getTopTenContact } from '../../../services/topTenContact/topTenContact.js';
import { domicileMiddleware } from '../../middleware/domicile.js';
import { getEntityAggregateId } from '../../utils/aggregateId.js';

const lambdaHandler: ResolverHandler<{
  entityUuid: string;
  date: string;
  reportType: ReportType;
  transactionDirection: TransactionDirection;
  timeZone?: string;
  accountUuid?: string;
}> = async (event, _context) => {
  debug(`getTopTenContactHandler request args -> ${JSON.stringify(event.args)}`, event.args.entityUuid);

  return getTopTenContact(event.args, event.domicile);
};

export const getTopTenContactHandler = withMiddlewaresV2(
  { component: ZellerComponent.REPS, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [appIdentityMiddleware(true), xrayAggregateMiddleware(getEntityAggregateId(lambdaHandler)), domicileMiddleware],
);
