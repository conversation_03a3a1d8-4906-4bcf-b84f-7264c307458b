import type { EntityUserContext } from '@npco/component-bff-core/dist/types/context';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import type { Callback, Context } from 'aws-lambda';

import { type ResolverEvent, ReportType, TransactionDirection } from '../../../common/types/index.js';
import '../../utils/testing/middlewareMock.js';
import { getBaseAppSyncResolverEvent } from '../../utils/testing/baseEvent.js';

const getTopTenContact = jest.fn();

jest.mock('../../../services/topTenContact/topTenContact.js', () => ({
  __esModule: true,
  getTopTenContact,
}));
const mockContext = {} as any;
const mockRequest = {} as any;

describe('top 10 contact lambdas', () => {
  const entityUuid = 'entityUuid';
  const domicile = Domicile.AU;

  let getTopTenContactHandler: (
    event: ResolverEvent<{
      entityUuid: string;
      date: string;
      reportType: ReportType;
      transactionDirection: TransactionDirection;
      timeZone?: string;
      accountUuid?: string;
    }>,
    context: Context & EntityUserContext,
    callback: Callback,
  ) => void;

  beforeEach(async () => {
    const lambdas = await import('./topTenContact.js');

    getTopTenContactHandler = lambdas.getTopTenContactHandler;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should be able invoke handler ', (done) => {
    getTopTenContact.mockResolvedValueOnce('any' as any);
    const date = '2024-01-01';
    const reportType = ReportType.MONTHLY;
    const event = getBaseAppSyncResolverEvent({
      args: {
        entityUuid,
        date,
        reportType,
        transactionDirection: TransactionDirection.EXPENSE,
      },
      request: mockRequest,
    });
    getTopTenContactHandler({ ...event, domicile }, mockContext, (error: any, response: any) => {
      expect(error).toBeNull();
      expect(getTopTenContact).toHaveBeenCalledTimes(1);
      expect(getTopTenContact).toHaveBeenCalledWith(event.args, domicile);
      expect(response).toEqual('any');
      done();
    });
  });
});
