import { appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware/sessionIdentityMiddleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { NetAmountLambdaInput, ResolverHandler, TransactionDirection } from '../../../common/types/index.js';
import { getContactsNetAmount } from '../../../services/contact/contact.js';
import { domicileMiddleware } from '../../middleware/domicile.js';

const getAggregateId = (_: Parameters<typeof lambdaHandler>[0], context: Parameters<typeof lambdaHandler>[1]) =>
  context.entityUuid;

const lambdaHandler: ResolverHandler<NetAmountLambdaInput & { transactionDirection: TransactionDirection }> = async (
  event,
  _context,
) => {
  debug(`getContactsNetAmountHandler request args -> ${JSON.stringify(event.args)}`, event.args.entityUuid);

  return getContactsNetAmount(event.args, event.domicile);
};

export const getContactsNetAmountHandler = withMiddlewaresV2(
  { component: ZellerComponent.REPS, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [appIdentityMiddleware(true), xrayAggregateMiddleware(getAggregateId), domicileMiddleware],
);
