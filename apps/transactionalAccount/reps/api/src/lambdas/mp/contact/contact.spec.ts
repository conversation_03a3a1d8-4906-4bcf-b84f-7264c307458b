import type { EntityUserContext } from '@npco/component-bff-core/dist/types/context';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import type { Callback, Context } from 'aws-lambda';

import {
  type NetAmountLambdaInput,
  ReportType,
  SortBy,
  SortOrder,
  TransactionDirection,
  type ResolverEvent,
} from '../../../common/types/index.js';
import '../../utils/testing/middlewareMock.js';
import { getBaseAppSyncResolverEvent } from '../../utils/testing/baseEvent.js';

const getContactsNetAmount = jest.fn();

jest.mock('../../../services/contact/contact.js', () => ({
  __esModule: true,
  getContactsNetAmount,
}));
const mockContext = {} as any;
const mockRequest = {} as any;

describe('contact new amount lambdas', () => {
  const entityUuid = 'entityUuid';
  const domicile = Domicile.AU;
  let getContactsNetAmountHandler: (
    event: ResolverEvent<NetAmountLambdaInput & { transactionDirection: TransactionDirection }>,
    context: Context & EntityUserContext,
    callback: Callback,
  ) => void;

  beforeEach(async () => {
    const lambdas = await import('./contact.js');

    getContactsNetAmountHandler = lambdas.getContactsNetAmountHandler;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should be able invoke handler ', (done) => {
    getContactsNetAmount.mockResolvedValueOnce('any' as any);
    const date = '2024-01-01';
    const transactionDirection = TransactionDirection.EXPENSE;
    const reportType = ReportType.MONTHLY;
    const event = getBaseAppSyncResolverEvent({
      args: {
        entityUuid,
        date,
        reportType,
        transactionDirection,
        sortBy: SortBy.TOTAL,
        sortOrder: SortOrder.DESC,
        limit: 1,
      },
      request: mockRequest,
    });
    getContactsNetAmountHandler({ ...event, domicile }, mockContext, (error: any, response: any) => {
      expect(error).toBeNull();
      expect(getContactsNetAmount).toHaveBeenCalledTimes(1);
      expect(getContactsNetAmount).toHaveBeenCalledWith(event.args, domicile);
      expect(response).toEqual('any');
      done();
    });
  });
});
