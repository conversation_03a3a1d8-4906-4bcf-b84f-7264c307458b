import type { EntityUserContext } from '@npco/component-bff-core/dist/types/context';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories } from '@npco/component-dto-core';

import type { Callback, Context } from 'aws-lambda';

import { type ResolverEvent, ReportType } from '../../../common/types/index.js';
import '../../utils/testing/middlewareMock.js';
import { getBaseAppSyncResolverEvent } from '../../utils/testing/baseEvent.js';

const getSubcategoriesNetAmount = jest.fn();

jest.mock('../../../services/category/category.js', () => ({
  __esModule: true,
  getSubcategoriesNetAmount,
}));
const mockContext = {} as any;
const mockRequest = {} as any;

describe('subcategories net amount lambdas', () => {
  const entityUuid = 'entityUuid';
  const domicile = Domicile.AU;
  let getSubcategoriesNetAmountHandler: (
    event: ResolverEvent<{
      category: EntityCategories;
      date: string;
      reportType: ReportType;
      timeZone?: string | undefined;
      accountUuid?: string | undefined;
    }>,
    context: Context & EntityUserContext,
    callback: Callback,
  ) => void;

  beforeEach(async () => {
    const lambdas = await import('./subCategory.js');

    getSubcategoriesNetAmountHandler = lambdas.getSubcategoriesNetAmountHandler;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should be able invoke handler ', (done) => {
    getSubcategoriesNetAmount.mockResolvedValueOnce('any' as any);
    const date = '2024-01-01';
    const category = EntityCategories.BANK_FEES;
    const reportType = ReportType.MONTHLY;
    const event = getBaseAppSyncResolverEvent({
      args: {
        entityUuid,
        category,
        date,
        reportType,
      },
      request: mockRequest,
    });
    getSubcategoriesNetAmountHandler({ ...event, domicile }, mockContext, (error: any, response: any) => {
      expect(error).toBeNull();
      expect(getSubcategoriesNetAmount).toHaveBeenCalledTimes(1);
      expect(getSubcategoriesNetAmount).toHaveBeenCalledWith(event.args, domicile);
      expect(response).toEqual('any');
      done();
    });
  });
});
