import { appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware/sessionIdentityMiddleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { NetAmountLambdaInput, ResolverHandler } from '../../../common/types/index.js';
import { getCategoriesNetAmount } from '../../../services/category/category.js';
import { domicileMiddleware } from '../../middleware/domicile.js';
import { getEntityAggregateId } from '../../utils/aggregateId.js';

const lambdaHandler: ResolverHandler<NetAmountLambdaInput> = async (event, _context) => {
  debug(`getCategoriesNetAmountHandler request args -> ${JSON.stringify(event.args)}`, event.args.entityUuid);

  return getCategoriesNetAmount(event.args, event.domicile);
};

export const getCategoriesNetAmountHandler = withMiddlewaresV2(
  { component: ZellerComponent.REPS, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [appIdentityMiddleware(true), xrayAggregateMiddleware(getEntityAggregateId(lambdaHandler)), domicileMiddleware],
);
