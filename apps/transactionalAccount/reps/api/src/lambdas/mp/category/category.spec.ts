import type { EntityUserContext } from '@npco/component-bff-core/dist/types/context';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import type { Callback, Context } from 'aws-lambda';

import {
  type ResolverEvent,
  ReportType,
  type NetAmountLambdaInput,
  SortBy,
  SortOrder,
} from '../../../common/types/index.js';
import '../../utils/testing/middlewareMock.js';
import { getBaseAppSyncResolverEvent } from '../../utils/testing/baseEvent.js';

const getCategoriesNetAmount = jest.fn();

jest.mock('../../../services/category/category.js', () => ({
  __esModule: true,
  getCategoriesNetAmount,
}));
const mockContext = {} as any;
const mockRequest = {} as any;

describe('category net amount lambdas', () => {
  const domicile = Domicile.AU;
  const entityUuid = 'entityUuid';
  let getCategoriesNetAmountHandler: (
    event: ResolverEvent<NetAmountLambdaInput>,
    context: Context & EntityUserContext,
    callback: Callback,
  ) => void;

  beforeEach(async () => {
    const lambdas = await import('./category.js');

    getCategoriesNetAmountHandler = lambdas.getCategoriesNetAmountHandler;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should be able invoke handler ', (done) => {
    getCategoriesNetAmount.mockResolvedValueOnce('any' as any);
    const date = '2024-01-01';
    const reportType = ReportType.MONTHLY;
    const event = getBaseAppSyncResolverEvent({
      args: {
        entityUuid,
        date,
        reportType,
        sortBy: SortBy.TOTAL,
        sortOrder: SortOrder.DESC,
        limit: 1,
      },
      request: mockRequest,
    });

    getCategoriesNetAmountHandler({ ...event, domicile }, mockContext, (error: any, response: any) => {
      expect(error).toBeNull();
      expect(getCategoriesNetAmount).toHaveBeenCalledTimes(1);
      expect(getCategoriesNetAmount).toHaveBeenCalledWith(event.args, domicile);
      expect(response).toEqual('any');
      done();
    });
  });
});
