import type { EntityUserContext } from '@npco/component-bff-core/dist/types/context';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import type { Callback, Context } from 'aws-lambda';

import { type ResolverEvent, ReportType, TransactionDirection } from '../../../common/types/index.js';
import '../../utils/testing/middlewareMock.js';
import { getBaseAppSyncResolverEvent } from '../../utils/testing/baseEvent.js';

const getAllCategoriesTotal = jest.fn();

jest.mock('../../../../config/environmentService');

jest.mock('../../../services/allCategoriesTotal/allCategoriesTotal.js', () => ({
  __esModule: true,
  getAllCategoriesTotal,
}));
const mockContext = {} as any;
const mockRequest = {} as any;

describe('all categories total lambdas', () => {
  const entityUuid = 'entityUuid';
  const domicile = Domicile.AU;
  let getAllCategoriesTotalHandler: (
    event: ResolverEvent<{
      entityUuid: string;
      date: string;
      reportType: ReportType;
      transactionDirection: TransactionDirection;
      timeZone?: string;
      accountUuid?: string;
    }>,
    context: Context & EntityUserContext,
    callback: Callback,
  ) => void;

  beforeEach(async () => {
    const lambdas = await import('./allCategoriesTotal.js');

    getAllCategoriesTotalHandler = lambdas.getAllCategoriesTotalHandler;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should be able invoke handler ', (done) => {
    getAllCategoriesTotal.mockResolvedValueOnce('any' as any);
    const date = '2024-01-01';
    const reportType = ReportType.MONTHLY;
    const event = getBaseAppSyncResolverEvent({
      args: {
        entityUuid,
        date,
        reportType,
        transactionDirection: TransactionDirection.EXPENSE,
      },
      request: mockRequest,
    });
    getAllCategoriesTotalHandler({ ...event, domicile }, mockContext, (error: any, response: any) => {
      expect(error).toBeNull();
      expect(getAllCategoriesTotal).toHaveBeenCalledTimes(1);
      expect(getAllCategoriesTotal).toHaveBeenCalledWith(event.args, domicile);
      expect(response).toEqual('any');
      done();
    });
  });
});
