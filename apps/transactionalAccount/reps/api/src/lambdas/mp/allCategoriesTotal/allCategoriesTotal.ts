import { appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware/sessionIdentityMiddleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { ReportType, ResolverHandler, TransactionDirection } from '../../../common/types/index.js';
import { getAllCategoriesTotal } from '../../../services/allCategoriesTotal/allCategoriesTotal.js';
import { domicileMiddleware } from '../../middleware/domicile.js';
import { getEntityAggregateId } from '../../utils/aggregateId.js';

const lambdaHandler: ResolverHandler<{
  entityUuid: string;
  date: string;
  reportType: ReportType;
  transactionDirection: TransactionDirection;
  timeZone?: string;
  accountUuid?: string;
}> = async (event, _context) => {
  const { domicile } = event;

  debug(
    `getAllCategoriesTotalHandler request args -> ${JSON.stringify(event.args)}, domicile -> ${domicile}`,
    event.args.entityUuid,
  );

  return getAllCategoriesTotal(event.args, event.domicile);
};

export const getAllCategoriesTotalHandler = withMiddlewaresV2(
  { component: ZellerComponent.REPS, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [appIdentityMiddleware(true), xrayAggregateMiddleware(getEntityAggregateId(lambdaHandler)), domicileMiddleware],
);
