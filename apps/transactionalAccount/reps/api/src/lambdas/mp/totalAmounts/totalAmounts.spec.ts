import type { EntityUserContext } from '@npco/component-bff-core/dist/types/context';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import type { Callback, Context } from 'aws-lambda';

import { type ResolverEvent } from '../../../common/types/index.js';
import '../../utils/testing/middlewareMock.js';
import { getBaseAppSyncResolverEvent } from '../../utils/testing/baseEvent.js';

const getCashFlowTotalAmounts = jest.fn();

jest.mock('../../../services/totalAmounts/totalAmounts.js', () => ({
  __esModule: true,
  getCashFlowTotalAmounts,
}));
const mockContext = {} as any;
const mockRequest = {} as any;

describe('contact new amount lambdas', () => {
  const entityUuid = 'entityUuid';
  const domicile = Domicile.AU;
  let getCashFlowTotalAmountsHandler: (
    event: ResolverEvent<{
      entityUuid: string;
      date: string;
      timeZone?: string | undefined;
      accountUuid?: string | undefined;
    }>,
    context: Context & EntityUserContext,
    callback: Callback,
  ) => void;

  beforeEach(async () => {
    const lambdas = await import('./totalAmounts.js');

    getCashFlowTotalAmountsHandler = lambdas.getCashFlowTotalAmountsHandler;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should be able invoke handler ', (done) => {
    getCashFlowTotalAmounts.mockResolvedValueOnce('any' as any);
    const date = '2024-01-01';
    const event = getBaseAppSyncResolverEvent({
      args: {
        entityUuid,
        date,
      },
      request: mockRequest,
    });
    getCashFlowTotalAmountsHandler({ ...event, domicile }, mockContext, (error: any, response: any) => {
      expect(error).toBeNull();
      expect(getCashFlowTotalAmounts).toHaveBeenCalledTimes(1);
      expect(getCashFlowTotalAmounts).toHaveBeenCalledWith(event.args, domicile);
      expect(response).toEqual('any');
      done();
    });
  });
});
