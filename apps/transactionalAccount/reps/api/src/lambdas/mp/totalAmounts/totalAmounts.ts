import { appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware/sessionIdentityMiddleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { ResolverHandler } from '../../../common/types/index.js';
import { getCashFlowTotalAmounts } from '../../../services/totalAmounts/totalAmounts.js';
import { domicileMiddleware } from '../../middleware/domicile.js';

const getAggregateId = (_: Parameters<typeof lambdaHandler>[0], context: Parameters<typeof lambdaHandler>[1]) =>
  context.entityUuid;

const lambdaHandler: ResolverHandler<{
  entityUuid: string;
  date: string;
  timeZone?: string;
  accountUuid?: string;
}> = async (event, _context) => {
  debug(`getCashFlowTotalAmountsHandler request args -> ${JSON.stringify(event.args)}`, event.args.entityUuid);

  return getCashFlowTotalAmounts(event.args, event.domicile);
};

export const getCashFlowTotalAmountsHandler = withMiddlewaresV2(
  { component: ZellerComponent.REPS, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [appIdentityMiddleware(true), xrayAggregateMiddleware(getAggregateId), domicileMiddleware],
);
