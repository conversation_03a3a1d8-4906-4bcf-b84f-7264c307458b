import { BffDynamoDbClient, DynamodbService } from '@npco/component-bff-core/dist/dynamodb';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { DbRecordType } from '@npco/component-dto-core';

import { domicileMiddleware } from './domicile';

jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

describe('domicile middlewares', () => {
  process.env.DOMICILE_LOOKUP_TABLE = 'dev-ams-engine-DomicileLookup';
  const entityUuid = 'entityUuid';
  const stage = process.env.STAGE || 'dev';
  const tableName = `${stage}-ams-engine-DomicileLookup`;
  const mockNext = jest.fn();
  const mockContext = {} as any;

  let dbService: DynamodbService;
  const mockBffDynamoDbClient = new BffDynamoDbClient();

  beforeAll(async () => {
    dbService = new DynamodbService(
      {
        componentTableName: tableName,
        entityGsi: 'entityGsi',
      } as any,
      mockBffDynamoDbClient,
    );
  });

  describe('setDomicleForProjectionMiddleware', () => {
    beforeEach(() => {
      process.env.DOMICILE_LOOKUP_ENABLED = 'true';
      jest.clearAllMocks();
    });

    it('should set domicile from region if no record found on the domicile lookup', async () => {
      await domicileMiddleware(
        {
          args: {
            field: 'value',
            entityUuid,
          },
        },
        {},
        mockNext,
      );
      expect(mockNext).toHaveBeenCalledWith(
        {
          args: {
            field: 'value',
            entityUuid,
          },
          domicile: Domicile.AU,
        },
        mockContext,
      );
    });

    it('should set domicile from region if entityUuid does not exist from the payload', async () => {
      await domicileMiddleware(
        {
          args: {
            field: 'value',
          },
        },
        {},
        mockNext,
      );
      expect(mockNext).toHaveBeenCalledWith(
        {
          args: {
            field: 'value',
          },
          domicile: Domicile.AU,
        },
        {},
      );
    });

    it('should set domicile from domicile lookup table by entityUuid', async () => {
      process.env.AWS_REGION = 'unspecified-region';
      await dbService.put({
        TableName: tableName,
        Item: {
          id: entityUuid,
          domicile: Domicile.GB,
          type: DbRecordType.ENTITY_DOMICILE,
        },
      });

      await domicileMiddleware(
        {
          args: {
            field: 'value',
            entityUuid,
          },
        },
        {},
        mockNext,
      );
      expect(mockNext).toHaveBeenCalledWith(
        {
          args: {
            field: 'value',
            entityUuid,
          },
          domicile: Domicile.GB,
        },
        mockContext,
      );
    });

    it('should not call domicile lookup if domicileLookupEnabled is false', async () => {
      const mockGetDomicleByEntityId = jest.fn();
      process.env.AWS_REGION = 'ap-southeast-2';
      jest.mock('@npco/component-bff-core/dist/domicile', () => {
        return {
          DomicileLookupDb: jest.fn().mockImplementation(() => ({
            getDomicileByEntityId: mockGetDomicleByEntityId,
          })),
        };
      });
      process.env.DOMICILE_LOOKUP_ENABLED = 'false';

      await domicileMiddleware(
        {
          args: {
            field: 'value',
            entityUuid,
          },
        },
        {},
        mockNext,
      );
      expect(mockNext).toHaveBeenCalledWith(
        {
          args: {
            field: 'value',
            entityUuid,
          },
          domicile: Domicile.AU,
        },
        mockContext,
      );

      expect(mockGetDomicleByEntityId).not.toHaveBeenCalled();
    });
  });
});
