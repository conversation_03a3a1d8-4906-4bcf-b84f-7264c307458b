import { DomicileLookupDb } from '@npco/component-bff-core/dist/domicile';
import { getSupportedDomiciles } from '@npco/component-bff-core/dist/utils/domicile';
import { warn } from '@npco/component-bff-core/dist/utils/logger';

import { debug } from 'console';

import { EnvironmentService } from '../../../config/environmentService';

const getDomicileByRegion = (region: string) => {
  const [domicile] = getSupportedDomiciles(region);
  if (!domicile) warn('No supported domiciles found');
  return domicile;
};

export const domicileMiddleware = async (event: any, context: any, next: any) => {
  const env = new EnvironmentService();
  const { entityUuid } = event.args;
  const defaultDomicile = getDomicileByRegion(env.region);
  let domicile = defaultDomicile;

  if (env.domicileLookupEnabled && entityUuid) {
    const domicileLookup = new DomicileLookupDb();
    const foundDomicile = await domicileLookup.getDomicileByEntityId(entityUuid);

    debug(`Domicile middleware - foundDomicile: ${foundDomicile}`, entityUuid);
    if (foundDomicile) {
      domicile = foundDomicile;
    }
  }

  debug(`Domicile middleware - domicile: ${domicile}`, entityUuid);

  return next(
    {
      ...event,
      domicile,
    },
    context,
  );
};
