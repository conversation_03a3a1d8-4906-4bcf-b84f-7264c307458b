import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import type { EntityCategories, Money } from '@npco/component-dto-core';

import type { ReportType } from './report.js';

import type { SortingAndLimit } from './index.js';

export enum TransactionDirection {
  EXPENSE = 'EXPENSE',
  INCOME = 'INCOME',
}

export type DateRanges = {
  dateRangeStart: string;
  dateRangeEnd: string;
};

export type TransactionCommon = DateRanges & {
  currency: Money['currency'];
};

export type TransactionQueryInput = {
  entityUuid: string;
  reportType: ReportType;
  date: string;
  timeZone?: string;
  accountUuid?: string;
  domicile: Domicile;
};

export type TransactionQueryInputWithSorting = SortingAndLimit & TransactionQueryInput;

export type TransactionsQueryWithFilterInput = TransactionQueryInputWithSorting & {
  groupBy: 'category' | 'contactUuid' | 'subcategory';
  transactionDirection?: TransactionDirection;
};

export type TransactionsQueryWithTransactionDirection = TransactionQueryInputWithSorting & {
  transactionDirection?: TransactionDirection;
};

export type TransactionsQueryWithCategoryFilterInput = TransactionQueryInput & {
  category: EntityCategories;
};

export type TransactionQueryBaseResponse = {
  entityUuid: string;
  total: string;
  average: string;
  change?: number;
  noOfTransaction: number;
} & TransactionCommon;

export type TransactionQueryResponse = TransactionQueryBaseResponse & {
  total: Money;
  average: Money;
};

export type TransactionQueryTotalAmountsInput = {
  entityUuid: string;
  date: string;
  timeZone?: string;
  accountUuid?: string;
  domicile: Domicile;
};

export type TransactionQueryTotalAmountBaseResponse = {
  income: number;
  noOfIncomeTransaction: number;
  expense: number;
  noOfExpenseTransaction: number;
} & TransactionCommon;
