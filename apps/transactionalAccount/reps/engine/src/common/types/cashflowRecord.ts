import type { Money, EntityCategories } from '@npco/component-dto-core';

import type {
  TransactionQueryTotalAmountBaseResponse,
  TransactionQueryBaseResponse,
  TransactionCommon,
} from './transaction.js';

export type CashFlowNetAmountsRecord = TransactionQueryBaseResponse & {
  contactUuid?: string;
  category?: string;
  subcategoryCount?: number;
};

export type CashFlowTotalAmountsRecord = TransactionQueryTotalAmountBaseResponse;

export type SummaryTotalQueryBaseResponse = {
  entityUuid: string;
  total: string;
  totalSum?: string;
  previousTotalSum?: string;
  change?: number;
  currency: Money['currency'];
} & TransactionCommon;

export type CategoryTotalRecord = SummaryTotalQueryBaseResponse & {
  category?: EntityCategories;
};

export type ContactTopTenRecord = SummaryTotalQueryBaseResponse & {
  contactUuid?: string;
};
