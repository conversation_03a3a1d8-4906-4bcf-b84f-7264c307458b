import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { ISO4217 } from '@npco/component-dto-core';

import { v4 as uuidv4 } from 'uuid';

import type { FunctionTypes } from '../../common/types/index.js';
import { EnvironmentService } from '../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../const/timezone.js';
import { getCurrentFormattedDate } from '../../utils/testing/date.js';

import type * as ServiceModule from './issuingTransactionFullDetailsService.js';
import { convertSQLResponseToTotalAmounts } from './utils/transactionResponse.js';

type ServiceModuleFunctionTypes = FunctionTypes<typeof ServiceModule>;

jest.mock('../../config/environmentService');

const getOverallTotalAmounts = jest.fn();
const getPeriodicTotalAmounts = jest.fn();

jest.mock('../issuingTransactionFullDetails/db/totalAmountReport/issuingTransactionFullDetailsDb', () => ({
  __esModule: true,
  getOverallTotalAmounts,
  getPeriodicTotalAmounts,
}));

const mockOverAllTotalAmountsResponse = [
  {
    dateRangeStart: '2020-02-01',
    dateRangeEnd: '2021-12-31',
    income: 123,
    expense: 123,
    noOfIncomeTransaction: 3,
    noOfExpenseTransaction: 2,
    currency: ISO4217.AUD,
  },
];
const mockPeriodicTotalAmountsResponse = [
  {
    dateRangeStart: '2021-01-01',
    dateRangeEnd: '2021-01-31',
    income: 123,
    expense: 123,
    noOfIncomeTransaction: 1,
    noOfExpenseTransaction: 2,
    currency: ISO4217.AUD,
  },
  {
    dateRangeStart: '2021-02-01',
    dateRangeEnd: '2021-02-28',
    income: 1234,
    expense: 1234,
    noOfIncomeTransaction: 3,
    noOfExpenseTransaction: 4,
    currency: ISO4217.AUD,
  },
];

const getCashFlowTotalAmountsExpectedResponse = () => {
  return {
    range: {
      start: mockOverAllTotalAmountsResponse[0].dateRangeStart,
      end: mockOverAllTotalAmountsResponse[0].dateRangeEnd,
    },
    transactionDirectionTotalAmounts: {
      income: { value: mockOverAllTotalAmountsResponse[0].income.toString(), currency: ISO4217.AUD },
      noOfExpense: mockOverAllTotalAmountsResponse[0].noOfExpenseTransaction,
      expense: { value: mockOverAllTotalAmountsResponse[0].expense.toString(), currency: ISO4217.AUD },
      noOfIncome: mockOverAllTotalAmountsResponse[0].noOfIncomeTransaction,
    },
    cashFlowPeriodicTotalAmounts: mockPeriodicTotalAmountsResponse.map((item: any) => ({
      range: {
        start: item.dateRangeStart,
        end: item.dateRangeEnd,
      },
      transactionDirectionTotalAmounts: {
        income: { value: item.income.toString(), currency: ISO4217.AUD },
        noOfExpense: item.noOfExpenseTransaction,
        expense: { value: item.expense.toString(), currency: ISO4217.AUD },
        noOfIncome: item.noOfIncomeTransaction,
      },
    })),
  };
};

describe('Issuing transactions full details service', () => {
  const domicile = Domicile.AU;
  let env: EnvironmentService;
  let service: any;

  beforeAll(async () => {
    env = new EnvironmentService();
    service = await import('./issuingTransactionFullDetailsService.js');
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getCashFlowTotalAmounts', () => {
    let getCashFlowTotalAmounts: ServiceModuleFunctionTypes['getCashFlowTotalAmounts'];
    const entityUuid = uuidv4();
    const date = getCurrentFormattedDate();

    beforeAll(async () => {
      getCashFlowTotalAmounts = service.getCashFlowTotalAmounts;
    });

    beforeEach(() => {
      getOverallTotalAmounts.mockResolvedValue(mockOverAllTotalAmountsResponse);
      getPeriodicTotalAmounts.mockResolvedValue(mockPeriodicTotalAmountsResponse);
    });

    it('should call get transaction records from db', async () => {
      const requestInput = {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date,
        domicile,
      };
      const result = await getCashFlowTotalAmounts(requestInput, env);

      expect(getOverallTotalAmounts).toHaveBeenCalledTimes(1);
      expect(getOverallTotalAmounts).toHaveBeenCalledWith(env, requestInput);

      expect(getPeriodicTotalAmounts).toHaveBeenCalledTimes(1);
      expect(getPeriodicTotalAmounts).toHaveBeenCalledWith(env, requestInput);

      expect(result).toEqual({
        timeZone: requestInput.timeZone,
        ...convertSQLResponseToTotalAmounts(mockOverAllTotalAmountsResponse)[0],
        cashFlowPeriodicTotalAmounts: convertSQLResponseToTotalAmounts(mockPeriodicTotalAmountsResponse),
      });
    });

    it('should call get transaction records from db with accountUuid', async () => {
      const accountUuid = uuidv4();
      const requestInput = {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date,
        accountUuid,
        domicile,
      };
      const result = await getCashFlowTotalAmounts(requestInput, env);

      expect(getOverallTotalAmounts).toHaveBeenCalledTimes(1);
      expect(getOverallTotalAmounts).toHaveBeenCalledWith(env, requestInput);

      expect(getPeriodicTotalAmounts).toHaveBeenCalledTimes(1);
      expect(getPeriodicTotalAmounts).toHaveBeenCalledWith(env, requestInput);

      expect(result).toEqual({
        accountUuid,
        timeZone: DEFAULT_TIMEZONE,
        ...getCashFlowTotalAmountsExpectedResponse(),
      });
    });

    it('should return default timezone if not provided', async () => {
      const requestInput = {
        entityUuid,
        date,
        domicile,
      };
      const expectedRequestInput = {
        ...requestInput,
        timeZone: DEFAULT_TIMEZONE,
      };
      const result = await getCashFlowTotalAmounts(requestInput, env);

      expect(getOverallTotalAmounts).toHaveBeenCalledTimes(1);
      expect(getOverallTotalAmounts).toHaveBeenCalledWith(env, expectedRequestInput);

      expect(getPeriodicTotalAmounts).toHaveBeenCalledTimes(1);
      expect(getPeriodicTotalAmounts).toHaveBeenCalledWith(env, expectedRequestInput);

      expect(result).toEqual({
        timeZone: DEFAULT_TIMEZONE,
        ...getCashFlowTotalAmountsExpectedResponse(),
      });
    });
  });
});
