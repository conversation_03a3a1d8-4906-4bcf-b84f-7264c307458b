import { endOfMonth, startOfMonth } from 'date-fns';

import type {
  DateRanges,
  TotalAmount,
  TransactionQueryBaseResponse,
  TransactionQueryResponse,
  TransactionQueryTotalAmountBaseResponse,
} from '../../../common/types/index.js';
import { getLastDayOfTheMonth, getShortDateFormat, getTTMDate } from '../../../utils/date.js';

export const addCurrenciesToTxnResponse = <Response extends TransactionQueryResponse>(
  txnResponse: TransactionQueryBaseResponse[],
) =>
  txnResponse.map((data) => {
    const { currency, ...rest } = data;
    return {
      ...rest,
      total: {
        value: data.total,
        currency,
      },
      average: {
        value: data.average,
        currency,
      },
    };
  }) as Response[];

const getDateRangeFromResponse = <T extends DateRanges>(response: T) => ({
  start: response.dateRangeStart,
  end: response.dateRangeEnd,
});

export const getTTMReportDateRange = <T extends DateRanges>(
  date: Date,
  response: T[],
): { start: string; end: string } => {
  if (response.length) {
    return getDateRangeFromResponse(response[0]);
  }
  return {
    start: getShortDateFormat(getTTMDate(new Date(date))),
    end: getShortDateFormat(getLastDayOfTheMonth(date)),
  };
};

export const getMonthlyReportDateRange = <T extends DateRanges>(
  date: Date,
  response: T[],
): { start: string; end: string } => {
  if (response.length) {
    return getDateRangeFromResponse(response[0]);
  }
  return {
    start: getShortDateFormat(startOfMonth(date)),
    end: getShortDateFormat(endOfMonth(date)),
  };
};

export const convertSQLResponseToTotalAmounts = (
  sqlResponse: TransactionQueryTotalAmountBaseResponse[],
): TotalAmount[] => {
  return sqlResponse.map((data) => ({
    range: {
      start: data.dateRangeStart,
      end: data.dateRangeEnd,
    },
    transactionDirectionTotalAmounts: {
      income: {
        value: data.income.toString(),
        currency: data.currency,
      },
      noOfExpense: data.noOfExpenseTransaction,
      expense: {
        value: data.expense.toString(),
        currency: data.currency,
      },
      noOfIncome: data.noOfIncomeTransaction,
    },
  }));
};
