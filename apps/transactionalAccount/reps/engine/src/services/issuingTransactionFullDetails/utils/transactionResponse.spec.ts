import { ISO4217 } from '@npco/component-dto-core';

import { endOfMonth, startOfMonth } from 'date-fns';
import { v4 as uuid } from 'uuid';

import { type TransactionQueryBaseResponse } from '../../../common/types/index.js';
import { getLastDayOfTheMonth, getShortDateFormat, getTTMDate } from '../../../utils/index.js';

import { addCurrenciesToTxnResponse, getTTMReportDateRange, getMonthlyReportDateRange } from './transactionResponse.js';

describe('updateTxnResponseWithCurrency', () => {
  const currency = ISO4217.AUD;
  const mockResponse = [
    {
      entityUuid: uuid(),
      change: 0,
      noOfTransaction: 2,
      total: '100',
      average: '50',
      dateRangeStart: '2021-01-01',
      dateRangeEnd: '2021-12-31',
      currency,
    },
  ];
  it.each([
    {
      txnResponse: mockResponse,
    },
  ])('should update transaction response and add currencies', ({ txnResponse }) => {
    const updatedResponse = addCurrenciesToTxnResponse(txnResponse);

    expect(updatedResponse).toEqual([
      {
        total: {
          value: '100',
          currency,
        },
        average: {
          value: '50',
          currency,
        },
        change: mockResponse[0].change,
        entityUuid: mockResponse[0].entityUuid,
        noOfTransaction: mockResponse[0].noOfTransaction,
        dateRangeStart: mockResponse[0].dateRangeStart,
        dateRangeEnd: mockResponse[0].dateRangeEnd,
      },
    ]);
  });
});
describe('getTTMReportDateRange', () => {
  it('should return date range from response if response is not empty', () => {
    const date = new Date();
    const response = [
      {
        dateRangeStart: '2021-01-01',
        dateRangeEnd: '2021-12-31',
      },
    ];
    const result = getTTMReportDateRange(date, response as TransactionQueryBaseResponse[]);
    expect(result).toEqual({
      start: '2021-01-01',
      end: '2021-12-31',
    });
  });

  it('should return default date range if response is empty', () => {
    const date = new Date();
    const response: { dateRangeStart: string; dateRangeEnd: string }[] = [];
    const result = getTTMReportDateRange(date, response as TransactionQueryBaseResponse[]);
    const expectedStart = getShortDateFormat(getTTMDate(new Date(date)));
    const expectedEnd = getShortDateFormat(getLastDayOfTheMonth(date));
    expect(result).toEqual({
      start: expectedStart,
      end: expectedEnd,
    });
  });
});

describe('getMonthlyReportDateRange', () => {
  it('should return date range from response if response is not empty', () => {
    const date = new Date();
    const response = [
      {
        dateRangeStart: '2021-01-01',
        dateRangeEnd: '2021-12-31',
      },
    ];
    const result = getMonthlyReportDateRange(date, response as TransactionQueryBaseResponse[]);
    expect(result).toEqual({
      start: '2021-01-01',
      end: '2021-12-31',
    });
  });

  it('should return default date range if response is empty', () => {
    const date = new Date();
    const response: { dateRangeStart: string; dateRangeEnd: string }[] = [];
    const result = getMonthlyReportDateRange(date, response as TransactionQueryBaseResponse[]);
    const expectedStart = getShortDateFormat(startOfMonth(date));
    const expectedEnd = getShortDateFormat(endOfMonth(date));
    expect(result).toEqual({
      start: expectedStart,
      end: expectedEnd,
    });
  });
});
