import { debug } from '@npco/component-bff-core/dist/utils/logger';

import { ReportType, TransactionDirection } from '../../common/types/index.js';
import type {
  CategoriesNetAmountResponse,
  CategoryNetAmount,
  ContactNetAmount,
  ContactsNetAmountResponse,
  SubcategoriesNetAmountResponse,
  SubcategoryNetAmount,
  TransactionsQueryWithCategoryFilterInput,
  TransactionsQueryWithTransactionDirection,
  TransactionQueryTotalAmountsInput,
  TotalAmountsResponse,
  AllCategoriesTotalResponse,
  TopTenContactResponse,
  TransactionsQueryWithFilterInput,
  TransactionCommon,
} from '../../common/types/index.js';
import type { EnvironmentService } from '../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../const/timezone.js';

import { getAllCategoriesBaseResponse } from './db/allCategoriesTotalReport/allCategoriesTotalReport';
import { getAllCategoriesTotalTxnRecordsByReportType } from './db/allCategoriesTotalReport/allCategoriesTotalReport.js';
import { getMonthlyNetAmountTransactionRecords } from './db/netAmountReport/monthly/issuingTransactionFullDetailsDb.js';
import { getTTMNetAmountTransactionRecords } from './db/netAmountReport/ttm/issuingTransactionFullDetailsDb.js';
import {
  getTopTenContactBaseResponse,
  getTopTenContactTxnRecordsByReportType,
} from './db/topTenContactReport/topTenContact.js';
import {
  getOverallTotalAmounts,
  getPeriodicTotalAmounts,
} from './db/totalAmountReport/issuingTransactionFullDetailsDb.js';
import {
  addCurrenciesToTxnResponse,
  convertSQLResponseToTotalAmounts,
  getMonthlyReportDateRange,
  getTTMReportDateRange,
} from './utils/transactionResponse.js';

const getTransactionsByReportType = async (env: EnvironmentService, input: TransactionsQueryWithFilterInput) => {
  const { reportType, ...restInput } = input;
  const transactionDirection =
    input.groupBy === 'category' || input.groupBy === 'subcategory'
      ? TransactionDirection.EXPENSE
      : input.transactionDirection;

  if (reportType === ReportType.MONTHLY) {
    return getMonthlyNetAmountTransactionRecords(env, {
      ...restInput,
      transactionDirection,
    });
  }

  if (reportType === ReportType.TTM) {
    return getTTMNetAmountTransactionRecords(env, {
      ...restInput,
      transactionDirection,
    });
  }

  throw new Error('Invalid report type');
};

const getReportDateRangeByReportType = <T extends TransactionCommon>(
  reportType: ReportType,
  date: string,
  response: T[],
) => {
  return reportType === ReportType.TTM
    ? getTTMReportDateRange(new Date(date), response)
    : getMonthlyReportDateRange(new Date(date), response);
};

export const getCategoryNetAmount = async (
  input: TransactionsQueryWithTransactionDirection,
  env: EnvironmentService,
): Promise<CategoriesNetAmountResponse> => {
  const { entityUuid, date, timeZone, accountUuid, reportType, sortBy, sortOrder, limit } = input;

  debug(`issuingTransactionFullDetailsService getCategoryNetAmount ${JSON.stringify(input)}`, entityUuid);

  const response = await getTransactionsByReportType(env, {
    ...input,
    timeZone: timeZone ?? DEFAULT_TIMEZONE,
    groupBy: 'category',
  });

  const categoryNetAmounts = addCurrenciesToTxnResponse<CategoryNetAmount>(response);

  return {
    sortBy,
    sortOrder,
    limit,
    reportType,
    range: getReportDateRangeByReportType(reportType, date, response),
    timeZone: timeZone ?? DEFAULT_TIMEZONE,
    accountUuid,
    categoryNetAmounts,
  };
};

export const getContactNetAmount = async (
  input: TransactionsQueryWithTransactionDirection,
  env: EnvironmentService,
): Promise<ContactsNetAmountResponse> => {
  const { entityUuid, date, timeZone, accountUuid, reportType, sortBy, sortOrder, limit } = input;

  debug(`issuingTransactionFullDetailsService getContactNetAmount ${JSON.stringify(input)}`, entityUuid);

  const response = await getTransactionsByReportType(env, {
    ...input,
    timeZone: timeZone ?? DEFAULT_TIMEZONE,
    groupBy: 'contactUuid',
  });

  const contactNetAmounts = addCurrenciesToTxnResponse<ContactNetAmount>(response);

  return {
    sortBy,
    sortOrder,
    limit,
    reportType,
    range: getReportDateRangeByReportType(reportType, date, response),
    timeZone: timeZone ?? DEFAULT_TIMEZONE,
    accountUuid,
    contactNetAmounts,
  };
};

export const getSubcategoriesNetAmountByCategory = async (
  input: TransactionsQueryWithCategoryFilterInput,
  env: EnvironmentService,
): Promise<SubcategoriesNetAmountResponse> => {
  const { entityUuid, date, timeZone, accountUuid, reportType } = input;

  debug(`issuingTransactionFullDetailsService getCategoryNetAmount ${JSON.stringify(input)}`, entityUuid);

  const response = await getTransactionsByReportType(env, {
    ...input,
    timeZone: timeZone ?? DEFAULT_TIMEZONE,
    groupBy: 'subcategory',
    transactionDirection: TransactionDirection.EXPENSE,
  });

  const subcategoryNetAmounts = addCurrenciesToTxnResponse<SubcategoryNetAmount>(response);

  return {
    reportType,
    range: getReportDateRangeByReportType(reportType, date, response),
    timeZone: timeZone ?? DEFAULT_TIMEZONE,
    accountUuid,
    subcategoryNetAmounts,
  };
};

export const getCashFlowTotalAmounts = async (
  input: TransactionQueryTotalAmountsInput,
  env: EnvironmentService,
): Promise<TotalAmountsResponse> => {
  const { entityUuid, timeZone, accountUuid } = input;

  debug(`issuingTransactionFullDetailsService getCashFlowTotalAmounts ${JSON.stringify(input)}`, entityUuid);

  const requestInput = {
    ...input,
    timeZone: timeZone ?? DEFAULT_TIMEZONE,
  };

  const [overTotalAmounts, periodicTotalAmounts] = await Promise.all([
    getOverallTotalAmounts(env, requestInput),
    getPeriodicTotalAmounts(env, requestInput),
  ]);

  const [convertedOverTotalAmounts] = convertSQLResponseToTotalAmounts(overTotalAmounts);
  const cashFlowPeriodicTotalAmounts = convertSQLResponseToTotalAmounts(periodicTotalAmounts);

  return {
    timeZone: timeZone ?? DEFAULT_TIMEZONE,
    accountUuid,
    ...convertedOverTotalAmounts,
    cashFlowPeriodicTotalAmounts,
  };
};

export const getAllCategoriesTotal = async (
  input: TransactionsQueryWithTransactionDirection,
  env: EnvironmentService,
): Promise<AllCategoriesTotalResponse> => {
  const { entityUuid, date, accountUuid, reportType } = input;
  const timeZone = input.timeZone ?? DEFAULT_TIMEZONE;

  debug(`issuingTransactionFullDetailsService getAllCategoriesTotal ${JSON.stringify(input)}`, entityUuid);

  const response = await getAllCategoriesTotalTxnRecordsByReportType(env, {
    ...input,
    timeZone,
  });

  return {
    reportType,
    range: getReportDateRangeByReportType(reportType, date, response),
    timeZone,
    accountUuid,
    ...getAllCategoriesBaseResponse(response, input.domicile),
  };
};

export const getTopTenContact = async (
  input: TransactionsQueryWithTransactionDirection,
  env: EnvironmentService,
): Promise<TopTenContactResponse> => {
  const { entityUuid, date, accountUuid, reportType } = input;
  const timeZone = input.timeZone ?? DEFAULT_TIMEZONE;

  debug(`issuingTransactionFullDetailsService getTopTenContact ${JSON.stringify(input)}`, entityUuid);

  const response = await getTopTenContactTxnRecordsByReportType(env, {
    ...input,
    timeZone,
  });

  return {
    reportType,
    range: getReportDateRangeByReportType(reportType, date, response),
    timeZone,
    accountUuid,
    ...getTopTenContactBaseResponse(response, input.domicile),
  };
};
