import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import {
  ReportType,
  type TransactionsQueryWithTransactionDirection,
  type CategoryTotalRecord,
  type AllCategoriesTotalBaseResponse,
} from '../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../config/environmentService.js';
import { getCurrencyByDomicile } from '../../../../utils/domicile.js';

import { getMonthlyCategoriesTotalRecords } from './monthly/issuingTransactionFullDetailsDb.js';
import { getTTMCategoriesTotalRecords } from './ttm/issuingTransactionFullDetailsDb.js';

export const getAllCategoriesTotalTxnRecordsByReportType = async (
  env: EnvironmentService,
  input: TransactionsQueryWithTransactionDirection,
) => {
  if (input.reportType === ReportType.MONTHLY) {
    return getMonthlyCategoriesTotalRecords(env, input);
  }

  if (input.reportType === ReportType.TTM) {
    return getTTMCategoriesTotalRecords(env, input);
  }

  throw new Error('Invalid report type');
};

export const getAllCategoriesBaseResponse = (
  response: CategoryTotalRecord[],
  domicile: Domicile,
): AllCategoriesTotalBaseResponse => {
  return {
    change: response?.[0]?.change ?? 0,
    totalSum: {
      currency: response?.[0]?.currency ?? getCurrencyByDomicile(domicile),
      value: response?.[0]?.totalSum ?? '0',
    },
    ...(response?.[0]?.previousTotalSum && {
      previousTotalSum: {
        currency: response[0].currency,
        value: response[0].previousTotalSum,
      },
    }),
    ...(response?.length && {
      categoriesTotal: response.map((data) => ({
        category: data.category,
        total: {
          currency: data.currency,
          value: data.total ?? '0',
        },
      })),
    }),
  };
};
