import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import {
  ReportType,
  type TransactionsQueryWithTransactionDirection,
  type ContactTopTenRecord,
  type TopTenContactBaseResponse,
} from '../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../config/environmentService.js';
import { getCurrencyByDomicile } from '../../../../utils/domicile.js';

import { getMonthlyTopTenContactRecords } from './monthly/issuingTransactionFullDetailsDb.js';
import { getTTMTopTenContactRecords } from './ttm/issuingTransactionFullDetailsDb.js';

export const getTopTenContactTxnRecordsByReportType = async (
  env: EnvironmentService,
  input: TransactionsQueryWithTransactionDirection,
) => {
  if (input.reportType === ReportType.MONTHLY) {
    return getMonthlyTopTenContactRecords(env, input);
  }

  if (input.reportType === ReportType.TTM) {
    return getTTMTopTenContactRecords(env, input);
  }

  throw new Error('Invalid report type');
};

export const getTopTenContactBaseResponse = (
  response: ContactTopTenRecord[],
  domicile: Domicile,
): TopTenContactBaseResponse => {
  const defaultCurrency = getCurrencyByDomicile(domicile);
  return {
    change: response?.[0]?.change ?? 0,
    totalSum: {
      currency: defaultCurrency,
      value: response?.[0]?.totalSum ?? '0',
    },
    ...(response?.[0]?.previousTotalSum && {
      previousTotalSum: {
        currency: response[0].currency ?? defaultCurrency,
        value: response[0].previousTotalSum,
      },
    }),
    ...(response?.length && {
      contactTopTenTotals: response.map((data) => ({
        contactUuid: data.contactUuid,
        total: {
          currency: data.currency ?? defaultCurrency,
          value: data.total ?? '0',
        },
      })),
    }),
  };
};
