import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';

import { v4 as uuid } from 'uuid';

import { EnvironmentService } from '../../../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../../../const/timezone.js';
import { getDebitCardTransactionDto } from '../../../debitCardAccountTransaction/mocks/getDebitCardTransactionMock.js';
import { saveSavingsAccountTransaction } from '../../../savingsAccountTransaction/db/savingsAccountTransactionDb.js';
import { getSavingsAccountTransactionTransactionDto } from '../../../savingsAccountTransaction/mocks/savingsAccountTransactionMock.js';
import { getTTMReportDateRange } from '../../utils/transactionResponse.js';
import { expenseTransactions, incomeTransactions, incomeTransactionsSavingsAccount } from '../const.js';

import { getPeriodicTotalAmounts, getOverallTotalAmounts } from './issuingTransactionFullDetailsDb.js';

jest.mock('../../../../config/environmentService');

const currentDate: any = new Date().toISOString().split('T')[0];

describe('issuingTransactionFullDetailsDb', () => {
  const domicile = Domicile.AU;
  let saveDebitCardTransaction: (
    data: DebitCardTransactionV2,
    env: EnvironmentService,
    domicile: Domicile,
  ) => Promise<void>;

  let env: EnvironmentService;

  beforeAll(async () => {
    env = new EnvironmentService();
    const dbService = await import('../../../debitCardAccountTransaction/db/debitCardAccountTransactionDb.js');
    saveDebitCardTransaction = dbService.saveDebitCardTransaction;
  });

  describe('Get periodic records', () => {
    const entityUuid = uuid();
    const merchantEntityUuid = uuid();

    const debitCardAccountTransactions = [...expenseTransactions, ...incomeTransactions];
    const savingsAccountTransactionDtos = incomeTransactionsSavingsAccount.map((type, index) =>
      getSavingsAccountTransactionTransactionDto({
        entityUuid,
        type,
        updatedTime: index,
      }),
    );
    const debitCardAccountTransactionDtos = debitCardAccountTransactions.map((type, index) =>
      getDebitCardTransactionDto({
        entityUuid,
        type,
        merchant: {
          id: merchantEntityUuid,
          name: uuid(),
          updatedTime: Date.now(),
        },
        updatedTime: index,
      }),
    );

    beforeEach(async () => {
      await Promise.all([
        ...debitCardAccountTransactionDtos.map((dto) => saveDebitCardTransaction(dto, env, domicile)),
        ...savingsAccountTransactionDtos.map((dto) => saveSavingsAccountTransaction(dto, env, domicile)),
      ]);
    });

    describe('for all accounts', () => {
      describe('getPeriodicTotalAmounts', () => {
        it('should return all records', async () => {
          const result = await getPeriodicTotalAmounts(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            domicile,
          });

          expect(result.length).toEqual(12);

          result.forEach((record) => {
            expect(record.dateRangeStart).toBeDefined();
            expect(record.dateRangeEnd).toBeDefined();
            expect(record.income).toBeDefined();
            expect(record.noOfIncomeTransaction).toBeDefined();
            expect(record.expense).toBeDefined();
            expect(record.noOfExpenseTransaction).toBeDefined();
            expect(record.currency).toBeDefined();
          });
        });
      });
    });

    describe('for single account', () => {
      describe('getPeriodicTotalAmounts', () => {
        it('should return all records', async () => {
          const result = await getPeriodicTotalAmounts(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            accountUuid: debitCardAccountTransactionDtos[0].debitCardAccountUuid,
            domicile,
          });

          expect(result.length).toEqual(12);

          result.forEach((record) => {
            expect(record.dateRangeStart).toBeDefined();
            expect(record.dateRangeEnd).toBeDefined();
            expect(record.income).toBeDefined();
            expect(record.noOfIncomeTransaction).toBeDefined();
            expect(record.expense).toBeDefined();
            expect(record.noOfExpenseTransaction).toBeDefined();
          });
        });
      });
    });

    describe('getOverallTotalAmounts', () => {
      describe('for all accounts', () => {
        it('should return all records', async () => {
          const result = await getOverallTotalAmounts(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            domicile,
          });

          expect(result.length).toEqual(1);

          expect(result[0].dateRangeStart).toBe(getTTMReportDateRange(currentDate, result).start);
          expect(result[0].dateRangeEnd).toBe(getTTMReportDateRange(currentDate, result).end);
          expect(result[0].income).toBeDefined();
          expect(result[0].noOfIncomeTransaction).toBeDefined();
          expect(result[0].expense).toBeDefined();
          expect(result[0].noOfExpenseTransaction).toBeDefined();
        });
      });

      describe('for single account', () => {
        it('should return all records', async () => {
          const result = await getOverallTotalAmounts(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            accountUuid: debitCardAccountTransactionDtos[0].debitCardAccountUuid,
            domicile,
          });

          expect(result.length).toEqual(1);

          expect(result[0].dateRangeStart).toBe(getTTMReportDateRange(currentDate, result).start);
          expect(result[0].dateRangeEnd).toBe(getTTMReportDateRange(currentDate, result).end);
          expect(result[0].income).toBeDefined();
          expect(result[0].noOfIncomeTransaction).toBeDefined();
          expect(result[0].expense).toBeDefined();
          expect(result[0].noOfExpenseTransaction).toBeDefined();
        });
      });
    });
  });
});
