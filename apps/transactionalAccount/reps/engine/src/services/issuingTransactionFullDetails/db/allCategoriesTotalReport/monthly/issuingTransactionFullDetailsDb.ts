import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { CategoryTotalRecord, TransactionsQueryCategoriesTotalInput } from '../../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../../config/environmentService.js';
import { getAppDataSource } from '../../../../../database/index.js';
import { FUNCTION_NAME_TO_BE_REPLACE } from '../../const.js';
import { getYearMonthlyRecordQuery } from '../../utils/queries.issuingTransactionsTotal.js';
import { getIssuingFullDetailsFunction } from '../../utils/queries.js';

const YEAR_MONTHLY_RECORD_ALIAS = 'YearMonthlyRecord';
const THIS_PERIOD_ALIAS = 'ThisPeriod';
const THIS_PERIOD_TOTAL_SUM_ALIAS = 'ThisPeriodTotalSum';
const PREVIOUS_PERIOD_TOTAL_SUM_ALIAS = 'PreviousPeriodTotalSum';

export const getMonthlyCategoriesTotalRecords = async (
  env: EnvironmentService,
  input: TransactionsQueryCategoriesTotalInput,
): Promise<CategoryTotalRecord[]> => {
  const { entityUuid, timeZone, date, accountUuid, transactionDirection } = input;
  debug(`issuingTransactionFullDetailsDb -> getMonthlyCategoriesTotalRecords ${JSON.stringify(input)}`, entityUuid);

  const dataSource = await getAppDataSource(env);

  const yearMonthlyRecordSQL = getYearMonthlyRecordQuery('category', dataSource, {
    entityUuid,
    timeZone,
    date,
    accountUuid,
    transactionDirection,
  });
  const issuingFullDetailsFunction = getIssuingFullDetailsFunction(env.stage, input);

  const QUERY = `with ${YEAR_MONTHLY_RECORD_ALIAS} AS (
    ${yearMonthlyRecordSQL}
    ), 
    ${THIS_PERIOD_ALIAS} AS (
        SELECT
            "entityUuid",
            TO_CHAR(yearMonth, 'YYYY-MM-DD') AS "dateRangeStart",
            TO_CHAR(yearMonth + interval '1 month - 1 microseconds','YYYY-MM-DD')  AS "dateRangeEnd",
            category,
            total,
            currency
        FROM 
          ${YEAR_MONTHLY_RECORD_ALIAS}
        WHERE 
          yearMonth = DATE_TRUNC('month', timestamp '${date}')
        ORDER BY 
          category IS NULL, ABS(total) DESC
    ), PreviousPeriodTotalSum AS (
        SELECT SUM(total) FROM (
            SELECT total FROM ${YEAR_MONTHLY_RECORD_ALIAS}
            WHERE yearMonth = DATE_TRUNC('month', timestamp '${date}') - MAKE_INTERVAL(months => 1)
        )
    ) SELECT
          "entityUuid",
          "dateRangeStart",
          "dateRangeEnd",
          category,
          total,
          COALESCE(${THIS_PERIOD_TOTAL_SUM_ALIAS}.sum, 0) AS "totalSum",
          COALESCE(${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS}.sum, 0) AS "previousTotalSum",
          (${THIS_PERIOD_TOTAL_SUM_ALIAS}.sum - ${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS}.sum) / NULLIF(${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS}.sum,0) * 100 AS change,
          currency
    FROM 
      ${THIS_PERIOD_ALIAS}, (SELECT SUM(total) FROM ${THIS_PERIOD_ALIAS}) AS ${THIS_PERIOD_TOTAL_SUM_ALIAS}, ${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS};`.replace(
    `"${FUNCTION_NAME_TO_BE_REPLACE}"`,
    `${issuingFullDetailsFunction.functionName}`,
  );

  const result = await dataSource.query(QUERY, issuingFullDetailsFunction.params);

  dataSource.destroy();

  return result;
};
