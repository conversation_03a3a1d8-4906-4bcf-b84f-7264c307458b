import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { ContactTopTenRecord, TransactionsQueryCategoriesTotalInput } from '../../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../../config/environmentService.js';
import { getAppDataSource } from '../../../../../database/index.js';
import { FUNCTION_NAME_TO_BE_REPLACE } from '../../const.js';
import { getYearMonthlyRecordQuery } from '../../utils/queries.issuingTransactionsTotal.js';
import { getIssuingFullDetailsFunction } from '../../utils/queries.js';

const YEAR_MONTHLY_RECORD_ALIAS = 'YearMonthlyRecord';
const TOP_TEN_ALIAS = 'Top10';
const TOP_TEN_SUM_ALIAS = 'Top10Sum';
const PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS = 'PreviousTop10Sum';

export const getMonthlyTopTenContactRecords = async (
  env: EnvironmentService,
  input: TransactionsQueryCategoriesTotalInput,
): Promise<ContactTopTenRecord[]> => {
  const { entityUuid, timeZone, date, accountUuid, transactionDirection } = input;
  debug(`issuingTransactionFullDetailsDb -> getMonthlyTopTenContactRecords ${JSON.stringify(input)}`, entityUuid);

  const dataSource = await getAppDataSource(env);

  const yearMonthlyRecordSQL = getYearMonthlyRecordQuery('contactUuid', dataSource, {
    entityUuid,
    timeZone,
    date,
    accountUuid,
    transactionDirection,
  });
  const issuingFullDetailsFunction = getIssuingFullDetailsFunction(env.stage, input);

  const QUERY = `with ${YEAR_MONTHLY_RECORD_ALIAS} AS (
    ${yearMonthlyRecordSQL}
    ), 
    ${TOP_TEN_ALIAS} AS (
        SELECT
            "entityUuid",
            TO_CHAR(yearMonth, 'YYYY-MM-DD') AS "dateRangeStart",
            TO_CHAR(yearMonth + interval '1 month - 1 microseconds','YYYY-MM-DD')  AS "dateRangeEnd",
            "contactUuid",
            total,
            currency
        FROM 
          ${YEAR_MONTHLY_RECORD_ALIAS}
        WHERE 
          yearMonth = DATE_TRUNC('month', timestamp '${date}')
        ORDER BY 
          "contactUuid" IS NULL, ABS(total) DESC LIMIT 10
    ), ${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS} AS (
        SELECT SUM(total) FROM (
            SELECT total FROM ${YEAR_MONTHLY_RECORD_ALIAS}
            WHERE yearMonth = DATE_TRUNC('month', timestamp '${date}') - MAKE_INTERVAL(months => 1)
        ORDER BY "contactUuid" IS NULL, ABS(total) DESC
        )
    ) SELECT
          "entityUuid",
          "dateRangeStart",
          "dateRangeEnd",
          "contactUuid",
          total,
          COALESCE(${TOP_TEN_SUM_ALIAS}.sum, 0) AS "totalSum",
          COALESCE(${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS}.sum, 0) AS "previousTotalSum",
          (${TOP_TEN_SUM_ALIAS}.sum - ${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS}.sum) / NULLIF(${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS}.sum,0) * 100 AS change,
          currency
    FROM 
      ${TOP_TEN_ALIAS}, (SELECT SUM(total) FROM ${TOP_TEN_ALIAS}) AS ${TOP_TEN_SUM_ALIAS}, ${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS};`.replace(
    `"${FUNCTION_NAME_TO_BE_REPLACE}"`,
    `${issuingFullDetailsFunction.functionName}`,
  );

  const result = await dataSource.query(QUERY, issuingFullDetailsFunction.params);

  dataSource.destroy();

  return result;
};
