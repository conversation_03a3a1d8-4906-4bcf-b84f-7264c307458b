import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories, ISO4217 } from '@npco/component-dto-core';

import { v4 as uuidV4 } from 'uuid';

import type { CategoryTotalRecord } from '../../../../common/types/index';

import { getAllCategoriesBaseResponse } from './allCategoriesTotalReport';

describe('getAllCategoriesBaseResponse', () => {
  const domicile = Domicile.AU;
  it('should return the correct response object', () => {
    const response = [
      {
        entityUuid: uuidV4(),
        dateRangeStart: '2024-01-01',
        dateRangeEnd: '2024-01-31',
        category: EntityCategories.ADVERTISING,
        total: '100',
        currency: ISO4217.AUD,
      },
      {
        entityUuid: uuidV4(),
        dateRangeStart: '2024-01-01',
        dateRangeEnd: '2024-01-31',
        category: EntityCategories.BANK_FEES,
        total: '200',
        currency: ISO4217.AUD,
      },
    ];

    const expected = {
      change: 0,
      totalSum: {
        currency: 'AUD',
        value: '0',
      },
      categoriesTotal: [
        {
          category: EntityCategories.ADVERTISING,
          total: {
            currency: 'AUD',
            value: '100',
          },
        },
        {
          category: EntityCategories.BANK_FEES,
          total: {
            currency: 'AUD',
            value: '200',
          },
        },
      ],
    };

    const result = getAllCategoriesBaseResponse(response, domicile);

    expect(result).toEqual(expected);
  });

  it('should return the correct response object when response is empty', () => {
    const response: CategoryTotalRecord[] = [];

    const expected = {
      change: 0,
      totalSum: {
        currency: 'AUD',
        value: '0',
      },
    };

    const result = getAllCategoriesBaseResponse(response, domicile);

    expect(result).toEqual(expected);
  });

  it('should return the correct response object when previousTotalSum is present', () => {
    const response = [
      {
        entityUuid: uuidV4(),
        dateRangeStart: '2024-01-01',
        dateRangeEnd: '2024-01-31',
        category: EntityCategories.ADVERTISING,
        total: '100',
        previousTotalSum: '50',
        totalSum: '100',
        currency: ISO4217.AUD,
      },
    ];

    const expected = {
      change: 0,
      totalSum: {
        currency: 'AUD',
        value: '100',
      },
      previousTotalSum: {
        currency: 'AUD',
        value: '50',
      },
      categoriesTotal: [
        {
          category: EntityCategories.ADVERTISING,
          total: {
            currency: 'AUD',
            value: '100',
          },
        },
      ],
    };

    const result = getAllCategoriesBaseResponse(response, domicile);

    expect(result).toEqual(expected);
  });
});
