import { debug } from '@npco/component-bff-core/dist/utils/logger';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import type { DataSource } from 'typeorm';

import type {
  CashFlowNetAmountsRecord,
  CategoryTransactionsQueryWithFilterInput,
  TransactionsQueryWithFilterInput,
} from '../../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../../config/environmentService.js';
import { getAppDataSource } from '../../../../../database/index.js';
import { FUNCTION_NAME_TO_BE_REPLACE } from '../../const.js';
import {
  getIssuingFullDetailsFunction,
  getPreviousMonthTransactionSelectQuery,
  getTransactionDirectionFilter,
} from '../../utils/queries.js';
import { getLimitClause, getNetAmountOrderBy } from '../utils/sortingAndLimit.js';

const YEAR_MONTHLY_RECORD_ALIAS = 'YearMonthlyRecord';
const THIS_YEAR_MONTH_ALIAS = 'thisYearMonth';
const LAST_YEAR_MONTH_ALIAS = 'lastYearMonth';

const getEntityPreviousTransactionsQuery = (
  repository: DataSource,
  entityUuid: string,
  date: string,
  timeZone?: string,
) => {
  return repository
    .createQueryBuilder()
    .select(getPreviousMonthTransactionSelectQuery(timeZone))
    .from(`${FUNCTION_NAME_TO_BE_REPLACE}`, 'IssuingTransactionFullDetails')
    .where(`"entityUuid" = '${entityUuid}'`)
    .andWhere(
      `DATE_TRUNC('month' ,timestamp AT TIME ZONE '${timeZone}') BETWEEN DATE_TRUNC('month', timestamp '${date}') - MAKE_INTERVAL(months => 1) AND DATE_TRUNC('month', timestamp '${date}')`,
    )
    .groupBy('"entityUuid", yearMonth, currency');
};

const getYearMonthlyRecordQuery = (
  repository: DataSource,
  {
    entityUuid,
    timeZone,
    date,
    groupBy,
    accountUuid,
    transactionDirection,
    category,
  }: CategoryTransactionsQueryWithFilterInput,
) => {
  const transactionQuery = getEntityPreviousTransactionsQuery(repository, entityUuid, date, timeZone);
  let queryBuilder = transactionQuery.addSelect(`"${groupBy}"`).addGroupBy(`"${groupBy}"`);

  if (groupBy === 'category') {
    queryBuilder.addSelect('COALESCE(COUNT(DISTINCT subcategory), 0)', 'subcategoryCount');
  }

  if (accountUuid) {
    queryBuilder.andWhere(`"accountUuid" = '${accountUuid}'`);
  } else {
    queryBuilder.andWhere(
      `type NOT IN ('${DebitCardTransactionTypeV2.TRANSFER_IN}', '${DebitCardTransactionTypeV2.TRANSFER_OUT}')`,
    );
  }

  if (groupBy === 'subcategory') {
    queryBuilder.addSelect('category').andWhere(`category = '${category}'`).addGroupBy('category');
  }

  queryBuilder = getTransactionDirectionFilter(queryBuilder, transactionDirection);

  return queryBuilder.getSql();
};

const getSelectGroupByQuery = (groupBy: TransactionsQueryWithFilterInput['groupBy']) => `
  ${groupBy === 'category' ? `${THIS_YEAR_MONTH_ALIAS}."subcategoryCount" AS "subcategoryCount"` : ''}
  ${groupBy === 'contactUuid' ? `${THIS_YEAR_MONTH_ALIAS}."contactUuid" AS "contactUuid"` : ''}
  ${
    groupBy === 'subcategory'
      ? `${THIS_YEAR_MONTH_ALIAS}."subcategory" AS "subcategory", ${THIS_YEAR_MONTH_ALIAS}."category" AS "category"`
      : ''
  }
  `;

export const getMonthlyNetAmountTransactionRecords = async (
  env: EnvironmentService,
  input: CategoryTransactionsQueryWithFilterInput,
): Promise<CashFlowNetAmountsRecord[]> => {
  const { entityUuid, date, groupBy, sortBy, sortOrder, limit } = input;
  debug(
    `issuingTransactionFullDetailsDb -> getMonthlyNetAmountTransactionRecords ${JSON.stringify(input)}`,
    entityUuid,
  );

  const dataSource = await getAppDataSource(env);

  const yearMonthlyRecordSQL = getYearMonthlyRecordQuery(dataSource, input);

  const issuingFullDetailsFunction = getIssuingFullDetailsFunction(env.stage, input);

  const QUERY = `WITH ${YEAR_MONTHLY_RECORD_ALIAS} AS (
    ${yearMonthlyRecordSQL}
  )
  SELECT
    ${THIS_YEAR_MONTH_ALIAS}."entityUuid" AS "entityUuid",
    TO_CHAR(thisYearMonth.yearMonth, 'YYYY-MM-DD') as "dateRangeStart",
    TO_CHAR(thisYearMonth.yearMonth +  interval '1 month - 1 microseconds', 'YYYY-MM-DD') as "dateRangeEnd",
    ${THIS_YEAR_MONTH_ALIAS}."noOfTransaction" AS "noOfTransaction",
    ${THIS_YEAR_MONTH_ALIAS}.total AS total,
    ${THIS_YEAR_MONTH_ALIAS}.average AS average,
    ${THIS_YEAR_MONTH_ALIAS}."${groupBy}" AS "${groupBy}",
    (${THIS_YEAR_MONTH_ALIAS}.total - ${LAST_YEAR_MONTH_ALIAS}.total)/NULLIF(${LAST_YEAR_MONTH_ALIAS}.total,0) * 100 AS change,
    ${THIS_YEAR_MONTH_ALIAS}.currency AS currency,
    ${getSelectGroupByQuery(groupBy)}
  FROM ${YEAR_MONTHLY_RECORD_ALIAS} AS ${THIS_YEAR_MONTH_ALIAS} left join ${YEAR_MONTHLY_RECORD_ALIAS} AS ${LAST_YEAR_MONTH_ALIAS}
  ON ${THIS_YEAR_MONTH_ALIAS}.yearMonth = ${LAST_YEAR_MONTH_ALIAS}.yearMonth + interval '1 month' AND
    ${THIS_YEAR_MONTH_ALIAS}."${groupBy}" = ${LAST_YEAR_MONTH_ALIAS}."${groupBy}"
  WHERE
    ${THIS_YEAR_MONTH_ALIAS}.yearMonth = DATE_TRUNC('month', timestamp '${date}')
  ORDER BY ${THIS_YEAR_MONTH_ALIAS}."${groupBy}" IS NULL, ${getNetAmountOrderBy(groupBy, {
    alias: THIS_YEAR_MONTH_ALIAS,
    sortBy,
    sortOrder,
  })}${getLimitClause(limit)}`.replace(
    `"${FUNCTION_NAME_TO_BE_REPLACE}"`,
    `${issuingFullDetailsFunction.functionName}`,
  );

  const result = await dataSource.query(QUERY, issuingFullDetailsFunction.params);

  dataSource.destroy();

  return result;
};
