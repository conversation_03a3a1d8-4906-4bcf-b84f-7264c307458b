import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { ISO4217 } from '@npco/component-dto-core';

import { v4 as uuidV4 } from 'uuid';

import type { CategoryTotalRecord } from '../../../../common/types/index';

import { getTopTenContactBaseResponse } from './topTenContact';

describe('getTopTenContactBaseResponse', () => {
  const domicile = Domicile.AU;
  it('should return the correct response object', () => {
    const contactUuid = uuidV4();
    const response = [
      {
        entityUuid: uuidV4(),
        dateRangeStart: '2024-01-01',
        dateRangeEnd: '2024-01-31',
        contactUuid,
        total: '100',
        currency: ISO4217.AUD,
      },
      {
        entityUuid: uuidV4(),
        dateRangeStart: '2024-01-01',
        dateRangeEnd: '2024-01-31',
        contactUuid,
        total: '200',
        currency: ISO4217.AUD,
      },
    ];

    const expected = {
      change: 0,
      totalSum: {
        currency: 'AUD',
        value: '0',
      },
      contactTopTenTotals: [
        {
          contactUuid,
          total: {
            currency: 'AUD',
            value: '100',
          },
        },
        {
          contactUuid,
          total: {
            currency: 'AUD',
            value: '200',
          },
        },
      ],
    };

    const result = getTopTenContactBaseResponse(response, domicile);

    expect(result).toEqual(expected);
  });

  it('should return the correct response object when response is empty', () => {
    const response: CategoryTotalRecord[] = [];

    const expected = {
      change: 0,
      totalSum: {
        currency: 'AUD',
        value: '0',
      },
    };

    const result = getTopTenContactBaseResponse(response, domicile);

    expect(result).toEqual(expected);
  });

  it('should return the correct response object when previousTotalSum is present', () => {
    const contactUuid = uuidV4();
    const response = [
      {
        entityUuid: uuidV4(),
        dateRangeStart: '2024-01-01',
        dateRangeEnd: '2024-01-31',
        contactUuid,
        total: '100',
        previousTotalSum: '50',
        totalSum: '100',
        currency: ISO4217.AUD,
      },
    ];

    const expected = {
      change: 0,
      totalSum: {
        currency: ISO4217.AUD,
        value: '100',
      },
      previousTotalSum: {
        currency: ISO4217.AUD,
        value: '50',
      },
      contactTopTenTotals: [
        {
          contactUuid,
          total: {
            currency: ISO4217.AUD,
            value: '100',
          },
        },
      ],
    };

    const result = getTopTenContactBaseResponse(response, domicile);

    expect(result).toEqual(expected);
  });

  it('should return default currency when currency is not provided', () => {
    const contactUuid = uuidV4();
    const response = [
      {
        entityUuid: uuidV4(),
        dateRangeStart: '2024-01-01',
        dateRangeEnd: '2024-01-31',
        contactUuid,
        total: '100',
        previousTotalSum: '50',
        totalSum: '100',
      },
    ];
    const expected = {
      change: 0,
      totalSum: {
        currency: ISO4217.AUD,
        value: '100',
      },
      previousTotalSum: {
        currency: ISO4217.AUD,
        value: '50',
      },
      contactTopTenTotals: [
        {
          contactUuid,
          total: {
            currency: ISO4217.AUD,
            value: '100',
          },
        },
      ],
    };

    const result = getTopTenContactBaseResponse(response as any, domicile);
    expect(result).toEqual(expected);
  });
});
