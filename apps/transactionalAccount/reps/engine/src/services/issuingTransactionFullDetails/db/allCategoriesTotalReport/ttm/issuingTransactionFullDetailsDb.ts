import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { CategoryTotalRecord, TransactionsQueryCategoriesTotalInput } from '../../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../../config/environmentService.js';
import { getAppDataSource } from '../../../../../database/index.js';
import { FUNCTION_NAME_TO_BE_REPLACE } from '../../const.js';
import {
  getIssuingFullDetailsFunction,
  getPrevTotalTtmTransactionsColsAndGroupings,
  getPrevTotalTtmTransactionsWhereClause,
} from '../../utils/queries.js';

const TTM_RECORD_ALIAS = 'ttmFullDetails';
const THIS_PERIOD_ALIAS = 'ThisPeriod';
const THIS_PERIOD_TOTAL_SUM_ALIAS = 'ThisPeriodTotalSum';
const PREVIOUS_PERIOD_TOTAL_SUM_ALIAS = 'PreviousPeriodTotalSum';

export const getTTMCategoriesTotalRecords = async (
  env: EnvironmentService,
  input: TransactionsQueryCategoriesTotalInput,
): Promise<CategoryTotalRecord[]> => {
  const { entityUuid, timeZone, date, accountUuid } = input;
  debug(`issuingTransactionFullDetailsDb -> getTTMCategoriesTotalRecords ${JSON.stringify(input)}`, entityUuid);

  const dataSource = await getAppDataSource(env);
  const issuingFullDetailsFunction = getIssuingFullDetailsFunction(env.stage, input);

  const QUERY = `WITH ${TTM_RECORD_ALIAS} AS (
    SELECT
      ${getPrevTotalTtmTransactionsColsAndGroupings('category', accountUuid).columns}
    FROM "${FUNCTION_NAME_TO_BE_REPLACE}" i JOIN (
        SELECT ttm, RANK() OVER (
            ORDER BY ttm desc
        ) - 1 rank FROM ${env.stage}.ttmlistgeneration('${date}')
    ) AS t ON floor(${env.stage}.ttmRankCalculation(timestamp, '${date}', '${timeZone}' ) )  = t.rank
    WHERE ${getPrevTotalTtmTransactionsWhereClause(input, env.stage)}
    GROUP BY  ${getPrevTotalTtmTransactionsColsAndGroupings('category', accountUuid).groupBy}
  ),
  ${THIS_PERIOD_ALIAS} AS (
    SELECT
        "entityUuid",
        TO_CHAR(ttm, 'YYYY-MM-DD') AS "dateRangeStart",
        TO_CHAR((ttm + make_interval(years => 1, days => -1))::date, 'YYYY-MM-DD') AS "dateRangeEnd",
        category,
        total,
        currency
    from ${TTM_RECORD_ALIAS}
    WHERE ttm = (SELECT ${env.stage}.ttmlistgeneration('${date}') limit 1)
    order by category IS NULL, ABS(total) DESC
), ${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS} AS (
    SELECT SUM(total) FROM (
        SELECT
        total
    FROM ${TTM_RECORD_ALIAS}
    WHERE ttm = (SELECT ${env.stage}.ttmlistgeneration('${date}') OFFSET 1 limit 1)
    ORDER BY category IS NULL, ABS(total) DESC
    )
)
SELECT
      "entityUuid",
      "dateRangeStart",
      "dateRangeEnd",
      category,
      total,
      COALESCE(${THIS_PERIOD_TOTAL_SUM_ALIAS}.sum, 0) AS "totalSum",
      COALESCE(${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS}.sum, 0) AS "previousTotalSum",
      (${THIS_PERIOD_TOTAL_SUM_ALIAS}.sum - ${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS}.sum) / NULLIF(${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS}.sum, 0) * 100 AS change,
      currency
FROM ${THIS_PERIOD_ALIAS}, 
(SELECT SUM(total) from ${THIS_PERIOD_ALIAS}) AS ${THIS_PERIOD_TOTAL_SUM_ALIAS}, ${PREVIOUS_PERIOD_TOTAL_SUM_ALIAS}`.replace(
    `"${FUNCTION_NAME_TO_BE_REPLACE}"`,
    `${issuingFullDetailsFunction.functionName}`,
  );

  const result = await dataSource.query(QUERY, issuingFullDetailsFunction.params);

  dataSource.destroy();

  return result;
};
