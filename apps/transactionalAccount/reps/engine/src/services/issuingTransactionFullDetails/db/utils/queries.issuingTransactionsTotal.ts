import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import type { DataSource } from 'typeorm';

import type {
  TransactionsQueryWithFilterInput,
  TransactionsQueryCategoriesTotalInput,
} from '../../../../common/types/index.js';
import { FUNCTION_NAME_TO_BE_REPLACE } from '../const.js';

import { getTransactionDirectionFilter } from './queries';

const getEntityPreviousTransactionsQuery = (
  groupBy: Omit<TransactionsQueryWithFilterInput['groupBy'], 'subcategory'>,
  dataSource: DataSource,
  entityUuid: string,
  date: string,
  timeZone?: string,
) => {
  const query = dataSource
    .createQueryBuilder()
    .select([
      '"entityUuid"',
      `DATE_TRUNC('month', timestamp AT TIME ZONE '${timeZone}') AS yearMonth`,
      'SUM(amount) AS total',
      'currency',
    ])
    .from(`${FUNCTION_NAME_TO_BE_REPLACE}`, 'IssuingTransactionFullDetails')
    .where(`"entityUuid" = '${entityUuid}'`)
    .andWhere(
      `DATE_TRUNC('month' ,timestamp AT TIME ZONE '${timeZone}') BETWEEN DATE_TRUNC('month', timestamp '${date}') - MAKE_INTERVAL(months => 1) AND DATE_TRUNC('month', timestamp '${date}')`,
    )
    .groupBy('"entityUuid", yearMonth, currency');

  if (groupBy === 'contactUuid') {
    query.addSelect(['"contactUuid"']).addGroupBy('"contactUuid"');
  }

  if (groupBy === 'category') {
    query.addSelect(['category']).addGroupBy('category');
  }

  return query;
};
export const getYearMonthlyRecordQuery = (
  groupBy: Omit<TransactionsQueryWithFilterInput['groupBy'], 'subcategory'>,
  dataSource: DataSource,
  {
    entityUuid,
    timeZone,
    date,
    accountUuid,
    transactionDirection,
  }: Omit<TransactionsQueryCategoriesTotalInput, 'domicile'>,
) => {
  let queryBuilder = getEntityPreviousTransactionsQuery(groupBy, dataSource, entityUuid, date, timeZone);

  if (accountUuid) {
    queryBuilder.andWhere(`"accountUuid" = '${accountUuid}'`);
  } else {
    queryBuilder.andWhere(
      `type NOT IN ('${DebitCardTransactionTypeV2.TRANSFER_IN}', '${DebitCardTransactionTypeV2.TRANSFER_OUT}')`,
    );
  }

  queryBuilder = getTransactionDirectionFilter(queryBuilder, transactionDirection);

  return queryBuilder.getSql();
};
