import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import type { ObjectLiteral, SelectQueryBuilder } from 'typeorm';

import type {
  TransactionDirection,
  TransactionsQueryCategoriesTotalInput,
  TransactionsQueryWithFilterInput,
} from '../../../../common/types/index.js';
import { type EnvironmentService } from '../../../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../../../const/timezone.js';

export const getPreviousMonthTransactionSelectQuery = (timeZone = DEFAULT_TIMEZONE) => {
  return [
    '"entityUuid"',
    `DATE_TRUNC('month', timestamp AT TIME ZONE '${timeZone}') AS yearMonth`,
    'COUNT(*) AS "noOfTransaction"',
    'SUM(amount) AS total',
    'AVG(amount) AS average',
    'currency',
  ];
};

export const getTransactionDirectionFilterWhereClause = (transactionDirection: TransactionDirection) => {
  return `cashflowType = '${transactionDirection}'`;
};

export const getTransactionDirectionFilter = (
  queryBuilder: SelectQueryBuilder<ObjectLiteral>,
  transactionDirection?: TransactionDirection,
) => {
  if (transactionDirection) {
    return queryBuilder.andWhere(getTransactionDirectionFilterWhereClause(transactionDirection));
  }

  return queryBuilder;
};

export const getPrevTotalTtmTransactionsColsAndGroupings = (
  groupBy: Omit<TransactionsQueryWithFilterInput['groupBy'], 'subcategory'>,
  accountUuid?: string,
) => {
  let columns = ['"entityUuid"', 'ttm', 'SUM(amount) AS total', 'currency'];

  let group = ['"entityUuid"', 'ttm', 'currency'];

  if (groupBy === 'contactUuid') {
    columns = columns.concat(['"contactUuid"']);
    group = group.concat(['"contactUuid"']);
  }

  if (groupBy === 'category') {
    columns = columns.concat(['category']);
    group = group.concat(['category']);
  }

  if (accountUuid) {
    columns = columns.concat(['"accountUuid"']);
    group = group.concat(['"accountUuid"']);
  }

  return {
    columns: columns.join(', '),
    groupBy: group.join(', '),
  };
};

export const getPrevTotalTtmTransactionsWhereClause = (
  { entityUuid, timeZone, date, accountUuid, transactionDirection }: TransactionsQueryCategoriesTotalInput,
  schema: EnvironmentService['stage'],
) => {
  let whereClause = `"entityUuid" = '${entityUuid}'`;

  if (accountUuid) {
    whereClause += ` AND "accountUuid" = '${accountUuid}'`;
  } else {
    whereClause += ` AND type NOT IN ('${DebitCardTransactionTypeV2.TRANSFER_IN}', '${DebitCardTransactionTypeV2.TRANSFER_OUT}')`;
  }

  if (transactionDirection) {
    whereClause += `AND ${getTransactionDirectionFilterWhereClause(transactionDirection)}`;
  }

  return `${whereClause} AND ${schema}.ttmRankCalculation(timestamp, '${date}', '${timeZone}' ) BETWEEN 0 AND 2`;
};

export const getIssuingFullDetailsFunction = <
  T extends Omit<TransactionsQueryWithFilterInput, 'reportType' | 'groupBy'>,
>(
  stage: EnvironmentService['stage'],
  input: T,
) => {
  const { entityUuid, accountUuid, transactionDirection, domicile } = input;

  if (accountUuid && transactionDirection) {
    return {
      functionName: `${stage}.issuingtransactionfulldetails($1::uuid, $2::uuid, $3, $4)`,
      params: [entityUuid, accountUuid, transactionDirection, domicile],
    };
  }

  if (accountUuid) {
    return {
      functionName: `${stage}.issuingtransactionfulldetails($1::uuid, $2::uuid, $3)`,
      params: [entityUuid, accountUuid, domicile],
    };
  }

  if (transactionDirection) {
    return {
      functionName: `${stage}.issuingtransactionfulldetails($1::uuid, $2, $3)`,
      params: [entityUuid, transactionDirection, domicile],
    };
  }

  return {
    functionName: `${stage}.issuingtransactionfulldetails($1::uuid, $2)`,
    params: [entityUuid, domicile],
  };
};
