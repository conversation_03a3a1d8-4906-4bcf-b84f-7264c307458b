import { debug } from '@npco/component-bff-core/dist/utils/logger';

import type { ContactTopTenRecord, TransactionsQueryCategoriesTotalInput } from '../../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../../config/environmentService.js';
import { getAppDataSource } from '../../../../../database/index.js';
import { FUNCTION_NAME_TO_BE_REPLACE } from '../../const.js';
import {
  getIssuingFullDetailsFunction,
  getPrevTotalTtmTransactionsColsAndGroupings,
  getPrevTotalTtmTransactionsWhereClause,
} from '../../utils/queries.js';

const TTM_RECORD_ALIAS = 'ttmFullDetails';
const TOP_TEN_ALIAS = 'Top10';
const TOP_TEN_SUM_ALIAS = 'Top10Sum';
const PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS = 'PreviousTop10Sum';

export const getTTMTopTenContactRecords = async (
  env: EnvironmentService,
  input: TransactionsQueryCategoriesTotalInput,
): Promise<ContactTopTenRecord[]> => {
  const { entityUuid, timeZone, date, accountUuid } = input;
  debug(`issuingTransactionFullDetailsDb -> getTTMTopTenContactRecords ${JSON.stringify(input)}`, entityUuid);

  const dataSource = await getAppDataSource(env);
  const issuingFullDetailsFunction = getIssuingFullDetailsFunction(env.stage, input);

  const QUERY = `WITH ${TTM_RECORD_ALIAS} AS (
    SELECT
      ${getPrevTotalTtmTransactionsColsAndGroupings('contactUuid', accountUuid).columns}
    FROM "${FUNCTION_NAME_TO_BE_REPLACE}" i JOIN (
        SELECT ttm, RANK() OVER (
            ORDER BY ttm desc
        ) - 1 rank FROM ${env.stage}.ttmlistgeneration('${date}')
    ) AS t ON floor(${env.stage}.ttmRankCalculation(timestamp, '${date}', '${timeZone}' ) )  = t.rank
    WHERE ${getPrevTotalTtmTransactionsWhereClause(input, env.stage)}
    GROUP BY  ${getPrevTotalTtmTransactionsColsAndGroupings('contactUuid', accountUuid).groupBy}
  ),
  ${TOP_TEN_ALIAS} AS (
    SELECT
        "entityUuid",
        "contactUuid",
        TO_CHAR(ttm, 'YYYY-MM-DD') AS "dateRangeStart",
        TO_CHAR((ttm + make_interval(years => 1, days => -1))::date, 'YYYY-MM-DD') AS "dateRangeEnd",
        total,
        currency
    from ${TTM_RECORD_ALIAS}
    WHERE ttm = (SELECT ${env.stage}.ttmlistgeneration('${date}') limit 1)
    ORDER BY "contactUuid" IS NULL, ABS(total) DESC limit 10
), ${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS} AS (
    SELECT SUM(total) FROM (
        SELECT
        total
    FROM ${TTM_RECORD_ALIAS}
    WHERE ttm = (SELECT ${env.stage}.ttmlistgeneration('${date}') OFFSET 1 limit 1)
    ORDER BY "contactUuid" IS NULL, ABS(total) DESC
    )
)
SELECT
      "entityUuid",
      "dateRangeStart",
      "dateRangeEnd",
      "contactUuid",
      total,
      COALESCE(${TOP_TEN_SUM_ALIAS}.sum, 0) AS "totalSum",
      COALESCE(${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS}.sum, 0) AS "previousTotalSum",
      (${TOP_TEN_SUM_ALIAS}.sum - ${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS}.sum) / NULLIF(${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS}.sum, 0) * 100 AS change,
      currency
FROM ${TOP_TEN_ALIAS}, 
(SELECT SUM(total) from ${TOP_TEN_ALIAS}) AS ${TOP_TEN_SUM_ALIAS}, ${PREVIOUS_PERIOD_TOP_TEN_SUM_ALIAS}`.replace(
    `"${FUNCTION_NAME_TO_BE_REPLACE}"`,
    `${issuingFullDetailsFunction.functionName}`,
  );

  const result = await dataSource.query(QUERY, issuingFullDetailsFunction.params);

  dataSource.destroy();

  return result;
};
