import { debug } from '@npco/component-bff-core/dist/utils/logger';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import type { CashFlowTotalAmountsRecord, TransactionQueryTotalAmountsInput } from '../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../config/environmentService.js';
import { getAppDataSource } from '../../../../database/index.js';
import { getCurrencyByDomicile } from '../../../../utils/index.js';
import { FUNCTION_NAME_TO_BE_REPLACE } from '../const.js';
import { getIssuingFullDetailsFunction } from '../utils/queries.js';

const getWhereClauseForIssuingTransactionTable = ({
  entityUuid,
  accountUuid,
  date,
  timeZone,
}: TransactionQueryTotalAmountsInput) => {
  const whereClause = [
    `"entityUuid" = '${entityUuid}'`,
    `
      timestamp BETWEEN ((date_trunc('month', timestamp '${date}') - interval '11 months')::timestamp at time zone '${timeZone}') 
      AND ((date_trunc('month', timestamp '${date}') + interval '1 month - 1 microsecond')::timestamp at time zone '${timeZone}')
    `,
  ];

  if (accountUuid) {
    whereClause.push(`"accountUuid" = '${accountUuid}'`);
  } else {
    whereClause.push(
      `TYPE NOT IN ('${DebitCardTransactionTypeV2.TRANSFER_IN}', '${DebitCardTransactionTypeV2.TRANSFER_OUT}')`,
    );
  }

  return whereClause.join(' AND ');
};

export const getSelectQueryForIssuingTransactionTable = () =>
  [
    `COALESCE(SUM(amount) FILTER(WHERE cashflowType = 'INCOME'), 0) AS "income"`,
    `COALESCE(SUM(noOfTransaction) FILTER(WHERE cashflowType = 'INCOME'), 0) AS "noOfIncomeTransaction"`,
    `COALESCE(SUM(amount) FILTER(WHERE cashflowType = 'EXPENSE'), 0) * -1 AS "expense"`,
    `COALESCE(SUM(noOfTransaction) FILTER(WHERE cashflowType = 'EXPENSE'), 0) AS "noOfExpenseTransaction"`,
    'currency',
  ].join(',\n');

export const getPeriodicTotalAmounts = async (
  env: EnvironmentService,
  input: TransactionQueryTotalAmountsInput,
): Promise<CashFlowTotalAmountsRecord[]> => {
  const { entityUuid, timeZone, date } = input;

  debug(`issuingTransactionFullDetailsDb -> getPeriodicTotalAmounts ${JSON.stringify(input)}`, entityUuid);

  const dataSource = await getAppDataSource(env);
  const issuingFullDetailsFunction = getIssuingFullDetailsFunction(env.stage, input);

  const QUERY = `
    SELECT
      to_char(min(yearMonth) AT TIME ZONE '${timeZone}', 'YYYY-MM-DD') AS "dateRangeStart",
      to_char(max(yearMonth) AT TIME ZONE '${timeZone}' + interval '1 month - 1 microsecond', 'YYYY-MM-DD') AS "dateRangeEnd",
      ${getSelectQueryForIssuingTransactionTable()}
    FROM (
      SELECT
          timeSeries.yearMonth yearMonth,
          coalesce(amount, 0) AS amount,
          coalesce(noOfTransaction,0) AS noOfTransaction,
          timeSeries.cashflowType cashflowType,
          COALESCE(transaction.currency, '${getCurrencyByDomicile(input.domicile)}') AS currency
      FROM
        ${env.stage}.getYearMonthIncomeExpenseSeries('${date}', '${timeZone}') AS timeSeries
      left join
      (
          SELECT
              date_trunc('month' ,timestamp)::timestamp AT TIME ZONE '${timeZone}' AS yearMonth,
              SUM(amount) AS amount,
              count(*) AS noOfTransaction,
              cashflowType,
              currency
          FROM "${FUNCTION_NAME_TO_BE_REPLACE}" "IssuingTransactionFullDetails"
          WHERE ${getWhereClauseForIssuingTransactionTable(input)}
          GROUP BY yearMonth, cashflowType, currency
      ) transaction on timeSeries.yearMonth = transaction.yearMonth AND timeSeries.cashflowType = transaction.cashflowType
    ) source GROUP BY yearMonth, currency ORDER BY yearMonth DESC;
  `.replace(`"${FUNCTION_NAME_TO_BE_REPLACE}"`, `${issuingFullDetailsFunction.functionName}`);

  const result = await dataSource.query(QUERY, issuingFullDetailsFunction.params);

  dataSource.destroy();

  return result;
};

export const getOverallTotalAmounts = async (
  env: EnvironmentService,
  input: TransactionQueryTotalAmountsInput,
): Promise<CashFlowTotalAmountsRecord[]> => {
  const { entityUuid, date } = input;

  debug(`issuingTransactionFullDetailsDb -> getOverallTotalAmounts ${JSON.stringify(input)}`, entityUuid);

  const dataSource = await getAppDataSource(env);
  const issuingFullDetailsFunction = getIssuingFullDetailsFunction(env.stage, input);

  const QUERY = `
    SELECT
      to_char((date_trunc('month', timestamp '${date}') - interval '11 months'), 'YYYY-MM-DD') AS "dateRangeStart",
      to_char((date_trunc('month', timestamp '${date}') + interval '1 month - 1 microsecond'), 'YYYY-MM-DD') AS "dateRangeEnd",
      ${getSelectQueryForIssuingTransactionTable()}
    FROM (
      SELECT coalesce(SUM(amount), 0) AS amount,
          coalesce(count(*), 0) AS noOfTransaction,
          cashflowType,
          currency
      FROM "${FUNCTION_NAME_TO_BE_REPLACE}" "IssuingTransactionFullDetails"
      WHERE ${getWhereClauseForIssuingTransactionTable(input)}
      GROUP BY cashflowType, currency
    ) GROUP BY currency;
  `.replace(`"${FUNCTION_NAME_TO_BE_REPLACE}"`, `${issuingFullDetailsFunction.functionName}`);

  const result = await dataSource.query(QUERY, issuingFullDetailsFunction.params);

  dataSource.destroy();

  return result;
};
