import { debug } from '@npco/component-bff-core/dist/utils/logger';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import type {
  CashFlowNetAmountsRecord,
  CategoryTransactionsQueryWithFilterInput,
  TransactionsQueryWithFilterInput,
} from '../../../../../common/types/index.js';
import type { EnvironmentService } from '../../../../../config/environmentService.js';
import { getAppDataSource } from '../../../../../database/index.js';
import { FUNCTION_NAME_TO_BE_REPLACE } from '../../const.js';
import { getIssuingFullDetailsFunction, getTransactionDirectionFilterWhereClause } from '../../utils/queries.js';
import { getLimitClause, getNetAmountOrderBy } from '../utils/sortingAndLimit.js';

const TTM_RECORD_ALIAS = 'ttmFullDetails';
const THIS_TTM_ALIAS = 'thisTTM';
const LAST_TTM_ALIAS = 'lastTTM';

const getPrevTtmTransactionsColsAndGroupings = (
  groupBy: TransactionsQueryWithFilterInput['groupBy'],
  accountUuid?: string,
) => {
  let columns = [
    '"entityUuid"',
    'ttm',
    'COUNT(*) AS "noOfTransaction"',
    'SUM(amount) AS total',
    'AVG(amount) AS average',
    'currency',
  ];

  let group = ['"entityUuid"', 'ttm', 'currency'];

  if (accountUuid) {
    columns = columns.concat(['"accountUuid"']);
    group = group.concat(['"accountUuid"']);
  }

  if (groupBy === 'category') {
    columns = columns.concat(['category', 'COALESCE(COUNT(DISTINCT subcategory), 0) AS "subcategoryCount"']);
    group = group.concat(['category']);
  }

  if (groupBy === 'subcategory') {
    columns = columns.concat(['subcategory', 'category']);
    group = group.concat(['subcategory', 'category']);
  }

  if (groupBy === 'contactUuid') {
    columns = columns.concat(['"contactUuid"']);
    group = group.concat(['"contactUuid"']);
  }

  return {
    columns: columns.join(', '),
    groupBy: group.join(', '),
  };
};

const getPreviousTtmTransactionsWhereClause = (
  {
    entityUuid,
    timeZone,
    date,
    groupBy,
    accountUuid,
    transactionDirection,
    category,
  }: CategoryTransactionsQueryWithFilterInput,
  schema: EnvironmentService['stage'],
) => {
  let whereClause = `"entityUuid" = '${entityUuid}'`;

  if (accountUuid) {
    whereClause += ` AND "accountUuid" = '${accountUuid}'`;
  } else {
    whereClause += ` AND type NOT IN ('${DebitCardTransactionTypeV2.TRANSFER_IN}', '${DebitCardTransactionTypeV2.TRANSFER_OUT}')`;
  }

  if (groupBy === 'subcategory' && category) {
    whereClause += ` AND category = '${category}'`;
  }

  if (transactionDirection) {
    whereClause += `AND ${getTransactionDirectionFilterWhereClause(transactionDirection)}`;
  }

  return `${whereClause} AND ${schema}.ttmRankCalculation(timestamp, '${date}', '${timeZone}' ) BETWEEN 0 AND 2`;
};

const getSelectGroupByQuery = (groupBy: TransactionsQueryWithFilterInput['groupBy']) => `
  ${groupBy === 'category' ? `${THIS_TTM_ALIAS}."subcategoryCount" AS "subcategoryCount"` : ''}
  ${groupBy === 'contactUuid' ? `${THIS_TTM_ALIAS}."contactUuid" AS "contactUuid"` : ''}
  ${
    groupBy === 'subcategory'
      ? `${THIS_TTM_ALIAS}."subcategory" AS "subcategory", ${THIS_TTM_ALIAS}."category" AS "category"`
      : ''
  }
  `;

export const getTTMNetAmountTransactionRecords = async (
  env: EnvironmentService,
  input: CategoryTransactionsQueryWithFilterInput,
): Promise<CashFlowNetAmountsRecord[]> => {
  const { entityUuid, timeZone, date, groupBy, accountUuid, sortBy, sortOrder, limit } = input;
  debug(`issuingTransactionFullDetailsDb -> getTTMNetAmountTransactionRecords ${JSON.stringify(input)}`, entityUuid);

  const dataSource = await getAppDataSource(env);
  const issuingFullDetailsFunction = getIssuingFullDetailsFunction(env.stage, input);

  const QUERY = `WITH ${TTM_RECORD_ALIAS} AS (
    SELECT
      ${getPrevTtmTransactionsColsAndGroupings(groupBy, accountUuid).columns}
    FROM "${FUNCTION_NAME_TO_BE_REPLACE}" i JOIN (
        SELECT ttm, RANK() OVER (
            ORDER BY ttm desc
        ) - 1 rank FROM ${env.stage}.ttmlistgeneration('${date}')
    ) AS t ON floor(${env.stage}.ttmRankCalculation(timestamp, '${date}', '${timeZone}' ) )  = t.rank
    WHERE ${getPreviousTtmTransactionsWhereClause(input, env.stage)}
    GROUP BY  ${getPrevTtmTransactionsColsAndGroupings(groupBy, accountUuid).groupBy}
  )
  SELECT
    ${THIS_TTM_ALIAS}."entityUuid" AS "entityUuid",
    to_char(thisTTM.ttm, 'YYYY-MM-DD') AS "dateRangeStart",
    to_char((thisTTM.ttm + interval '1 year - 1 day')::date, 'YYYY-MM-DD') AS "dateRangeEnd",
    ${THIS_TTM_ALIAS}."noOfTransaction" AS "noOfTransaction",
    ${THIS_TTM_ALIAS}.total AS total,
    ${THIS_TTM_ALIAS}.average AS average,
    ${THIS_TTM_ALIAS}."${groupBy}" AS "${groupBy}",
    (${THIS_TTM_ALIAS}.total - ${LAST_TTM_ALIAS}.total)/NULLIF(${LAST_TTM_ALIAS}.total, 0) * 100 AS change,
    ${THIS_TTM_ALIAS}.currency AS currency,
    ${getSelectGroupByQuery(groupBy)}
  FROM ${TTM_RECORD_ALIAS} AS ${THIS_TTM_ALIAS} left join ${TTM_RECORD_ALIAS} AS ${LAST_TTM_ALIAS}
  ON ${THIS_TTM_ALIAS}.ttm = ${LAST_TTM_ALIAS}.ttm + interval '1 years' AND
    ${THIS_TTM_ALIAS}."${groupBy}" = ${LAST_TTM_ALIAS}."${groupBy}"
  WHERE
    ${THIS_TTM_ALIAS}.ttm = (select ${env.stage}.ttmlistgeneration('${date}' ) limit 1)
  ORDER BY ${THIS_TTM_ALIAS}."${groupBy}" IS NULL, ${getNetAmountOrderBy(groupBy, {
    alias: THIS_TTM_ALIAS,
    sortBy,
    sortOrder,
  })}${getLimitClause(limit)}`.replace(
    `"${FUNCTION_NAME_TO_BE_REPLACE}"`,
    `${issuingFullDetailsFunction.functionName}`,
  );

  const result = await dataSource.query(QUERY, issuingFullDetailsFunction.params);

  dataSource.destroy();

  return result;
};
