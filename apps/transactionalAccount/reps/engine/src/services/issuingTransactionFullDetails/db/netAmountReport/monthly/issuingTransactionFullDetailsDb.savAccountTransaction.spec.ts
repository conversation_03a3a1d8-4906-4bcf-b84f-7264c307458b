import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories } from '@npco/component-dto-core';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type {
  SavingsAccountTransactionCreatedEventDto,
  SavingsAccountTransactionUpdatedEventDto,
} from '@npco/component-dto-issuing-transaction';

import { v4 as uuid } from 'uuid';

import { TransactionDirection } from '../../../../../common/types/index.js';
import type { FunctionTypes } from '../../../../../common/types/index.js';
import { EnvironmentService } from '../../../../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../../../../const/timezone.js';
import type { SavingsAccountTransaction } from '../../../../../database/entities/index.js';
import { convertAmountWithSign } from '../../../../../utils/covertAmountWithSign.js';
import { getAllSavingsAccountTransactionRecords } from '../../../../debitCardAccountTransaction/db/debitCardAccountTransactionDb.js';
import { getAllSavingsAccountTransactionRecordsById } from '../../../../savingsAccountTransaction/db/savingsAccountTransactionDb.js';
import type * as ServiceModule from '../../../../savingsAccountTransaction/db/savingsAccountTransactionDb.js';
import { getSavingsAccountTransactionTransactionDto } from '../../../../savingsAccountTransaction/mocks/savingsAccountTransactionMock.js';
import { expenseTransactions, incomeTransactions } from '../../const.js';

import { getMonthlyNetAmountTransactionRecords } from './issuingTransactionFullDetailsDb.js';

type ServiceModuleFunctionTypes = FunctionTypes<typeof ServiceModule>;

jest.mock('../../../../../config/environmentService');

const getTransactionRecord = async (primaryKeys: string[], env: EnvironmentService, domicile: Domicile) =>
  getAllSavingsAccountTransactionRecords(env, {
    where: primaryKeys.map((id) => ({ id, domicile })),
  });

const expectedIssuingTransactionRecord = async (
  env: EnvironmentService,
  id: string,
  domicile: Domicile,
): Promise<SavingsAccountTransaction> => {
  const issuingTxnRecord = (await getAllSavingsAccountTransactionRecordsById(id, env, domicile))[0];

  return {
    id: issuingTxnRecord.id,
    amount: issuingTxnRecord.amount,
    attachments: issuingTxnRecord.attachments ?? 0,
    category: issuingTxnRecord.category,
    accountUuid: issuingTxnRecord.accountUuid,
    entityUuid: issuingTxnRecord.entityUuid,
    note: !!issuingTxnRecord.note,
    status: issuingTxnRecord.status,
    subcategory: issuingTxnRecord.subcategory,
    tags: issuingTxnRecord.tags,
    timestamp: issuingTxnRecord.timestamp,
    type: issuingTxnRecord.type,
    updatedTime: issuingTxnRecord.updatedTime,
    currency: issuingTxnRecord.currency,
    domicile,
  };
};

const currentDate: any = new Date().toISOString().split('T')[0];

describe('issuingTransactionFullDetailsDb', () => {
  const domicile = Domicile.AU;
  let saveSavingsAccountTransaction: ServiceModuleFunctionTypes['saveSavingsAccountTransaction'];

  let env: EnvironmentService;

  beforeAll(async () => {
    env = new EnvironmentService();
    const dbService = await import('../../../../savingsAccountTransaction/db/savingsAccountTransactionDb.js');
    saveSavingsAccountTransaction = dbService.saveSavingsAccountTransaction;
  });

  describe('Issuing transaction full details view by entity', () => {
    const entityUuid = uuid();
    const dtos = Array(5)
      .fill(0)
      .map((_, index) =>
        getSavingsAccountTransactionTransactionDto({
          entityUuid,
          updatedTime: index,
        }),
      );

    it.each(dtos)('should be able to return transaction full details records', async (dto) => {
      await saveSavingsAccountTransaction(dto, env, domicile);

      const result = await getTransactionRecord([dto.id], env, domicile);

      const expectedRecord = await expectedIssuingTransactionRecord(env, dto.id, domicile);
      expect(result[0]).toMatchObject(expectedRecord);
    });
  });

  describe('Get records by category without subcategories', () => {
    const entityUuid = uuid();

    const dtos = [
      getSavingsAccountTransactionTransactionDto({
        entityUuid,
        subcategory: undefined,
      }),
    ];

    const primaryKeys = dtos.map((dto) => dto.id);

    beforeEach(async () => {
      await Promise.all(dtos.map((dto) => saveSavingsAccountTransaction(dto, env, domicile)));

      const result = await getTransactionRecord(primaryKeys, env, domicile);

      expect(result.length).toBe(dtos.length);
    });

    it('should return zero value on subcategory count', async () => {
      const result = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        groupBy: 'category',
        domicile,
      });

      expect(Number(result[0].subcategoryCount)).toEqual(0);
    });
  });

  describe('Get records by category', () => {
    const entityUuid = uuid();

    const allTransactions = [...expenseTransactions, ...incomeTransactions];
    const dtos = allTransactions.map((type, index) =>
      getSavingsAccountTransactionTransactionDto({
        entityUuid,
        type,
        updatedTime: index,
      }),
    );

    const primaryKeys = dtos.map((dto) => dto.id);

    beforeEach(async () => {
      await Promise.all(dtos.map((dto) => saveSavingsAccountTransaction(dto, env, domicile)));

      const result = await getTransactionRecord(primaryKeys, env, domicile);

      expect(result.length).toBe(dtos.length);
    });

    describe('for all accounts', () => {
      describe('getMonthlyNetAmountTransactionRecords', () => {
        it('should return all expense records', async () => {
          const result = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            transactionDirection: TransactionDirection.EXPENSE,
            groupBy: 'category',
            domicile,
          });

          expect(result.length).toBeGreaterThan(0);
          expect(result[0].entityUuid).toBe(entityUuid);

          expect(result[0].category).toBe(dtos[0].category);
          expect(result[0].subcategoryCount).toBeDefined();
          expect(result[0].total).toBeDefined();
          expect(result[0].average).toBeDefined();
          expect(result[0].change).toBeDefined();

          expect(result[0].noOfTransaction).toBe(expenseTransactions.length.toString());
        });

        it('should return all records if TransactionDirection field not set', async () => {
          const result = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            groupBy: 'category',
            domicile,
          });

          expect(result[0].noOfTransaction).toBe(dtos.length.toString());
        });
      });
    });

    describe('for single account', () => {
      describe('getMonthlyNetAmountTransactionRecords', () => {
        it.each(dtos)('should return record for a debit account record id', async (dto) => {
          const results = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            accountUuid: dto.accountUuid,
            groupBy: 'category',
            domicile,
          });

          expect(results.length).toBe(1);

          const [result] = results;
          expect(result.entityUuid).toBe(entityUuid);
          expect(new Date(result.dateRangeStart).getMonth()).toBe(new Date(currentDate).getMonth());
          expect(new Date(result.dateRangeEnd).getMonth()).toBe(new Date(currentDate).getMonth());

          expect(result.category).toBe(dto.category);
          expect(result.subcategoryCount).toBe('1');
          expect(result.total).toBe(convertAmountWithSign(Number(dto.amount.value), dto.type).toString());
          expect(result.average).toBeDefined();
          expect(result.change).toBeDefined();

          expect(result.noOfTransaction).toBe('1');
        });

        it('should not return record if debit account does not exist', async () => {
          const results = await getMonthlyNetAmountTransactionRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            accountUuid: uuid(),
            groupBy: 'category',
            domicile,
          });

          expect(results.length).toBe(0);
        });
      });
    });
  });

  describe('Get all subcategories net amount records', () => {
    const entityUuid = uuid();

    const expenseCategoryAndSubCategory: Partial<
      SavingsAccountTransactionCreatedEventDto | SavingsAccountTransactionUpdatedEventDto
    >[] = [
      {
        category: EntityCategories.ADVERTISING,
        subcategory: 'subCategory',
      },
      {
        category: EntityCategories.ADVERTISING,
        subcategory: 'subCategory',
      },
      {
        category: EntityCategories.ADVERTISING,
        subcategory: 'subCategory1',
      },
      {
        category: EntityCategories.BANK_FEES,
        subcategory: 'subCategory',
      },
      {
        category: EntityCategories.CLEANING,
        subcategory: 'subCategory',
      },
    ];

    const expenseTransactionDtos = expenseCategoryAndSubCategory.map((category, index) =>
      getSavingsAccountTransactionTransactionDto({
        entityUuid,
        category: category.category,
        subcategory: category.subcategory,
        updatedTime: index,
      }),
    );

    const incomeTransactionDto = getSavingsAccountTransactionTransactionDto({
      entityUuid,
      type: DebitCardTransactionTypeV2.DE_IN,
      category: EntityCategories.CONSULTING_ACCOUNTING,
      subcategory: 'subCategory',
      updatedTime: expenseTransactionDtos.length + 1,
    });

    const allTransactions = expenseTransactionDtos.concat(incomeTransactionDto);

    const primaryKeys = allTransactions.map((dto) => dto.id);

    beforeEach(async () => {
      await Promise.all(allTransactions.map((dto) => saveSavingsAccountTransaction(dto, env, domicile)));

      const result = await getTransactionRecord(primaryKeys, env, domicile);

      expect(result.length).toBe(allTransactions.length);
    });

    it.each(expenseTransactionDtos)('should be able to return expense records by category name', async (dto) => {
      const results = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        accountUuid: dto.accountUuid,
        groupBy: 'subcategory',
        category: dto.category!,
        transactionDirection: TransactionDirection.EXPENSE,
        domicile,
      });

      results.forEach((result: any) => {
        expect(result.entityUuid).toBe(dto.entityUuid);
        expect(result.category).toBe(dto.category);
        expect(result.subcategory).toEqual(expect.any(String));
        expect(result.yearmonth).toBeInstanceOf(Date);
        expect(result.total).toBeDefined();
        expect(result.average).toBeDefined();
      });
    });

    it('should not be able to return income records by category name', async () => {
      const results = await getMonthlyNetAmountTransactionRecords(env, {
        entityUuid,
        timeZone: DEFAULT_TIMEZONE,
        date: currentDate,
        groupBy: 'subcategory',
        category: incomeTransactionDto.category!,
        transactionDirection: TransactionDirection.EXPENSE,
        domicile,
      });

      expect(results.length).toBe(0);
    });
  });
});
