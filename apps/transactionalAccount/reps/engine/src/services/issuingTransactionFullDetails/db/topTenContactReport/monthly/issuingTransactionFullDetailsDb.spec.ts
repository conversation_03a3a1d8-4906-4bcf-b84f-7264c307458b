import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';

import { v4 as uuid } from 'uuid';

import type { FunctionTypes } from '../../../../../common/types/index.js';
import { EnvironmentService } from '../../../../../config/environmentService.js';
import { DEFAULT_TIMEZONE } from '../../../../../const/timezone.js';
import { saveContact } from '../../../../contact/db/contactDb.js';
import { getContactCreatedEventDto } from '../../../../contact/mocks/contactDto.js';
import type * as DebitCardAccountTransactionDbModule from '../../../../debitCardAccountTransaction/db/debitCardAccountTransactionDb.js';
import { getDebitCardTransactionDto } from '../../../../debitCardAccountTransaction/mocks/getDebitCardTransactionMock.js';
import { savePaymentInstrumentToDb } from '../../../../paymentInstrument/db/paymentInstrumentDb.js';
import { getPaymentInstrumentCreatedDto } from '../../../../paymentInstrument/mocks/getPaymentInstrumentDto.js';
import { saveSavingsAccountTransaction } from '../../../../savingsAccountTransaction/db/savingsAccountTransactionDb.js';
import { getSavingsAccountTransactionTransactionDto } from '../../../../savingsAccountTransaction/mocks/savingsAccountTransactionMock.js';
import { expenseTransactions, incomeTransactions, incomeTransactionsSavingsAccount } from '../../const.js';

import { getMonthlyTopTenContactRecords } from './issuingTransactionFullDetailsDb.js';

type DebitCardAccountTransactionDbFunctionTypes = FunctionTypes<typeof DebitCardAccountTransactionDbModule>;

jest.mock('../../../../../config/environmentService');

const currentDate: any = new Date().toISOString().split('T')[0];

describe('issuingTransactionFullDetailsDb', () => {
  const domicile = Domicile.AU;
  let saveDebitCardTransaction: DebitCardAccountTransactionDbFunctionTypes['saveDebitCardTransaction'];
  let env: EnvironmentService;

  beforeAll(async () => {
    env = new EnvironmentService();
    const dbService = await import('../../../../debitCardAccountTransaction/db/debitCardAccountTransactionDb.js');
    saveDebitCardTransaction = dbService.saveDebitCardTransaction;
  });

  describe('getMonthlyTopTenContactRecords', () => {
    const entityUuid = uuid();
    const merchantEntityUuid = uuid();
    const contactUuid = uuid();
    const dtoContact = getContactCreatedEventDto({
      entityUuid,
      contactUuid,
    });
    const paymentInstrumentDto = getPaymentInstrumentCreatedDto({
      entityUuid,
      contactUuid: dtoContact.contactUuid,
    });

    const debitCardAccountTransactions = [...expenseTransactions, ...incomeTransactions];
    const savingsAccountTransactionDtos = incomeTransactionsSavingsAccount.map((type, index) =>
      getSavingsAccountTransactionTransactionDto({
        entityUuid,
        type,
        payeeDetails: {
          recipientUuid: paymentInstrumentDto.paymentInstrumentUuid,
        },
        updatedTime: index,
      }),
    );
    const debitCardAccountTransactionDtos = debitCardAccountTransactions.map((type, index) =>
      getDebitCardTransactionDto({
        entityUuid,
        type,
        contactUuid: paymentInstrumentDto.contactUuid,
        payeeDetails: {
          recipientUuid: paymentInstrumentDto.paymentInstrumentUuid,
        },
        merchant: {
          id: merchantEntityUuid,
          name: uuid(),
          updatedTime: Date.now(),
        },
        updatedTime: index,
      }),
    );

    beforeEach(async () => {
      await Promise.all([
        saveContact(dtoContact, env, domicile),
        savePaymentInstrumentToDb(paymentInstrumentDto, env, domicile),
      ]);

      await Promise.all([
        ...debitCardAccountTransactionDtos.map((dto) => saveDebitCardTransaction(dto, env, domicile)),
        ...savingsAccountTransactionDtos.map((dto) => saveSavingsAccountTransaction(dto, env, domicile)),
      ]);
    });

    describe('for all accounts', () => {
      it('should return all records', async () => {
        const result = await getMonthlyTopTenContactRecords(env, {
          entityUuid,
          timeZone: DEFAULT_TIMEZONE,
          date: currentDate,
          domicile,
        });

        expect(result).toEqual(expect.arrayContaining([expect.objectContaining({ contactUuid })]));

        result.forEach((record) => {
          expect(record.dateRangeStart).toBeDefined();
          expect(record.dateRangeEnd).toBeDefined();
          expect(record.entityUuid).toEqual(entityUuid);
          expect(record.contactUuid).toBeDefined();
          expect(record.change).toBeDefined();
          expect(record.previousTotalSum).toBeDefined();
          expect(record.total).toBeDefined();
          expect(record.totalSum).toBeDefined();
          expect(record.currency).toBeDefined();
        });
      });
    });

    describe('for single account', () => {
      it.each([debitCardAccountTransactionDtos[0].debitCardAccountUuid, savingsAccountTransactionDtos[0].accountUuid])(
        'should return all records for account %s',
        async (accountUuid) => {
          const result = await getMonthlyTopTenContactRecords(env, {
            entityUuid,
            timeZone: DEFAULT_TIMEZONE,
            date: currentDate,
            accountUuid,
            domicile,
          });

          result.forEach((record) => {
            expect(record.dateRangeStart).toBeDefined();
            expect(record.dateRangeEnd).toBeDefined();
            expect(record.entityUuid).toEqual(entityUuid);
            expect(record.contactUuid).toBeDefined();
            expect(record.change).toBeDefined();
            expect(record.previousTotalSum).toBeDefined();
            expect(record.total).toBeDefined();
            expect(record.totalSum).toBeDefined();
            expect(record.currency).toBeDefined();
          });
        },
      );
    });
  });
});
