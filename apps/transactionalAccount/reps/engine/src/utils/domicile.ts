import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { ISO4217 } from '@npco/component-dto-core';

const domicileCurrencyMap: Record<Domicile, ISO4217> = {
  [Domicile.GB]: ISO4217.GBP,
  [Domicile.AU]: ISO4217.AUD,
};

export const getCurrencyByDomicile = (domicile: Domicile): ISO4217 => {
  const currency = domicileCurrencyMap[domicile];
  if (!currency) {
    throw new Error(`Unsupported domicile: ${domicile}`);
  }
  return currency;
};
