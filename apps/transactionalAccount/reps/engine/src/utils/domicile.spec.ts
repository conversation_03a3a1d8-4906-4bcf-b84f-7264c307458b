import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { ISO4217 } from '@npco/component-dto-core';

import { getCurrencyByDomicile } from './domicile';

describe('domicile utils', () => {
  it.each([
    {
      domicile: Domicile.GB,
      expectedCurrency: ISO4217.GBP,
    },
    {
      domicile: Domicile.AU,
      expectedCurrency: ISO4217.AUD,
    },
  ])('should return the correct currency for $domicile domicile', ({ domicile, expectedCurrency }) => {
    const currency = getCurrencyByDomicile(domicile);
    expect(currency).toBe(expectedCurrency);
  });

  it('should throw an error for unsupported domicile', () => {
    expect(() => getCurrencyByDomicile('UNSUPPORTED' as Domicile)).toThrowError('Unsupported domicile: UNSUPPORTED');
  });
});
