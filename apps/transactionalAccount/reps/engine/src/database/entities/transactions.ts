import type { Money } from '@npco/component-dto-core';
import { EntityCategories } from '@npco/component-dto-core';
import { DebitCardTransactionStatusV2, DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import { Column, PrimaryColumn } from 'typeorm';

import { Common } from './common';

export abstract class Transaction extends Common {
  @PrimaryColumn({ type: 'uuid' })
  id!: string;

  @Column({ type: 'uuid' })
  entityUuid!: string;

  @Column({ type: 'uuid' })
  accountUuid!: string;

  @Column({ type: 'text' })
  type!: DebitCardTransactionTypeV2;

  @Column({ type: 'text' })
  status!: DebitCardTransactionStatusV2;

  @Column({ type: 'timestamptz' })
  timestamp!: Date;

  @Column({ type: 'bigint' })
  amount!: number;

  @Column({ type: 'text', nullable: true })
  category?: EntityCategories;

  @Column({ type: 'text', nullable: true })
  subcategory?: string;

  @Column({ type: 'text', nullable: true })
  accountCategory?: string;

  @Column({ type: 'int', nullable: true })
  attachments?: number;

  @Column({ type: 'boolean', nullable: true })
  note?: boolean;

  @Column('text', { array: true, nullable: true })
  tags?: string[];

  @Column({ type: 'bigint' })
  updatedTime!: number;

  @Column({ type: 'text', nullable: true })
  currency!: Money['currency'];
}
