DROP FUNCTION IF EXISTS issuingtransactionfulldetails(entityuuid uuid, dom varchar(3)) CASCADE;
DROP FUNCTION IF EXISTS IssuingTransactionFullDetails(entityUuid uuid, cashflow text, dom varchar(3)) CASCADE;
DROP FUNCTION IF EXISTS IssuingTransactionFullDetails(entityUuid uuid, accountUuid uuid, dom varchar(3)) CASCADE;
DROP FUNCTION IF EXISTS IssuingTransactionFullDetails(entityUuid uuid, accountUuid uuid, cashflow text, dom varchar(3)) CASCADE;


CREATE OR REPLACE FUNCTION issuingtransactionfulldetails(entityuuid uuid, dom varchar(3))
    returns TABLE(
        id uuid, 
        "entityUuid" uuid, 
        "accountUuid" uuid, 
        "debitCardId" uuid, 
        type text, 
        cashflowtype text, 
        status text, 
        currency text, 
        amount bigint, 
        "merchantId" uuid, 
        "paymentInstrumentUuid" uuid, 
        "senderUuid" uuid, 
        "contactUuid" uuid, 
        category text, 
        subcategory text, 
        attachments integer, 
        note boolean, 
        tags text[], 
        "timestamp" timestamp with time zone, 
        "updatedTime" bigint
        )
    language plpgsql
as
$$
BEGIN
    -- Return all the entity related transactions
    RETURN QUERY
        SELECT t.id,
               t."entityUuid",
               t."accountUuid",
               t."debitCardId",
               t.type,
               ${flyway:defaultSchema}.getcashflowtype(t.type)                 AS cashflowtype,
               t.status,
               t.currency::text AS currency,
               t.amount,
               t."merchantId",
               t."paymentInstrumentUuid",
               t."senderUuid",
               c."contactUuid",
               COALESCE(t.category, c.category, m.category) AS category,
               CASE
                   WHEN t.category IS NOT NULL THEN t.subcategory
                   ELSE
                       CASE
                           WHEN c.category IS NOT NULL THEN c.subcategory
                           ELSE m.subcategory
                           END
                   END                                      AS subcategory,
               t.attachments,
               t.note,
               t.tags,
               t."timestamp",
               t."updatedTime"
        FROM ${flyway:defaultSchema}."DebitCardAccountTransaction" t
                 LEFT JOIN ${flyway:defaultSchema}.ContactLookup(entityUuid, dom) c
                           ON (t."paymentInstrumentUuid" = c."typeId" OR t."senderUuid" = c."typeId" OR t."merchantId" = c."typeId")
                               AND c."entityUuid" = t."entityUuid"
                 LEFT JOIN ${flyway:defaultSchema}."Merchant" m ON t."merchantId" = m.id
             WHERE t."entityUuid" = entityUuid AND t.domicile = dom
        UNION ALL
        SELECT "SavingsAccountTransaction".id,
               "SavingsAccountTransaction"."entityUuid",
               "SavingsAccountTransaction"."accountUuid",
               NULL::uuid                                             AS "debitCardId",
               "SavingsAccountTransaction".type,
               ${flyway:defaultSchema}.getcashflowtype("SavingsAccountTransaction".type) AS cashflowtype,
               "SavingsAccountTransaction".status,
               "SavingsAccountTransaction".currency::text AS currency,
               "SavingsAccountTransaction".amount,
               NULL::uuid                                             AS "merchantId",
               NULL::uuid                                             AS "paymentInstrumentUuid",
               NULL::uuid                                             AS "senderUuid",
               NULL::uuid                                             AS "contactUuid",
               "SavingsAccountTransaction".category,
               "SavingsAccountTransaction".subcategory,
               "SavingsAccountTransaction".attachments,
               "SavingsAccountTransaction".note,
               "SavingsAccountTransaction".tags,
               "SavingsAccountTransaction"."timestamp",
               "SavingsAccountTransaction"."updatedTime"
        FROM ${flyway:defaultSchema}."SavingsAccountTransaction" where "SavingsAccountTransaction"."entityUuid" = entityUuid AND "SavingsAccountTransaction".domicile = dom;
END;
$$;

CREATE OR REPLACE FUNCTION IssuingTransactionFullDetails(entityUuid uuid, cashflow text, dom varchar(3) )
    RETURNS TABLE (
                      id   uuid,
                      "entityUuid"     uuid,
                      "accountUuid"    uuid,
                      "debitCardId"    uuid,
                      type             text,
                      cashflowtype     text,
                      status           text,
                      currency          text,
                      amount           bigint,
                      "merchantId"     uuid,
                      "paymentInstrumentUuid"  uuid,
                      "senderUuid"     uuid,
                      "contactUuid"    uuid,
                      category         text,
                      subcategory      text,
                      attachments      integer,
                      note             boolean,
                      tags             text[],
                      "timestamp"      timestamp with time zone,
                      "updatedTime"    bigint
                  )
    LANGUAGE plpgsql AS
$func$
BEGIN
    RETURN QUERY
        -- Return all the entity related transactions with cashflow type
        SELECT t.id,
               t."entityUuid",
               t."accountUuid",
               t."debitCardId",
               t.type,
               ${flyway:defaultSchema}.getcashflowtype(t.type)                 AS cashflowtype,
               t.status,
               t.currency::text AS currency,
               t.amount,
               t."merchantId",
               t."paymentInstrumentUuid",
               t."senderUuid",
               c."contactUuid",
               COALESCE(t.category, c.category, m.category) AS category,
               CASE
                   WHEN t.category IS NOT NULL THEN t.subcategory
                   ELSE
                       CASE
                           WHEN c.category IS NOT NULL THEN c.subcategory
                           ELSE m.subcategory
                           END
                   END                                      AS subcategory,
               t.attachments,
               t.note,
               t.tags,
               t."timestamp",
               t."updatedTime"
        FROM ${flyway:defaultSchema}."DebitCardAccountTransaction" t
                 LEFT JOIN ${flyway:defaultSchema}.ContactLookup(entityUuid, dom) c
                           ON (t."paymentInstrumentUuid" = c."typeId" OR t."senderUuid" = c."typeId" OR t."merchantId" = c."typeId")
                               AND c."entityUuid" = t."entityUuid"
                 LEFT JOIN ${flyway:defaultSchema}."Merchant" m ON t."merchantId" = m.id
             WHERE t."entityUuid" = entityUuid
                AND ${flyway:defaultSchema}.getcashflowtype(t.type) = cashflow
                AND t.domicile = dom
        UNION ALL
        SELECT "SavingsAccountTransaction".id,
               "SavingsAccountTransaction"."entityUuid",
               "SavingsAccountTransaction"."accountUuid",
               NULL::uuid                                             AS "debitCardId",
               "SavingsAccountTransaction".type,
               ${flyway:defaultSchema}.getcashflowtype("SavingsAccountTransaction".type) AS cashflowtype,
               "SavingsAccountTransaction".status,
               "SavingsAccountTransaction".currency::text AS currency,
               "SavingsAccountTransaction".amount,
               NULL::uuid                                             AS "merchantId",
               NULL::uuid                                             AS "paymentInstrumentUuid",
               NULL::uuid                                             AS "senderUuid",
               NULL::uuid                                             AS "contactUuid",
               "SavingsAccountTransaction".category,
               "SavingsAccountTransaction".subcategory,
               "SavingsAccountTransaction".attachments,
               "SavingsAccountTransaction".note,
               "SavingsAccountTransaction".tags,
               "SavingsAccountTransaction"."timestamp",
               "SavingsAccountTransaction"."updatedTime"
        FROM ${flyway:defaultSchema}."SavingsAccountTransaction" 
        where 
            "SavingsAccountTransaction"."entityUuid" = entityUuid 
            AND "SavingsAccountTransaction".domicile = dom
            AND ${flyway:defaultSchema}.getcashflowtype("SavingsAccountTransaction".type) = cashflow;
END;
$func$;

CREATE OR REPLACE FUNCTION IssuingTransactionFullDetails(entityUuid uuid, accountUuid uuid, dom varchar(3))
    RETURNS TABLE (
                      id   uuid,
                      "entityUuid"     uuid,
                      "accountUuid"    uuid,
                      "debitCardId"    uuid,
                      type             text,
                      cashflowtype     text,
                      status           text,
                      currency          text,
                      amount           bigint,
                      "merchantId"     uuid,
                      "paymentInstrumentUuid"  uuid,
                      "senderUuid"     uuid,
                      "contactUuid"    uuid,
                      category         text,
                      subcategory      text,
                      attachments      integer,
                      note             boolean,
                      tags             text[],
                      "timestamp"      timestamp with time zone,
                      "updatedTime"    bigint
                  )
    LANGUAGE plpgsql AS
$func$
BEGIN
    RETURN QUERY
        -- Return single account related transactions with cashflow type
        SELECT t.id,
               t."entityUuid",
               t."accountUuid",
               t."debitCardId",
               t.type,
               ${flyway:defaultSchema}.getcashflowtype(t.type)                 AS cashflowtype,
               t.status,
               t.currency::text AS currency,
               t.amount,
               t."merchantId",
               t."paymentInstrumentUuid",
               t."senderUuid",
               c."contactUuid",
               COALESCE(t.category, c.category, m.category) AS category,
               CASE
                   WHEN t.category IS NOT NULL THEN t.subcategory
                   ELSE
                       CASE
                           WHEN c.category IS NOT NULL THEN c.subcategory
                           ELSE m.subcategory
                           END
                   END                                      AS subcategory,
               t.attachments,
               t.note,
               t.tags,
               t."timestamp",
               t."updatedTime"
        FROM ${flyway:defaultSchema}."DebitCardAccountTransaction" t
                 LEFT JOIN ${flyway:defaultSchema}.ContactLookup(entityUuid, dom) c
                           ON (t."paymentInstrumentUuid" = c."typeId" OR t."senderUuid" = c."typeId" OR t."merchantId" = c."typeId")
                               AND c."entityUuid" = t."entityUuid"
                               AND t."entityUuid" = entityUuid
                               AND t."accountUuid" = accountUuid
                 LEFT JOIN ${flyway:defaultSchema}."Merchant" m ON t."merchantId" = m.id
        WHERE t."entityUuid" = entityUuid
          AND t."accountUuid" = accountUuid
          AND t.domicile = dom
        UNION ALL
        SELECT "SavingsAccountTransaction".id,
               "SavingsAccountTransaction"."entityUuid",
               "SavingsAccountTransaction"."accountUuid",
               NULL::uuid                                             AS "debitCardId",
               "SavingsAccountTransaction".type,
               ${flyway:defaultSchema}.getcashflowtype("SavingsAccountTransaction".type) AS cashflowtype,
               "SavingsAccountTransaction".status,
               "SavingsAccountTransaction".currency::text AS currency,
               "SavingsAccountTransaction".amount,
               NULL::uuid                                             AS "merchantId",
               NULL::uuid                                             AS "paymentInstrumentUuid",
               NULL::uuid                                             AS "senderUuid",
               NULL::uuid                                             AS "contactUuid",
               "SavingsAccountTransaction".category,
               "SavingsAccountTransaction".subcategory,
               "SavingsAccountTransaction".attachments,
               "SavingsAccountTransaction".note,
               "SavingsAccountTransaction".tags,
               "SavingsAccountTransaction"."timestamp",
               "SavingsAccountTransaction"."updatedTime"
        FROM ${flyway:defaultSchema}."SavingsAccountTransaction" 
        where "SavingsAccountTransaction"."entityUuid" = entityUuid 
            AND "SavingsAccountTransaction"."accountUuid" = accountUuid
            AND "SavingsAccountTransaction".domicile = dom;
END;
$func$;

CREATE OR REPLACE FUNCTION IssuingTransactionFullDetails(entityUuid uuid, accountUuid uuid, cashflow text, dom varchar(3))
    RETURNS TABLE (
                      id   uuid,
                      "entityUuid"     uuid,
                      "accountUuid"    uuid,
                      "debitCardId"    uuid,
                      type             text,
                      cashflowtype     text,
                      status           text,
                      currency          text,
                      amount           bigint,
                      "merchantId"     uuid,
                      "paymentInstrumentUuid"  uuid,
                      "senderUuid"     uuid,
                      "contactUuid"    uuid,
                      category         text,
                      subcategory      text,
                      attachments      integer,
                      note             boolean,
                      tags             text[],
                      "timestamp"      timestamp with time zone,
                      "updatedTime"    bigint
                  )
    LANGUAGE plpgsql AS
$func$
BEGIN
    RETURN QUERY
        -- Return single account related transactions with cashflow type
        SELECT t.id,
               t."entityUuid",
               t."accountUuid",
               t."debitCardId",
               t.type,
               ${flyway:defaultSchema}.getcashflowtype(t.type)                 AS cashflowtype,
               t.status,
               t.currency::text AS currency,
               t.amount,
               t."merchantId",
               t."paymentInstrumentUuid",
               t."senderUuid",
               c."contactUuid",
               COALESCE(t.category, c.category, m.category) AS category,
               CASE
                   WHEN t.category IS NOT NULL THEN t.subcategory
                   ELSE
                       CASE
                           WHEN c.category IS NOT NULL THEN c.subcategory
                           ELSE m.subcategory
                           END
                   END                                      AS subcategory,
               t.attachments,
               t.note,
               t.tags,
               t."timestamp",
               t."updatedTime"
        FROM ${flyway:defaultSchema}."DebitCardAccountTransaction" t
                 LEFT JOIN ${flyway:defaultSchema}.ContactLookup(entityUuid, dom) c
                           ON (t."paymentInstrumentUuid" = c."typeId" OR t."senderUuid" = c."typeId" OR t."merchantId" = c."typeId")
                               AND c."entityUuid" = t."entityUuid"
                               AND t."entityUuid" = entityUuid
                               AND t."accountUuid" = accountUuid
                 LEFT JOIN ${flyway:defaultSchema}."Merchant" m ON t."merchantId" = m.id
            WHERE t."entityUuid" = entityUuid
              AND t."accountUuid" = accountUuid
              AND ${flyway:defaultSchema}.getcashflowtype(t.type) = cashflow
              AND t.domicile = dom
        UNION ALL
        SELECT "SavingsAccountTransaction".id,
               "SavingsAccountTransaction"."entityUuid",
               "SavingsAccountTransaction"."accountUuid",
               NULL::uuid                                             AS "debitCardId",
               "SavingsAccountTransaction".type,
               ${flyway:defaultSchema}.getcashflowtype("SavingsAccountTransaction".type) AS cashflowtype,
               "SavingsAccountTransaction".status,
               "SavingsAccountTransaction".currency::text AS currency,
               "SavingsAccountTransaction".amount,
               NULL::uuid                                             AS "merchantId",
               NULL::uuid                                             AS "paymentInstrumentUuid",
               NULL::uuid                                             AS "senderUuid",
               NULL::uuid                                             AS "contactUuid",
               "SavingsAccountTransaction".category,
               "SavingsAccountTransaction".subcategory,
               "SavingsAccountTransaction".attachments,
               "SavingsAccountTransaction".note,
               "SavingsAccountTransaction".tags,
               "SavingsAccountTransaction"."timestamp",
               "SavingsAccountTransaction"."updatedTime"
        FROM ${flyway:defaultSchema}."SavingsAccountTransaction" 
        where "SavingsAccountTransaction"."entityUuid" = entityUuid 
        AND "SavingsAccountTransaction"."accountUuid" = accountUuid
        AND ${flyway:defaultSchema}.getcashflowtype("SavingsAccountTransaction".type) = cashflow
        AND "SavingsAccountTransaction".domicile = dom;
END;
$func$;

