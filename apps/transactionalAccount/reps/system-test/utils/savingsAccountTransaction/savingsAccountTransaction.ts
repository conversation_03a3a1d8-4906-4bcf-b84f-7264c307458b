import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories } from '@npco/component-dto-core';
import type { SavingsAccountTransactionUpdatedEventDto } from '@npco/component-dto-issuing-transaction';
import {
  DebitCardTransactionStatusV2,
  DebitCardTransactionTypeV2,
  SavingsAccountTransactionCreatedEventDto,
} from '@npco/component-dto-issuing-transaction';

import { v4 as uuidv4 } from 'uuid';

import { getCurrencyByRegion } from '../domicile';

export const getSavingsAccountTxnDto = (
  overrides?: Partial<SavingsAccountTransactionCreatedEventDto | SavingsAccountTransactionUpdatedEventDto>,
) => {
  const currency = getCurrencyByRegion();
  const dto = {
    id: uuidv4(),
    accountUuid: uuidv4(),
    entityUuid: uuidv4(),
    type: DebitCardTransactionTypeV2.DE_IN,
    status: DebitCardTransactionStatusV2.APPROVED,
    amount: {
      value: '100',
      currency,
    },
    balance: {
      value: '100',
      currency,
    },
    note: 'note',
    tags: ['tags1'],
    category: EntityCategories.ADVERTISING,
    attachments: ['attachments1'],
    reference: 'reference',
    referencePayee: 'referencePayee',
    payeeDetails: {
      recipientUuid: uuidv4(),
      debitCardAccountUuid: uuidv4(),
    },
    payerDetails: {
      recipientUuid: uuidv4(),
      senderUuid: uuidv4(),
    },
    subcategory: 'subCategory',
    accountingCategory: 'accountingCategory',
    updatedTime: new Date().getTime(),
    timestamp: new Date().getTime(),
    ...overrides,
  };

  dto.amount.currency = currency;
  dto.balance.currency = currency;

  return new SavingsAccountTransactionCreatedEventDto(dto);
};

export const expectedSavingsAccountTransactionDbRecord = (
  data: SavingsAccountTransactionCreatedEventDto | SavingsAccountTransactionUpdatedEventDto,
  domicile: Domicile,
) => {
  return {
    id: data.id,
    entityUuid: data.entityUuid,
    accountUuid: data.accountUuid,
    type: data.type,
    status: data.status,
    timestamp: new Date(Number(data.timestamp)),
    amount: data.amount.value,
    currency: data.amount.currency,
    category: data.category,
    subcategory: data.subcategory,
    accountCategory: data.accountingCategory,
    attachments: data.attachments?.length ?? 0,
    note: !!(data.note && data.note?.trim().length > 0),
    tags: data.tags,
    updatedTime: data.updatedTime.toString(),
    domicile,
  };
};
