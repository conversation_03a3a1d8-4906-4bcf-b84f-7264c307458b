import { Domicile, getSupportedDomiciles } from '@npco/component-bff-core/dist/utils/domicile';
import { ISO4217 } from '@npco/component-dto-core';

export const getDomicileByRegion = () => {
  const region = process.env.AWS_REGION?.trim() || 'ap-southeast-2';
  return getSupportedDomiciles(region)[0] ?? Domicile.AU;
};

export const getCurrencyByRegion = () => {
  const region = process.env.AWS_REGION;

  switch (region) {
    case 'ap-southeast-2':
      return ISO4217.AUD;
    case 'eu-west-2':
      return ISO4217.GBP;
    default:
      return ISO4217.AUD;
  }
};
