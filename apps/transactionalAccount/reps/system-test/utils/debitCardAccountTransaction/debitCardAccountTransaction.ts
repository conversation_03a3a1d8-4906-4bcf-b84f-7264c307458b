import type { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EntityCategories } from '@npco/component-dto-core';
import type {
  DebitCardAccountTransactionUpdatedEventDto,
  DebitCardTransactionV2,
} from '@npco/component-dto-issuing-transaction';
import {
  DebitCardAccountTransactionCreatedEventDto,
  DebitCardTransactionStatusV2,
  DebitCardTransactionTypeV2,
} from '@npco/component-dto-issuing-transaction';
import type { Merchant } from '@npco/component-dto-richdata';

import { v4 as uuidv4 } from 'uuid';

import { getCurrencyByRegion } from '../domicile';

export const getDcaTxnDto = (overrides?: Partial<DebitCardTransactionV2>) => {
  const currency = getCurrencyByRegion();
  const dto = {
    id: uuidv4(),
    debitCardAccountUuid: uuidv4(),
    entityUuid: uuidv4(),
    type: DebitCardTransactionTypeV2.DE_IN,
    status: DebitCardTransactionStatusV2.APPROVED,
    amount: {
      value: '100',
      currency,
    },
    note: 'note',
    tags: ['tags1'],
    category: EntityCategories.ADVERTISING,
    attachments: ['attachments1'],
    reference: 'reference',
    debitCardId: uuidv4(),
    referencePayee: 'referencePayee',
    payeeDetailsUuid: 'payeeDetailsUuid',
    payeeDetails: {
      recipientUuid: uuidv4(),
      debitCardAccountUuid: uuidv4(),
    },
    payerDetails: {
      recipientUuid: uuidv4(),
      senderUuid: uuidv4(),
    },
    merchant: {
      id: uuidv4(),
      name: 'system-test-merchant',
      updatedTime: new Date().getTime(),
    },
    subcategory: 'subCategory',
    accountingCategory: 'accountingCategory',
    updatedTime: new Date().getTime(),
    timestamp: new Date().getTime().toString(),
    ...overrides,
  };

  // prevent overwrites currency
  dto.amount.currency = currency;

  return new DebitCardAccountTransactionCreatedEventDto(dto);
};

export const expectedIssuingTransactionDbRecord = (
  data: DebitCardAccountTransactionCreatedEventDto | DebitCardAccountTransactionUpdatedEventDto,
  domicile: Domicile,
) => {
  return {
    id: data.id,
    entityUuid: data.entityUuid,
    accountUuid: data.debitCardAccountUuid,
    debitCardId: data.debitCardId,
    type: data.type,
    status: data.status,
    timestamp: new Date(Number(data.timestamp)),
    amount: data.amount.value,
    currency: data.amount.currency,
    merchantId: data.merchant?.id,
    paymentInstrumentUuid: data.payeeDetails?.recipientUuid,
    senderUuid: data.payerDetails?.senderUuid,
    category: data.category,
    subcategory: data.subcategory,
    accountCategory: data.accountingCategory,
    attachments: data.attachments?.length ?? 0,
    note: !!(data.note && data.note?.trim().length > 0),
    tags: data.tags,
    updatedTime: data.updatedTime.toString(),
    domicile,
  };
};

export const expectedMerchantDbRecord = (data: Merchant) => {
  const { id, locationOf, category, subcategory, updatedTime, ...payload } = data;

  return {
    id,
    locationOf,
    category,
    subcategory,
    payload,
    updatedTime: updatedTime.toString(),
  };
};
