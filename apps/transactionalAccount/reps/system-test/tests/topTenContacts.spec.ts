import { sleep } from '@npco/bff-systemtest-utils';
import type { ContactCreatedEventDto } from '@npco/component-dto-addressbook';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type { PaymentInstrumentCreatedDto } from '@npco/component-dto-payment-instrument';

import { Client } from 'pg';
import { v4 as uuid } from 'uuid';

import { getContactCreatedEventDto } from '../utils/contact/contactDto';
import { createContact } from '../utils/contact/contactSqs';
import { getDbClientOptions } from '../utils/database/database';
import { getDcaTxnDto } from '../utils/debitCardAccountTransaction/debitCardAccountTransaction';
import { createDebitCardAccountTransaction } from '../utils/debitCardAccountTransaction/debitCardAccountTransactionSqs';
import { getDomicileByRegion } from '../utils/domicile';
import { MerchantPortalEnvService } from '../utils/envService';
import { getPaymentInstrumentCreatedDto } from '../utils/paymentInstrument/paymentInstrument';
import { createPaymentInstrument } from '../utils/paymentInstrument/paymentInstrumentSqs';
import { ApiTestHelper } from '../utils/testHelper';

const shortCurrentDate: any = new Date().toISOString().split('T')[0];

describe('Top 10 Contact test suite', () => {
  const domicile = getDomicileByRegion();
  const mpEnvService = new MerchantPortalEnvService();
  const apiTestHelper = new ApiTestHelper(mpEnvService);

  let client: Client;

  const invoke = async (lambda: string, args: any) =>
    apiTestHelper.invokeLambda(`${mpEnvService.COMPONENT_NAME}-reps-${lambda}`, {
      args,
    });

  const getTopTenContactHandler = async (args: any) => invoke('getTopTenContactHandler', args);

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
    await sleep();
    const options = await getDbClientOptions();

    client = new Client(options);
    await client.connect();
    await client.query(`SET search_path TO ${options.search_path}`);
  });

  afterAll(async () => {
    await client.end();
  });

  describe.each(['MONTHLY', 'TTM'])('get top 10 contacts by report type %s', (reportType) => {
    const entityUuid = apiTestHelper.getEntityUuid();
    const dtoContact = getContactCreatedEventDto({
      entityUuid,
    });
    const paymentInstrumentDto = getPaymentInstrumentCreatedDto({
      entityUuid,
    });
    let paymentInstrumentDetails: PaymentInstrumentCreatedDto;
    let contactDetails: ContactCreatedEventDto;
    let dcaTxnDto: DebitCardTransactionV2;

    beforeAll(async () => {
      contactDetails = (await createContact(dtoContact, client, domicile)).rows[0];

      paymentInstrumentDetails = (
        await createPaymentInstrument(
          {
            ...paymentInstrumentDto,
            contactUuid: contactDetails.contactUuid,
          },
          client,
          domicile,
        )
      ).rows[0];

      dcaTxnDto = getDcaTxnDto({
        entityUuid,
        type: DebitCardTransactionTypeV2.DE_OUT,
        payerDetails: {
          recipientUuid: uuid(),
          senderUuid: uuid(),
        },
        payeeDetails: {
          recipientUuid: paymentInstrumentDetails.paymentInstrumentUuid,
        },
      });

      await Promise.all([createDebitCardAccountTransaction(dcaTxnDto, client, domicile)]);
    });

    it('should be able to get all records ', async () => {
      const input = {
        entityUuid,
        reportType,
        date: shortCurrentDate,
        transactionDirection: 'EXPENSE',
      };

      const response = await getTopTenContactHandler(input);

      expect(response.contactTopTenTotals).toEqual(
        expect.arrayContaining([expect.objectContaining({ contactUuid: contactDetails.contactUuid })]),
      );

      expect(response).toBeDefined();
      expect(response.reportType).toBe(reportType);
      expect(response.range).toEqual(
        expect.objectContaining({
          start: expect.any(String),
          end: expect.any(String),
        }),
      );
      expect(response.timeZone).toEqual(expect.any(String));
      expect(response.totalSum).toBeDefined();
      expect(response.change).toEqual(expect.any(Number));
      expect(response.contactTopTenTotals.length).toBeGreaterThan(0);
      response.contactTopTenTotals.forEach((item: any) => {
        expect(item.contactUuid).toBeDefined();
        expect(item.total).toBeDefined();
      });
    });

    it('should be able to get all records for single account', async () => {
      const input = {
        entityUuid,
        date: shortCurrentDate,
        reportType,
        transactionDirection: 'EXPENSE',
        accountUuid: dcaTxnDto.debitCardAccountUuid,
      };

      const response = await getTopTenContactHandler(input);

      expect(response).toBeDefined();
      expect(response.reportType).toBe(reportType);
      expect(response.range).toEqual(
        expect.objectContaining({
          start: expect.any(String),
          end: expect.any(String),
        }),
      );
      expect(response.timeZone).toEqual(expect.any(String));
      expect(response.totalSum).toBeDefined();
      expect(response.change).toEqual(expect.any(Number));
      expect(response.contactTopTenTotals.length).toBeGreaterThan(0);
      response.contactTopTenTotals.forEach((item: any) => {
        expect(item.contactUuid).toBeDefined();
        expect(item.total).toBeDefined();
      });
    });
  });
});
