import 'jest';

import { describeIf } from '@npco/bff-systemtest-utils';
import { isRegionAP } from '@npco/bff-systemtest-utils/dist/helper/baseTestHelper';
import { DebitCardTransactionTypeV2, type DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';
import type { PaymentInstrumentCreatedDto } from '@npco/component-dto-payment-instrument';

import axios from 'axios';
import { Client } from 'pg';
import { v4 as uuid } from 'uuid';

import { getApiEndpoint } from '../utils/api';
import { assertTotalAmounts } from '../utils/assertions/totalAmounts';
import { getContactCreatedEventDto } from '../utils/contact/contactDto';
import { createContact } from '../utils/contact/contactSqs';
import { getDbClientOptions } from '../utils/database/database';
import { getDcaTxnDto } from '../utils/debitCardAccountTransaction/debitCardAccountTransaction';
import { createDebitCardAccountTransaction } from '../utils/debitCardAccountTransaction/debitCardAccountTransactionSqs';
import { getDomicileByRegion } from '../utils/domicile';
import { getPaymentInstrumentCreatedDto } from '../utils/paymentInstrument/paymentInstrument';
import { createPaymentInstrument } from '../utils/paymentInstrument/paymentInstrumentSqs';

const currentDate = new Date().toISOString().split('T')[0];

describe('Cashflow total amount test suite', () => {
  const domicile = getDomicileByRegion();
  let client: Client;

  beforeAll(async () => {
    const options = await getDbClientOptions();
    client = new Client(options);
    await client.connect();
    await client.query(`SET search_path TO ${options.search_path}`);
  });

  afterAll(() => client.end());

  describe('get cash flow total amounts by api', () => {
    const entityUuid = uuid();
    const contactUuid = uuid();

    const dtoContact = getContactCreatedEventDto({ entityUuid });
    const paymentInstrumentDto = getPaymentInstrumentCreatedDto({ entityUuid });

    let paymentInstrument: PaymentInstrumentCreatedDto;
    let txn: DebitCardTransactionV2;

    beforeAll(async () => {
      await createContact({ ...dtoContact, contactUuid }, client, domicile);

      const result = await createPaymentInstrument({ ...paymentInstrumentDto, contactUuid }, client, domicile);
      paymentInstrument = result.rows[0];

      txn = getDcaTxnDto({
        entityUuid,
        type: DebitCardTransactionTypeV2.DE_IN,
        payerDetails: {
          recipientUuid: uuid(),
          senderUuid: uuid(),
        },
        payeeDetails: {
          recipientUuid: paymentInstrument.paymentInstrumentUuid,
        },
      });

      await createDebitCardAccountTransaction(txn, client, domicile);
    });

    const postAndAssert = async (endpoint: string, payload: any) => {
      const { data, status } = await axios.post(endpoint, JSON.stringify(payload), {
        headers: { 'Content-Type': 'application/json' },
      });
      expect(status).toBe(200);
      expect(data).toBeDefined();
      expect(data.timeZone).toEqual(expect.any(String));
      expect(data.range).toEqual(
        expect.objectContaining({
          start: expect.any(String),
          end: expect.any(String),
        }),
      );
      assertTotalAmounts(data);
    };

    describeIf(isRegionAP, 'v1 API version', () => {
      let endpoint: string;
      beforeAll(async () => {
        endpoint = `${await getApiEndpoint()}/v1/entity/${entityUuid}/cashFlowTotalAmounts`;
      });

      it('should return all records', async () => {
        await postAndAssert(endpoint, { date: currentDate });
      });

      it('should return records for single account', async () => {
        await postAndAssert(endpoint, {
          date: currentDate,
          accountUuid: txn.debitCardAccountUuid,
        });
      });
    });

    describe('v2 API version', () => {
      let endpoint: string;
      beforeAll(async () => {
        endpoint = `${await getApiEndpoint()}/v2/${domicile}/entity/${entityUuid}/cashFlowTotalAmounts`;
      });

      it('should return all records', async () => {
        await postAndAssert(endpoint, { date: currentDate });
      });

      it('should return records for single account', async () => {
        await postAndAssert(endpoint, {
          date: currentDate,
          accountUuid: txn.debitCardAccountUuid,
        });
      });
    });
  });
});
