import { sleep } from '@npco/bff-systemtest-utils';
import type { ContactCreatedEventDto } from '@npco/component-dto-addressbook';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type { PaymentInstrumentCreatedDto } from '@npco/component-dto-payment-instrument';

import { Client } from 'pg';
import { v4 as uuid } from 'uuid';

import { assertNetAmount } from '../utils/assertions/netAmount';
import { getContactCreatedEventDto } from '../utils/contact/contactDto';
import { createContact } from '../utils/contact/contactSqs';
import { getDbClientOptions } from '../utils/database/database';
import { getDcaTxnDto } from '../utils/debitCardAccountTransaction/debitCardAccountTransaction';
import { createDebitCardAccountTransaction } from '../utils/debitCardAccountTransaction/debitCardAccountTransactionSqs';
import { getDomicileByRegion } from '../utils/domicile';
import { MerchantPortalEnvService } from '../utils/envService';
import { getPaymentInstrumentCreatedDto } from '../utils/paymentInstrument/paymentInstrument';
import { createPaymentInstrument } from '../utils/paymentInstrument/paymentInstrumentSqs';
import { ApiTestHelper } from '../utils/testHelper';

const currentDate: any = new Date().toISOString().split('T')[0];

describe('Merchant Portal BFF API - Category net amount test suite', () => {
  const domicile = getDomicileByRegion();
  const mpEnvService = new MerchantPortalEnvService();
  const apiTestHelper = new ApiTestHelper(mpEnvService);

  let client: Client;

  const invoke = async (lambda: string, args: any) =>
    apiTestHelper.invokeLambda(`${mpEnvService.COMPONENT_NAME}-reps-${lambda}`, {
      args,
    });

  const getContactsNetAmountHandler = async (args: any) => invoke('getContactsNetAmountHandler', args);

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
    await sleep();
    const options = await getDbClientOptions();

    client = new Client(options);
    await client.connect();
    await client.query(`SET search_path TO ${options.search_path}`);
  });

  afterAll(async () => {
    await client.end();
  });

  describe('get category net amount api', () => {
    const entityUuid = apiTestHelper.getEntityUuid();
    const transactionDirection = 'EXPENSE';
    const dtoContact = getContactCreatedEventDto({
      entityUuid,
    });
    const paymentInstrumentDto = getPaymentInstrumentCreatedDto({
      entityUuid,
    });
    let paymentInstrumentDetails: PaymentInstrumentCreatedDto;
    let contactDetails: ContactCreatedEventDto;
    let dcaTxnDto: DebitCardTransactionV2;

    beforeAll(async () => {
      contactDetails = (await createContact(dtoContact, client, domicile)).rows[0];

      paymentInstrumentDetails = (
        await createPaymentInstrument(
          {
            ...paymentInstrumentDto,
            contactUuid: contactDetails.contactUuid,
          },
          client,
          domicile,
        )
      ).rows[0];

      dcaTxnDto = getDcaTxnDto({
        entityUuid,
        type: DebitCardTransactionTypeV2.DE_OUT,
        payerDetails: {
          recipientUuid: uuid(),
          senderUuid: uuid(),
        },
        payeeDetails: {
          recipientUuid: paymentInstrumentDetails.paymentInstrumentUuid,
        },
      });
      await createDebitCardAccountTransaction(dcaTxnDto, client, domicile);
    });

    describe.each(['MONTHLY', 'TTM'])('by report type %s', (reportType) => {
      it('should be able to get all records ', async () => {
        const input = {
          entityUuid,
          reportType,
          transactionDirection,
          date: currentDate,
        };

        const response = await getContactsNetAmountHandler(input);

        expect(response).not.toBeUndefined();
        expect(response.reportType).toBe(reportType);
        expect(response.range).toEqual(
          expect.objectContaining({
            start: expect.any(String),
            end: expect.any(String),
          }),
        );
        expect(response.timeZone).toEqual(expect.any(String));
        expect(response.contactNetAmounts.length).toBe(1);
        response.contactNetAmounts.forEach((item: any) => {
          expect(item.contactUuid).toBe(dtoContact.contactUuid);
          expect(item.entityUuid).toBe(entityUuid);
          assertNetAmount(item);
        });
      });

      it('should be able to get all records for single account', async () => {
        const input = {
          entityUuid,
          date: currentDate,
          reportType,
          transactionDirection,
          accountUuid: dcaTxnDto.debitCardAccountUuid,
        };

        const response = await getContactsNetAmountHandler(input);

        expect(response).not.toBeUndefined();
        expect(response.reportType).toBe(reportType);
        expect(response.range).toEqual(
          expect.objectContaining({
            start: expect.any(String),
            end: expect.any(String),
          }),
        );
        expect(response.timeZone).toEqual(expect.any(String));
        expect(response.accountUuid).toEqual(dcaTxnDto.debitCardAccountUuid);
        expect(response.contactNetAmounts.length).toBe(1);
        response.contactNetAmounts.forEach((item: any) => {
          expect(item.contactUuid).toBe(dtoContact.contactUuid);
          expect(item.entityUuid).toBe(entityUuid);
          assertNetAmount(item);
        });
      });
    });
  });
});
