import { lambdaHandlerUnitTestWrapper } from '@npco/component-bff-core/dist/lambda/lambdaUnitTestWrapper';
import { CmsService } from '@npco/component-dbs-mp-common';

import { updateDbtCardTxnAnnotnsHandler } from './cmsLambda';

const mockUpdateDebitCardTransactionAnnotations = jest.fn();

jest.mock('@npco/component-dbs-mp-common/dist/cms/cmsService', () => {
  return {
    CmsService: jest.fn(() => ({
      updateDebitCardTransactionAnnotations: mockUpdateDebitCardTransactionAnnotations,
    })),
  };
});

jest.mock('aws-xray-sdk', () => {
  return {
    getSegment: () => ({
      addNewSubsegment: jest.fn().mockReturnValue({}),
      addAnnotation: jest.fn(),
    }),
  };
});

describe('Cms lambda test suite', () => {
  let service: CmsService;

  beforeEach(() => {
    mockUpdateDebitCardTransactionAnnotations.mockReset();
    const mock = {} as any;
    service = new CmsService(mock, mock, mock);
  });

  it('should be able to handle updating debit card transaction annotations request', async () => {
    const result = await lambdaHandlerUnitTestWrapper(updateDbtCardTxnAnnotnsHandler)(
      {
        args: { input: { tags: [], note: '' } },
        request: { headers: { authorization: 'accessToken' } },
      },
      {} as any,
    );
    expect(result).toBeNull();
    expect(service.updateDebitCardTransactionAnnotations).toHaveBeenCalledTimes(1);
  });
});
