import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { CmsModule, CmsService } from '@npco/component-dbs-mp-common';
import { bootstrapNestJSMiddleware } from '@npco/component-dbs-mp-common/dist/middleware';
import type { NestAppContext } from '@npco/component-dbs-mp-common/dist/types';
import type { DebitCardTransactionAnnotations } from '@npco/component-dto-issuing-transaction';

import type { Handler } from 'aws-lambda';

// updateDebitCardTransactionAnnotations
export const updateDbtCardTxnAnnotnsHandler: Handler = withMiddlewaresV2(
  {
    component: ZellerComponent.CRMS,
    eventType: LambdaEventSource.APPSYNC,
  },
  async (
    event: {
      args: { input: DebitCardTransactionAnnotations; entityUuid: string };
    },
    context,
  ) => {
    const { app } = context as NestAppContext;
    return app.get(CmsService).updateDebitCardTransactionAnnotations(event.args.entityUuid, event.args.input);
  },
  [bootstrapNestJSMiddleware(CmsModule)],
);
