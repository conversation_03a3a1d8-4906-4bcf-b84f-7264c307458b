import { ApiAppServerlessStack, ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless';

import { lambdas } from './resources/crms/lambdas';
import {
  publishDebitCardTransactionsExportResolver,
  publishDebitCardTransactionsExportDataSource,
} from './resources/crms/resolvers';
import { envConfig, esbuildWithSharpAndPdfkit, pluginsApp } from './resources/crms/common';

const sls = new ApiAppServerlessStack('cms', envConfig, {
  plugins: pluginsApp,
  package: {
    individually: true,
    patterns: ['!node_modules/aws-sdk', 'src/fonts/*'],
  },
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    esbuild: esbuildWithSharpAndPdfkit,
    vpcImport,
    merchantTableName: '${self:custom.dynamodbStackName}-${env:MERCHANT_TABLE}',
    debitCardTransactionSummaryBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-txn-de-bucket}',
    exportTransactionsBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-txn-de-bucket}',
    richDataStackName: '${self:custom.service}-richdata',
    getMerchantDetailsLambda: '${self:custom.richDataStackName}-getMerchantDetails',
    cmsStackName: '${env:STATIC_ENV_NAME}-cms-cqrs-apiHandlers',
    cmsEndpoint: '${ssm:${env:STATIC_ENV_NAME}-bankingwrapper-engine-api-endpoint}',
    debitCardIdGsi: '${env:DEBIT_CARD_ID_GSI}',
    merchantTableQueryRolePolicyArn: envConfig.merchantTableQueryRolePolicyArn,
    iamUserKey: envConfig.iamUserKey,
    iamUserSecret: envConfig.iamUserSecret,
    entityGsi: envConfig.entityGsi,
    
    dependsOn: {
      // Optional. Defaults to true, set to false to disable the plugin
      enabled: true,
      // Optional. Sets amount of lambda deployment parallelization plugin will attempt to create. Defaults to 1
      chains: 3,
    },
  },
  functions: lambdas,
  resources: {
    publishDebitCardTransactionsExportResolver,
    publishDebitCardTransactionsExportDataSource,
  },
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
  },
});

module.exports = sls.build();
