import type { ServerlessFunctions } from '@npco/component-bff-serverless';
import { loadTemplateFile, Action, Arn, ManagedPolicy } from '@npco/component-bff-serverless';

import { envConfig, lambdaCommon } from './common';

export const lambdas: ServerlessFunctions = {
  // does not exist?
  // getDebitCardTxnSummaryHandler: {
  //   handler: 'src/lambda/crms/debitCardAccountLambda.getDebitCardTxnSummaryHandler',
  //   name: 'getDebitCardTxnSummaryHandler',
  //   ...lambdaCommon,
  //   environment: {
  //     CMS_API_ENDPOINT: '${self:custom.cmsEndpoint}',
  //     MERCHANT_TABLE: '${self:custom.merchantTableName}',
  //     DEBIT_CARD_TRANSACTION_SUMMARY_BUCKET: '${self:custom.debitCardTransactionSummaryBucket}',
  //   },
  //   appsync: {
  //     fieldName: 'getDebitCardTransactionSummary',
  //     typeName: 'Query',
  //   },
  //   policy: {
  //     managed: ['merchantTableQueryRolePolicyArn', ManagedPolicy.entityTableQueryRolePolicy],
  //     inline: {
  //       getDebitCardTxnSummaryS3Policy: [
  //         {
  //           actions: [Action.s3.PutObject, Action.s3.GetObject],
  //           resources: [Arn.s3('${self:custom.debitCardTransactionSummaryBucket}/dcaTransactionSummary/')],
  //         },
  //       ],
  //     },
  //   },
  // },
  updateDbtCardTxnAnnotnsHandler: {
    handler: 'src/lambda/crms/cmsLambda.updateDbtCardTxnAnnotnsHandler',
    name: 'updateDbtCardTxnAnnotnsHandler',
    ...lambdaCommon,
    environment: { CMS_API_ENDPOINT: '${self:custom.cmsEndpoint}' },
    appsync: {
      fieldName: 'updateDebitCardTransactionAnnotations',
      typeName: 'Mutation',
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy] },
  },
  exportDebitCardTransactionsHandler: {
    handler: 'src/lambda/crms/debitCardTransactionsExportLambda.exportDebitCardTransactionsHandler',
    name: 'exportDcaTxns',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      PUBLISH_HANDLER_NAME: '${self:provider.stackName}-publishDcaTxnsExport',
    },
    appsync: {
      fieldName: 'exportDebitCardTransactions',
      typeName: 'Subscription',
      template: { request: loadTemplateFile('resources/crms/template/getDebitCardTxnsRequest.vtl') },
      dataResolverName: {
        namePrefix: 'exportDebitCardTransactions',
      },
    },
    policy: {
      inline: {
        invokePublishLambdaPolicy: [
          {
            actions: [Action.lambda.InvokeFunction],
            resources: [Arn.lambda.function('${self:provider.stackName}-publishDcaTxnsExport')],
          },
        ],
      },
    },
  },
  publishDebitCardTransactionsExportHandler: {
    handler: 'src/lambda/crms/debitCardTransactionsExportLambda.publishDebitCardTransactionsExportHandler',
    name: 'publishDcaTxnsExport',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      MERCHANT_TABLE: '${self:custom.merchantTableName}',
      TRANSACTION_EXPORT_BUCKET: '${self:custom.exportTransactionsBucket}',
      IAM_USER_KEY: '${self:custom.iamUserKey}',
      IAM_USER_SECRET: '${self:custom.iamUserSecret}',
      APP_SYNC_ENDPOINT: '${self:custom.appSyncEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, '${self:custom.merchantTableQueryRolePolicyArn}'],
      inline: {
        exportS3Policy: [
          {
            actions: [Action.s3.PutObject, Action.s3.GetObject],
            resources: [Arn.s3('${self:custom.exportTransactionsBucket}/*')],
          },
        ],
      },
    },
    timeout: 300,
    memorySize: 1024,
  },
};
