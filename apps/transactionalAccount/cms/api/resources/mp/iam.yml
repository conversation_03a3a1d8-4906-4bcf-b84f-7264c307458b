Resources:
  updateDbtCardTxnAnnotnsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-updateDbtCardTxnAnnotnsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${opt:region}:*:log-group:/aws/lambda/${self:provider.stackName}-updateDbtCardTxnAnnotnsHandler:*'

  validateEntityCanMigrateRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-validateEntityCanMigrateLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-validateEntityCanMigrate:*'

  migrateAccountsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-migrateAccountsInvokeLambdaPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - lambda:InvokeFunction
                Resource:
                  - 'arn:aws:lambda:${self:provider.region}:*:function:${self:custom.mpCqrsCommandHandler}*'
        - PolicyName: ${self:provider.stackName}-migrateAccountsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-migrateAccounts:*'

  rollbackMigrateAccountsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-rollbackMigrateAccountsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-rollbackMigrateAccounts:*'

  closeZtaAccountsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-closeZtaAccountsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-closeZtaAccounts:*'

  closeZtaCardsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-closeZtaCardsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-closeZtaCards:*'

  updateSimBillingAccountRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-updateSimBillingAccountLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateSimBillingAccount:*'

  updateSettlementAccountRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-updateSettlementAccountLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateSettlementAccount:*'

  finaliseMigrationRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-finaliseMigrationLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-finaliseMigration:*'

  updateDepositAccountRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-updateDepositAccountLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateDepositAccount:*'

  updateToThirdPartySimBillingAccountRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-updateToThirdPartySimBillingAccountLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-updateToThirdPartySimBillingAccount:*'

  closeAccountsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-closeAccountsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-closeAccounts:*'

  transferAllEztaBalancesToThirdPartyAccountRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-transferAllEztaBalancesToThirdPartyAccountLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-transferAllEztaBalancesToThirdPartyAccount:*'

  getForcedMigrationErrorStatesRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-getForcedMigrationErrorStatesDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:GetItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.bankingMigrationTableName}'
        - PolicyName: ${self:provider.stackName}-getForcedMigrationErrorStatesLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-getForcedMigrationErrorStates:*'

  incrementMigrationErrorRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
      Policies:
        - PolicyName: ${self:provider.stackName}-incrementMigrationErrorLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-incrementMigrationError:*'
        - PolicyName: ${self:provider.stackName}-incrementMigrationErrorDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:UpdateItem
                  - dynamodb:PutItem
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.bankingMigrationTableName}'

  exportDebitCardTransactionsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-exportDcaTxnsLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-exportDcaTxns:*'
        - PolicyName: ${self:provider.stackName}-ExportS3Policy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                Resource: 'arn:aws:s3:::${self:custom.exportTransactionsBucket}/*'
              - Effect: Allow
                Action:
                  - lambda:invokeFunction
                Resource:
                  - !Join ['', [!GetAtt PublishDebitCardTransactionsExportHandlerLambdaFunction.Arn, '*']]

  publishDebitCardTransactionsExportRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-xrayPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-sessionCacheTableDBPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-lambdaVpcPolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-entityTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-merchantTableQueryRolePolicyArn'
        - Fn::ImportValue: !Sub '${self:provider.serviceName}-permissionsTableReadRolePolicyArn'
        - ${self:custom.domicileLookupTableReadRolePolicyArn}
      Policies:
        - PolicyName: ${self:provider.stackName}-publishExportLogPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:CreateLogGroup
                Resource: 'arn:aws:logs:${self:provider.region}:${self:provider.accountId}:log-group:/aws/lambda/${self:provider.stackName}-publishDcaTxnsExport:*'
        - PolicyName: ${self:provider.stackName}-ExportS3Policy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 's3:PutObject'
                  - 's3:GetObject'
                Resource: 'arn:aws:s3:::${self:custom.exportTransactionsBucket}/*'
        - PolicyName: ${self:provider.stackName}-publishDcaTxnsExportDbPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - dynamodb:Query
                Resource:
                  - 'arn:aws:dynamodb:${self:provider.region}:${self:provider.accountId}:table/${self:provider.entityTableName}/index/${self:provider.typeGsi}'