import { lambdaExtensions } from '@npco/component-bff-serverless/dist/aws/lambda/extensions';
import { ServerlessPlugin } from '@npco/component-bff-serverless/dist/serverless/common/plugins';
import { vpcImport } from '@npco/component-bff-serverless/dist/serverless/common/vpc';
import { ApiAppServerlessStack } from '@npco/component-bff-serverless/dist/serverless/stacks/apiAppServerlessStack';

import { getLambdas as getDcaRematerialisationLambda } from './iac/common/resources/dcaRematerialisationLambda';
import { dcaTransactionLookupLambda } from './iac/common/resources/dcaTransactionLookupLambda';
import { eventBridgeRules } from './iac/common/resources/eventBridgeRules';
import { getLambdas as getProjectionLambda } from './iac/common/resources/projectionLambda';
import { getProjectionSqsResources } from './iac/common/resources/projectionSqsResources';
import { mpEnvConfig } from './iac/mp/env';
import { lambdas as mpAbusiveDescriptionLambda } from './iac/mp/resources/abusiveDescriptionLambda';
import { lambdas as accountStatementExportLambdas } from './iac/mp/resources/accountStatementExportLambda';
import { accountStatementExportSqsResources } from './iac/mp/resources/accountStatementExportSqsResources';
import { lambdas as mpCardProvisionLambdas } from './iac/mp/resources/cardProvisionLambda';
import { lambdas as mpCustomerLambda } from './iac/mp/resources/customerLambda';
import { lambdas as mpDebitCardAccountTransactionLambdas } from './iac/mp/resources/debitCardAccountTransactionLambda';
import { lambdas as mpEntityLambdas } from './iac/mp/resources/entityLambda';
import { lambdas as pinOperationsLambdas } from './iac/mp/resources/pinOperationsLambda';
import { lambdas as mpSavingsAccountLambda } from './iac/mp/resources/savingsAccountLambda';

const sls = new ApiAppServerlessStack('banking', mpEnvConfig, {
  provider: {
    layers: [lambdaExtensions.parametersAndSecretsLambdaLayer],
  },
  plugins: [
    ServerlessPlugin.Dotenv,
    ServerlessPlugin.ResourceTagging,
    ServerlessPlugin.CanaryDeployments,
    'serverless-esbuild',
    'serverless-plugin-scripts',
    'serverless-dependson-plugin',
  ],

  environment: {
    ...mpEnvConfig.dotenvConfig,
    COMPONENT_TABLE: mpEnvConfig.componentTableName,
    BANKING_PRODUCT_TABLE: mpEnvConfig.bankingProductTableName,
    MERCHANT_TABLE: mpEnvConfig.merchantTableName,
    SESSION_CACHE_TABLE: mpEnvConfig.sessionCacheTableName,
    OPENID_ISSUER_URL: mpEnvConfig.auth0IssuerUrl,
    AUTH0_CLIENT_ID: mpEnvConfig.auth0ClientId,
    AUTH0_CLIENT_SECRET: mpEnvConfig.auth0ClientSecret,
    AUTH0_TENANT: mpEnvConfig.auth0Tenant,
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}',
  },

  functions: {
    ...mpDebitCardAccountTransactionLambdas,
    ...mpEntityLambdas,
    ...mpSavingsAccountLambda,
    ...mpCustomerLambda,
    ...mpCardProvisionLambdas,
    ...getProjectionLambda('mp'),
    ...accountStatementExportLambdas,
    ...getDcaRematerialisationLambda('mp'),
    ...dcaTransactionLookupLambda,
    ...mpAbusiveDescriptionLambda,
    ...pinOperationsLambdas,
  },
  resources: {
    ...getProjectionSqsResources('mp').Resources,
    ...accountStatementExportSqsResources.Resources,
    ...eventBridgeRules.Resources,
  },
  outputs: {
    ...getProjectionSqsResources('mp').Outputs,
    ...accountStatementExportSqsResources.Outputs,
  },
  package: {
    individually: true,
  },

  custom: {
    ...mpEnvConfig.getDynamoDb(),
    ...mpEnvConfig.getAmsApi(),
    ...mpEnvConfig.getAppsync(),
    cqrsCommandHandler: mpEnvConfig.cqrsCommandHandler,
    typeGsi: '${env:TYPE_GSI}',
    firebaseAdminPrivateKeySsmName: '/${self:custom.serviceName}/ZELLER_APP_FIREBASE_ADMIN_PRIVATE_KEY',
    firebaseAdminEmailSsmName: '/${self:custom.serviceName}/ZELLER_APP_FIREBASE_ADMIN_EMAIL',
    zellerAppAuth0ClientId: '${ssm:/${self:custom.serviceName}/ZELLER_APP_AUTH0_CLIENT_ID}',
    debitCardTransactionSummaryBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-txn-de-bucket}',
    secondaryGsiV1: '${env:SECONDARY_GSI_V1}',
    debitCardIdGsi: '${env:DEBIT_CARD_ID_GSI}',
    contactUuidGsi: '${env:CONTACT_UUID_GSI}',
    sortKeyGsi: '${env:SORT_KEY_GSI}',
    entitySortKeyGsi: '${env:ENTITY_SORT_KEY_GSI}',
    bankingProductTableName: mpEnvConfig.bankingProductTableName,
    bedrockRegion: 'eu-west-2',
    bedrockModelName: 'meta.llama3-70b-instruct-v1:0',
    merchantTableName: mpEnvConfig.merchantTableName,
    richDataStackName: '${self:custom.service}-richdata',
    getMerchantDetailsLambda: '${self:custom.richDataStackName}-getMerchantDetails',
    vpcImport,
    serviceName: '${env:STATIC_ENV_NAME}-mp-api',
    cbsEndpoint: 'http://${ssm:${env:STATIC_ENV_NAME}-banking-service-endpoint}',
    accountStatementExportBucket: '${ssm:${self:custom.serviceName}-assets-txn-de-bucket}',
    lambdaPararameterExtensionAccountId: mpEnvConfig.lambdaPararameterExtensionAccountId,
    dependsOn: {
      // Optional. Defaults to true, set to false to disable the plugin
      enabled: true,
      // Optional. Sets amount of lambda deployment parallelization plugin will attempt to create. Defaults to 1
      chains: 3,
    },
    esbuild: {
      bundle: true,
      minify: true,
      sourcemap: 'linked',
      exclude: [], // defaults to `aws-sdk` but as we are using node 18 it is no longer included by default.
      target: 'node18',
      external: ['pdfmake'],
    },
    scripts: {
      hooks: {
        'after:deploy:deploy':
          '/bin/bash ./iac/bin/tagEventRules.sh -s ${self:provider.stackName} -e ${opt:stage} -c ${env:COMPONENT_NAME} -p ${env:PART_NAME}',
      },
    },
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
    lambdaParameterExtensionAccountId: '${ssm:Parameters-and-Secrets-Lambda-Extension}',
  },
});

module.exports = sls.build();
