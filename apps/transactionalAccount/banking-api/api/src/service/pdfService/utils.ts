import type PDFDocument from 'pdfkit';

import { PdfColor } from './basePdfService';

export const insertTextVerticallyCenter = (
  doc: typeof PDFDocument,
  totalHeight: number,
  text: string,
  textOptions: PDFKit.Mixins.TextOptions,
  x = 0,
  y = 0,
) => {
  const lineHeight = doc.heightOfString(text, {
    width: textOptions.width,
  });
  const centerPosition = totalHeight / 2 - lineHeight / 2;

  doc
    .translate(0, centerPosition)
    .text(text, x, y, {
      width: textOptions.width,
      align: textOptions.align,
    })
    .translate(0, -centerPosition);
};

export const getMoneyValue = (value?: string | number) => {
  if (!value) {
    return 0;
  }

  if (typeof value === 'number') {
    return value;
  }

  return parseInt(value, 10);
};

export const drawHorizontalLine = (doc: typeof PDFDocument, documentWidth: number) => {
  doc.strokeColor(PdfColor.Grey150).lineWidth(0.5).moveTo(0, 0).lineTo(documentWidth, 0).stroke();
};

export const stripUndefinedFromArray = <T>(array: (T | undefined)[]) =>
  array.reduce<T[]>((carry, line) => {
    if (line) {
      carry.push(line);
    }
    return carry;
  }, []);

export enum PdfTableColumnAlign {
  Left = 'left',
  Right = 'right',
  Center = 'center',
  Justify = 'justify',
}
