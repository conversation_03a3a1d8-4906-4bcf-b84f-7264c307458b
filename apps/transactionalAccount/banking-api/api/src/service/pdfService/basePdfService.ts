import { ISO4217 } from '@npco/component-dto-core/dist/types';

import fs from 'fs';
import PDFDocument from 'pdfkit';
import stream from 'stream';

import type { EnvironmentService } from '../environment/environmentService';

export enum PdfColor {
  Black900 = '#212322',
  Grey700 = '#454443',
  Grey550 = '#6D7274',
  Grey150 = '#DEDCD9',
  Blue1000 = '#0071CE',
  Orange1000 = '#F4BE49',
  Green1000 = '#62CB84',
  Red1000 = '#E74C39',
}

export enum PdfFont {
  Regular = 'Regular',
  Medium = 'Medium',
  Bold = 'Bold',
}

export interface FontOptions {
  fontRegularPath?: string;
  fontMediumPath?: string;
  fontBoldPath?: string;
}

export const DEFAULT_FONT_REGULAR = 'Helvetica';

export const DEFAULT_FONT_MEDIUM = 'Helvetica';

export const DEFAULT_FONT_BOLD = 'Helvetica-Bold';

export abstract class BasePdfService<T> {
  defaultPageSize: string | undefined = 'A4';

  defaultMargin: number | undefined = 70;

  defaultMarginTop: number | undefined;

  defaultMarginLeft: number | undefined;

  defaultLayout: 'portrait' | 'landscape' | undefined = 'portrait';

  private readonly envService?: EnvironmentService;

  private readonly fontOptions?: FontOptions;

  public async createDocument(data: T) {
    const doc = new PDFDocument({
      autoFirstPage: false,
    });
    this.addPage(doc);

    await this.registerFonts(doc);
    await this.render(doc, data);
    const pass = new stream.PassThrough();
    doc.end();
    doc.pipe(pass);
    return pass;
  }

  public async registerFonts(pdf: typeof PDFDocument) {
    const regularFontPath = this.fontOptions?.fontRegularPath?.length
      ? this.fontOptions?.fontRegularPath
      : this.envService?.fontRegularPath;
    if (regularFontPath) {
      pdf.registerFont(PdfFont.Regular, fs.readFileSync(regularFontPath));
    } else {
      pdf.registerFont(PdfFont.Regular, DEFAULT_FONT_REGULAR);
    }

    const mediumFontPath = this.fontOptions?.fontMediumPath?.length
      ? this.fontOptions?.fontMediumPath
      : this.envService?.fontMediumPath;
    if (mediumFontPath) {
      pdf.registerFont(PdfFont.Medium, fs.readFileSync(mediumFontPath));
    } else {
      pdf.registerFont(PdfFont.Medium, DEFAULT_FONT_MEDIUM);
    }

    const boldFontPath = this.fontOptions?.fontBoldPath?.length
      ? this.fontOptions?.fontBoldPath
      : this.envService?.fontBoldPath;
    if (boldFontPath) {
      pdf.registerFont(PdfFont.Bold, fs.readFileSync(boldFontPath));
    } else {
      pdf.registerFont(PdfFont.Bold, DEFAULT_FONT_BOLD);
    }
  }

  protected addPage(doc: typeof PDFDocument) {
    doc.addPage({
      size: this.defaultPageSize,
      layout: this.defaultLayout,
    });

    if (this.defaultMarginTop && this.defaultMarginLeft) {
      doc.translate(this.defaultMarginLeft, this.defaultMarginTop);
    } else if (this.defaultMargin) {
      doc.translate(this.defaultMargin, this.defaultMargin);
    }
  }

  protected formatMoney(moneyInCents: number, currency?: ISO4217) {
    return (moneyInCents / 100).toLocaleString('en-US', {
      style: 'currency',
      currency: this.formatEntityCurrency(currency ?? ISO4217.AUD),
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  }

  private formatEntityCurrency(currency: ISO4217) {
    switch (currency) {
      case ISO4217.GBP:
        return ISO4217.GBP;
      case ISO4217.AUD:
      default:
        return ISO4217.USD;
    }
  }

  protected abstract render(pdf: typeof PDFDocument, data: T): Promise<void>;
}
