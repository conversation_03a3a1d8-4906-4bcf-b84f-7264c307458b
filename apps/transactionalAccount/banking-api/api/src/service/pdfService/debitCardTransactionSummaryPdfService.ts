/* eslint-disable @typescript-eslint/no-non-null-assertion */
import type { ISO4217 } from '@npco/component-dto-core/dist/types';

import type PDFDocument from 'pdfkit';

import { BasePdfService, PdfColor, PdfFont } from './basePdfService';
import { insertNewZellerLogo } from './svgIcons';
import {
  PdfTableColumnAlign,
  drawHorizontalLine,
  getMoneyValue,
  insertTextVerticallyCenter,
  stripUndefinedFromArray,
} from './utils';

type BusinessInfo = {
  name?: string;
  address?: {
    street1?: string;
    street2?: string;
    suburb?: string;
    state?: string;
    postcode?: string;
  };
  abn?: string;
  acn?: string;
};
type To = BusinessInfo & {
  accountName?: string;
  bsb?: string;
  accountNumber?: string;
};

export interface DebitCardTransactionSummaryPdfData {
  from: BusinessInfo;
  to: To;
  accountName?: string;
  time: string;
  completedDate: string;
  description: string;
  paidOut: string;
  paidIn: string;
  entityCurrency: ISO4217;
}

interface TableRow {
  rowLabel: string;
  rowValue: string | number;
}
interface TableColumn {
  label?: string;
  width: number;
  align?: PDFKit.Mixins.TextOptions['align'];
  renderCell: (
    pdf: typeof PDFDocument,
    item: TableRow,
    columnOptions: Omit<TableColumn, 'renderCell'>,
    totalHeight: number,
  ) => void;
}

export class DebitCardTransactionSummaryPdfService extends BasePdfService<DebitCardTransactionSummaryPdfData> {
  override defaultLayout: 'landscape' | 'portrait' | undefined = 'portrait';

  override defaultMarginLeft = 142;

  override defaultMarginTop = 71;

  defaultCellHeight = 30;

  documentWidth = 400;

  maxFromToHeight = 0;

  public async render(doc: typeof PDFDocument, data: DebitCardTransactionSummaryPdfData) {
    this.insertLogoAndTitle(doc, 'Transaction Summary');

    this.insertFrom(doc, stripUndefinedFromArray(this.getBaseRecipientLines(data.from)));

    const { accountName, accountNumber, bsb } = data.to;
    const bankInfo =
      accountNumber && bsb
        ? [accountName && `Account Name: ${accountName}`, `BSB: ${bsb}`, `Account: ${accountNumber}`]
        : [];
    this.insertTo(doc, stripUndefinedFromArray([...this.getBaseRecipientLines(data.to), ...bankInfo]));

    this.insertTableTitle(doc, data.accountName ?? '');
    const { time, completedDate, description, paidOut, paidIn } = data;
    const rows: TableRow[] = [
      {
        rowLabel: 'Time',
        rowValue: time,
      },
      {
        rowLabel: 'Completed date',
        rowValue: completedDate,
      },
      {
        rowLabel: 'Description',
        rowValue: description,
      },
      {
        rowLabel: 'Paid out',
        rowValue: `-${this.formatMoney(getMoneyValue(paidOut), data.entityCurrency)}`,
      },
      {
        rowLabel: 'Paid in',
        rowValue: this.formatMoney(getMoneyValue(paidIn), data.entityCurrency),
      },
    ];
    this.insertTable(doc, rows, this.getTableColumns(), 10);
    this.insertFooter(doc);
  }

  private insertLogoAndTitle(doc: typeof PDFDocument, title: string) {
    doc.fill(PdfColor.Black900);
    insertNewZellerLogo(doc);
    doc.translate(0, 41);
    doc.font(PdfFont.Medium).fontSize(16);
    doc.text(title, 0, 0);
    doc.translate(0, 46);
  }

  private insertFrom(doc: typeof PDFDocument, lines: string[]) {
    const maxWidth = this.documentWidth / 2;

    let height = 20;
    doc.fontSize(10).font(PdfFont.Bold).text('From:', 0, 0);
    lines.forEach((line) => {
      const maxHeight = doc.heightOfString(line, { width: maxWidth, lineGap: 3 });
      doc.font(PdfFont.Regular).text(line, 0, height, { width: maxWidth, lineGap: 3 });
      height += maxHeight;
    });

    if (this.maxFromToHeight < height) {
      this.maxFromToHeight = height;
    }
  }

  private insertTo(doc: typeof PDFDocument, lines: string[]) {
    doc.fontSize(10).font(PdfFont.Bold);

    const maxWidth = this.documentWidth / 2;
    const x = this.documentWidth - maxWidth;
    let height = 20;

    doc.fontSize(10).font(PdfFont.Bold).text('To:', x, 0);
    lines.forEach((line) => {
      const maxHeight = doc.heightOfString(line, { width: maxWidth, lineGap: 3 });
      doc.font(PdfFont.Regular).text(line, x, height, { width: maxWidth, lineGap: 3 });
      height += maxHeight;
    });

    if (this.maxFromToHeight < height) {
      this.maxFromToHeight = height;
    }
  }

  private insertTableTitle(doc: typeof PDFDocument, accountName: string) {
    doc.translate(0, 44 + this.maxFromToHeight);
    doc.fontSize(12);
    doc.fill(PdfColor.Black900);

    const tableTitle = 'Account';
    const tableTitleOptions = {
      lineGap: 6,
    };
    const tableTitleHeight = doc.heightOfString(tableTitle, tableTitleOptions);
    doc.text(tableTitle, 0, 0, tableTitleOptions);

    const remainingWidth = doc.widthOfString(tableTitle, tableTitleOptions);
    doc.translate(remainingWidth, 0);

    const accountNameOptions: any = {
      align: 'right',
      lineGrap: 0,
      width: this.documentWidth - remainingWidth,
    };

    const accountNameHeight = doc.heightOfString(accountName, accountNameOptions);

    const maxTotalHeight = accountNameHeight > tableTitleHeight ? accountNameHeight : tableTitleHeight;

    doc.text(accountName, 0, 0, accountNameOptions);
    doc.translate(-remainingWidth, maxTotalHeight);
  }

  private insertTable(doc: typeof PDFDocument, rows: TableRow[], columns: TableColumn[], fontSize = 8) {
    doc.fontSize(fontSize);

    const lineHeight = doc.heightOfString('0');
    const verticalPadding = (this.defaultCellHeight - lineHeight) / 2;

    let xPostition = 0;
    rows.forEach((row, index) => {
      const maxRowTextHeight = Object.values(row).reduce((max, rowText: string, i) => {
        const { renderCell, ...colOptions } = columns[i]!;
        const height = doc.heightOfString(rowText, colOptions);

        return height > max ? height : max;
      }, 0);
      const totalHeight = maxRowTextHeight + verticalPadding * 2;
      xPostition = 0;

      columns.forEach((col) => {
        const { renderCell, ...colOptions } = col;
        doc.font(PdfFont.Regular).fill(PdfColor.Black900);

        doc.translate(xPostition, 0);
        renderCell(doc, row, colOptions, totalHeight);
        doc.translate(-xPostition, 0);

        xPostition += col.width;
      }, 0);
      doc.translate(0, totalHeight);
      if (index !== rows.length - 1) {
        drawHorizontalLine(doc, this.documentWidth);
      }
    });
  }

  /**
   *
   * This method should be the LAST method called. It calls doc.restore() which will reset the relative page translation
   * to 0,0.
   */
  private insertFooter(doc: typeof PDFDocument) {
    doc.translate(0, 20);
    doc.fontSize(8).fill(PdfColor.Grey700);

    const textOptions = {
      width: this.documentWidth,
      lineGap: 5,
    };
    const copyrightText = `© ${new Date().getFullYear()} NPCO Operations Pty Ltd`;

    doc.restore(); // Used to reset all preexisting translations.
    doc.text(copyrightText, 0, doc.page.maxY() - 80, textOptions);
  }

  private getTableColumns() {
    const columns: TableColumn[] = [
      {
        width: 200,
        align: PdfTableColumnAlign.Left,
        renderCell: (doc, item, column, totalHeight) => {
          insertTextVerticallyCenter(doc, totalHeight, item.rowLabel, column);
        },
      },
      {
        width: 200,
        align: PdfTableColumnAlign.Right,
        renderCell: (doc, item, column, totalHeight) => {
          insertTextVerticallyCenter(doc, totalHeight, item.rowValue.toString(), column);
        },
      },
    ];

    return columns;
  }

  private getBaseRecipientLines(item: BusinessInfo | To) {
    const { name, address, abn, acn } = item;
    const suburbStatePostcode = stripUndefinedFromArray([address?.suburb, address?.state, address?.postcode]);
    return [
      name,
      address?.street1,
      address?.street2,
      suburbStatePostcode.length > 0 ? suburbStatePostcode.join(' ') : undefined,
      abn && `ABN: ${abn}`,
      acn && `ACN: ${acn}`,
    ];
  }
}
