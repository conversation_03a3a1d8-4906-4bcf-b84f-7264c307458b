import type PDFDocument from 'pdfkit';

import { PdfColor } from './basePdfService';

export const insertZellerLogo = (doc: typeof PDFDocument, color?: string) => {
  doc
    .scale(1)
    .path(
      'M4.44349 16.739H12.4381V19.3867H0V17.3815L0.0400643 17.3409L7.7834 ' +
        '9.01576H0.265881V6.3647H12.2269V8.37328L12.1868 8.41386L4.44349 16.739ZM21.6092 8.76553C19.4894 ' +
        '8.76553 17.9706 9.85436 17.519 11.6871H25.5355C25.1494 9.82731 23.729 8.76553 21.6092 ' +
        '8.76553ZM21.6092 6.11448C23.7071 6.11448 25.5574 6.82796 26.8176 8.12644C28.1834 9.52974 28.7953 ' +
        '11.5485 28.544 13.8107L28.4966 14.2367H17.57C18.1018 16.0086 19.6971 16.9825 22.09 ' +
        '16.9825C23.6452 16.9825 24.9746 16.5057 26.1547 15.525L26.6391 15.126L28.0231 17.5032L27.7026 ' +
        '17.7534C26.0381 19.0384 24.2717 19.6369 22.1446 19.6369C17.519 19.6369 14.4122 16.9114 14.4122 ' +
        '12.8537C14.4086 8.94813 17.4389 6.11448 21.6092 6.11448ZM31.3995 ' +
        '17.4761H34.4407V0.03125H31.3995V17.4761ZM36.8992 21.6455H39.9404V4.20058H36.8992V21.6455ZM53.5477 ' +
        '11.6871C53.1617 9.82731 51.7412 8.76553 49.6214 8.76553C47.5017 8.76553 45.9829 9.85436 45.5312 ' +
        '11.6871H53.5477ZM49.6214 6.11448C51.7193 6.11448 53.5696 6.82796 54.8298 8.12644C56.1956 9.52974 ' +
        '56.8075 11.5518 56.5562 13.8107L56.5088 14.2367H45.5822C46.114 16.0086 47.7093 16.9825 50.1022 ' +
        '16.9825C51.6574 16.9825 52.9868 16.5057 54.1669 15.525L54.6513 15.126L56.0317 17.5032L55.7112 ' +
        '17.7534C54.0467 19.0384 52.2839 19.6369 50.1532 19.6369C45.5276 19.6369 42.4208 16.9114 42.4208 ' +
        '12.8537C42.4208 8.94813 45.4475 6.11448 49.6214 6.11448ZM67.501 6.11448C65.4541 6.06714 63.837 ' +
        '6.6386 62.5585 7.8593V6.36132H59.5173V19.3867H62.5585V11.4605C63.8734 9.10029 66.6597 8.91432 ' +
        '67.4828 8.91432L68 8.92108V6.128L67.501 6.11448Z',
    )
    .fill(color ?? PdfColor.Black900)
    .save();
};

// as of 24-08-2023
export const insertNewZellerLogo = (doc: typeof PDFDocument, color?: string) => {
  doc
    .scale(1)
    .path(
      'M31.5871 0H34.6489V19.3959H31.5871V0Z' +
        'M0.400024 10.0768V7.01496H12.7125V9.24653L4.97407 18.4299H12.7125V21.4917H0.400024V19.2622L8.14367 10.0768H0.400024Z' +
        'M40.1848 4.6041H37.123V24H40.1848V4.6041Z' +
        'M63.1214 8.68066C64.4087 7.32182 66.036 6.68645 68.0976 6.74035L68.6013 6.75175V9.85708L68.0799 9.84983C67.2507 9.85086 64.4439 10.0582 63.1214 12.6815V21.4928H60.0596V7.01502H63.1214V8.68066Z' +
        'M26.3041 17.2006C25.1142 18.291 23.775 18.8207 22.2089 18.8207C19.8022 18.8207 18.1946 17.7386 17.6587 15.7671H28.6621L28.7098 15.2935C28.9627 12.78 28.346 10.536 26.9716 8.97501C25.7029 7.53325 23.8403 6.73929 21.728 6.73929C17.526 6.73929 14.4756 9.88712 14.4756 14.2259C14.4756 18.5646 17.6048 21.7664 22.2618 21.7664C24.4052 21.7664 26.1839 21.1009 27.8588 19.6727L28.1822 19.3959L26.6347 16.8969L26.303 17.1996L26.3041 17.2006ZM21.728 9.68708C23.8611 9.68708 25.2925 10.8666 25.6812 12.9323H17.6069C18.065 10.8956 19.5918 9.68708 21.7269 9.68708H21.728Z' +
        'M50.7394 18.8207C52.3055 18.8207 53.6447 18.291 54.8346 17.2006L54.8335 17.1996L55.1652 16.8969L56.7127 19.3959L56.3893 19.6727C54.7143 21.1009 52.9357 21.7664 50.7923 21.7664C46.1353 21.7664 43.0061 18.5646 43.0061 14.2259C43.0061 9.88712 46.0565 6.73929 50.2585 6.73929C52.3708 6.73929 54.2334 7.53325 55.5021 8.97501C56.8765 10.536 57.4932 12.78 57.2403 15.2935L57.1926 15.7671H46.1892C46.7251 17.7386 48.3327 18.8207 50.7394 18.8207ZM54.2117 12.9323C53.823 10.8666 52.3916 9.68708 50.2585 9.68708H50.2574C48.1223 9.68708 46.5955 10.8956 46.1374 12.9323H54.2117Z',
    )
    .fill(color ?? PdfColor.Black900)
    .save();
};
