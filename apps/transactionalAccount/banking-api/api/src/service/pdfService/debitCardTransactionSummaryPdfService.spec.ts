import { ISO4217 } from '@npco/component-dto-core/dist/types';

import fs from 'fs';
import * as path from 'path';

// import fontBoldPath from '../fonts/InterZeller-Bold.ttf';
// import fontMediumPath from '../fonts/InterZeller-Medium.ttf';
// import fontRegularPath from '../fonts/InterZeller-Regular.ttf';

import type { DebitCardTransactionSummaryPdfData } from './debitCardTransactionSummaryPdfService';
import { DebitCardTransactionSummaryPdfService } from './debitCardTransactionSummaryPdfService';

const baseDir = path.join(__dirname, '..', '..', 'dist', 'pdf');

// const getTestModule = () => {
//   return Test.createTestingModule({
//     providers: [
//       {
//         provide: 'FONT_OPTIONS',
//         useValue: {
//           fontRegularPath,
//           fontMediumPath,
//           fontBoldPath,
//         },
//       },
//       DebitCardTransactionSummaryPdfService,
//     ],
//     exports: [DebitCardTransactionSummaryPdfService],
//   }).compile();
// };
const baseSummaryData: DebitCardTransactionSummaryPdfData = {
  entityCurrency: ISO4217.AUD,
  from: {
    name: 'Zeller Australia',
    address: {
      street1: 'The Building',
      street2: '123 Fake Street',
      suburb: 'Melbourne',
      postcode: '3000',
      state: 'VIC',
    },
    acn: '**************',
    abn: '**************',
  },
  to: {
    name: 'Merchant Name Pty Ltd',
    address: {
      street1: 'Another Building',
      street2: '123 Fake Avenue',
      suburb: 'Melbourne',
      postcode: '3000',
      state: 'VIC',
    },
    abn: '**************',
    acn: '**************',
    accountName: 'Test Bank Account Name Long',
    bsb: '123456',
    accountNumber: '1234 1111',
  },
  accountName: 'aaaaa aa aasaaaaaaaa',
  time: 'time',
  completedDate: 'date',
  description: 'pika pika pikachuuuuuuu',
  paidOut: '400',
  paidIn: '0',
};

describe('pdf test suite', () => {
  describe('deposit statement pdf', () => {
    // let testModule: TestingModule;

    // beforeAll(async () => {
    //   testModule = await getTestModule();
    // });

    it.each([
      [
        'normal sized data',
        {
          pdfData: baseSummaryData,
          pdfName: 'debitCardTransactionSummaryPdfTest',
        },
      ],
      [
        'normal sized data in GBP',
        {
          pdfData: { ...baseSummaryData, entityCurrency: ISO4217.GBP },
          pdfName: 'debitCardTransactionSummaryPdfTestInGBP',
        },
      ],
      [
        'long from text',
        {
          pdfData: {
            ...baseSummaryData,
            from: {
              ...baseSummaryData.from,
              name: 'Very Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Long Zeller Entity Pty Ltd..?',
            },
          },
          pdfName: 'debitCardTransactionSummaryLongFromPdfTest',
        },
      ],
      [
        'long to text',
        {
          pdfData: {
            ...baseSummaryData,
            to: {
              ...baseSummaryData.to,
              address: {
                ...baseSummaryData.to.address,
                street1:
                  'I have a very long street1 name which should not really be possible but anyways lets hope this works',
              },
            },
          },
          pdfName: 'debitCardTransactionSummaryLongToPdfTest',
        },
      ],
      [
        'long account name',
        {
          pdfData: {
            ...baseSummaryData,
            accountName:
              'àààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààààà',
          },
          pdfName: 'debitCardTransactionSummaryLongAccountNamePdfTest',
        },
      ],
      [
        'long description',
        {
          pdfData: {
            ...baseSummaryData,
            description:
              'pika pika pikachuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu',
          },
          pdfName: 'debitCardTransactionSummaryLongDescriptionPdfTest',
        },
      ],
      [
        'partial address',
        {
          pdfData: {
            ...baseSummaryData,
            to: {
              ...baseSummaryData.to,
              address: {
                ...baseSummaryData.to.address,
                street1: undefined,
                suburb: undefined,
              },
            },
          },
          pdfName: 'debitCardTransactionSummaryPartialAddress',
        },
      ],
      [
        'missing data',
        {
          pdfData: {
            ...baseSummaryData,
            to: {
              ...baseSummaryData.to,
              name: undefined,
            },
          },
          pdfName: 'debitCardTransactionSummaryMissingDataPdfTest',
        },
      ],
    ])('should be able to create deposit statement pdf for %p', async (_, { pdfData, pdfName }) => {
      const tesPdfService = new DebitCardTransactionSummaryPdfService();
      const pdfStream = await tesPdfService.createDocument(pdfData);
      const writeStream = fs.createWriteStream(path.join(baseDir, `${pdfName}.pdf`));
      pdfStream.pipe(writeStream);
    });
  });
});
