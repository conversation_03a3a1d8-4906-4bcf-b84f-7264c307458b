import fs from 'fs';
import * as path from 'path';
import type PDFDocument from 'pdfkit';

// import { EnvironmentService } from '../environment/environmentService';
// import fontBoldPath from '../fonts/InterZeller-Bold.ttf';
// import fontMediumPath from '../fonts/InterZeller-Medium.ttf';
// import fontRegularPath from '../fonts/InterZeller-Regular.ttf';

import { BasePdfService, PdfColor, PdfFont } from './basePdfService';
import { insertZellerLogo } from './svgIcons';

const baseDir = path.join(__dirname, '..', '..', 'dist', 'pdf');

// const envService: any = {
//   fontRegularPath,
//   fontMediumPath,
//   fontBoldPath,
// };

class TestPdfService extends BasePdfService<any> {
  render = async (doc: typeof PDFDocument) => {
    insertZellerLogo(doc);

    doc.translate(0, 40);
    doc.fontSize(18).font(PdfFont.Bold).text('This is a test string', 0, 0);
    doc.translate(0, 20);
    doc
      .fontSize(12)
      .font(PdfFont.Regular)
      .fill(PdfColor.Red1000)
      .text([...Array(20).keys()].map(() => 'This is an another test string').join(' '), 0, 0);

    this.addPage(doc);
    doc.fill(PdfColor.Green1000).text('Second page', 0, 0);
  };
}

describe('pdf test suite', () => {
  let tesPdfService: TestPdfService;
  beforeAll(async () => {
    tesPdfService = new TestPdfService();
  });

  describe('base pdf', () => {
    it('should be able to extend base pdf', async () => {
      const pdfStream = await tesPdfService.createDocument({});
      const writeStream = fs.createWriteStream(path.join(baseDir, 'basePdfTest.pdf'));
      pdfStream.pipe(writeStream);
      await new Promise((resolve) => {
        pdfStream.on('end', resolve);
      });
    });

    it('should be able to generate more than 1 pdf', async () => {
      const pdfStream = await tesPdfService.createDocument({});
      const writeStream = fs.createWriteStream(path.join(baseDir, 'basePdfTest.pdf'));
      pdfStream.pipe(writeStream);
      await new Promise((resolve) => {
        pdfStream.on('end', resolve);
      });
    });
  });
});
