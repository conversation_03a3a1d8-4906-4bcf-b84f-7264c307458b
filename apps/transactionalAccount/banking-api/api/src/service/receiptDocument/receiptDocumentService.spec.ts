import { v4 } from 'uuid';

import { DebitCardTransactionAnnotationOperation } from '../coreBanking/type';

import { ReceiptDocumentService } from './receiptDocumentService';

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'uuid-1234'),
}));

const mockS3Client = {
  listObjectsV2: jest.fn(),
  getSignedUrl: jest.fn(),
  deleteObject: jest.fn(),
  headObject: jest.fn(),
};

const mockCoreBankingService = {
  handleDebitCardTransactionAnnotation: jest.fn(),
};

const mockEnvService = {
  awsRegion: 'ap-southeast-2',
  receiptDocProcessedBucket: 'processed-bucket',
  rawReceiptDocUploadsBucket: 'raw-bucket',
  maxReceiptDocumentUploadsUrls: 3,
  maxReceiptDocumentFileNameLength: 20,
};

jest.mock('@npco/component-bff-core/dist/aws/s3Client', () => ({
  S3Client: jest.fn(() => mockS3Client),
}));

describe('ReceiptDocumentService', () => {
  let service: ReceiptDocumentService;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new ReceiptDocumentService(mockEnvService as any, mockCoreBankingService as any);
  });

  describe('getExistingDocumentNames', () => {
    it('returns filtered keys from S3 listObjectsV2', async () => {
      mockS3Client.listObjectsV2.mockResolvedValueOnce({
        Contents: [{ Key: 'entity/tx/' }, { Key: 'entity/tx/file1.jpg' }, { Key: 'entity/tx/file2.png' }, { Key: '' }],
      });

      const result = await service.getExistingDocumentNames('entity', 'tx');

      expect(mockS3Client.listObjectsV2).toHaveBeenCalledWith({
        Bucket: 'processed-bucket',
        MaxKeys: 1000,
        Prefix: 'entity/tx/',
      });
      expect(result).toEqual(['entity/tx/file1.jpg', 'entity/tx/file2.png']);
    });

    it('returns empty array if no Contents', async () => {
      mockS3Client.listObjectsV2.mockResolvedValueOnce({ Contents: undefined });

      const result = await service.getExistingDocumentNames('entity', 'tx');
      expect(result).toEqual([]);
    });
  });

  describe('handleMappingOfDocumentUploadUrls', () => {
    it('maps upload URLs, file names, and UUIDs to ReceiptDocumentUpload[]', () => {
      const urls = ['url1', 'url2'];
      const fileNames = ['file1.png', 'file2.jpg'];
      const uuids = ['uuid1.png', 'uuid2.jpg'];

      const result = service.handleMappingOfDocumentUploadUrls(urls, fileNames, uuids);
      expect(result).toEqual([
        { uploadUrl: 'url1', fileName: 'file1.png', documentUuid: 'uuid1.png' },
        { uploadUrl: 'url2', fileName: 'file2.jpg', documentUuid: 'uuid2.jpg' },
      ]);
    });
  });

  describe('validateReceiptDocumentUploadUrls', () => {
    it('throws if fileNames length exceeds max', () => {
      const files = ['a.png', 'b.png', 'c.png', 'd.png'];
      expect(() => service.validateReceiptDocumentUploadUrls('entity', files)).toThrow(
        'You cannot upload more than 3 files at once',
      );
    });

    it('throws if any file name is too long', () => {
      const longName = `${'a'.repeat(21)}.png`;
      expect(() => service.validateReceiptDocumentUploadUrls('entity', [longName])).toThrow(
        'You cannot have a file name over 20 characters (includes file extension)',
      );
    });

    it('does not throw for valid file names', () => {
      const validFiles = ['short.png', 'midlength.jpg'];
      expect(() => service.validateReceiptDocumentUploadUrls('entity', validFiles)).not.toThrow();
    });
  });

  describe('getPredefinedFileExt', () => {
    it('returns mapped extension from convertedImageTypeMappings', () => {
      const result = service.getPredefinedFileExt('file.jpeg');
      expect(result).toBe('.png');
    });

    it('throws if extension is unknown', () => {
      expect(() => service.getPredefinedFileExt('file.unknown')).toThrow(
        'Cannot find extension type with file .unknown',
      );
    });
  });

  describe('updatedFileExtension', () => {
    beforeEach(() => {
      jest.spyOn(service, 'getPredefinedFileExt').mockImplementation((fileName) => {
        if (fileName.endsWith('.jpeg')) return '.png';
        if (fileName.endsWith('.jpg')) return '.jpg';
        return '.ext';
      });
    });

    it('updates extensions correctly', () => {
      const result = service.updatedFileExtension(['file1.jpeg', 'file2.jpg']);
      expect(result).toEqual(['file1.png', 'file2.jpg']);
    });
  });

  describe('getReceiptDocumentUploadUrl', () => {
    beforeEach(() => {
      jest.spyOn(service, 'validateReceiptDocumentUploadUrls').mockImplementation(() => {});
      jest.spyOn(service as any, 'trimFileNames').mockImplementation((files) => files);
      jest.spyOn(service, 'updatedFileExtension').mockImplementation((files) => files);
      jest.spyOn(service, 'getPredefinedFileExt').mockReturnValue('.png');
      jest.spyOn(service as any, 'getPreSignedUrlForUpload').mockImplementation(async () => 'signed-url');
      jest.spyOn(service, 'handleMappingOfDocumentUploadUrls').mockImplementation((urls, files, uuids) => {
        return urls.map((url, i) => ({
          uploadUrl: url,
          fileName: files[i]!,
          documentUuid: uuids[i]!,
        }));
      });

      (v4 as jest.Mock).mockReturnValue('uuid-1234');
    });

    it('returns mapped upload urls with predefined uuids', async () => {
      const files = ['file1.png', 'file2.png'];
      const res = await service.getReceiptDocumentUploadUrl('entity', 'tx', files);
      expect(res.length).toBe(2);
      expect(res[0]).toHaveProperty('uploadUrl', 'signed-url');
      expect(res[0]).toHaveProperty('fileName', 'file1.png');
      expect(res[0]).toHaveProperty('documentUuid', 'uuid-1234.png');
    });

    it('throws error on internal error', async () => {
      jest.spyOn(service, 'handleMappingOfDocumentUploadUrls').mockImplementation(() => {
        throw new Error('fail');
      });
      await expect(service.getReceiptDocumentUploadUrl('entity', 'tx', ['file.png'])).rejects.toThrow(
        'Error when fetching upload url',
      );
    });
  });

  describe('removeReceiptDocument', () => {
    it('calls S3 delete and cbsService handleDebitCardTransactionAnnotation', async () => {
      mockS3Client.deleteObject.mockResolvedValueOnce({});
      mockCoreBankingService.handleDebitCardTransactionAnnotation.mockResolvedValueOnce({});

      const res = await service.removeReceiptDocument('entity', 'tx', 'file.png');

      expect(mockS3Client.deleteObject).toHaveBeenCalledWith({
        Bucket: 'processed-bucket',
        Key: 'entity/tx/file.png',
      });
      expect(mockCoreBankingService.handleDebitCardTransactionAnnotation).toHaveBeenCalledWith(
        DebitCardTransactionAnnotationOperation.REMOVE_ATTACHMENT,
        'tx',
        { entityUuid: 'entity', fileName: 'file.png' },
      );
      expect(res).toBe(true);
    });

    it('throws error if S3 delete fails', async () => {
      mockS3Client.deleteObject.mockRejectedValueOnce(new Error('fail'));
      await expect(service.removeReceiptDocument('entity', 'tx', 'file.png')).rejects.toThrow(
        'Error when trying to remove receipt document',
      );
    });
  });

  describe('getReceiptDocumentDownloadUrl', () => {
    beforeEach(() => {
      jest.spyOn(service, 'getExistingDocumentNames').mockResolvedValue(['entity/tx/file1.png']);
      jest.spyOn(service as any, 'getMetaDataFromObject').mockResolvedValue({
        Metadata: { metadata: JSON.stringify({ fileName: encodeURIComponent('file1.png') }) },
        ContentType: 'image/png',
      });
      mockS3Client.getSignedUrl.mockResolvedValue('signed-download-url');
    });

    it('returns download url info for documents', async () => {
      const res = await service.getReceiptDocumentDownloadUrl('entity', 'tx');
      expect(res[0]).toMatchObject({
        downloadUrl: 'signed-download-url',
        fileName: 'file1.png',
        documentUuid: 'file1.png',
        contentType: 'image/png',
      });
    });

    it('throws error if underlying call fails', async () => {
      jest.spyOn(service, 'getExistingDocumentNames').mockRejectedValueOnce(new Error('fail'));
      await expect(service.getReceiptDocumentDownloadUrl('entity', 'tx')).rejects.toThrow(
        'Error when trying to get receipt document download url',
      );
    });
  });

  describe('private methods', () => {
    describe('getPreSignedUrlForUpload', () => {
      it('calls S3 getSignedUrl with correct params and returns url', async () => {
        mockS3Client.getSignedUrl.mockResolvedValueOnce('signed-upload-url');

        const result = await (service as any).getPreSignedUrlForUpload('test-bucket', 'some/key', { test: 'meta' });

        expect(mockS3Client.getSignedUrl).toHaveBeenCalledWith(
          {
            Bucket: 'test-bucket',
            Key: 'some/key',
            Metadata: {
              metadata: JSON.stringify({ test: 'meta' }),
            },
          },
          'putObject',
        );
        expect(result).toBe('signed-upload-url');
      });

      it('calls S3 getSignedUrl without metadata', async () => {
        mockS3Client.getSignedUrl.mockResolvedValueOnce('signed-upload-url');

        const result = await (service as any).getPreSignedUrlForUpload('test-bucket', 'some/key');

        expect(mockS3Client.getSignedUrl).toHaveBeenCalledWith(
          {
            Bucket: 'test-bucket',
            Key: 'some/key',
          },
          'putObject',
        );
        expect(result).toBe('signed-upload-url');
      });
    });

    describe('getMetaDataFromObject', () => {
      it('calls S3 headObject and returns Metadata and ContentType', async () => {
        mockS3Client.headObject.mockResolvedValueOnce({
          Metadata: { foo: 'bar' },
          ContentType: 'image/png',
        });

        const result = await (service as any).getMetaDataFromObject('test-bucket', 'some/key');

        expect(mockS3Client.headObject).toHaveBeenCalledWith({
          Bucket: 'test-bucket',
          Key: 'some/key',
        });
        expect(result).toEqual({
          Metadata: { foo: 'bar' },
          ContentType: 'image/png',
        });
      });
    });
  });
});
