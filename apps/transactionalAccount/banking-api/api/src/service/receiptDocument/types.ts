export interface AttachmentDocUploadedMetaData {
  fileName?: string;
  originalFileName?: string;
}

export interface JsonObject<T> {
  [key: string]: T;
}

export interface ReceiptDocumentDownload {
  downloadUrl: string;
  contentType?: string;
  fileName: string;
  documentUuid: string;
}

export interface ReceiptDocumentUpload {
  uploadUrl: string;
  fileName: string;
  documentUuid: string;
}

export type MediaTypeDictionary<T extends string, U> = {
  [K in T]: U;
};

export const convertedImageTypeMappings = (): MediaTypeDictionary<string, string> =>
  ({
    '.jpg': '.png',
    '.jpeg': '.png',
    '.jpe': '.png',
    '.png': '.png',
    '.webp': '.png',
    '.pdf': '.pdf',
    '.tiff': '.png',
    '.tif': '.png',
    '.avif': '.png',
    '.heic': '.png',
    '.heif': '.png',
  } as MediaTypeDictionary<string, string>);
