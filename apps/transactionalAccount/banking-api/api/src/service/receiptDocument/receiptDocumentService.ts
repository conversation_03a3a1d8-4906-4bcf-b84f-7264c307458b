/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { debug, error, warn } from '@npco/component-bff-core/dist/utils/logger';

import { type ListObjectsV2CommandInput, type ListObjectsV2CommandOutput } from '@aws-sdk/client-s3';
import path from 'path';
import { v4 } from 'uuid';

import type { CoreBankingService } from '../coreBanking/coreBankingService';
import { DebitCardTransactionAnnotationOperation } from '../coreBanking/type';
import type { EnvironmentService } from '../environment/environmentService';

import {
  convertedImageTypeMappings,
  type AttachmentDocUploadedMetaData,
  type JsonObject,
  type ReceiptDocumentDownload,
  type ReceiptDocumentUpload,
} from './types';

export class ReceiptDocumentService {
  private readonly s3: S3Client;

  constructor(protected readonly envService: EnvironmentService, private readonly cbsService: CoreBankingService) {
    this.s3 = new S3Client(envService.awsRegion || 'ap-southeast-2');
  }

  getExistingDocumentNames = async (entityUuid: string, transactionUuid: string): Promise<string[]> => {
    const params: ListObjectsV2CommandInput = {
      Bucket: this.envService.receiptDocProcessedBucket,
      MaxKeys: 1000,
      Prefix: `${entityUuid}/${transactionUuid}/`,
    };
    let keys: string[] = [];
    const docs: ListObjectsV2CommandOutput = await this.s3.listObjectsV2(params);
    if (docs.Contents) {
      const unfilteredKeys = docs.Contents.map((doc) => doc.Key ?? '');
      keys = unfilteredKeys.filter((a) => a !== '' && a !== `${entityUuid}/${transactionUuid}/`);
    }
    return keys;
  };

  handleMappingOfDocumentUploadUrls = (
    promises: string[],
    fileNames: string[],
    predefinedUuids: string[],
  ): Array<ReceiptDocumentUpload> => {
    const fulfilledPromises: Array<ReceiptDocumentUpload> = [];
    promises.forEach((url, index) => {
      fulfilledPromises.push({
        uploadUrl: url,
        fileName: fileNames[index]!,
        documentUuid: predefinedUuids[index]!,
      });
    });
    return fulfilledPromises;
  };

  validateReceiptDocumentUploadUrls = (entityUuid: string, fileNames: Array<string>) => {
    if (fileNames.length > this.envService.maxReceiptDocumentUploadsUrls) {
      warn(
        `Entity ${entityUuid} tried to get ${fileNames.length} upload urls to upload in S3 
        (max is ${this.envService.maxReceiptDocumentUploadsUrls})`,
        entityUuid,
      );
      throw new Error(`You cannot upload more than ${this.envService.maxReceiptDocumentUploadsUrls} files at once`);
    }
    const maxFileNameLength = fileNames.find((a) => a.length > this.envService.maxReceiptDocumentFileNameLength);
    if (maxFileNameLength) {
      throw new Error(
        `You cannot have a file name over ${this.envService.maxReceiptDocumentFileNameLength} characters (includes file extension)`,
      );
    }
  };

  getPredefinedFileExt = (fileName: string) => {
    const fileExt = path.parse(fileName).ext.toLocaleLowerCase();
    const predefinedFileExt = convertedImageTypeMappings()[fileExt] ?? undefined;
    if (!predefinedFileExt) {
      throw new Error(`Cannot find extension type with file ${fileExt}`);
    }
    return predefinedFileExt;
  };

  updatedFileExtension = (fileNames: Array<string>): Array<string> => {
    const trimmedFileNames = fileNames.map((fileName) => {
      const currentFileExtension = path.parse(fileName).ext.toLocaleLowerCase();
      const newFileExtension = this.getPredefinedFileExt(fileName);
      const updatedFileName = fileName.slice(0, fileName.length - currentFileExtension.length);
      return `${updatedFileName}${newFileExtension}`;
    });
    return trimmedFileNames;
  };

  getReceiptDocumentUploadUrl = async (
    entityUuid: string,
    transactionUuid: string,
    fileNames: Array<string>,
  ): Promise<Array<ReceiptDocumentUpload>> => {
    this.validateReceiptDocumentUploadUrls(entityUuid, fileNames);
    try {
      const maxLengthFileNames = this.trimFileNames(fileNames);
      const updatedfileNameExt = this.updatedFileExtension(maxLengthFileNames);
      // We predefine the url so the frontend can remove any images without having to refresh the page
      // This will also be the same key value in the processed bucket
      const predefinedUuids = maxLengthFileNames.map((file: any) => `${v4()}${this.getPredefinedFileExt(file)}`);
      const bucket = this.envService.rawReceiptDocUploadsBucket;
      const promises = await Promise.all(
        updatedfileNameExt.map((fileName, index) => {
          const key = `${entityUuid}/${transactionUuid}/${predefinedUuids[index]}`;
          return this.getPreSignedUrlForUpload(bucket, key, {
            fileName: encodeURIComponent(fileName),
            originalFileName: encodeURIComponent(maxLengthFileNames[index]!),
          });
        }),
      );

      return this.handleMappingOfDocumentUploadUrls(promises, fileNames, predefinedUuids);
    } catch (e) {
      error(`getReceiptDocumentUploadUrl error is :${e}`, entityUuid);
      throw new Error('Error when fetching upload url');
    }
  };

  removeReceiptDocument = async (entityUuid: string, transactionUuid: string, fileName: string): Promise<boolean> => {
    try {
      const params = {
        Bucket: this.envService.receiptDocProcessedBucket,
        Key: `${entityUuid}/${transactionUuid}/${fileName}`,
      };
      await this.s3.deleteObject(params);

      const body = {
        entityUuid,
        fileName,
      };
      await this.cbsService.handleDebitCardTransactionAnnotation(
        DebitCardTransactionAnnotationOperation.REMOVE_ATTACHMENT,
        transactionUuid,
        body,
      );
      return true;
    } catch (e) {
      error(e, entityUuid);
      throw new Error('Error when trying to remove receipt document');
    }
  };

  getReceiptDocumentDownloadUrl = async (
    entityUuid: string,
    transactionUuid: string,
  ): Promise<ReceiptDocumentDownload[]> => {
    try {
      const documents = await this.getExistingDocumentNames(entityUuid, transactionUuid);
      return await Promise.all(
        documents.map(async (documentPath) => {
          const getObjectParams = {
            Bucket: this.envService.receiptDocProcessedBucket,
            Key: `${documentPath}`,
          };
          const splitKey = documentPath.split('/');
          const documentUuid = splitKey[2];
          let documentFileName = splitKey[2];
          const signedUrlParams = {
            ...getObjectParams,
            Expires: 7200, // 2 hour expiry
          };
          const { ContentType, Metadata } = await this.getMetaDataFromObject(
            this.envService.receiptDocProcessedBucket,
            documentPath,
          );
          const metaData = Metadata?.metadata;
          if (metaData) {
            debug(`getReceiptDocumentDownloadUrl metaData is -> ${JSON.stringify(metaData)} for key: ${documentPath}`);
            const parsedMetaData: AttachmentDocUploadedMetaData = JSON.parse(metaData);
            const { fileName } = parsedMetaData;
            if (fileName) {
              documentFileName = decodeURIComponent(fileName);
            }
          }
          const result = await this.s3.getSignedUrl(signedUrlParams, 'getObject');
          return {
            ...(ContentType ? { contentType: ContentType } : {}),
            downloadUrl: result,
            fileName: documentFileName,
            documentUuid,
          } as ReceiptDocumentDownload;
        }),
      );
    } catch (e) {
      error(e, entityUuid);
      throw new Error('Error when trying to get receipt document download url');
    }
  };

  private readonly trimFileNames = (fileNames: Array<string>, maxCharactersPerFileName = 100): Array<string> => {
    const trimmedFileNames = fileNames.map((fileName) => {
      const fileExtension = path.parse(fileName).ext.toLocaleLowerCase();
      if (fileName.length > maxCharactersPerFileName) {
        const slicedFileName = fileName.slice(0, maxCharactersPerFileName - fileExtension.length);
        return `${slicedFileName}${fileExtension}`;
      }
      return fileName;
    });
    return trimmedFileNames;
  };

  private readonly getPreSignedUrlForUpload = async (
    bucket: string,
    key: string,
    metadata?: JsonObject<any>,
  ): Promise<string> => {
    const params = {
      Bucket: bucket,
      Key: key,
      ...(metadata
        ? {
            Metadata: {
              metadata: JSON.stringify(metadata),
            },
          }
        : {}),
    };
    return this.s3.getSignedUrl(params, 'putObject');
  };

  private readonly getMetaDataFromObject = async (
    bucket: string,
    key: string,
  ): Promise<{ Metadata?: Record<string, string>; ContentType?: string }> => {
    const getObjectParams = {
      Bucket: bucket,
      Key: key,
    };
    const { Metadata, ContentType } = await this.s3.headObject(getObjectParams);
    return { Metadata, ContentType };
  };
}
