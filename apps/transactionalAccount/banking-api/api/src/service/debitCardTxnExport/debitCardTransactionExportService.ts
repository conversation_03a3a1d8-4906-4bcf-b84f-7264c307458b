import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { InvalidRequest } from '@npco/component-bff-core/dist/error';

import type { Readable } from 'stream';

import type { EnvironmentService } from '../environment/environmentService';
import type { DebitCardTransactionSummaryPdfService } from '../pdfService/debitCardTransactionSummaryPdfService';

import type { DebitCardTransactionExportDataService } from './debitCardTransactionExportDataService';

type S3PresignedDownloadLink = {
  downloadLink: string;
  expire: Date;
};

export class DebitCardTransactionExportService {
  private static readonly FILE_EXPIRY = 365 * 24 * 60 * 60 * 1000; // 1 year

  private static readonly PRESIGNED_EXPIRY = 60 * 60; // 60 minutes

  constructor(
    private readonly debitCardTransactionExportDataService: DebitCardTransactionExportDataService,
    private readonly pdfService: DebitCardTransactionSummaryPdfService,
    private readonly envService: EnvironmentService,
  ) {}

  private static isValidTimeZone(timeZone: string) {
    try {
      Intl.DateTimeFormat(undefined, { timeZone });
      return true;
    } catch {
      return false;
    }
  }

  async getSummaryExport(
    entityUuid: string,
    transactionUuid: string,
    timeZone = 'Australia/Melbourne',
  ): Promise<S3PresignedDownloadLink> {
    if (!DebitCardTransactionExportService.isValidTimeZone(timeZone)) {
      throw new InvalidRequest('Invalid timezone');
    }
    const summaryData = await this.debitCardTransactionExportDataService.getDebitCardTransactionSummaryData(
      entityUuid,
      transactionUuid,
      timeZone,
    );
    const pdfExport = await this.pdfService.createDocument(summaryData);

    const now = new Date();
    const fileExpiry = new Date(now.getTime() + DebitCardTransactionExportService.FILE_EXPIRY);
    const presignedUrlExpiry = new Date(now.getTime() + DebitCardTransactionExportService.PRESIGNED_EXPIRY);

    const filePath = 'dcaTransactionSummary';
    const key = `${filePath}/Transaction summary - ${transactionUuid} - ${new Date().getTime()}.pdf`;

    const signedUrl = await this.uploadToS3(
      pdfExport,
      key,
      fileExpiry,
      DebitCardTransactionExportService.PRESIGNED_EXPIRY,
    );

    return {
      downloadLink: signedUrl,
      expire: presignedUrlExpiry,
    };
  }

  private async uploadToS3(content: Readable, key: string, fileExpiry: Date, presignedUrlExpiry: number) {
    const s3 = new S3Client();

    await s3.upload({
      Body: content,
      Bucket: this.envService.debitCardTransactionSummaryBucket,
      Key: key,
      Expires: fileExpiry,
    });

    return s3.getSignedUrl({
      Bucket: this.envService.debitCardTransactionSummaryBucket,
      Key: key,
      Expires: presignedUrlExpiry,
    });
  }
}
