import { InvalidRequest, ServerError } from '@npco/component-bff-core/dist/error';
import { ContactType } from '@npco/component-dto-addressbook/dist';
import { EntityType, ISO4217 } from '@npco/component-dto-core/dist';
import type { DebitCardAccount } from '@npco/component-dto-issuing-account/dist';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction/dist';
import { DebitCardTransactionStatusV2, DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction/dist';
import { PaymentInstrumentStatus, PaymentInstrumentType } from '@npco/component-dto-payment-instrument/dist/const';
import type { Merchant } from '@npco/component-dto-richdata/dist';

import { mock, instance, when, reset, anything } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import { DebitCardAccountService } from '../debitCardAccountFromMpCommon/debitCardAccountService';
import { DebitCardAccountTxnService } from '../debitCardAccountFromMpCommon/debitCardAccountTxn/debitCardAccountTxnService';
import { BankingDynamoDbService } from '../dynamodb/bankingDynamoDbService';
import { EntityQueryService } from '../entity/entityQueryService';
import { PaymentInstrumentQueryService } from '../paymentInstrument/paymentInstrumentQueryService';
import type { DebitCardTransactionSummaryPdfData } from '../pdfService/debitCardTransactionSummaryPdfService';
import { DebitCardTransactionSummaryPdfService } from '../pdfService/debitCardTransactionSummaryPdfService';
import { MerchantService } from '../richData/merchantService';

import { DebitCardTransactionExportDataService } from './debitCardTransactionExportDataService';

describe('getSummaryExport test suite', () => {
  const mockPdfService = mock(DebitCardTransactionSummaryPdfService);
  const mockDebitCardAccountService = mock(DebitCardAccountService);
  const mockDebitCardAccountTxnService = mock(DebitCardAccountTxnService);
  const mockPaymentInstrumentService = mock(PaymentInstrumentQueryService);

  const mockRichDataService = mock(MerchantService);
  const mockDynamodbService = mock(BankingDynamoDbService);
  const mockEntityQueryService = mock(EntityQueryService);

  const service = new DebitCardTransactionExportDataService(
    instance(mockRichDataService),
    instance(mockDynamodbService),
    instance(mockDebitCardAccountService),
    instance(mockDebitCardAccountTxnService),
    instance(mockPaymentInstrumentService),
    instance(mockEntityQueryService),
  );

  const entityUuid = 'aaaa';
  const transactionUuid = 'bbbb';
  const merchantUuid = 'cccc';

  const now = new Date();
  const entity: any = {
    id: entityUuid,
    name: uuidv4(),
    tradingName: uuidv4(),
    manualEntry: false,
    registeredAddress: {},
    businessAddress: {},
    estimatedAnnualRevenue: 1,
    remitToCard: false,
    debitCardAccountUuid: uuidv4(),
    type: EntityType.INDIVIDUAL,
  };
  const contact = {
    id: uuidv4(),
    contactType: ContactType.PERSON,
    firstName: uuidv4(),
    lastName: uuidv4(),
    address: {},
    businessName: uuidv4(),
    abn: uuidv4(),
    acn: uuidv4(),
  };
  const paymentInstrument: any = {
    id: uuidv4(),
    contactUuid: contact.id,
    entityUuid: entity.id,
    bankAccountDetails: {
      bsb: 'bsb',
      account: 'account',
      name: 'name',
    },
    status: PaymentInstrumentStatus.ACTIVE,
    type: PaymentInstrumentType.BSB,
  };
  const merchant: Merchant = {
    id: merchantUuid,
    name: uuidv4(),
    address: {},
    abn: uuidv4(),
    updatedTime: 0,
  };
  const transaction: DebitCardTransactionV2 = {
    id: uuidv4(),
    entityUuid,
    payeeDetails: {
      paymentInstrumentUuid: paymentInstrument.id,
    },
    debitCardAccountUuid: uuidv4(),
    status: DebitCardTransactionStatusV2.APPROVED,
    type: DebitCardTransactionTypeV2.PURCHASE,
    timestamp: now.toISOString(),
    merchantId: merchantUuid,
    amount: {
      value: '6000',
      currency: ISO4217.AUD,
    },
    updatedTime: now.getTime(),
  };
  const account = {
    name: 'accountName',
  } as DebitCardAccount;

  const timeZone = 'Australia/Melbourne';
  const pdfDate = new Intl.DateTimeFormat('en-AU', { timeZone }).format(now);
  const pdfTime = new Intl.DateTimeFormat('en-AU', {
    timeZone,
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    timeZoneName: 'short',
  }).format(now);

  beforeEach(() => {
    when(mockDebitCardAccountService.getDebitCardAccount(entityUuid, transaction.debitCardAccountUuid)).thenResolve(
      account,
    );
  });

  afterEach(() => {
    reset(mockPdfService);
    reset(mockDebitCardAccountTxnService);
    reset(mockDebitCardAccountService);
    reset(mockRichDataService);
  });

  it.each([DebitCardTransactionTypeV2.PURCHASE, DebitCardTransactionTypeV2.PURCHASE_CNP])(
    'should return s3 download link with merchant data for %p',
    async (type) => {
      const pdfData: DebitCardTransactionSummaryPdfData = {
        entityCurrency: ISO4217.AUD,
        from: {
          name: entity.tradingName,
          address: entity.businessAddress,
          abn: entity.abn,
          acn: entity.acn,
        },
        to: {
          name: merchant.name,
          address: merchant.address,
          abn: merchant.abn,
        },
        accountName: account.name,
        completedDate: pdfDate,
        time: pdfTime,
        description: transaction.note || '',
        paidOut: transaction.amount?.value || '',
        paidIn: '0',
      };

      const testTransaction = { ...transaction, type };

      when(mockDebitCardAccountTxnService.getDebitCardAccountTxn(entityUuid, transactionUuid)).thenResolve(
        testTransaction,
      );

      when(mockEntityQueryService.getEntity(entityUuid)).thenResolve(entity);
      // mockEntityQueryService.getEntity.mockResolvedValue(entity);
      when(mockRichDataService.getMerchant(merchantUuid)).thenResolve(merchant);

      const result = await service.getDebitCardTransactionSummaryData(entityUuid, transactionUuid, timeZone);
      expect(result).toEqual(pdfData);
    },
  );

  it.each([DebitCardTransactionTypeV2.PURCHASE, DebitCardTransactionTypeV2.PURCHASE_CNP])(
    'should return s3 download link with missing merchant data for %p',
    async (type) => {
      const pdfData: DebitCardTransactionSummaryPdfData = {
        entityCurrency: ISO4217.AUD,
        from: {
          name: entity.tradingName,
          address: entity.businessAddress,
          abn: entity.abn,
          acn: entity.acn,
        },
        to: {
          name: undefined,
          address: undefined,
          abn: undefined,
        },
        accountName: account.name,
        completedDate: pdfDate,
        time: pdfTime,
        description: transaction.note || '',
        paidOut: transaction.amount?.value || '',
        paidIn: '0',
      };

      const testTransaction = { ...transaction, type };

      when(mockDebitCardAccountTxnService.getDebitCardAccountTxn(entityUuid, transactionUuid)).thenResolve(
        testTransaction,
      );
      when(mockEntityQueryService.getEntity(entityUuid)).thenResolve(entity);
      when(mockRichDataService.getMerchant(merchantUuid)).thenResolve(null);

      const result = await service.getDebitCardTransactionSummaryData(entityUuid, transactionUuid, timeZone);
      expect(result).toEqual(pdfData);
    },
  );

  it.each([
    [
      'personal contact data',
      {
        testContact: {
          ...contact,
          contactType: ContactType.PERSON,
        },
        contactName: `${contact.firstName} ${contact.lastName}`,
      },
    ],
    [
      'personal contact data with missing first name and last name',
      {
        testContact: {
          ...contact,
          firstName: undefined,
          lastName: undefined,
          contactType: ContactType.PERSON,
        },
        contactName: undefined,
      },
    ],
    [
      'business contact data',
      {
        testContact: {
          ...contact,
          contactType: ContactType.BUSINESS,
        },
        contactName: contact.businessName,
      },
    ],
  ])('should return s3 download with %p for "DE_OUT"', async (_, { testContact, contactName }) => {
    const pdfData: DebitCardTransactionSummaryPdfData = {
      entityCurrency: ISO4217.AUD,
      from: {
        name: entity.tradingName,
        address: entity.businessAddress,
        abn: entity.abn,
        acn: entity.acn,
      },
      to: {
        name: contactName,
        address: {
          street1: undefined,
          suburb: undefined,
          state: undefined,
          postcode: undefined,
        },
        abn: contact.abn,
        acn: contact.acn,
        bsb: 'bsb',
        accountName: 'name',
        accountNumber: 'account',
      },
      accountName: account.name,
      completedDate: pdfDate,
      time: pdfTime,
      description: transaction.note || '',
      paidOut: transaction.amount?.value || '',
      paidIn: '0',
    };
    const testTransaction = { ...transaction, type: DebitCardTransactionTypeV2.DE_OUT };

    when(mockDebitCardAccountTxnService.getDebitCardAccountTxn(entityUuid, transactionUuid)).thenResolve(
      testTransaction,
    );
    when(mockEntityQueryService.getEntity(entityUuid)).thenResolve(entity);
    when(
      mockPaymentInstrumentService.getPaymentInstrument(
        entityUuid,
        testTransaction.payeeDetails?.paymentInstrumentUuid ?? '',
      ),
    ).thenResolve(paymentInstrument);

    when(mockDynamodbService.queryIdByType(anything(), anything(), anything(), anything())).thenResolve({
      Items: [testContact],
    } as any);

    const result = await service.getDebitCardTransactionSummaryData(entityUuid, transactionUuid, timeZone);
    expect(result).toEqual(pdfData);
  });

  it('should return s3 download link with missing contact data for DE_OUT', async () => {
    const pdfData: DebitCardTransactionSummaryPdfData = {
      entityCurrency: ISO4217.AUD,
      from: {
        name: entity.tradingName,
        address: entity.businessAddress,
        abn: entity.abn,
        acn: entity.acn,
      },
      to: {
        name: undefined,
        address: {
          street1: undefined,
          suburb: undefined,
          state: undefined,
          postcode: undefined,
        },
        abn: undefined,
        acn: undefined,
        bsb: undefined,
        accountName: undefined,
        accountNumber: undefined,
      },
      accountName: account.name,
      completedDate: pdfDate,
      time: pdfTime,
      description: transaction.note || '',
      paidOut: transaction.amount?.value || '',
      paidIn: '0',
    };
    const testTransaction = { ...transaction, type: DebitCardTransactionTypeV2.DE_OUT };

    when(mockDebitCardAccountTxnService.getDebitCardAccountTxn(entityUuid, transactionUuid)).thenResolve(
      testTransaction,
    );
    when(mockEntityQueryService.getEntity(entityUuid)).thenResolve(entity);

    when(
      mockPaymentInstrumentService.getPaymentInstrument(
        entityUuid,
        testTransaction.payeeDetails?.paymentInstrumentUuid ?? '',
      ),
    ).thenResolve({ contactUuid: uuidv4() } as any);

    when(mockDynamodbService.queryIdByType(anything(), anything(), anything(), anything())).thenResolve({
      Items: [],
    } as any);

    const result = await service.getDebitCardTransactionSummaryData(entityUuid, transactionUuid, timeZone);
    expect(result).toEqual(pdfData);
  });

  it.each([
    [
      'unsupported type',
      {
        testTransaction: { ...transaction, type: DebitCardTransactionTypeV2.DE_IN } as DebitCardTransactionV2,
        error: new InvalidRequest('Unsupported type'),
      },
    ],
    [
      'missing amount value',
      {
        testTransaction: {
          ...transaction,
          type: DebitCardTransactionTypeV2.DE_IN,
          amount: undefined,
        } as any as DebitCardTransactionV2,
        error: new ServerError('Transaction is missing amount value'),
      },
    ],
  ])('it should throw error for transactions with %p', async (_, { testTransaction, error }) => {
    when(mockDebitCardAccountTxnService.getDebitCardAccountTxn(entityUuid, transactionUuid)).thenResolve(
      testTransaction,
    );
    when(mockEntityQueryService.getEntity(entityUuid)).thenResolve(entity);
    when(mockRichDataService.getMerchant(merchantUuid)).thenResolve(merchant);

    await expect(
      service.getDebitCardTransactionSummaryData(entityUuid, transactionUuid, timeZone),
    ).rejects.toThrowError(error);
  });

  it('should throw error if unable to find transaction', async () => {
    when(mockDebitCardAccountTxnService.getDebitCardAccountTxn(entityUuid, transactionUuid)).thenResolve(null);
    await expect(
      service.getDebitCardTransactionSummaryData(entityUuid, transactionUuid, timeZone),
    ).rejects.toThrowError(new InvalidRequest('Unable to find transaction'));
  });

  it('should throw error for invalid IANA timestamp', async () => {
    when(mockDebitCardAccountTxnService.getDebitCardAccountTxn(entityUuid, transactionUuid)).thenResolve(transaction);
    await expect(
      service.getDebitCardTransactionSummaryData(entityUuid, transactionUuid, 'Henesy/Victoria Island'),
    ).rejects.toThrowError(new InvalidRequest('Invalid time zone specified: Henesy/Victoria Island'));
  });
});
