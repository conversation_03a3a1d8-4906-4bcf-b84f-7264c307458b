import { ContactType } from '@npco/component-dto-addressbook/dist';

import { getContactName } from './utils';

describe('contactUtils test suite', () => {
  const contact = {
    contactType: ContactType.PERSON,
    firstName: 'firstName',
    lastName: 'lastName',
    businessName: 'businessName',
  };

  it('getContactName', () => {
    expect(getContactName(contact)).toBe('firstName lastName');
    expect(getContactName({ ...contact, firstName: undefined })).toBeUndefined();
    expect(getContactName({ ...contact, lastName: undefined })).toBeUndefined();
    expect(getContactName({ ...contact, contactType: ContactType.BUSINESS })).toBe('businessName');
    expect(getContactName({ ...contact, contactType: ContactType.BUSINESS, businessName: undefined })).toBeUndefined();
    expect(getContactName({ ...contact, contactType: undefined })).toBe('businessName');
  });
});
