import { InvalidRequest } from '@npco/component-bff-core/dist/error';
import { ISO4217 } from '@npco/component-dto-core/dist/types';

import { PassThrough } from 'stream';
import { mock, instance, verify, when, deepEqual, reset } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import type { DebitCardTransactionSummaryPdfData } from '../pdfService/debitCardTransactionSummaryPdfService';
import { DebitCardTransactionSummaryPdfService } from '../pdfService/debitCardTransactionSummaryPdfService';

import { DebitCardTransactionExportDataService } from './debitCardTransactionExportDataService';
import { DebitCardTransactionExportService } from './debitCardTransactionExportService';

const mockUploadFn = jest.fn();
const mockGetSignedUrlFn = jest.fn().mockReturnValue('url');

jest.mock('@npco/component-bff-core/dist/aws/s3Client', () => ({
  S3Client: jest.fn().mockReturnValue({
    upload: () => mockUploadFn(),
    getSignedUrl: jest.fn().mockImplementation(() => mockGetSignedUrlFn()),
  }),
}));

describe('getSummaryExport test suite', () => {
  const mockDebitCardTransactionExportDataService = mock(DebitCardTransactionExportDataService);
  const mockPdfService = mock(DebitCardTransactionSummaryPdfService);
  const envService = {} as any;

  const service = new DebitCardTransactionExportService(
    instance(mockDebitCardTransactionExportDataService),
    instance(mockPdfService),
    envService,
  );

  const entityUuid = 'aaaa';
  const transactionUuid = 'bbbb';

  const now = Date.now();
  const timeZone = 'Australia/Melbourne';
  const pdfDate = new Intl.DateTimeFormat('en-AU', { timeZone }).format(now);
  const pdfTime = new Intl.DateTimeFormat('en-AU', {
    timeZone,
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    timeZoneName: 'short',
  }).format(now);

  afterEach(() => {
    reset(mockDebitCardTransactionExportDataService);
    reset(mockPdfService);
  });

  it('should return s3 download link', async () => {
    const pdfData: DebitCardTransactionSummaryPdfData = {
      entityCurrency: ISO4217.AUD,
      from: {
        name: uuidv4(),
        address: {
          street1: uuidv4(),
        },
        abn: uuidv4(),
        acn: uuidv4(),
      },
      to: {
        name: uuidv4(),
        address: {
          street1: uuidv4(),
        },
        abn: uuidv4(),
      },
      accountName: uuidv4(),
      completedDate: pdfDate,
      time: pdfTime,
      description: uuidv4(),
      paidOut: '90',
      paidIn: '0',
    };

    when(
      mockDebitCardTransactionExportDataService.getDebitCardTransactionSummaryData(
        entityUuid,
        transactionUuid,
        timeZone,
      ),
    ).thenResolve(pdfData);
    when(mockPdfService.createDocument(deepEqual(pdfData))).thenResolve(new PassThrough());

    const result = await service.getSummaryExport(entityUuid, transactionUuid);

    verify(mockPdfService.createDocument(deepEqual(pdfData))).once();
    expect(result.downloadLink).toBe('url');
    expect(result.expire).toBeTruthy();
  });

  it('should throw error for invalid IANA timestamp', async () => {
    await expect(service.getSummaryExport(entityUuid, transactionUuid, 'Henesy/Victoria Island')).rejects.toThrowError(
      new InvalidRequest('Invalid timezone'),
    );
  });
});
