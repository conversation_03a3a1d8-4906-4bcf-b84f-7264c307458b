import { InvalidRequest, NotFoundError, ServerError } from '@npco/component-bff-core/dist/error';
import { info } from '@npco/component-bff-core/dist/utils/logger';
import type { ContactBaseEventDto } from '@npco/component-dto-addressbook/dist/contactBaseEventDto';
import { DbRecordType, ISO4217 } from '@npco/component-dto-core/dist';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction/dist';

import type { DebitCardAccountService } from '../debitCardAccountFromMpCommon/debitCardAccountService';
import type { DebitCardAccountTxnService } from '../debitCardAccountFromMpCommon/debitCardAccountTxn/debitCardAccountTxnService';
import type { BankingDynamoDbService } from '../dynamodb/bankingDynamoDbService';
import type { EntityQueryService } from '../entity/entityQueryService';
import type { PaymentInstrumentQueryService } from '../paymentInstrument/paymentInstrumentQueryService';
import type { DebitCardTransactionSummaryPdfData } from '../pdfService/debitCardTransactionSummaryPdfService';
import type { MerchantService } from '../richData/merchantService';

import { getContactName } from './utils';

export class DebitCardTransactionExportDataService {
  constructor(
    private readonly richDataService: MerchantService,
    private readonly dynamodbService: BankingDynamoDbService,
    private readonly debitCardAccountService: DebitCardAccountService,
    private readonly debitCardAccountTxnService: DebitCardAccountTxnService,
    private readonly paymentInstrumentService: PaymentInstrumentQueryService,
    private readonly entityQueryService: EntityQueryService,
  ) {}

  private static getDateAndTime(timestamp: Date, timeZone: string) {
    const date = new Intl.DateTimeFormat('en-AU', { timeZone }).format(timestamp);
    const time = new Intl.DateTimeFormat('en-AU', {
      timeZone,
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
      timeZoneName: 'short',
    }).format(timestamp);

    return { date, time };
  }

  async getDebitCardTransactionSummaryData(
    entityUuid: string,
    transactionUuid: string,
    timezone: string,
  ): Promise<DebitCardTransactionSummaryPdfData> {
    const transaction = await this.debitCardAccountTxnService.getDebitCardAccountTxn(entityUuid, transactionUuid);
    if (!transaction) {
      throw new InvalidRequest('Unable to find transaction');
    }

    info(`Transaction ${JSON.stringify(transaction)}`);
    // get debit card account without saving account
    const debitCardAccount = await this.debitCardAccountService.getDebitCardAccount(
      entityUuid,
      transaction.debitCardAccountUuid,
    );

    const { entityUuid: transactionEntityUuid, payeeDetails, merchantId, type, timestamp, note, amount } = transaction;
    if (!amount?.value) {
      throw new ServerError('Transaction is missing amount value');
    }

    const entity = await this.entityQueryService.getEntity(transactionEntityUuid);

    if (!entity) {
      throw new NotFoundError('Entity not found', transactionEntityUuid);
    }

    info(`Entity ${JSON.stringify(entity)}`);

    const { date, time } = DebitCardTransactionExportDataService.getDateAndTime(new Date(timestamp), timezone);
    const baseData = {
      from: {
        name: entity.tradingName?.length ? entity.tradingName : entity.name,
        address: entity.businessAddress ?? entity.registeredAddress,
        abn: entity.abn,
        acn: entity.acn,
      },
      accountName: debitCardAccount.name,
      completedDate: date,
      time,
      description: note ?? '',
      paidOut: amount?.value,
      paidIn: '0',
      entityCurrency: (entity.currency ?? ISO4217.AUD) as ISO4217,
    };

    switch (type) {
      case DebitCardTransactionTypeV2.DE_OUT: {
        const paymentInstrumentUuid = payeeDetails?.paymentInstrumentUuid;
        const paymentInstrument = await (paymentInstrumentUuid
          ? this.paymentInstrumentService.getPaymentInstrument(entityUuid, paymentInstrumentUuid)
          : null);
        info(`payment instrument: ${JSON.stringify(paymentInstrument)}: `, paymentInstrumentUuid);

        const contact = await (paymentInstrument?.contactUuid
          ? this.getContact(entityUuid, paymentInstrument.contactUuid)
          : null);
        info(`payment instrument contact: ${JSON.stringify(contact)}: `, paymentInstrument?.contactUuid);

        return {
          ...baseData,
          to: {
            name: getContactName({ ...contact, contactType: contact?.contactType }),
            address: {
              street1: contact?.address?.street,
              suburb: contact?.address?.suburb,
              state: contact?.address?.state,
              postcode: contact?.address?.postcode,
            },
            abn: contact?.abn,
            acn: contact?.acn,
            bsb: paymentInstrument?.bankAccountDetails?.bsb,
            accountName: paymentInstrument?.bankAccountDetails?.name,
            accountNumber: paymentInstrument?.bankAccountDetails?.account,
          },
        };
      }
      case DebitCardTransactionTypeV2.PURCHASE:
      case DebitCardTransactionTypeV2.PURCHASE_CNP: {
        const merchant = await (merchantId ? this.richDataService.getMerchant(merchantId) : null);
        return {
          ...baseData,
          to: {
            name: merchant?.name,
            address: merchant?.address,
            abn: merchant?.abn,
          },
        };
      }
      default:
        throw new InvalidRequest('Unsupported type');
    }
  }

  private readonly getContact = async (
    entityUuid: string,
    contactUuid: string,
  ): Promise<ContactBaseEventDto | undefined> => {
    const contact = await this.dynamodbService.queryIdByType(entityUuid, contactUuid, DbRecordType.CONTACT, {
      filterDeleted: true,
    });

    if (!contact.Items || contact.Items.length <= 0) {
      return undefined;
    }

    return contact.Items[0] as ContactBaseEventDto;
  };
}
