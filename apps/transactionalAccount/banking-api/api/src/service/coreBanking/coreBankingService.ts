import { InvalidRequest } from '@npco/component-bff-core/dist/error/graphQlError';
import { debug, error, info, logger, warn } from '@npco/component-bff-core/dist/utils/logger';
import type { Icon } from '@npco/component-dto-core/dist/types';
import { TransferErrorCode } from '@npco/component-dto-issuing-transaction/dist';

import type { AxiosError } from 'axios';

import { BaseApi } from '../api/baseApi';
import type { KnownErrorResponseData } from '../api/types';
import type { CbsValidateBPayResponse } from '../debitCardAccountTransaction/bpay/cbsValidateBPayResponse';
import type { ValidateBpayPaymentInput } from '../debitCardAccountTransaction/types';
import type { EnvironmentService } from '../environment/environmentService';

import { convertAxiosErrorToCbsGetBpayBillerDetailErrResp } from './bpay/convertAxiosErrorToCbsGetBpayBillerDetailResp';
import { convertAxiosErrorToCbsResponse } from './bpay/convertAxiosErrorToCbsResponse';
import { mapValidateBpayPaymentInputToCbsRequest } from './bpay/mapValidateBpayPaymentInputToCbsRequest';
import type {
  CbsBpayCommonErrorResponse,
  CbsGetBpayBillerDetailErrorResponse,
  CbsGetBpayBillerDetailSuccessResponse,
  CoreBankingGetBpayBillerDetailResponse,
} from './bpay/types';
import { convertAxiosErrorToCbsCreateSavingsAccountErrResp } from './savings/convertAxiosErrorToCreateSavingsAccountError';
import type {
  DebitCardAccountResponse,
  DebitCardResponse,
  CbsSubmitBpayPaymentRequestPayload,
  CbsSubmitBpayPaymentResponse,
  CoreBankingBillerSearchResponse,
  VelocityControlResult,
  SetVelocityControlInput,
  CreateNewDebitCardInput,
  UpdateVelocityControlInput,
  SavingsAccountBaseResponse,
  CreateSavingsAccountInput,
  SavingsAccountResponse,
  PersonaliseSavingsAccountInput,
  PersonaliseSavingsAccountResponse,
  CbsReportLostDebitCardInput,
  CbsCreateSavingsAccountV2Input,
  DebitCardTransactionAnnotationOperation,
} from './type';

export type CardOperationInput = {
  cardUuid: string;
  customerUuid: string;
};

export type CardSuspensionInput = {
  cardUuid: string;
  reason: string;
};

export const customErrorCode = [TransferErrorCode.DAILY_TRANSFER_LIMIT_EXCEEDED];

export const convertErrorResponse = (err?: CbsBpayCommonErrorResponse): Record<string, unknown> | undefined => {
  if (typeof err === 'string') {
    return undefined;
  }
  if (typeof err?.message === 'string') {
    return { message: err.message };
  }
  return err === undefined
    ? undefined
    : {
        success: err.success,
        error: err.error,
      };
};

export class CoreBankingService {
  private readonly api: BaseApi;

  constructor(private readonly envService: EnvironmentService) {
    this.api = new BaseApi(this.envService.cbsApiEndpoint);
  }

  async getBpayBillerDetail(billerCode: string, entityUuid: string): Promise<CoreBankingGetBpayBillerDetailResponse> {
    const path = `/bpay/biller/details/${billerCode}`;
    info(`Path ${path}`, entityUuid);

    return this.api.client
      .get<CbsGetBpayBillerDetailSuccessResponse>(path)
      .then(({ data }) => {
        info(`Response: ${JSON.stringify(data)}`, entityUuid);
        return data.data;
      })
      .catch((err: AxiosError<CbsGetBpayBillerDetailErrorResponse>) =>
        convertAxiosErrorToCbsGetBpayBillerDetailErrResp(err, entityUuid),
      );
  }

  async validateBpayPayment(input: ValidateBpayPaymentInput, entityUuid: string): Promise<CbsValidateBPayResponse> {
    const request = mapValidateBpayPaymentInputToCbsRequest(input);
    info(`Path ${request.path}`);
    info(`Body ${JSON.stringify(request.body)}`);

    const data: CbsValidateBPayResponse = await this.api.client
      .post<CbsValidateBPayResponse>(request.path, request.body)
      .then((e): CbsValidateBPayResponse => {
        info(`Response: ${JSON.stringify(e.data)}`, entityUuid);
        return e.data;
      })
      .catch((err: AxiosError<CbsValidateBPayResponse>): CbsValidateBPayResponse => {
        return convertAxiosErrorToCbsResponse(err, request, entityUuid);
      });

    debug(`Response ${JSON.stringify(data)}`);

    return data;
  }

  async createDebitCardAccount({
    entityUuid,
    tradingName,
  }: {
    entityUuid: string;
    tradingName: string;
  }): Promise<DebitCardAccountResponse> {
    const { data } = await this.api.post<DebitCardAccountResponse>({
      path: `/accounts`,
      body: { entityUuid, tradingName },
      aggregateId: entityUuid,
    });

    return data;
  }

  async personaliseDebitCardAccount({
    entityUuid,
    accountUuid,
    displayName,
    icon,
  }: {
    entityUuid: string;
    accountUuid: string;
    displayName: string;
    icon: Icon;
  }): Promise<DebitCardAccountResponse> {
    const { data } = await this.api.post<DebitCardAccountResponse>({
      path: `/accounts/${accountUuid}/display-name-icon`,
      body: { entityUuid, displayName, icon },
      aggregateId: accountUuid,
    });

    return data;
  }

  async closeDebitCardAccount({
    entityUuid,
    accountUuid,
  }: {
    entityUuid: string;
    accountUuid: string;
  }): Promise<DebitCardAccountResponse> {
    const { data } = await this.api.post<DebitCardAccountResponse>({
      path: `/accounts/${accountUuid}/close`,
      body: { entityUuid },
      aggregateId: accountUuid,
    });

    return data;
  }

  async suspendDebitCardAccount({
    accountUuid,
    reason,
  }: {
    accountUuid: string;
    reason: string;
  }): Promise<DebitCardAccountResponse> {
    const { data } = await this.api.post<DebitCardAccountResponse>({
      path: `/admin/accounts/${accountUuid}/suspend`,
      body: { reason },
      aggregateId: accountUuid,
    });

    return data;
  }

  async unsuspendDebitCardAccount({
    accountUuid,
    reason,
  }: {
    accountUuid: string;
    reason: string;
  }): Promise<DebitCardAccountResponse> {
    const { data } = await this.api.post<DebitCardAccountResponse>({
      path: `/admin/accounts/${accountUuid}/unsuspend`,
      body: { reason },
      aggregateId: accountUuid,
    });

    return data;
  }

  async createSavingsAccount({
    entityUuid,
    tfn,
    savingsAccountType,
    displayName,
  }: CreateSavingsAccountInput): Promise<SavingsAccountResponse> {
    debug(`create savings account: ${JSON.stringify({ entityUuid, savingsAccountType, displayName })}`);
    const response = await this.api.client
      .post<SavingsAccountResponse>(`/accounts/savings`, {
        entityUuid,
        tfn,
        savingsAccountType,
        displayName,
      })
      .then(({ data }) => {
        debug(JSON.stringify(data), entityUuid);
        return data;
      })
      .catch((e) => {
        return convertAxiosErrorToCbsCreateSavingsAccountErrResp(e, {
          aggregateId: entityUuid,
          sensitiveDataToStrip: [tfn],
        });
      });

    return response;
  }

  async createSavingsAccountV2(input: CbsCreateSavingsAccountV2Input): Promise<SavingsAccountResponse> {
    debug(
      `create savings account: ${JSON.stringify({
        entityUuid: input.entityUuid,
        savingsAccountType: input.savingsAccountType,
        displayName: input.displayName,
      })}`,
    );
    const response = await this.api.client
      .post<SavingsAccountResponse>(`/v2/accounts/savings`, input)
      .then(({ data }) => {
        debug(JSON.stringify(data), input.entityUuid);
        return data;
      })
      .catch((e) => {
        return convertAxiosErrorToCbsCreateSavingsAccountErrResp(e, {
          aggregateId: input.entityUuid,
          sensitiveDataToStrip: [],
        });
      });

    return response;
  }

  async personaliseSavingsAccount(
    accountUuid: string,
    body: PersonaliseSavingsAccountInput,
  ): Promise<PersonaliseSavingsAccountResponse> {
    const { data } = await this.api.put<PersonaliseSavingsAccountResponse>({
      path: `/accounts/savings/${accountUuid}/personalise`,
      body,
      aggregateId: accountUuid,
    });
    console.debug(JSON.stringify(data));

    return data;
  }

  async closeSavingsAccount({
    savingsAccountUuid,
    entityUuid,
  }: {
    savingsAccountUuid: string;
    entityUuid: string;
  }): Promise<SavingsAccountBaseResponse> {
    const { data } = await this.api.post<SavingsAccountBaseResponse>({
      path: `/accounts/savings/${savingsAccountUuid}/close`,
      body: { entityUuid },
      aggregateId: savingsAccountUuid,
    });

    return data;
  }

  async suspendSavingsAccount({
    savingsAccountUuid,
    reason,
  }: {
    savingsAccountUuid: string;
    reason: string;
  }): Promise<SavingsAccountBaseResponse> {
    const { data } = await this.api.post<SavingsAccountBaseResponse>({
      path: `/admin/accounts/savings/${savingsAccountUuid}/suspend`,
      body: { reason },
      aggregateId: savingsAccountUuid,
    });

    return data;
  }

  async unsuspendSavingsAccount({
    savingsAccountUuid,
    reason,
  }: {
    savingsAccountUuid: string;
    reason: string;
  }): Promise<SavingsAccountBaseResponse> {
    const { data } = await this.api.post<SavingsAccountBaseResponse>({
      path: `/admin/accounts/savings/${savingsAccountUuid}/unsuspend`,
      body: { reason },
      aggregateId: savingsAccountUuid,
    });

    return data;
  }

  async updateDebitCardDisplayName({
    cardUuid,
    customerUuid,
    displayName,
  }: CardOperationInput & { displayName: string }): Promise<DebitCardResponse> {
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/cards/${cardUuid}/display-name`,
      body: { customerUuid, displayName },
      aggregateId: cardUuid,
    });

    return data;
  }

  async lockDebitCard({ cardUuid, customerUuid }: CardOperationInput): Promise<DebitCardResponse> {
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/cards/${cardUuid}/lock`,
      body: { customerUuid },
      aggregateId: cardUuid,
    });

    return data;
  }

  async unlockDebitCard({ cardUuid, customerUuid }: CardOperationInput): Promise<DebitCardResponse> {
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/cards/${cardUuid}/unlock`,
      body: { customerUuid },
      aggregateId: cardUuid,
    });

    return data;
  }

  async closeDebitCard({
    cardUuid,
    customerUuid,
    userUuid,
  }: CardOperationInput & { userUuid?: string }): Promise<DebitCardResponse> {
    const requestBody = {
      customerUuid,
      ...(userUuid !== undefined && { userUuid }),
    };
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/v2/cards/${cardUuid}/close`,
      body: requestBody,
      aggregateId: cardUuid,
    });
    logger.debug(`close debit card response: ${JSON.stringify(data)}`);
    return data;
  }

  async reportLostDebitCard({ cardUuid, customerUuid }: CardOperationInput): Promise<DebitCardResponse> {
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/cards/${cardUuid}/lost`,
      body: { customerUuid },
      aggregateId: cardUuid,
    });

    return data;
  }

  async reportLostDebitCardV2(input: CbsReportLostDebitCardInput): Promise<DebitCardResponse> {
    const { isReplaced, customerUuid, userUuid, address } = input;
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/v2/cards/${input.cardUuid}/lost`,
      body: { customerUuid, userUuid, isReplaced, address },
      aggregateId: input.cardUuid,
    });

    return data;
  }

  async createDebitCard(input: CreateNewDebitCardInput): Promise<DebitCardResponse> {
    const { data } = await this.api.post<DebitCardResponse>({
      path: `cards`,
      body: input,
      aggregateId: input.accountUuid,
    });

    return data;
  }

  async setVelocityControls(input: SetVelocityControlInput) {
    const { data } = await this.api.post<VelocityControlResult>({
      path: `velocityControls`,
      body: input,
      aggregateId: input.customerUuid,
    });

    return data;
  }

  async updateVelocityControls(input: UpdateVelocityControlInput & { cardUuid: string }) {
    const { cardUuid, ...updateVelocityControlBody } = input;

    const { data } = await this.api.put<VelocityControlResult>({
      path: `/velocityControls/${cardUuid}`,
      body: updateVelocityControlBody,
      aggregateId: input.cardUuid,
    });

    return data;
  }

  async activateDebitCard(input: {
    entityUuid: string;
    customerUuid: string;
    accountUuid: string;
    shortCode: string;
    customerName: string;
    phone: string;
  }): Promise<DebitCardResponse> {
    const { entityUuid, customerUuid, accountUuid, shortCode, customerName, phone } = input;
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/cards/activate`,
      body: { entityUuid, customerUuid, accountUuid, shortCode, customerName, phone },
      aggregateId: accountUuid,
    });

    return data;
  }

  async suspendDebitCard({ cardUuid, reason }: CardSuspensionInput): Promise<DebitCardResponse> {
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/admin/cards/${cardUuid}/suspend`,
      body: { reason },
      aggregateId: cardUuid,
    });

    return data;
  }

  async unsuspendDebitCard({ cardUuid, reason }: CardSuspensionInput): Promise<DebitCardResponse> {
    const { data } = await this.api.post<DebitCardResponse>({
      path: `/admin/cards/${cardUuid}/unsuspend`,
      body: { reason },
      aggregateId: cardUuid,
    });

    return data;
  }

  async searchBpayBillers(billerCode: string, pageToken?: string): Promise<CoreBankingBillerSearchResponse> {
    let path = `/bpay/biller/search?searchText=${billerCode}`;
    if (pageToken) {
      path = path.concat(`&page=${pageToken}`);
    }

    const { data } = await this.api.customGet<CoreBankingBillerSearchResponse>({ path }, [this.customBpayErrorLogger]);

    debug(`Response ${JSON.stringify(data)}`);

    return data;
  }

  async submitBpayPayment(payload: CbsSubmitBpayPaymentRequestPayload) {
    const response = await this.api.customPost<CbsSubmitBpayPaymentResponse>(
      {
        path: `/bpay/payment/submit`,
        body: payload,
        aggregateId: payload.payerAccountUuid,
      },
      [this.customBpayErrorLogger, this.customSubmitBpayPaymentErrorHandler],
    );

    const data = response?.data;
    info(`Response ${JSON.stringify(data)}`);

    return data;
  }

  async generatePinToken(customerUuid: string, cardUuid: string) {
    const { data } = await this.api.post<{ token: string }>({
      body: { customerUuid },
      path: `/cards/${cardUuid}/pinChangeToken`,
      aggregateId: customerUuid,
    });
    return data;
  }

  async generatePanToken(customerUuid: string, cardUuid: string) {
    const { data } = await this.api.post<{ token: string }>({
      body: { customerUuid },
      path: `/cards/${cardUuid}/showPanToken`,
      aggregateId: customerUuid,
    });
    return data;
  }

  async handleDebitCardTransactionAnnotation(
    operation: DebitCardTransactionAnnotationOperation,
    id: string,
    body: unknown,
  ) {
    const { data } = await this.api.post({
      body,
      path: `transactions/${id}/${operation}`,
      aggregateId: id,
    });
    return data;
  }

  private readonly customBpayErrorLogger = (err: AxiosError<CbsBpayCommonErrorResponse>) => {
    const data = convertErrorResponse(err.response?.data);
    warn(`[400] Error data ${err ? JSON.stringify(data) : undefined}`);
  };

  private readonly customSubmitBpayPaymentErrorHandler = (err: AxiosError<KnownErrorResponseData>) => {
    if (err.response?.data && customErrorCode.includes(err.response?.data.errorCode)) {
      const errorCode = err.response.data.errorCode;
      error(`submit payment error: ${errorCode}`);
      throw new InvalidRequest(errorCode);
    }
  };
}
