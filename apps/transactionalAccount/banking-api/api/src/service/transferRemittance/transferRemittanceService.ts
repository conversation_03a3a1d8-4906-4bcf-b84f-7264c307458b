/* eslint-disable @typescript-eslint/no-non-null-assertion */
import type { DynamodbService } from '@npco/component-bff-core/dist/dynamodb/dynamodbService';
import { EmailService } from '@npco/component-bff-core/dist/notification/emailService';
import { SmsService } from '@npco/component-bff-core/dist/notification/smsService';
import { logger } from '@npco/component-bff-core/dist/utils/logger';
import type { DebitCardTransactionV2 } from '@npco/component-dto-issuing-transaction/dist/types';

import type { SendMessageCommandInput } from '@aws-sdk/client-sqs';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import { v4 } from 'uuid';

import { CustomerService } from '../customer/customerService';
import { convertDbItemToAccount } from '../debitCardAccount/dbItemToDto';
import { DebitCardAccountQueryService } from '../debitCardAccount/debitCardAccountQueryService';
import { DebitCardAccountTransactionRepository } from '../debitCardAccountTransaction/debitCardAccountTransactionRepository';
import { convertDbItemToDto } from '../dynamodb/utils';
import type { EntityQueryService } from '../entity/entityQueryService';
import type { EnvironmentService } from '../environment/environmentService';
import { PaymentInstrumentQueryService } from '../paymentInstrument/paymentInstrumentQueryService';
import { convertMoneyStringToFormattedMoneyString } from '../pdfRenderUtils/formatting';

import type {
  TransferRemittanceFailureQueueInput,
  TransferRemittanceNotificationInput,
  TransferRemittanceNotificationResponse,
} from './models/graphql/types';
import { TransferRemittanceErrorEnum, ReceiptNotificationType } from './models/graphql/types';
import { PdfRemittanceEmailHtmlTemplate } from './remittanceEmailUtils/pdfRemittanceEmailTemplateHtml';
import { TransferRemittancePdfRenderService } from './transferRemittancePdfRenderService';
import { TransferRemittanceRepository } from './transferRemittanceRepository';

type ValidationResult = {
  valid: boolean;
  response: TransferRemittanceNotificationResponse;
  transaction?: DebitCardTransactionV2;
  messageDetails?: MessageDetails;
};

type MessageDetails = {
  amount: string;
  accountEnding: string;
  businessName: string;
};

export class TransferRemittanceService {
  private readonly repo: DebitCardAccountTransactionRepository;

  private readonly sqs: SQSClient;

  private readonly smsService: SmsService;

  private readonly emailService: EmailService;

  private readonly customerService: CustomerService;

  private readonly transferRemittanceRepo: TransferRemittanceRepository;

  private readonly dcaQueryService: DebitCardAccountQueryService;

  private readonly entityQueryService: EntityQueryService;

  private readonly remittanceEmailHtml = PdfRemittanceEmailHtmlTemplate;

  private readonly remittancePdfService: TransferRemittancePdfRenderService;

  private readonly paymentInstrumentService: PaymentInstrumentQueryService;

  constructor(
    envService: EnvironmentService,
    dynamodbService: DynamodbService,
    entityQueryService: EntityQueryService,
  ) {
    this.repo = new DebitCardAccountTransactionRepository(dynamodbService, envService, entityQueryService);
    this.sqs = new SQSClient({});
    this.smsService = new SmsService(envService);
    this.emailService = new EmailService(envService);
    this.customerService = new CustomerService(dynamodbService);
    this.transferRemittanceRepo = new TransferRemittanceRepository(envService, dynamodbService);
    this.dcaQueryService = new DebitCardAccountQueryService(dynamodbService);
    this.entityQueryService = entityQueryService;
    this.remittancePdfService = new TransferRemittancePdfRenderService(envService, dynamodbService, entityQueryService);
    this.paymentInstrumentService = new PaymentInstrumentQueryService(dynamodbService);
  }

  async sendTransferRemittanceNotification(
    input: TransferRemittanceNotificationInput & { entityUuid: string; customerUuid: string },
    isTransactionRetried = false,
  ): Promise<TransferRemittanceNotificationResponse> {
    const validationResult = await this.validateTransferRemittance(input);

    if (!validationResult.valid) {
      return validationResult.response;
    }

    if (validationResult.transaction && validationResult.messageDetails) {
      return this.processValidTransaction(input, validationResult);
    }

    return this.processInvalidTransaction(input, isTransactionRetried);
  }

  async handleFailureQueue(input: TransferRemittanceFailureQueueInput): Promise<void> {
    logger.debug(`Retrying to find transaction ${input.dcaTransactionUuid}`);
    const response = await this.sendTransferRemittanceNotification(input, true);
    if (response.isTransferRemittanceQueued && !response.error) {
      logger.debug(`Transaction ${input.dcaTransactionUuid} is queued successfully :)`);
    } else {
      console.log(`Error -> ${response.error}`);
      logger.error(`Error -> ${response.error}`);
      throw new Error(`Transaction ${input.dcaTransactionUuid} is not queued :(`);
    }
  }

  async sendSmsNotification(input: {
    dcaTransactionUuid: string;
    type: ReceiptNotificationType;
    mobileNumber: string;
    senderMobileNumber?: string;
    messageDetails: MessageDetails;
  }): Promise<void> {
    const { dcaTransactionUuid, mobileNumber, senderMobileNumber, messageDetails } = input;
    logger.debug(`Sending sms notification for transaction ${dcaTransactionUuid}`);
    const message = `You've been paid ${messageDetails.amount} to your account ending in ${messageDetails.accountEnding} from ${messageDetails.businessName} via Zeller.`;
    await this.smsService.sendSms(mobileNumber, message);
    if (senderMobileNumber) {
      logger.debug(`Sending another sms notification to sender ${senderMobileNumber}`);
      await this.smsService.sendSms(senderMobileNumber, message);
    }
  }

  async sendEmailNotification(input: {
    dcaTransactionUuid: string;
    type: ReceiptNotificationType;
    email: string;
    ccEmail?: string;
    messageDetails: MessageDetails;
    entityUuid: string;
  }): Promise<void> {
    const { dcaTransactionUuid, email, ccEmail, messageDetails, entityUuid } = input;
    const subject = `Transfer from ${messageDetails.businessName} via Zeller`;
    const emailHtml = this.remittanceEmailHtml.generateHtml(
      messageDetails.amount,
      messageDetails.accountEnding,
      messageDetails.businessName,
    );
    const destEmail = email;

    const record = (await this.repo.getDebitCardAccountTransaction({
      id: dcaTransactionUuid,
      entityUuid,
    })) as DebitCardTransactionV2;

    const transaction = convertDbItemToDto<DebitCardTransactionV2>({
      item: record,
      dbToDtoKeyMapping: {
        transactionType: 'type',
      },
    });

    const pdfInput = await this.remittancePdfService.getTransferRemittancePdfInput(transaction);
    const pdfBase64 = await this.remittancePdfService.renderTransferRemittancePdf(pdfInput);

    logger.debug(`PDF generated for transaction ${dcaTransactionUuid}`);
    logger.debug(
      `Sending email to ${destEmail} with cc ${ccEmail ?? 'no cc email'} for transaction ${dcaTransactionUuid}`,
    );
    await this.emailService.sendRawEmail(
      { to: [destEmail], cc: ccEmail ? [ccEmail] : [] },
      { subject, emailHtml, attachments: [this.generateAttachment(pdfBase64.toString('base64'))] },
    );
  }

  async sendToSqsQueue(sqsInput: {
    dcaTransactionUuid: string;
    queueUrl: string;
    type: ReceiptNotificationType;
    isSenderNotified: boolean;
    mobileNumber?: string;
    senderMobileNumber?: string;
    email?: string;
    ccEmail?: string;
    entityUuid?: string;
    customerUuid?: string;
    messageDetails?: MessageDetails;
  }): Promise<void> {
    const {
      dcaTransactionUuid,
      queueUrl,
      type,
      mobileNumber,
      senderMobileNumber,
      email,
      ccEmail,
      isSenderNotified,
      entityUuid,
      customerUuid,
      messageDetails,
    } = sqsInput;
    const input: SendMessageCommandInput = {
      MessageGroupId: v4(),
      MessageDeduplicationId: v4(),
      MessageBody: JSON.stringify({
        dcaTransactionUuid,
        type,
        mobileNumber,
        senderMobileNumber,
        email,
        ccEmail,
        isSenderNotified,
        entityUuid,
        customerUuid,
        messageDetails,
      }),
      QueueUrl: queueUrl,
    };
    logger.debug(`Transaction ${dcaTransactionUuid} is sent to ${queueUrl}`);
    logger.debug(`Message body: ${JSON.stringify(input)}`);

    const command = new SendMessageCommand(input);
    await this.sqs.send(command);
  }

  private readonly breakUpAttachmentToShortLines = (longBase64: string) => {
    return longBase64.match(/(.|[\r\n]){1,76}/g);
  };

  private generateAttachment(base64: string) {
    // creating chunks from base64 string with line length = 76
    const chunkedBase64 = this.breakUpAttachmentToShortLines(base64);
    const file = chunkedBase64!.join('\n');
    return {
      filename: `Transfer_Remittance.pdf`,
      data: file,
      contentType: 'application/pdf',
    };
  }

  private async validateTransferRemittance(
    input: TransferRemittanceNotificationInput & { entityUuid: string; customerUuid: string },
  ): Promise<ValidationResult> {
    const { recipientEmail, recipientMobile, dcaTransactionUuid, type } = input;
    if (
      (type === ReceiptNotificationType.SMS && !recipientMobile) ||
      (type === ReceiptNotificationType.EMAIL && !recipientEmail)
    ) {
      return {
        valid: false,
        response: {
          isTransferRemittanceQueued: false,
          error:
            type === ReceiptNotificationType.SMS
              ? TransferRemittanceErrorEnum.RECIPIENT_MOBILE_NOT_FOUND
              : TransferRemittanceErrorEnum.RECIPIENT_EMAIL_NOT_FOUND,
        },
      };
    }

    const transaction = (await this.repo.getDebitCardAccountTransaction({
      id: dcaTransactionUuid,
      entityUuid: input.entityUuid,
    })) as DebitCardTransactionV2;

    const record = await this.transferRemittanceRepo.getTransferRemittanceRecord(input.dcaTransactionUuid, type);

    logger.debug(`Remittance record for transaction ${dcaTransactionUuid} is ${JSON.stringify(record)}`);
    logger.debug(`Transaction record for transaction ${dcaTransactionUuid} is ${JSON.stringify(transaction)}`);
    if (record && record.remittanceAttempts >= 5) {
      return {
        valid: false,
        response: {
          isTransferRemittanceQueued: false,
          error: TransferRemittanceErrorEnum.TOO_MANY_REQUESTS,
        },
      };
    }

    if (!transaction) {
      return {
        valid: true,
        response: {
          isTransferRemittanceQueued: false,
          error: TransferRemittanceErrorEnum.TRANSACTION_NOT_FOUND,
        },
      };
    }

    const twoHoursInMillis = 2 * 60 * 60 * 1000;
    const now = Date.now();
    const transactionTimestamp = new Date(transaction.updatedTime).getTime();

    // If transaction is older than 2 hours
    if (now - transactionTimestamp > twoHoursInMillis) {
      return {
        valid: false,
        response: {
          isTransferRemittanceQueued: false,
          error: TransferRemittanceErrorEnum.TRANSACTION_NOTIFICATION_PERIOD_EXPIRED,
        },
      };
    }

    // Increment retry count and save the record
    await this.transferRemittanceRepo.saveTransferRemittanceRecord({
      transactionUuid: dcaTransactionUuid,
      transferRemittanceNotificationType: type,
      remittanceAttempts: record?.remittanceAttempts ? record.remittanceAttempts + 1 : 1,
      ttlExpiresAt: record?.ttlExpiresAt ?? Math.floor(now / 1000) + 900, // TTL: 15 minutes
    });

    return {
      valid: true,
      response: { isTransferRemittanceQueued: true },
      transaction,
      messageDetails: await this.getRemittanceMessageDetails(transaction, input.entityUuid),
    };
  }

  private async getRemittanceMessageDetails(
    transaction: DebitCardTransactionV2,
    entityUuid: string,
  ): Promise<MessageDetails> {
    const isPayeeAccountDetailsAvailable = transaction.payeeDetails?.accountDetails?.account;
    const isPaymentInstrumentUuidAvailable = transaction.payeeDetails?.paymentInstrumentUuid;

    if (
      !transaction.payeeDetails?.debitCardAccountUuid &&
      !isPayeeAccountDetailsAvailable &&
      !isPaymentInstrumentUuidAvailable
    )
      throw new Error('Debit Card Account ID not found');

    const entity = await this.entityQueryService.getEntity(entityUuid);

    if (!entity) {
      throw new Error(`Entity not found`);
    }

    // If payee account details are available in transaction, return
    if (isPayeeAccountDetailsAvailable) {
      return {
        amount: convertMoneyStringToFormattedMoneyString(transaction.amount.value.toString()),
        accountEnding: `*${transaction.payeeDetails?.accountDetails?.account.slice(-3)}`,
        businessName: entity.tradingName ?? entity.name,
      };
    }
    if (transaction.payeeDetails?.paymentInstrumentUuid) {
      const paymentInstrument = await this.paymentInstrumentService.getPaymentInstrument(
        entityUuid,
        transaction.payeeDetails.paymentInstrumentUuid,
      );

      if (!paymentInstrument.bankAccountDetails) {
        throw new Error(`Payment instrument account details not found`);
      }

      return {
        amount: convertMoneyStringToFormattedMoneyString(transaction.amount.value.toString()),
        accountEnding: `*${paymentInstrument.bankAccountDetails.account.slice(-3)}`,
        businessName: entity.tradingName ?? entity.name,
      };
    }

    // if no paymentInstrument then fallback to debit card account query
    const dbAccount = await this.dcaQueryService.getAccountById({
      accountUuid: transaction.payeeDetails!.debitCardAccountUuid! ?? '',
    });

    if (!dbAccount) {
      throw new Error(`Debit Card Account not found`);
    }

    const payeeAccount = convertDbItemToAccount(dbAccount);

    return {
      amount: convertMoneyStringToFormattedMoneyString(transaction.amount.value.toString()),
      accountEnding: `*${payeeAccount.accountDetails.account.slice(-3)}`,
      businessName: entity.tradingName ?? entity.name,
    };
  }

  private async processValidTransaction(
    input: TransferRemittanceNotificationInput & { entityUuid: string; customerUuid: string },
    validationResult: ValidationResult,
  ): Promise<TransferRemittanceNotificationResponse> {
    const { dcaTransactionUuid, type, recipientMobile, recipientEmail, isSenderNotified, entityUuid, customerUuid } =
      input;
    const customer = await this.customerService.getCustomer({
      entityUuid,
      customerUuid,
    });

    const senderMobileNumber = customer?.phone;
    const ccEmail = customer?.email;

    if ((isSenderNotified && !senderMobileNumber) || (isSenderNotified && !ccEmail)) {
      return {
        isTransferRemittanceQueued: false,
        error: TransferRemittanceErrorEnum.SENDER_DETAILS_NOT_FOUND,
      };
    }

    await this.sendToSqsQueue({
      dcaTransactionUuid,
      queueUrl: process.env.TRANSFER_REMITTANCE_SUCCESS_QUEUE_URL as string,
      type,
      isSenderNotified,
      entityUuid,
      messageDetails: validationResult.messageDetails!,
      ...(recipientMobile ? { mobileNumber: recipientMobile } : {}),
      ...(recipientEmail ? { email: recipientEmail } : {}),
      ...(isSenderNotified && senderMobileNumber ? { senderMobileNumber } : {}),
      ...(isSenderNotified && ccEmail ? { ccEmail } : {}),
    });

    return {
      isTransferRemittanceQueued: true,
    };
  }

  private async processInvalidTransaction(
    input: TransferRemittanceNotificationInput & { entityUuid: string; customerUuid: string },
    isTransactionRetried: boolean,
  ): Promise<TransferRemittanceNotificationResponse> {
    const { dcaTransactionUuid, type, isSenderNotified, entityUuid, customerUuid, recipientEmail, recipientMobile } =
      input;

    if (isTransactionRetried) {
      return {
        isTransferRemittanceQueued: true,
        error: TransferRemittanceErrorEnum.TRANSACTION_NOT_FOUND,
      };
    }

    await this.sendToSqsQueue({
      dcaTransactionUuid,
      queueUrl: process.env.TRANSFER_REMITTANCE_FAILURE_QUEUE_URL as string,
      type,
      isSenderNotified,
      entityUuid,
      customerUuid,
      ...(recipientMobile ? { mobileNumber: recipientMobile } : {}),
      ...(recipientEmail ? { email: recipientEmail } : {}),
    });

    return {
      isTransferRemittanceQueued: true,
    };
  }
}
