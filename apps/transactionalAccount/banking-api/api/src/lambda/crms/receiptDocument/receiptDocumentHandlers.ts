import { appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';

import type { <PERSON><PERSON> } from 'aws-lambda';

import { receiptDocumentService } from '../../dependencies/debitCardAccountDependencies';

export enum ReceiptDocumentHandlerType {
  GET_TXN_ATTACHMENT_UPLOAD_URL,
  REMOVE_TXN_ATTACHMENT,
  GET_TXN_ATTACHMENT_DOWNLOAD_URL,
}

const handlerMap: Record<ReceiptDocumentHandlerType, Handler> = {
  [ReceiptDocumentHandlerType.GET_TXN_ATTACHMENT_UPLOAD_URL]: withMiddlewaresV2(
    { component: ZellerComponent.CRMS, eventType: LambdaEventSource.APPSYNC },
    async (event: any, context: any) => {
      const { transactionUuid, fileNames } = event.args;
      const { entityUuid } = context;
      return receiptDocumentService.getReceiptDocumentUploadUrl(entityUuid, transactionUuid, fileNames);
    },
    [appIdentityMiddleware(true)],
  ),

  [ReceiptDocumentHandlerType.REMOVE_TXN_ATTACHMENT]: withMiddlewaresV2(
    { component: ZellerComponent.CRMS, eventType: LambdaEventSource.APPSYNC },
    async (event: any, context: any) => {
      const { transactionUuid, documentUuid } = event.args;
      const { entityUuid } = context;
      return receiptDocumentService.removeReceiptDocument(entityUuid, transactionUuid, documentUuid);
    },
    [appIdentityMiddleware(true)],
  ),

  [ReceiptDocumentHandlerType.GET_TXN_ATTACHMENT_DOWNLOAD_URL]: withMiddlewaresV2(
    { component: ZellerComponent.CRMS, eventType: LambdaEventSource.APPSYNC },
    async (event: any, context: any) => {
      const { transactionUuid } = event.args;
      const { entityUuid } = context;
      return receiptDocumentService.getReceiptDocumentDownloadUrl(entityUuid, transactionUuid);
    },
    [appIdentityMiddleware(true)],
  ),
};

export const getReceiptDocumentHandler = (type: ReceiptDocumentHandlerType): Handler => {
  if (!(type in handlerMap)) {
    throw new Error(`Unsupported handler type: ${type}`);
  }
  return handlerMap[type];
};
