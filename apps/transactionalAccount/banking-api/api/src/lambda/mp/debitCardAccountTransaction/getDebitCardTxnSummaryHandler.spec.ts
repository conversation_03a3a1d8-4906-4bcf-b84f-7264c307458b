import '../../testing/middlewareMock';

import { DebitCardTransactionExportService } from '../../../service/debitCardTxnExport/debitCardTransactionExportService';
import { getBaseAppSyncResolverEvent } from '../../testing/baseEvent';

import { handler } from './getDebitCardTxnSummaryHandler';

jest.mock('../../../service/debitCardTxnExport/debitCardTransactionExportService');

const MockDebitCardTransactionExportService = DebitCardTransactionExportService as unknown as jest.MockedClass<
  typeof DebitCardTransactionExportService
>;

const mockDebitCardTransactionExportService = <jest.Mocked<DebitCardTransactionExportService>>(
  MockDebitCardTransactionExportService.mock.instances[0]
);

const mockContext = {
  entityUuid: 'test-entity-uuid',
} as any;

const mockRequest = {} as any;

describe('getDctxnSummaryHandler', () => {
  it('should call getSummaryExport with correct arguments', async () => {
    const event = getBaseAppSyncResolverEvent({
      args: {
        debitCardTransactionUuid: 'txn-uuid-123',
        timeZone: 'Australia/Sydney',
      },
      request: mockRequest,
    });

    await handler(event, mockContext, () => {});

    expect(MockDebitCardTransactionExportService).toHaveBeenCalledTimes(1);
    expect(mockDebitCardTransactionExportService.getSummaryExport).toHaveBeenCalledTimes(1);
    expect(mockDebitCardTransactionExportService.getSummaryExport).toHaveBeenCalledWith(
      'test-entity-uuid',
      'txn-uuid-123',
      'Australia/Sydney',
    );
  });

  it('should call getSummaryExport without timeZone if not provided', async () => {
    const event = getBaseAppSyncResolverEvent({
      args: {
        debitCardTransactionUuid: 'txn-uuid-456',
      },
      request: mockRequest,
    });

    await handler(event, mockContext, () => {});

    expect(mockDebitCardTransactionExportService.getSummaryExport).toHaveBeenCalledWith(
      'test-entity-uuid',
      'txn-uuid-456',
      undefined,
    );
  });
});
