import { appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware/xrayAggregateMiddleware';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';

import { DebitCardTransactionExportService } from '../../../service/debitCardTxnExport/debitCardTransactionExportService';
import { EnvironmentService } from '../../../service/environment/environmentService';
import { DebitCardTransactionSummaryPdfService } from '../../../service/pdfService/debitCardTransactionSummaryPdfService';
import { debitCardTransactionExportDataService } from '../../dependencies/debitCardAccountDependencies';
import type { ResolverHandler } from '../../types';

const envService = new EnvironmentService();
const pdfService = new DebitCardTransactionSummaryPdfService();
const debitCardTransactionExportService = new DebitCardTransactionExportService(
  debitCardTransactionExportDataService,
  pdfService,
  envService,
);

const getAggregateId = (e: Parameters<typeof lambdaHandler>[0]) => e.args.debitCardTransactionUuid;

const lambdaHandler: ResolverHandler<
  {
    debitCardTransactionUuid: string;
    timeZone?: string;
  },
  any
> = async (e, c) => {
  const { debitCardTransactionUuid, timeZone } = e.args;
  const { entityUuid } = c;
  return debitCardTransactionExportService.getSummaryExport(entityUuid, debitCardTransactionUuid, timeZone);
};

export const handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  lambdaHandler,
  [appIdentityMiddleware(true), xrayAggregateMiddleware(getAggregateId)],
);
