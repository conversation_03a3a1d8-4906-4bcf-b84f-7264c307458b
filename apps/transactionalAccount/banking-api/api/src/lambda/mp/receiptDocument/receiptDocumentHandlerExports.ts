import { ReceiptDocumentHandlerType, getReceiptDocumentHandler } from './receiptDocumentHandlers';

export const getDebitCardTxnAtchUploadUrlHandler = getReceiptDocumentHandler(
  ReceiptDocumentHandlerType.GET_TXN_ATTACHMENT_UPLOAD_URL,
);

export const removeDebitCardTxnAttachmentHandler = getReceiptDocumentHandler(
  ReceiptDocumentHandlerType.REMOVE_TXN_ATTACHMENT,
);

export const getDebitCardTxnAtchDownloadUrlHandler = getReceiptDocumentHandler(
  ReceiptDocumentHandlerType.GET_TXN_ATTACHMENT_DOWNLOAD_URL,
);
