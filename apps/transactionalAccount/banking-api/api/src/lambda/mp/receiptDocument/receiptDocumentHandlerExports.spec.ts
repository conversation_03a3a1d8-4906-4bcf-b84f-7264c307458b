import '../../testing/middlewareMock';

import { v4 } from 'uuid';

import { receiptDocumentService } from '../../dependencies/debitCardAccountDependencies';
import { getBaseAppSyncResolverEvent } from '../../testing/baseEvent';

// Import handlers from index file
import {
  getDebitCardTxnAtchUploadUrlHandler,
  removeDebitCardTxnAttachmentHandler,
  getDebitCardTxnAtchDownloadUrlHandler,
} from './index';

jest.mock('../../dependencies/debitCardAccountDependencies', () => ({
  receiptDocumentService: {
    getReceiptDocumentUploadUrl: jest.fn(),
    removeReceiptDocument: jest.fn(),
    getReceiptDocumentDownloadUrl: jest.fn(),
  },
}));

describe('Receipt Document Handlers', () => {
  const context = { entityUuid: v4() } as any;

  it('should handle getDebitCardTxnAtchUploadUrlHandler', async () => {
    const event = getBaseAppSyncResolverEvent({
      args: {
        transactionUuid: v4(),
        fileNames: ['file1.jpg', 'file2.pdf'],
      },
    });

    await getDebitCardTxnAtchUploadUrlHandler(event, context, () => {});
    expect(receiptDocumentService.getReceiptDocumentUploadUrl).toHaveBeenCalledTimes(1);
  });

  it('should handle removeDebitCardTxnAttachmentHandler', async () => {
    const event = getBaseAppSyncResolverEvent({
      args: {
        transactionUuid: v4(),
        documentUuid: v4(),
      },
    });

    await removeDebitCardTxnAttachmentHandler(event, context, () => {});
    expect(receiptDocumentService.removeReceiptDocument).toHaveBeenCalledTimes(1);
  });

  it('should handle getDebitCardTxnAtchDownloadUrlHandler', async () => {
    const event = getBaseAppSyncResolverEvent({
      args: {
        transactionUuid: v4(),
      },
    });

    await getDebitCardTxnAtchDownloadUrlHandler(event, context, () => {});
    expect(receiptDocumentService.getReceiptDocumentDownloadUrl).toHaveBeenCalledTimes(1);
  });
});
