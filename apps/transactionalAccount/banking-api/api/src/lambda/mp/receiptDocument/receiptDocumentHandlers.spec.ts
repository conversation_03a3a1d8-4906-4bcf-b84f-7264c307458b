import '../../testing/middlewareMock';
import { ReceiptDocumentHandlerType, getReceiptDocumentHandler } from './receiptDocumentHandlers';

describe('getReceiptDocumentHandler', () => {
  it('should return a handler for each valid type', () => {
    for (const type of Object.values(ReceiptDocumentHandlerType).filter((v) => typeof v === 'number') as number[]) {
      const handler = getReceiptDocumentHandler(type);
      expect(typeof handler).toBe('function');
    }
  });

  it('should throw for unsupported handler type', () => {
    // @ts-expect-error testing invalid value
    expect(() => getReceiptDocumentHandler('INVALID_TYPE')).toThrow('Unsupported handler type');
  });
});
