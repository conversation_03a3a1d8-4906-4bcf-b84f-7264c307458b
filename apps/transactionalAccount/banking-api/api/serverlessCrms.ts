import { ServerlessPlugin } from '@npco/component-bff-serverless/dist/serverless/common/plugins';
import { ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless/dist/serverless/common/vpc';
import { ApiAppServerlessStack } from '@npco/component-bff-serverless/dist/serverless/stacks/apiAppServerlessStack';

import { getLambdas as getDcaRematerialisationLambda } from './iac/common/resources/dcaRematerialisationLambda';
import { dcaTransactionLookupLambda } from './iac/common/resources/dcaTransactionLookupLambda';
import { getLambdas as getProjectionLambda } from './iac/common/resources/projectionLambda';
import { getProjectionSqsResources } from './iac/common/resources/projectionSqsResources';
import { crmsEnvConfig } from './iac/crms/env';
import { lambdas as accountStatementExportLambdas } from './iac/crms/resources/accountStatementExportLambda';
import { lambdas as crmsCardProvisionLambdas } from './iac/crms/resources/cardProvisionLambda';
import { lambdas as crmsDebitCardAccountLambdas } from './iac/crms/resources/debitCardAccountLambda';
import { lambdas as crmsDebitCardAccountTransactionLambdas } from './iac/crms/resources/debitCardAccountTransactionLambda';
import { lambdas as crmsDebitCardLambdas } from './iac/crms/resources/debitCardLambda';
import { lambdas as crmsEntityLambdas } from './iac/crms/resources/entityLambda';
import { lambdas as crmsReceiptDocLambdas } from './iac/crms/resources/receiptDocLambdas';
import { lambdas as crmsSavingsAccountLambdas } from './iac/crms/resources/savingsAccountLambda';

const sls = new ApiAppServerlessStack('banking', crmsEnvConfig, {
  plugins: [
    ServerlessPlugin.Dotenv,
    ServerlessPlugin.ResourceTagging,
    ServerlessPlugin.CanaryDeployments,
    'serverless-esbuild',
    'serverless-plugin-scripts',
    'serverless-dependson-plugin',
  ],
  environment: {
    ...crmsEnvConfig.dotenvConfig,
    COMPONENT_TABLE: crmsEnvConfig.componentTableName,
    BANKING_PRODUCT_TABLE: crmsEnvConfig.bankingProductTableName,
    MERCHANT_TABLE: crmsEnvConfig.merchantTableName,
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}',
  },
  functions: {
    ...crmsCardProvisionLambdas,
    ...crmsDebitCardAccountLambdas,
    ...crmsDebitCardAccountTransactionLambdas,
    ...crmsDebitCardLambdas,
    ...crmsEntityLambdas,
    ...accountStatementExportLambdas,
    ...crmsSavingsAccountLambdas,
    ...crmsReceiptDocLambdas,
    ...getProjectionLambda('crms'),
    ...getDcaRematerialisationLambda('crms'),
    ...dcaTransactionLookupLambda,
  },
  package: { individually: true },
  resources: {
    ...getProjectionSqsResources('crms').Resources,
  },
  outputs: {
    ...getProjectionSqsResources('crms').Outputs,
  },
  custom: {
    ...crmsEnvConfig.getDynamoDb(),
    ...crmsEnvConfig.getAmsApi(),
    ...crmsEnvConfig.getAppsync(),
    ...crmsEnvConfig.getDefaults(),
    vpcImport,
    serviceName: '${env:STATIC_ENV_NAME}-crms-engine',
    cbsEndpoint: 'http://${ssm:${env:STATIC_ENV_NAME}-banking-service-endpoint}',
    accountStatementExportBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-txn-de-bucket}',
    secondaryGsiV1: '${env:SECONDARY_GSI_V1}',
    debitCardIdGsi: '${env:DEBIT_CARD_ID_GSI}',
    contactUuidGsi: '${env:CONTACT_UUID_GSI}',
    sortKeyGsi: '${env:SORT_KEY_GSI}',
    entitySortKeyGsi: '${env:ENTITY_SORT_KEY_GSI}',
    componentTableName: crmsEnvConfig.componentTableName,
    bankingProductTableName: crmsEnvConfig.bankingProductTableName,
    merchantTableName: crmsEnvConfig.merchantTableName,
    merchantTableQueryRolePolicyArn: crmsEnvConfig.merchantTableQueryRolePolicyArn,
    richDataStackName: '${self:custom.service}-richdata',
    getMerchantDetailsLambda: '${self:custom.richDataStackName}-getMerchantDetails',
    exportTransactionsBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-txn-de-bucket}',
    rawReceiptDocUploadsBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-receipt-doc-uploads}',
    receiptDocProcessedBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-receipt-doc-downloads}',
    iamUserKey: crmsEnvConfig.iamUserKey,
    iamUserSecret: crmsEnvConfig.iamUserSecret,
    dependsOn: {
      // Optional. Defaults to true, set to false to disable the plugin
      enabled: true,
      // Optional. Sets amount of lambda deployment parallelization plugin will attempt to create. Defaults to 1
      chains: 3,
    },
    esbuild: {
      bundle: true,
      minify: true,
      sourcemap: 'linked',
      exclude: [], // defaults to `aws-sdk` but as we are using node 18 it is no longer included by default.
      target: 'node18',
      external: ['pdfmake'],
    },
    scripts: {
      hooks: {
        'after:deploy:deploy':
          '/bin/bash ./iac/bin/tagEventRules.sh -s ${self:provider.stackName} -e ${opt:stage} -c ${env:COMPONENT_NAME} -p ${env:PART_NAME}',
      },
    },
  },
});

module.exports = sls.build();
