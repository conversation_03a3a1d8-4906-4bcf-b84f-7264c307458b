import { ManagedPolicy } from '@npco/component-bff-serverless/dist/aws/iam/managedPolicy';
import { Action, Arn } from '@npco/component-bff-serverless/dist/param';
import type { ServerlessFunctions } from '@npco/component-bff-serverless/dist/serverless/types';

import { lambdaCommon } from '../../common/env';
import { mpCommonPolicies } from '../env';

export const lambdas: ServerlessFunctions = {
  getDcTxnAtchUploadUrl: {
    handler: 'src/lambda/mp/receiptDocument/index.getDebitCardTxnAtchUploadUrlHandler',
    name: 'getDcTxnAtchUploadUrl',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      S3_RECEIPT_DOCUMENT_UPLOADS: '${self:custom.rawReceiptDocUploadsBucket}',
    },
    appsync: {
      fieldName: 'getDebitCardTxnAttachmentUploadUrls',
      typeName: 'Query',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        createRecDocUploadUrlS3Policy: [
          { actions: [Action.s3.PutObject], resources: [Arn.s3('${self:custom.rawReceiptDocUploadsBucket}/*')] },
        ],
      },
    },
  },
  getDcTxnAtchDownloadUrl: {
    handler: 'src/lambda/mp/receiptDocument/index.getDebitCardTxnAtchDownloadUrlHandler',
    name: 'getDcTxnAtchDownloadUrl',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      S3_RECEIPT_DOCUMENT_PROCESSED: '${self:custom.receiptDocProcessedBucket}',
    },
    appsync: {
      fieldName: 'getDebitCardTxnAttachmentDownloadUrls',
      typeName: 'Query',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        getObjectS3Policy: [
          { actions: [Action.s3.PutObject], resources: [Arn.s3('${self:custom.rawReceiptDocUploadsBucket}/*')] },
          { actions: [Action.s3.ListBucket], resources: [Arn.s3('${self:custom.rawReceiptDocUploadsBucket}')] },
        ],
      },
    },
  },
  removeDcTxnAttachment: {
    handler: 'src/lambda/mp/receiptDocument/index.removeDebitCardTxnAttachmentHandler',
    name: 'removeDcTxnAttachment',
    ...lambdaCommon,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      S3_RECEIPT_DOCUMENT_PROCESSED: '${self:custom.receiptDocProcessedBucket}',
    },
    appsync: {
      fieldName: 'removeDebitCardTxnAttachment',
      typeName: 'Mutation',
    },
    policy: {
      managed: [...mpCommonPolicies, ManagedPolicy.entityTableQueryRolePolicy],
      inline: {
        removeReceiptDocS3Policy: [
          { actions: [Action.s3.DeleteObject], resources: [Arn.s3('${self:custom.receiptDocProcessedBucket}/*')] },
        ],
      },
    },
  },
};
