{"name": "bff-banking-api", "private": true, "version": "1.0.0", "description": "bff api for banking", "scripts": {"test:local": "jest --config=jest.quick.config.ts --watch", "test": "NODE_OPTIONS=--max_old_space_size=8192 yarn jest --maxWorkers=75% --bail=1", "lint": "eslint .", "build": "yarn tsc --build tsconfig.json", "deploy": "sh iac/bin/deploy.sh", "prepare": "cd .. && husky install apps/.husky", "run-audit": "yarn npm audit --environment production"}, "repository": {"type": "git", "url": "git+https://bitbucket.org/npco_dev/component-bff-banking-api.git"}, "keywords": ["component", "bff", "banking", "banking-api"], "license": "ISC", "homepage": "https://bitbucket.org/npco_dev/component-bff-banking-api#readme", "dependencies": {"@aws-sdk/client-bedrock-runtime": "3.435.0", "@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-s3": "3.435.0", "@aws-sdk/client-sqs": "3.435.0", "@aws-sdk/lib-dynamodb": "3.435.0", "@aws-sdk/s3-presigned-post": "3.435.0", "@aws-sdk/s3-request-presigner": "3.435.0", "@aws-sdk/util-dynamodb": "3.435.0", "@npco/bff-common": "workspace:*", "@npco/component-bff-core": "workspace:*", "@npco/component-bff-serverless": "workspace:*", "@npco/component-dto-addressbook": "workspace:*", "@npco/component-dto-core": "workspace:*", "@npco/component-dto-customer": "workspace:*", "@npco/component-dto-device": "workspace:*", "@npco/component-dto-digital-wallet-token": "workspace:*", "@npco/component-dto-entity": "workspace:*", "@npco/component-dto-issuing-account": "workspace:*", "@npco/component-dto-issuing-card": "workspace:*", "@npco/component-dto-issuing-statement-storage": "workspace:*", "@npco/component-dto-issuing-transaction": "workspace:*", "@npco/component-dto-issuing-transaction-statement": "workspace:*", "@npco/component-dto-merchant": "workspace:*", "@npco/component-dto-payment-instrument": "workspace:*", "@npco/component-dto-richdata": "workspace:*", "@npco/component-dto-scheduled-transfer": "workspace:*", "@npco/component-dto-transaction": "workspace:*", "@smithy/node-http-handler": "^2.1.4", "aws-xray-sdk-core": "^3.5.0", "axios": "^1.8.2", "date-fns": "^2.29.3", "date-fns-legacy": "npm:date-fns@2.16.1", "date-fns-tz": "^2.0.0", "dayjs": "^1.11.8", "graphql-tag": "^2.12.5", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "luxon": "^3.6.1", "p-limit": "^3.0.0", "pdfkit": "^0.13.0", "pdfmake": "^0.2.7", "uuid": "^9.0.0"}, "devDependencies": {"@npco/eslint-config-backend": "^1.0.12", "@nx/jest": "20.7.0", "@rushstack/eslint-patch": "^1.6.0", "@shelf/jest-dynamodb": "^3.4.2", "@swc/core": "^1.3.35", "@swc/jest": "^0.2.24", "@types/aws-lambda": "^8.10.114", "@types/jest": "^29.5.11", "@types/jwt-decode": "^3.1.0", "@types/lodash": "^4.17.5", "@types/luxon": "^3.6.2", "@types/node": "^20.6.2", "@types/pdfmake": "^0.2.2", "@types/serverless": "^3.12.22", "@types/uuid": "^9.0.1", "@typescript-eslint/parser": "^5.38.0", "aws-sdk-client-mock": "^3.0.0", "aws-sdk-client-mock-jest": "^3.0.0", "esbuild": "^0.19.3", "eslint": "^8.56.0", "husky": "^8.0.3", "improved-yarn-audit": "^3.0.0", "jest": "^29.7.0", "jest-html-reporter": "^3.7.0", "jest-junit": "^15.0.0", "jest-sonar-reporter": "^2.0.0", "nock": "^13.3.0", "serverless": "^3.39.0", "serverless-dependson-plugin": "^1.1.2", "serverless-dotenv-plugin": "^6.0.0", "serverless-esbuild": "^1.52.1", "serverless-plugin-canary-deployments": "^0.8.0", "serverless-plugin-resource-tagging": "^1.2.0", "serverless-plugin-scripts": "^1.0.2", "serverless-prune-plugin": "^2.0.2", "serverless-pseudo-parameters": "^2.6.1", "ts-mockito": "^2.6.1", "ts-node": "^10.9.2", "typescript": "^5.4.5"}, "prettier": "@npco/eslint-config-backend/prettier", "jestSonar": {"reportPath": "dist"}}