import { v4 as uuidv4 } from 'uuid';

import { IPersistentEvent } from '../persistentEvent.interface';

export abstract class ScheduledTransferUnSkippedBaseEventPayload {
  entityUuid: string;

  /**
   * Date when schedule transfer will be skipped
   */
  scheduledTransferSkipDateTime: number;

  /**
   * Identifier indicating particular scheduledTransfer execution that will no longer be skipped.
   */
  scheduledTransferExecutionUuid: string;

  constructor(params: ScheduledTransferUnSkippedBaseEventPayload) {
    this.entityUuid = params.entityUuid;
    this.scheduledTransferSkipDateTime = params.scheduledTransferSkipDateTime;
    this.scheduledTransferExecutionUuid = params.scheduledTransferExecutionUuid;
  }
}

export abstract class ScheduledTransferUnSkippedBaseEvent<
  Payload extends ScheduledTransferUnSkippedBaseEventPayload,
> extends IPersistentEvent<Payload> {
  // only change VERSION if the semantics in particular the payload schema changes for this event
  static readonly VERSION: number = 1;

  readonly aggregate: string = 'ScheduledTransfer';

  readonly name: string = 'UnSkipped';

  protected constructor(
    aggregateId: string,
    version: number,
    payload: Payload,
    eventId: string = uuidv4(),
    sequenceNo: number = IPersistentEvent.NOT_SET,
    createdTimestamp: number = new Date().getTime(),
  ) {
    super(aggregateId, version, payload, eventId, sequenceNo, createdTimestamp);
  }
}
