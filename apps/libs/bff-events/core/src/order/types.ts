import type { Money } from '@npco/component-domain-events/dist/commonTypes/money';

import type { CatalogItemSnapshot, CatalogModifier, CatalogTax, CatalogUnit } from '../catalog/types';

export enum OrderStatus {
  OPEN = 'OPEN',
  PAID = 'PAID',
  PART_PAID = 'PART_PAID',
}

export enum CatalogDiscountConfig {
  PERCENTAGE = 'PERCENTAGE',
  AMOUNT = 'AMOUNT',
}

export enum CatalogServiceChargeConfig {
  PERCENTAGE = 'PERCENTAGE',
  AMOUNT = 'AMOUNT',
}

export type OrderItemAttribute = {
  attributeName: string;
  attributeValue: string;
};

export type OrderDiscount = {
  id: string;
  catalogDiscountUuid?: string;
  name?: string;
  config: CatalogDiscountConfig;
  value: string;
  discountedAmount: Money;
  amount?: number;
  ordinal: number;
};

export type OrderServiceCharge = {
  id: string;
  catalogServiceChargeUuid?: string;
  name?: string;
  config: CatalogServiceChargeConfig;
  value: string;
  serviceChargeAmount: Money;
  amount?: number;
  ordinal: number;
};

export type OrderItemModifier = {
  id: string;
  catalogModifier?: CatalogModifier;
  name: string;
  /**
   * @deprecated: UK currency refactor. Use amount instead.
   */
  price: Money;
  amount?: number;
  currency?: string;
  ordinal: number;
  unit: CatalogUnit;
  quantity: number;
  /**
   * @deprecated: UK currency refactor. Use subtotal instead.
   */
  subtotalAmount?: Money;
  subtotal?: number;
};

export type OrderItem = {
  id: string;
  catalogItem?: CatalogItemSnapshot;
  name: string;
  /**
   * @deprecated: UK currency refactor. Use amount instead.
   */
  price: Money;
  amount?: number;
  currency?: string;
  ordinal: number;
  description: string;
  type: OrderItemType;
  unit: CatalogUnit;
  quantity: number;
  discounts?: OrderDiscount[];
  serviceCharges?: OrderServiceCharge[];
  taxes: CatalogTax[];
  modifiers?: OrderItemModifier[];
  variantName?: string;
  discountedAmount?: Money;
  serviceChargeAmount?: Money;
  /**
   * @deprecated: UK currency refactor. Use subtotal instead.
   */
  subtotalAmount?: Money;
  subtotal?: number;
  /**
   * @deprecated: UK currency refactor.
   */
  totalAmount?: Money;
};

export type TaxAmount = { name: string; amount: number };

export enum TenderType {
  CASH = 'CASH',
  CARD = 'CARD',
  OTHER = 'OTHER',
}

export enum OrderPaymentStatus {
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
}

export type OrderPayment = {
  id: string;
  entityUuid: string;
  shortId?: string;
  transactionUuid: string;
  status: OrderPaymentStatus;
  /**
   * @deprecated: UK currency refactor. Use amounts.amount instead.
   */
  amount: Money;
  /**
   * @deprecated: UK currency refactor. Use amounts.tips instead.
   */
  tips?: Money;
  /**
   * @deprecated: UK currency refactor. Use amounts.surchargeAmount instead.
   */
  surchargeAmount?: Money;
  taxAmounts?: TaxAmount[];
  type: any; // TransactionRequestType type not match with dto enum
  tenderType: TenderType;
  tenderSubType?: string;
  timestamp: string;
  timestampLocal?: string;
  note?: string;
  /**
   * @deprecated: UK currency refactor. Use amounts.cashRoundingAdjustment instead.
   */
  amountTendered?: Money;
  /**
   * @deprecated: UK currency refactor. Use amounts.change instead.
   */
  change?: Money;
  amounts?: OrderPaymentAmounts;
};

export type CatalogSettingsSnapshot = { itemsTaxInclusive: boolean; itemsApplyTax: boolean; autoSkuEnabled: boolean };

export enum OrderItemType {
  SINGLE = 'SINGLE',
  VARIANT = 'VARIANT',
  MODIFIER = 'MODIFIER',
  ONE_TIME = 'ONE_TIME',
}

/**
 * Order amounts in cents
 */
export type OrderAmounts = {
  paidAmount: number;
  dueAmount: number;
  subtotalAmount: number;
  orderAmount: number;
  totalSurcharge: number;
  totalGst: number;
  orderGst: number;
  totalDiscount: number;
  totalServiceCharge: number;
  totalTips: number;
  totalChange: number;
  totalAmountTendered: number;
  cashRoundingAdjustment: number;
};

export type OrderPaymentAmounts = {
  amount: number;
  surchargeAmount: number;
  tips: number;
  amountTendered: number;
  change: number;
  cashRoundingAdjustment: number;
};
