import type { Money } from '@npco/component-domain-events/dist/commonTypes/money';

import { v4 as uuidv4 } from 'uuid';

import { IPersistentEvent } from '../persistentEvent.interface';

import type {
  CatalogSettingsSnapshot,
  OrderAmounts,
  OrderDiscount,
  OrderItem,
  OrderServiceCharge,
  OrderStatus,
} from './types';

export abstract class OrderCreatedBaseEventPayload {
  entityUuid: string;

  createdFromDeviceUuid: string;

  status: OrderStatus;

  referenceNumber: string;

  siteUuid: string;

  items: OrderItem[];

  discounts?: OrderDiscount[];

  serviceCharges?: OrderServiceCharge[];

  /**
   * @deprecated: UK currency refactor. Use amounts.paidAmount instead.
   */
  paidAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.dueAmount instead.
   */
  dueAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.subtotalAmount instead.
   */
  subtotalAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalAmount instead.
   */
  totalAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.orderAmount instead.
   */
  orderAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalSurcharge instead.
   */
  totalSurcharge: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalGst instead.
   */
  totalGst: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.orderGst instead.
   */
  orderGst: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalDiscount instead.
   */
  totalDiscount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalServiceCharge instead.
   */
  totalServiceCharge: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalTips instead.
   */
  totalTips: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.orderDisplayAmount instead.
   */
  orderDisplayAmount?: Money;

  createdTime: number;

  updatedTime?: number;

  updatedTimeInMilliseconds?: number;

  paidTime?: number;

  /**
   * @deprecated: UK currency refactor. Use amounts.discountedAmount instead.
   */
  discountedAmount?: Money;

  catalogSettings: CatalogSettingsSnapshot;

  createdTimestampLocal?: string;

  amounts?: OrderAmounts;

  constructor(params: OrderCreatedBaseEventPayload) {
    this.entityUuid = params.entityUuid;
    this.createdFromDeviceUuid = params.createdFromDeviceUuid;
    this.status = params.status;
    this.referenceNumber = params.referenceNumber;
    this.siteUuid = params.siteUuid;
    this.items = params.items;
    this.discounts = params.discounts;
    this.serviceCharges = params.serviceCharges;
    this.paidAmount = params.paidAmount;
    this.dueAmount = params.dueAmount;
    this.subtotalAmount = params.subtotalAmount;
    this.totalAmount = params.totalAmount;
    this.totalSurcharge = params.totalSurcharge;
    this.totalGst = params.totalGst;
    this.orderGst = params.orderGst;
    this.totalDiscount = params.totalDiscount;
    this.totalServiceCharge = params.totalServiceCharge;
    this.totalTips = params.totalTips;
    this.createdTime = params.createdTime;
    this.updatedTime = params.updatedTime;
    this.paidTime = params.paidTime;
    this.discountedAmount = params.discountedAmount;
    this.catalogSettings = params.catalogSettings;
    this.createdTimestampLocal = params.createdTimestampLocal;
    this.updatedTimeInMilliseconds = params.updatedTimeInMilliseconds;
    this.orderDisplayAmount = params.orderDisplayAmount;
    this.orderAmount = params.orderAmount;
    this.amounts = params.amounts;
  }
}

export abstract class OrderCreatedBaseEvent<
  Payload extends OrderCreatedBaseEventPayload,
> extends IPersistentEvent<Payload> {
  // only change VERSION if the semantics in particular the payload schema changes for this event
  static readonly VERSION: number = 1;

  readonly aggregate: string = 'Order';

  readonly name: string = 'Created';

  protected constructor(
    aggregateId: string,
    version: number,
    payload: Payload,
    eventId: string = uuidv4(),
    sequenceNo: number = IPersistentEvent.NOT_SET,
    createdTimestamp: number = new Date().getTime(),
  ) {
    super(aggregateId, version, payload, eventId, sequenceNo, createdTimestamp);
  }
}
