import type { Money } from '@npco/component-domain-events/dist/commonTypes/money';

import { v4 as uuidv4 } from 'uuid';

import { IPersistentEvent } from '../persistentEvent.interface';

import type { OrderStatus, OrderPayment, OrderAmounts } from './types';

export abstract class OrderPartPaidBaseEventPayload {
  entityUuid: string;

  /**
   * The new payment that was made for the order. It's empty for Zero Dollar Sales
   */
  newPayment?: OrderPayment;

  status: OrderStatus;

  /**
   * @deprecated: UK currency refactor. Use amounts.paidAmount instead.
   */
  paidAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.dueAmount instead.
   */
  dueAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.subtotalAmount instead.
   */
  subtotalAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalAmount instead.
   */
  totalAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.orderAmount instead.
   */
  orderAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalSurcharge instead.
   */
  totalSurcharge: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalGst instead.
   */
  totalGst: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalDiscount instead.
   */
  totalDiscount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalServiceCharge instead.
   */
  totalServiceCharge: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalTips instead.
   */
  totalTips: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.orderDisplayAmount instead.
   */
  orderDisplayAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalChange instead.
   */
  totalChange?: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalAmountTendered instead.
   */
  totalAmountTendered?: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.cashRoundingAdjustment instead.
   */
  cashRoundingAdjustment?: Money;

  updatedTimeInMilliseconds?: number;

  updatedTime?: number;

  amount?: OrderAmounts;

  constructor(params: OrderPartPaidBaseEventPayload) {
    this.entityUuid = params.entityUuid;
    this.status = params.status;
    this.paidAmount = params.paidAmount;
    this.dueAmount = params.dueAmount;
    this.subtotalAmount = params.subtotalAmount;
    this.totalAmount = params.totalAmount;
    this.totalSurcharge = params.totalSurcharge;
    this.totalGst = params.totalGst;
    this.totalDiscount = params.totalDiscount;
    this.totalServiceCharge = params.totalServiceCharge;
    this.totalTips = params.totalTips;
    this.newPayment = params.newPayment;
    this.updatedTimeInMilliseconds = params.updatedTimeInMilliseconds;
    this.updatedTime = params.updatedTime;
    this.orderDisplayAmount = params.orderDisplayAmount;
    this.totalChange = params.totalChange;
    this.totalAmountTendered = params.totalAmountTendered;
    this.orderAmount = params.orderAmount;
    this.cashRoundingAdjustment = params.cashRoundingAdjustment;
    this.amount = params.amount;
  }
}

export abstract class OrderPartPaidBaseEvent<
  Payload extends OrderPartPaidBaseEventPayload,
> extends IPersistentEvent<Payload> {
  // only change VERSION if the semantics in particular the payload schema changes for this event
  static readonly VERSION: number = 1;

  readonly aggregate: string = 'Order';

  readonly name: string = 'PartPaid';

  protected constructor(
    aggregateId: string,
    version: number,
    payload: Payload,
    eventId: string = uuidv4(),
    sequenceNo: number = IPersistentEvent.NOT_SET,
    createdTimestamp: number = new Date().getTime(),
  ) {
    super(aggregateId, version, payload, eventId, sequenceNo, createdTimestamp);
  }
}
