import { v4 as uuidv4 } from 'uuid';

import { IPersistentEvent } from '../persistentEvent.interface';

export abstract class EntityFullSearchRequestedBaseEventPayload {
  /**
   * ACN used for the search if the initial search cache is expired
   */
  acn?: string;

  /**
   * Customer UUID to identify the initial search request for the new flow
   */
  customerUuid?: string;

  constructor(params: EntityFullSearchRequestedBaseEventPayload) {
    this.acn = params.acn;
    this.customerUuid = params.customerUuid;
  }
}

export abstract class EntityFullSearchRequestedBaseEvent<
  Payload extends EntityFullSearchRequestedBaseEventPayload,
> extends IPersistentEvent<Payload | null> {
  static readonly VERSION: number = 1;

  readonly aggregate: string = 'Entity';

  readonly name: string = 'FullSearchRequested';

  constructor(
    aggregateId: string,
    version: number,
    payload: Payload | null,
    eventId: string = uuidv4(),
    sequenceNo: number = IPersistentEvent.NOT_SET,
    createdTimestamp: number = new Date().getTime(),
  ) {
    super(aggregateId, version, payload, eventId, sequenceNo, createdTimestamp);
  }
}
