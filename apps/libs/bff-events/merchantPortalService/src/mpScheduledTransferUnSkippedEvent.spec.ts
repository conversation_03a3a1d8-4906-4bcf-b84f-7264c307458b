import { IPersistentEvent } from '@npco/component-domain-events/dist/persistentEvent.interface';
import { wait } from '@npco/component-events-core/dist/utils/wait';

import {
  MpScheduledTransferUnSkippedEvent,
  MpScheduledTransferUnSkippedEventPayload,
} from './mpScheduledTransferUnSkippedEvent';

const mockData: MpScheduledTransferUnSkippedEventPayload = {
  entityUuid: 'mockEntityUuid',
  scheduledTransferSkipDateTime: 12345678910,
  scheduledTransferExecutionUuid: 'mockScheduleTransferExecutionUuid',
};

describe('MpScheduledTransferUnSkippedEventPayload', () => {
  describe('constructor', () => {
    it('should create a valid instance with properties set', () => {
      // SETUP

      // ACT
      const result = new MpScheduledTransferUnSkippedEventPayload(mockData);

      // VERIFY
      expect(result).toEqual(mockData);
    });
  });
});

describe('MpScheduledTransferUnSkippedEvent', () => {
  // as URI is critically important - lets safeguard it
  describe('URI', () => {
    it('should have correct value', () => {
      const expected = 'mp.ScheduledTransfer.UnSkipped';
      const actual = MpScheduledTransferUnSkippedEvent.URI;
      expect(actual).toEqual(expected);
    });
  });

  describe('create', () => {
    it(
      'should return an instance with correct parameters ' +
        'eventId = random uuidv4, ' +
        'sequenceNo = IPersistentEvent.NOT_SET, ' +
        'CompletedTimestamp = current time, ' +
        'aggregateId = as supplied, ' +
        'payload = as supplied ' +
        'version = MpScheduledTransferUnSkippedEvent.VERSION',
      async () => {
        // SETUP
        const mockAggregateId = 'mockAggregateId';
        const mockPayload = MpScheduledTransferUnSkippedEvent.createPayload(mockData);
        const nowTimestamp = new Date().getTime();

        // ACT
        await wait(5);
        const actual1 = MpScheduledTransferUnSkippedEvent.createInstance(mockAggregateId, mockPayload);
        await wait(5);
        const actual2 = MpScheduledTransferUnSkippedEvent.createInstance(mockAggregateId, mockPayload);

        // VERIFY
        expect(actual1).toBeInstanceOf(MpScheduledTransferUnSkippedEvent);
        expect(actual1.aggregate).toEqual('ScheduledTransfer');
        expect(actual1.name).toEqual('UnSkipped');
        expect(actual1.eventId).not.toBeNull();
        expect(actual1.eventId).not.toBeUndefined();
        expect(actual1.eventId.length).toBe(36); // uuidv4
        expect(actual2.eventId).not.toBeNull();
        expect(actual2.eventId).not.toBeUndefined();
        expect(actual2.eventId.length).toBe(36); // uuidv4
        expect(actual1.eventId).not.toEqual(actual2.eventId); // unique / randomness
        expect(actual1.aggregateId).toEqual(mockAggregateId);
        expect(actual1.sequenceNo).toEqual(IPersistentEvent.NOT_SET);
        expect(actual1.version).toEqual(MpScheduledTransferUnSkippedEvent.VERSION);
        expect(actual1.createdTimestamp).toBeGreaterThan(nowTimestamp);
        expect(actual2.createdTimestamp).toBeGreaterThan(actual1.createdTimestamp);
        expect(actual1.payload).toEqual(mockPayload);
      },
    );
  });

  describe('createInstanceFromExistingData from event bridge', () => {
    it(
      'should return an MpScheduledTransferUnSkippedEvent instance with correct parameters ' +
        'extracted as is from eventBridgeEventData.detail',
      async () => {
        const mockEventBridgeEventData = {
          detail: {
            eventId: 'mockEventId',
            aggregateId: 'mockAggregateId',
            sequenceNo: 2,
            version: 1,
            createdTimestamp: 12345678910,
            payload: new MpScheduledTransferUnSkippedEventPayload(mockData),
          },
        };
        const actual = IPersistentEvent.createInstanceFromExistingData<MpScheduledTransferUnSkippedEvent>(
          mockEventBridgeEventData.detail.aggregateId,
          mockEventBridgeEventData.detail.version,
          mockEventBridgeEventData.detail.payload,
          mockEventBridgeEventData.detail.eventId,
          mockEventBridgeEventData.detail.sequenceNo,
          mockEventBridgeEventData.detail.createdTimestamp,
          MpScheduledTransferUnSkippedEvent as any,
        );

        expect(actual).not.toBeNull();
        expect(actual).toBeInstanceOf(MpScheduledTransferUnSkippedEvent);
        expect(actual.eventId).toEqual(mockEventBridgeEventData.detail.eventId);
        expect(actual.aggregateId).toEqual(mockEventBridgeEventData.detail.aggregateId);
        expect(actual.sequenceNo).toEqual(mockEventBridgeEventData.detail.sequenceNo);
        expect(actual.version).toEqual(mockEventBridgeEventData.detail.version);
        expect(actual.createdTimestamp).toEqual(mockEventBridgeEventData.detail.createdTimestamp);
        expect(actual.payload).toEqual(mockEventBridgeEventData.detail.payload);
      },
    );
  });

  describe('createInstanceFromExistingData from event store', () => {
    it(
      'should return an MpScheduledTransferUnSkippedEvent instance with correct parameters ' +
        'extracted as is from eventStoreEventData',
      async () => {
        const mockEventStoreEventData = {
          eventId: 'mockEventId',
          aggregateId: 'mockAggregateId',
          sequenceNo: 2,
          version: 1,
          createdTimestamp: 12345678910,
          payload: new MpScheduledTransferUnSkippedEventPayload(mockData),
        };
        const actual = IPersistentEvent.createInstanceFromExistingData<MpScheduledTransferUnSkippedEvent>(
          mockEventStoreEventData.aggregateId,
          mockEventStoreEventData.version,
          mockEventStoreEventData.payload,
          mockEventStoreEventData.eventId,
          mockEventStoreEventData.sequenceNo,
          mockEventStoreEventData.createdTimestamp,
          MpScheduledTransferUnSkippedEvent as any,
        );

        expect(actual).not.toBeNull();
        expect(actual).toBeInstanceOf(MpScheduledTransferUnSkippedEvent);
        expect(actual.eventId).toEqual(mockEventStoreEventData.eventId);
        expect(actual.aggregateId).toEqual(mockEventStoreEventData.aggregateId);
        expect(actual.sequenceNo).toEqual(mockEventStoreEventData.sequenceNo);
        expect(actual.version).toEqual(mockEventStoreEventData.version);
        expect(actual.createdTimestamp).toEqual(mockEventStoreEventData.createdTimestamp);
        expect(actual.payload).toEqual(mockEventStoreEventData.payload);
      },
    );
  });
});
