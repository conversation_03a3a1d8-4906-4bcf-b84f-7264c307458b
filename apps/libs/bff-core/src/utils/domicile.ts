export enum Domicile {
  AU = 'AUS',
  GB = 'GBR',
}

export enum AwsRegions {
  SYDNEY = 'ap-southeast-2',
  LONDON = 'eu-west-2',
}

const supportedDomiciles = {
  [AwsRegions.SYDNEY]: [Domicile.AU],
  [AwsRegions.LONDON]: [Domicile.GB],
};

export const getSupportedDomiciles = (region: string): Domicile[] => {
  return supportedDomiciles[region as AwsRegions] || [];
};

export const getRegionForDomicile = (domicile: string): AwsRegions => {
  return Object.entries(supportedDomiciles).find(([_, domiciles]) =>
    domiciles.find((d) => d === domicile),
  )?.[0] as AwsRegions;
};

export const isDomicileSupportedInRegion = (region: string, domicile?: string | null): boolean => {
  const domiciles = getSupportedDomiciles(region);
  return domiciles.findIndex((d) => d === domicile) >= 0;
};

export const getDomicileFromISOAlpha2CountryCode = (countryCode: string): Domicile | undefined => {
  const countryToDomicile: Map<string, Domicile> = new Map([
    ['CC', Domicile.AU], // Cocos (Keeling) Islands
    ['CX', Domicile.AU], // Christmas Island
    ['NF', Domicile.AU], // Norfolk Island
  ]);

  Object.keys(Domicile).forEach((key) => {
    countryToDomicile.set(key, Domicile[key as keyof typeof Domicile]);
  });

  return countryToDomicile.get(countryCode);
};

export const getDefaultDomicileFromRegion = (region: string) => {
  switch (region) {
    case AwsRegions.SYDNEY.toString():
      return Domicile.AU;
    case AwsRegions.LONDON.toString():
      return Domicile.GB;
    default:
      throw new Error(`Unsupported domicile: ${region}`);
  }
};
