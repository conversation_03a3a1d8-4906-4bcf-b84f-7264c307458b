import {
  getUnvalidatedEntityUuidFromArgs,
  getUnvalidatedCustomerUuidFromArgs,
  getUnvalidatedDeviceUuidFromArgs,
} from './getUnvalidatedUuidFromArgs';

describe('getUnvalidatedCustomerUuidFromArgs', () => {
  it('should return the customerUuid when it exists in the event args', () => {
    const event = { args: { customerUuid: '5678-customer-uuid' } };
    const result = getUnvalidatedCustomerUuidFromArgs(event);
    expect(result).toBe('5678-customer-uuid');
  });

  it('should return undefined when customerUuid does not exist in the event args', () => {
    const event = { args: {} };
    const result = getUnvalidatedCustomerUuidFromArgs(event);
    expect(result).toBeUndefined();
  });

  it('should return undefined when args is undefined', () => {
    const event = {};
    const result = getUnvalidatedCustomerUuidFromArgs(event as any);
    expect(result).toBeUndefined();
  });
});

describe('getUnvalidatedDeviceUuidFromArgs', () => {
  it('should return the deviceUuid when it exists in the event args', () => {
    const event = { args: { deviceUuid: '1234-device-uuid' } };
    const result = getUnvalidatedDeviceUuidFromArgs(event);
    expect(result).toBe('1234-device-uuid');
  });

  it('should return undefined when deviceUuid does not exist in the event args', () => {
    const event = { args: {} };
    const result = getUnvalidatedDeviceUuidFromArgs(event);
    expect(result).toBeUndefined();
  });

  it('should return undefined when args is undefined', () => {
    const event = {};
    const result = getUnvalidatedDeviceUuidFromArgs(event as any);
    expect(result).toBeUndefined();
  });
});

describe('getUnvalidatedEntityUuidFromArgs', () => {
  it('should return the entityUuid when it exists in the event args', () => {
    const event = { args: { entityUuid: '1234-entity-uuid' } };
    const result = getUnvalidatedEntityUuidFromArgs(event);
    expect(result).toBe('1234-entity-uuid');
  });

  it('should return undefined when entityUuid does not exist in the event args', () => {
    const event = { args: {} };
    const result = getUnvalidatedEntityUuidFromArgs(event);
    expect(result).toBeUndefined();
  });

  it('should return undefined when args is undefined', () => {
    const event = {};
    const result = getUnvalidatedEntityUuidFromArgs(event as any);
    expect(result).toBeUndefined();
  });
});
