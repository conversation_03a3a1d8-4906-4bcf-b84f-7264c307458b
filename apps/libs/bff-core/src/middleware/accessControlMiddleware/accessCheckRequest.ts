import type { ZellerComponent } from '../../types/zellerComponent';

import type { AppsyncAuthHeader } from './sessionCheckRequest';

export type CustomerSpecificEvent = {
  args?: {
    customerUuid?: string;
  };
};

export type DeviceSpecificEvent = {
  args?: {
    deviceUuid?: string;
  };
};

export type EntitySpecificEvent = {
  args?: {
    entityUuid?: string;
  };
};

export type SDKAccessCheckRequest = {
  component: ZellerComponent.SDK;
  event: {
    request: AppsyncAuthHeader;
    args: { entityUuid?: string; deviceUuid?: string };
  };
};

export type SDKDeviceAccessCheckRequest = {
  component: ZellerComponent.SDK;
  event: {
    request: AppsyncAuthHeader;
    args: { entityUuid?: string; deviceUuid: string };
  };
};

export type MPAccessCheckRequest = {
  component: ZellerComponent.MP;
  event: {
    request: AppsyncAuthHeader;
    args: { entityUuid?: string };
  };
};

export type MpMultiEntityAccessCheckEvent = {
  request: AppsyncAuthHeader;
  args: { entityUuids: string[] };
};

export type MpMultiEntityAccessCheckRequest = {
  component: ZellerComponent.MP;
  event: MpMultiEntityAccessCheckEvent;
};

export type DBSAccessCheckRequest = {
  component: ZellerComponent.DBS;
  event: {
    request: AppsyncAuthHeader;
    args: { entityUuid?: string; deviceUuid: string };
  };
};

export type MpDbsAccessCheckRequest = MPAccessCheckRequest | DBSAccessCheckRequest;

export type AccessCheckRequest = MPAccessCheckRequest | DBSAccessCheckRequest | SDKAccessCheckRequest;
