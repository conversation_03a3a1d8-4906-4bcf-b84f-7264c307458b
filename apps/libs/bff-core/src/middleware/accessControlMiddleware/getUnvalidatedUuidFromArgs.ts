import type { CustomerSpecificEvent, DeviceSpecificEvent, EntitySpecificEvent } from './accessCheckRequest';

export const getUnvalidatedCustomerUuidFromArgs = (event: CustomerSpecificEvent): string | undefined => {
  return event?.args?.customerUuid;
};

export const getUnvalidatedDeviceUuidFromArgs = (event: DeviceSpecificEvent): string | undefined => {
  return event?.args?.deviceUuid;
};

export const getUnvalidatedEntityUuidFromArgs = (event: EntitySpecificEvent): string | undefined => {
  return event?.args?.entityUuid;
};
