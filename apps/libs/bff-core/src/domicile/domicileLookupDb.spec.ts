import { DbRecordType } from '@npco/component-dto-core';

import { anything, mock, when } from 'ts-mockito';
import { v4 } from 'uuid';

import { EnvironmentService } from '../config';
import { BffDynamoDbClient, DynamodbService } from '../dynamodb';
import { DomicileContext } from '../middleware/domicileContext';
import { AwsRegions, Domicile } from '../utils/domicile';

import { DomicileLookupDb } from './domicileLookupDb';

jest.mock('../dynamodb/bffDynamoDbClient');
jest.mock('../config/envService');

describe('DomicileLookupDb suite', () => {
  let domicileLookupDb: DomicileLookupDb;
  let dbService: DynamodbService;

  beforeAll(async () => {
    dbService = new DynamodbService(new EnvironmentService(), new BffDynamoDbClient());
    domicileLookupDb = new DomicileLookupDb();
  });

  afterEach(() => {
    jest.restoreAllMocks();
    (DynamodbService as any).instance = undefined;
  });

  describe('constructor', () => {
    it('should be able to create instance of London region', async () => {
      const env = new EnvironmentService();
      env.awsRegion = AwsRegions.LONDON;
      env.domicileLookupTableName = '';
      const db = new DomicileLookupDb(env);
      expect(db.getTableName()).toBe('arn:aws:dynamodb:eu-west-2:sydneyAccountId:table/dev-ams-engine-DomicileLookup');
    });
  });

  // Insert a test record before running the tests
  describe('getDomicileByEntityId', () => {
    const domicileLookupItem = { id: '123', domicile: Domicile.AU, type: DbRecordType.ENTITY_DOMICILE };

    // Create a new instance of DomicileLookupDb for each test to ensure isolation
    const domicileLookupEntityDb = new DomicileLookupDb(
      new EnvironmentService(),
      new DynamodbService(new EnvironmentService(), new BffDynamoDbClient()),
    );

    beforeAll(async () => {
      const putCommandInput = {
        TableName: domicileLookupEntityDb.getTableName(),
        Item: domicileLookupItem,
      };
      await dbService.put(putCommandInput);
    });

    afterAll(async () => {
      jest.restoreAllMocks();
    });

    it('should return domicile when item exists', async () => {
      const result = await domicileLookupEntityDb.getDomicileByEntityId(domicileLookupItem.id);
      expect(result).toBe(Domicile.AU);
    });

    it('should return null when item does not exist', async () => {
      const result = await domicileLookupEntityDb.getDomicileByEntityId('456');
      expect(result).toBeNull();
    });

    it('should return null when error is thrown', async () => {
      const mockBffClient = mock(BffDynamoDbClient);
      when(mockBffClient.send(anything())).thenThrow(new Error('something went wrong'));

      const mockDynamoService = new DynamodbService(new EnvironmentService(), mockBffClient);
      jest.spyOn(DynamodbService, 'getInstance').mockReturnValue(mockDynamoService);
      const mockedDomicileLookupEntityDb = new DomicileLookupDb();

      const result = await mockedDomicileLookupEntityDb.getDomicileByEntityId(domicileLookupItem.id);
      expect(result).toBe(null);
    });
  });

  // Insert a test record before running the tests
  describe('getDomicileBySdkDeviceUuid', () => {
    const domicileLookupItem = { id: '123', domicile: Domicile.AU, type: DbRecordType.DEVICE_DOMICILE };

    // Create a new instance of DomicileLookupDb for each test to ensure isolation
    const domicileLookupDeviceDb = new DomicileLookupDb(
      new EnvironmentService(),
      new DynamodbService(new EnvironmentService(), new BffDynamoDbClient()),
    );

    beforeAll(async () => {
      const putCommandInput = {
        TableName: domicileLookupDeviceDb.getTableName(),
        Item: domicileLookupItem,
      };
      await dbService.put(putCommandInput);
    });

    afterAll(async () => {
      jest.restoreAllMocks();
    });

    it('should return domicile when item exists', async () => {
      const result = await domicileLookupDeviceDb.getDomicileBySdkDeviceUuid(domicileLookupItem.id);
      expect(result).toBe(Domicile.AU);
    });

    it('should return null when item does not exist', async () => {
      const result = await domicileLookupDeviceDb.getDomicileBySdkDeviceUuid('456');
      expect(result).toBeNull();
    });

    it('should return null when error is thrown', async () => {
      const mockBffClient = mock(BffDynamoDbClient);
      when(mockBffClient.send(anything())).thenThrow(new Error('something went wrong'));

      const mockDynamoService = new DynamodbService(new EnvironmentService(), mockBffClient);
      jest.spyOn(DynamodbService, 'getInstance').mockReturnValue(mockDynamoService);
      const mockedDomicileLookupDeviceDb = new DomicileLookupDb();

      const result = await mockedDomicileLookupDeviceDb.getDomicileBySdkDeviceUuid(domicileLookupItem.id);
      expect(result).toBe(null);
    });
  });

  describe('getDomicileByCustomerUuid', () => {
    const domicileLookupItem = { id: 'customerUuid', domicile: Domicile.AU, type: DbRecordType.CUSTOMER_DOMICILE };

    // Create a new instance of DomicileLookupDb for each test to ensure isolation
    const domicileLookupCustomerDb = new DomicileLookupDb(
      new EnvironmentService(),
      new DynamodbService(new EnvironmentService(), new BffDynamoDbClient()),
    );

    beforeAll(async () => {
      const putCommandInput = {
        TableName: domicileLookupCustomerDb.getTableName(),
        Item: domicileLookupItem,
      };
      await dbService.put(putCommandInput);
    });

    afterAll(async () => {
      jest.restoreAllMocks();
    });

    it('should return domicile when item exists', async () => {
      const result = await domicileLookupCustomerDb.getDomicileByCustomerUuid(domicileLookupItem.id);
      expect(result).toBe(Domicile.AU);
    });

    it('should return null when item does not exist', async () => {
      const result = await domicileLookupCustomerDb.getDomicileByCustomerUuid('456');
      expect(result).toBeNull();
    });

    it('should return null when error is thrown', async () => {
      const mockBffClient = mock(BffDynamoDbClient);
      when(mockBffClient.send(anything())).thenThrow(new Error('something went wrong'));

      const mockDynamoService = new DynamodbService(new EnvironmentService(), mockBffClient);
      jest.spyOn(DynamodbService, 'getInstance').mockReturnValue(mockDynamoService);
      const mockedDomicileLookupCustomerDb = new DomicileLookupDb();

      const result = await mockedDomicileLookupCustomerDb.getDomicileByCustomerUuid(domicileLookupItem.id);
      expect(result).toBe(null);
    });
  });

  describe('createDomicileRecord', () => {
    afterAll(async () => {
      jest.restoreAllMocks();
    });

    it('should create a new entity domicile record', async () => {
      const newDomicile = Domicile.AU;
      const entityUuid = '789';

      const result = await domicileLookupDb.createDomicileRecord(entityUuid, DbRecordType.ENTITY_DOMICILE, newDomicile);

      expect(result).toBe(newDomicile);
    });

    it('should create a new device domicile record', async () => {
      const newDomicile = Domicile.AU;
      const deviceUuid = '789';

      const result = await domicileLookupDb.createDomicileRecord(deviceUuid, DbRecordType.DEVICE_DOMICILE, newDomicile);

      expect(result).toBe(newDomicile);
    });

    it('should create a new customer domicile record', async () => {
      const newDomicile = Domicile.AU;
      const entityUuid = '789';

      const result = await domicileLookupDb.createDomicileRecord(
        entityUuid,
        DbRecordType.CUSTOMER_DOMICILE,
        newDomicile,
      );

      expect(result).toBe(newDomicile);
    });

    it('should throw error when failed to create record', async () => {
      domicileLookupDb = new DomicileLookupDb();

      await expect(
        domicileLookupDb.createDomicileRecord('', DbRecordType.ENTITY_DOMICILE, Domicile.AU),
      ).rejects.toThrow('Error creating domicile record for entity.domicile - ');
    });

    it('should throw error when domicile is not provided', async () => {
      await expect(domicileLookupDb.createDomicileRecord('789', DbRecordType.ENTITY_DOMICILE)).rejects.toThrow(
        'Domicile is required',
      );
    });
  });

  describe('deleteDomicileRecord', () => {
    beforeAll(async () => {
      domicileLookupDb = new DomicileLookupDb();
    });

    it('should delete domicile record when item exists', async () => {
      // insert and assert
      await domicileLookupDb.createDomicileRecord('000', DbRecordType.ENTITY_DOMICILE, Domicile.AU);
      const result = await domicileLookupDb.getDomicileByEntityId('000');
      expect(result).toBe('AUS');

      // delete and assert
      await domicileLookupDb.deleteDomicileRecord('000', DbRecordType.ENTITY_DOMICILE);
      const result2 = await domicileLookupDb.getDomicileByEntityId('000');
      expect(result2).toBeNull();
    });

    it('should throw error when delete fails', async () => {
      domicileLookupDb = new DomicileLookupDb();

      await expect(domicileLookupDb.deleteDomicileRecord('', DbRecordType.ENTITY_DOMICILE)).rejects.toThrow(
        'Error deleting domicile record for entity.domicile - ',
      );
    });
  });

  describe('getDomicileValue', () => {
    beforeAll(() => {
      domicileLookupDb = new DomicileLookupDb();
    });

    it('should return domicile for entityUuid when domicileLookupEnabled is true', async () => {
      const mockEntityUuid = 'entity-123';
      const mockDomicile = Domicile.AU;

      jest.spyOn(domicileLookupDb, 'getDomicileByEntityId').mockResolvedValue(mockDomicile);

      const result = await domicileLookupDb.getDomicileValue({ entityUuid: mockEntityUuid });

      expect(result).toBe(mockDomicile);
      expect(domicileLookupDb.getDomicileByEntityId).toHaveBeenCalledWith(mockEntityUuid);
    });

    it('should return domicile for deviceUuid when domicileLookupEnabled is true', async () => {
      const mockSdkDeviceUuid = 'device-123';
      const mockDomicile = Domicile.AU;

      jest.spyOn(domicileLookupDb, 'getDomicileBySdkDeviceUuid').mockResolvedValue(mockDomicile);

      const result = await domicileLookupDb.getDomicileValue({ deviceUuid: mockSdkDeviceUuid });

      expect(result).toBe(mockDomicile);
      expect(domicileLookupDb.getDomicileBySdkDeviceUuid).toHaveBeenCalledWith(mockSdkDeviceUuid);
    });

    it('should return domicile for customerUuid when domicileLookupEnabled is true', async () => {
      const mockCustomerUuid = 'customer-456';
      const mockDomicile = Domicile.AU;

      jest.spyOn(domicileLookupDb, 'getDomicileByCustomerUuid').mockResolvedValue(mockDomicile);

      const result = await domicileLookupDb.getDomicileValue({ customerUuid: mockCustomerUuid });

      expect(result).toBe(mockDomicile);
      expect(domicileLookupDb.getDomicileByCustomerUuid).toHaveBeenCalledWith(mockCustomerUuid);
    });

    it('should return null when domicileLookupEnabled is false', async () => {
      const lookupDb = new DomicileLookupDb({ domicileLookupEnabled: false } as any);
      const result = await lookupDb.getDomicileValue({ entityUuid: 'entity-123' });

      expect(result).toBeNull();
    });

    it('should return null  when domicile is not found', async () => {
      const mockEntityUuid = 'entity-789';

      jest.spyOn(domicileLookupDb, 'getDomicileByEntityId').mockResolvedValue(null);

      const result = await domicileLookupDb.getDomicileValue({ entityUuid: mockEntityUuid });

      expect(result).toBeNull();
    });
  });

  describe('setDomicileContext', () => {
    const customerUuid = v4();
    const domicileLookupItem = { id: customerUuid, domicile: Domicile.GB, type: DbRecordType.CUSTOMER_DOMICILE };

    beforeAll(async () => {
      const putCommandInput = {
        TableName: domicileLookupDb.getTableName(),
        Item: domicileLookupItem,
      };
      await dbService.put(putCommandInput);
    });

    it('should be able to skip set domicile when domicileLookupEnabled is false', async () => {
      const env = new EnvironmentService();
      env.domicileLookupEnabled = false;
      const db = new DomicileLookupDb(env);
      await db.setDomicileContext({ customerUuid: v4() });
      const domicile = DomicileContext.getInstance().getDomicile();
      expect(domicile).toBeUndefined();
    });

    it('should be able to skip the domicile when domicileLookupEnabled is true but context has been set', async () => {
      const env = new EnvironmentService();
      env.domicileLookupEnabled = true;
      DomicileContext.getInstance().setDomicile(Domicile.AU);
      const db = new DomicileLookupDb(env);
      await db.setDomicileContext({ customerUuid });
      const domicile = DomicileContext.getInstance().getDomicile();
      expect(domicile).toBe(Domicile.AU);
      DomicileContext.getInstance().setDomicile(null);
    });

    it('should be able to set the domicile to context when domicileLookupEnabled is true', async () => {
      const env = new EnvironmentService();
      env.domicileLookupEnabled = true;
      const db = new DomicileLookupDb(env);
      await db.setDomicileContext({ customerUuid });
      const domicile = DomicileContext.getInstance().getDomicile();
      expect(domicile).toBe(Domicile.GB);
    });
  });

  describe('getAccountId', () => {
    const mockItem = {
      id: 'metadata',
      type: DbRecordType.ACCOUNT_ID_REGION_MAPPING,
      accounts: {
        'us-east-1': '************',
        'us-west-2': '************',
      },
    };
    beforeAll(async () => {
      domicileLookupDb = new DomicileLookupDb();
      const putCommandInput = {
        TableName: domicileLookupDb.getTableName(),
        Item: mockItem,
      };
      await dbService.put(putCommandInput);
    });

    it('should return account ID for a valid region', async () => {
      const mockRegion = 'us-east-1';
      const result = await domicileLookupDb.getAccountId(mockRegion);
      expect(result).toBe(mockItem.accounts[mockRegion]);
    });

    it('should throw an error when no account mapping is found for the region', async () => {
      const mockRegion = 'invalid-region';

      await expect(domicileLookupDb.getAccountId(mockRegion)).rejects.toThrow(
        `No account mapping found for region: ${mockRegion}`,
      );
    });
  });
});
