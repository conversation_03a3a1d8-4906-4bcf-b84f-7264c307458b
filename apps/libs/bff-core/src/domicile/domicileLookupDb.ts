import { DbRecordType } from '@npco/component-dto-core';

import type { DeleteCommandInput, GetCommandInput, PutCommandInput } from '@aws-sdk/lib-dynamodb';
import { DeleteCommand, GetCommand, PutCommand } from '@aws-sdk/lib-dynamodb';

import { EnvironmentService } from '../config';
import { DynamodbService } from '../dynamodb/dynamodbService';
import { ServerError } from '../error/graphQlError';
import { DomicileContext } from '../middleware/domicileContext';
import { type Domicile } from '../utils/domicile';
import { error, info, warn } from '../utils/logger';

export class DomicileLookupDb {
  tableName: string;

  private readonly COMPONENT_TABLE_NAME = 'DomicileLookup';

  constructor(
    private readonly envService = new EnvironmentService(),
    private readonly dynamodbService = DynamodbService.getInstance(),
  ) {
    const name = `${this.envService.stage}-ams-engine-${this.COMPONENT_TABLE_NAME}`;
    this.tableName = this.envService.domicileLookupTableName
      ? this.envService.domicileLookupTableName
      : `arn:aws:dynamodb:${this.envService.awsRegion}:${this.envService.globalAccountId}:table/${name}`;
    info(`DomicileLookupDb tableName: ${this.tableName} ${envService.awsRegion}`);
  }

  async getDomicileByCustomerUuid(customerUuid: string) {
    info(`calling getDomicileByCustomerUuid for customerUuid: `, customerUuid);
    try {
      const input: GetCommandInput = {
        TableName: this.tableName,
        Key: {
          id: customerUuid,
          type: DbRecordType.CUSTOMER_DOMICILE,
        },
      };
      const result = await this.dynamodbService.documentClient.send(new GetCommand(input));
      info(`result-getDomicileByCustomerUuid: ${JSON.stringify(result)}`, customerUuid);
      return result.Item?.domicile ?? null;
    } catch (err: any) {
      // change to error once all bff resolvers are updated
      warn(`error-getDomicileByCustomerUuid: ${err?.message}`, customerUuid);
      return null;
    }
  }

  async getDomicileBySdkDeviceUuid(deviceUuid: string): Promise<Domicile | null> {
    info(`calling getDomicileBySdkDeviceUuid for sdk-device`, deviceUuid);
    try {
      const input: GetCommandInput = {
        TableName: this.tableName,
        Key: {
          id: deviceUuid,
          type: DbRecordType.DEVICE_DOMICILE,
        },
      };
      const result = await this.dynamodbService.documentClient.send(new GetCommand(input));
      info(`result-getDomicileBySdkDeviceUuid: ${JSON.stringify(result)}`, deviceUuid);
      return result.Item?.domicile ?? null;
    } catch (err: any) {
      error(`error-getDomicileBySdkDeviceUuid: ${this.tableName} ${err?.message}`, deviceUuid);
      return null;
    }
  }

  async getDomicileByEntityId(entityUuid: string): Promise<Domicile | null> {
    info(`calling getDomicileByEntityId for entity`, entityUuid);
    try {
      const input: GetCommandInput = {
        TableName: this.tableName,
        Key: {
          id: entityUuid,
          type: DbRecordType.ENTITY_DOMICILE,
        },
      };
      const result = await this.dynamodbService.documentClient.send(new GetCommand(input));
      info(`result-getDomicileByEntityId: ${JSON.stringify(result)}`, entityUuid);
      return result.Item?.domicile ?? null;
    } catch (err: any) {
      error(`error-getDomicileByEntityId: ${this.tableName} ${err?.message}`, entityUuid);
      return null;
    }
  }

  async createDomicileRecord(id: string, dbRecordType: DbRecordType, domicile?: string) {
    info(`calling createDomicileRecord for ${dbRecordType}`, id);

    if (!domicile) {
      error(`error-createDomicileRecord: domicile is required`, id);
      throw new Error(`Domicile is required`);
    }
    const putParams: PutCommandInput = {
      TableName: this.tableName,
      Item: {
        id,
        type: dbRecordType,
        domicile,
      },
    };

    try {
      const result = await this.dynamodbService.documentClient.send(new PutCommand(putParams));
      info(`success-createDomicileRecord: ${JSON.stringify(result)} , id: ${id} domicile created`);
      return domicile;
    } catch (err: any) {
      error(`error-createDomicileRecord: ${this.tableName}: ${err?.message}`, id);
      throw new Error(`Error creating domicile record for ${dbRecordType} - ${id}`);
    }
  }

  async deleteDomicileRecord(id: string, dbRecordType: DbRecordType) {
    info(`calling deleteDomicileRecord for ${dbRecordType}`, id);
    const deleteParams: DeleteCommandInput = {
      TableName: this.tableName,
      Key: {
        id,
        type: dbRecordType,
      },
    };

    try {
      const result = await this.dynamodbService.documentClient.send(new DeleteCommand(deleteParams));
      info(`success-deleteDomicileRecord: ${JSON.stringify(result)} , id: ${id} domicile deleted`);
      return result;
    } catch (err: any) {
      error(`error-deleteDomicileRecord: ${err?.message}`, id);
      throw new Error(`Error deleting domicile record for ${dbRecordType} - ${id}`);
    }
  }

  async setDomicileContext(input: { entityUuid?: string; customerUuid?: string }) {
    if (this.envService.domicileLookupEnabled && !DomicileContext.getInstance().getDomicile()) {
      const domicile = await this.getDomicileValue(input);
      DomicileContext.getInstance().setDomicile(domicile);
    }
  }

  getTableName() {
    return this.tableName;
  }

  getDomicileValue = async (uuid: { customerUuid?: string; deviceUuid?: string; entityUuid?: string }) => {
    info(`look up domicile value ${JSON.stringify(uuid)}`);
    const { customerUuid, deviceUuid: sdkDeviceUuid, entityUuid } = uuid;

    let domicile: string | null = null;
    if (this.envService.domicileLookupEnabled) {
      if (entityUuid) {
        // get domicile value based on entity uuid
        domicile = await this.getDomicileByEntityId(entityUuid);
      } else if (sdkDeviceUuid) {
        // get domicile value based on sdk device uuid
        domicile = await this.getDomicileBySdkDeviceUuid(sdkDeviceUuid);
      } else if (customerUuid) {
        // get domicile value based on customer uuid
        domicile = await this.getDomicileByCustomerUuid(customerUuid);
      }
      if (!domicile) {
        warn(
          `Domicile not found for entity ${entityUuid} or customer ${customerUuid} or sdk deviceUuid ${sdkDeviceUuid}`,
        );
      }
    }
    return domicile;
  };

  getAccountId = async (region: string) => {
    info(`getAccountId for region: ${region}`);
    const input: GetCommandInput = {
      TableName: this.tableName,
      Key: {
        id: 'metadata',
        type: DbRecordType.ACCOUNT_ID_REGION_MAPPING,
      },
    };
    const result = await this.dynamodbService.documentClient.send(new GetCommand(input));
    info(`result-getAccountId: ${JSON.stringify(result)}`);
    if (!result?.Item?.accounts[region]) {
      error(`No account mapping found for region: ${region}`);
      throw new ServerError(`No account mapping found for region: ${region}`);
    }
    return result.Item.accounts[region];
  };
}
