import { CustomerRole, Status } from '@npco/component-dto-core';
import { DeviceStatus } from '@npco/component-dto-device';

import axios from 'axios';
import { mock, instance, when, anything } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import { Auth0Service } from '../authzero/auth0Service';
import { BffDynamoDbClient } from '../dynamodb/bffDynamoDbClient';
import { DynamodbService } from '../dynamodb/dynamodbService';
import { InvalidRequest, UnAuthorized } from '../error/graphQlError';
import { LambdaService } from '../lambda/lambdaService';
import { CacheType } from '../types/cacheType';
import { warn } from '../utils/logger';

import { InternalSessionService } from './internalSessionService';

jest.mock('../dynamodb/bffDynamoDbClient');
jest.mock('axios');

jest.mock('../utils/logger', () => {
  const originalModule = jest.requireActual('../utils/logger');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  };
});

describe('Session service test suite', () => {
  const mockAuthService = mock(Auth0Service);
  const mockLambdaService = mock(LambdaService);
  let entityUuid: string;

  const envService: any = {
    componentTableName: 'Entities',
    cacheTableName: 'SessionCache',
    accessTokenGsi: 'accessTokenGsi',
    entityCacheGsi: 'entityCacheGsi',
    cacheModelSerialGsi: 'cacheModelSerialGsi',
    cacheEntityModelSerialGsi: 'entityModelSerialGsi',
    entityGsi: 'entityGsi',
    shortIdGsi: 'shortIdGsi',
    deviceSerialModelGsi: 'deviceSerialModelGsi',
    sessionCacheTtlInSeconds: 86400,
    dynamoDbMarshallOptions: {
      convertEmptyValues: false,
      removeUndefinedValues: true,
      convertClassInstanceToMap: true,
    },
    deviceGsi: 'deviceGsi',
  };
  let sessionService: InternalSessionService;
  const bffDocumentClient = new BffDynamoDbClient(envService);
  const dynamodb = new DynamodbService(envService, bffDocumentClient);

  beforeEach(() => {
    entityUuid = uuidv4();
    (axios as any).post = jest.fn();

    sessionService = new InternalSessionService(
      dynamodb,
      envService,
      instance(mockAuthService),
      instance(mockLambdaService),
    );
  });

  const saveEntity = async (item: any) =>
    dynamodb.put({
      TableName: 'Entities',
      Item: {
        id: entityUuid,
        type: 'entity.core',
        ...item,
      },
    });

  const saveCacheItem = async (item: any) =>
    dynamodb.put({
      TableName: 'SessionCache',
      Item: item,
    });

  const validateCache = async (id: string, expected: any) => {
    const result = await dynamodb.query({
      TableName: 'SessionCache',
      KeyConditionExpression: 'id = :id',
      ExpressionAttributeValues: {
        ':id': id,
      },
    });

    expect((result.Items || [])[0]).toEqual(expected);
  };

  describe('Entity Status', () => {
    it('should get Entity Status', async () => {
      expect(await sessionService.getEntityStatuses(entityUuid)).toEqual({
        onboardingStatus: undefined,
        status: undefined,
      });
      await saveEntity({
        status: 'ACTIVE',
        onboardingStatus: 'ONBOARDED',
      });
      expect(await sessionService.getEntityStatuses(entityUuid)).toEqual({
        status: 'ACTIVE',
        onboardingStatus: 'ONBOARDED',
      });
    });

    it('should update Entity Status', async () => {
      const item = {
        id: uuidv4(),
        type: 'identitySub',
        entityUuid,
      };
      await saveCacheItem(item);
      await sessionService.updateEntityStatus(entityUuid, Status.DISABLED);
      await validateCache(item.id, {
        ...item,
        accountStatus: 'DISABLED',
      });
    });

    it('should clear identity device entity cache', async () => {
      const expiry = Math.floor(new Date().getTime() / 1000) + 86400;
      const item = {
        id: uuidv4(),
        deviceUuid: uuidv4(),
        type: CacheType.identity,
        entityUuid,
        status: 'ACTIVE',
        ttl: expiry,
      };
      await saveCacheItem(item);
      await sessionService.clearIdentityDeviceEntityCache(item.deviceUuid);
      const result = await dynamodb.query({
        TableName: 'SessionCache',
        IndexName: 'deviceGsi',
        KeyConditionExpression: 'deviceUuid = :deviceUuid AND #type = :type',
        ExpressionAttributeValues: {
          ':deviceUuid': item.deviceUuid,
          ':type': CacheType.identity,
        },
        ExpressionAttributeNames: {
          '#type': 'type',
        },
      });
      expect((result.Items || [])[0].entityUuid).toBeUndefined();
    });

    it('should get Devices For Entity', async () => {
      const device1 = {
        id: uuidv4(),
        type: 'deviceUuid',
        entityUuid,
      };
      await saveCacheItem(device1);
      const device2 = {
        id: uuidv4(),
        type: 'deviceUuid',
        entityUuid,
      };
      await saveCacheItem(device2);
      await saveCacheItem({
        id: uuidv4(),
        type: 'deviceUuid',
        entityUuid: uuidv4(),
      });
      expect((await sessionService.getDevicesForEntity(entityUuid)).length).toBe(2);
    });
  });

  describe('DeviceCache', () => {
    it('should successful save and clear device to the cache', async () => {
      const deviceUuid = uuidv4();
      const model = uuidv4().slice(-16);
      const serial = uuidv4().slice(-16);
      const cache = {
        status: DeviceStatus.ACTIVE,
        accessToken: 'accessToken',
        model,
        serial,
      };
      await sessionService.clearDeviceCache(deviceUuid, cache);
      await sessionService.saveDeviceCache(deviceUuid, cache);
      expect(await sessionService.checkDeviceCache(deviceUuid)).toEqual({
        id: deviceUuid,
        type: 'deviceUuid',
        ...cache,
      });
      await sessionService.clearDeviceCache(deviceUuid, cache);
      expect(await sessionService.checkDeviceCache(deviceUuid)).toEqual({
        id: deviceUuid,
        type: 'deviceUuid',
        modelSerial: `${model}.${serial}`,
        ...cache,
      });
    });

    it('should throw error when device id not in cache', async () => {
      await expect(sessionService.checkDeviceCache('id')).rejects.toThrowError('Device not found id');
    });

    it('should handle not found when getting device by modelSerial', async () => {
      const model = uuidv4().slice(-16);
      const serial = uuidv4().slice(-16);
      expect(await sessionService.getDeviceByModelSerial(model, serial)).toEqual(null);
    });
  });

  describe('checkDeviceSession', () => {
    let accessToken: string;
    beforeEach(() => {
      accessToken = uuidv4();
    });
    it('should get an device session', async () => {
      const deviceUuid = uuidv4();
      const item = {
        accessToken,
        type: 'deviceUuid',
        entityUuid,
        id: deviceUuid,
        customerUuid: 'cid',
      };
      await saveCacheItem(item);
      await expect(sessionService.checkDeviceSession(accessToken, false)).resolves.toEqual(item);
    });

    it('should get an active device session and validate', async () => {
      const deviceUuid = uuidv4();
      const item = {
        accessToken,
        type: 'deviceUuid',
        entityUuid,
        id: deviceUuid,
        customerUuid: 'cid',
        status: 'ACTIVE',
        accountStatus: 'ACTIVE',
      };
      await saveCacheItem(item);
      expect(await sessionService.checkDeviceSession(accessToken, true)).toEqual(item);
    });

    it('should not get an inactive device session', async () => {
      const deviceUuid = uuidv4();
      const item = {
        accessToken,
        type: 'deviceUuid',
        entityUuid,
        id: deviceUuid,
        customerUuid: 'cid',
      };
      await saveCacheItem(item);
      await expect(sessionService.checkDeviceSession(accessToken, true)).rejects.toEqual(
        new InvalidRequest('Device is disabled or inactive.'),
      );
    });

    it('should throw an error on invalid token', async () => {
      when(mockAuthService.decodeToken(anything())).thenResolve({
        sub: 'auth0|12313-12312',
      } as any);
      await expect(sessionService.checkDeviceSession(accessToken, false)).rejects.toThrow(
        new Error('Invalid access token - Device not found.'),
      );
    });
  });

  describe('checkIdentitySession', () => {
    let accessToken: string;
    beforeEach(() => {
      accessToken = uuidv4();
    });
    it('should successful get an identity session', async () => {
      const item = {
        type: 'identitySub',
        role: 'ADMIN',
        entityUuid,
        id: accessToken,
        customerUuid: 'c123',
        ttl: new Date().getTime() + 24 * 60 * 60,
      };
      await saveCacheItem(item);
      await expect(sessionService.checkIdentitySession(accessToken, false)).resolves.toEqual(item);
    });

    it('should successful get an identity session and validate', async () => {
      const item = {
        type: 'identitySub',
        role: 'ADMIN',
        entityUuid,
        id: accessToken,
        customerUuid: 'c123',
        ttl: new Date().getTime() + 24 * 60 * 60,
      };
      await saveCacheItem(item);
      await expect(sessionService.checkIdentitySession(accessToken, true)).resolves.toEqual(item);
    });

    it('should fail when account status blocked', async () => {
      const item = {
        type: 'identitySub',
        role: 'ADMIN',
        entityUuid,
        id: accessToken,
        customerUuid: 'c123',
        accountStatus: 'BLOCKED',
        ttl: new Date().getTime() + 24 * 60 * 60,
      };
      await saveCacheItem(item);
      await expect(sessionService.checkIdentitySession(accessToken, true)).rejects.toEqual(
        new InvalidRequest('Account is disabled or inactive.'),
      );
    });

    it('should create identity session when token not found in db', async () => {
      when(mockAuthService.getSubFromAccessToken(anything())).thenResolve('user');
      when(mockAuthService.getUserById(anything())).thenResolve({
        sub: 'user',
        nickname: '',
        name: '',
        picture: '',
        updated_at: '',
        email: '<EMAIL>',
        email_verified: true,
        user_metadata: {
          role: 'ADMIN',
          entityUuid: 'entityUuid',
          customerUuid: 'customerUuid',
        },
      });
      await expect(sessionService.checkIdentitySession(accessToken, false)).resolves.toEqual({
        auth0sub: 'user',
        customerUuid: 'customerUuid',
        role: 'ADMIN',
        id: expect.any(String),
        ttl: expect.any(Number),
        type: 'identitySub',
      });
    });

    it('should throw an error on invalid token', async () => {
      when(mockAuthService.getUserById(anything())).thenReject(new Error('something went wrong'));
      await expect(sessionService.checkIdentitySession(accessToken, true)).rejects.toThrow(
        new Error('Invalid access token.'),
      );
    });
  });

  describe('checkSdkDeviceAndIdentitySession', () => {
    let accessToken: string;

    beforeEach(() => {
      accessToken = uuidv4();
    });

    it('should throws error when existing identity session deviceUuid does not match the request deviceUuid', async () => {
      const item = {
        type: 'identitySub',
        entityUuid,
        id: accessToken,
        customerUuid: 'customerUuid',
        deviceUuid: 'deviceUuid',
        ttl: new Date().getTime() + 24 * 60 * 60,
      };
      const deviceUuid2 = uuidv4();
      await saveCacheItem(item);
      await expect(sessionService.checkSdkDeviceAndIdentitySession(deviceUuid2, accessToken)).rejects.toEqual(
        new UnAuthorized('Action is not allowed'),
      );
    });

    it('should succeed when a session exist and status is valid', async () => {
      const item = {
        type: 'identitySub',
        entityUuid,
        id: accessToken,
        customerUuid: 'customerUuid',
        deviceUuid: 'deviceUuid',
        ttl: new Date().getTime() + 24 * 60 * 60,
      };
      await saveCacheItem(item);
      await expect(sessionService.checkSdkDeviceAndIdentitySession('deviceUuid', accessToken)).resolves.toEqual(item);
    });

    it('should create sdk identity session when session not found in db', async () => {
      const deviceCache = {
        id: uuidv4(),
        type: 'deviceUuid',
        entityUuid: uuidv4(),
      };
      await saveCacheItem(deviceCache);
      when(mockAuthService.getSubAndCustomerFromAccessToken(anything())).thenResolve({
        sub: 'user',
        customerUuid: 'customerUuid',
      } as any);

      await expect(sessionService.checkSdkDeviceAndIdentitySession(deviceCache.id, accessToken)).resolves.toEqual({
        auth0sub: 'user',
        customerUuid: 'customerUuid',
        entityUuid: deviceCache.entityUuid,
        deviceUuid: deviceCache.id,
        id: expect.any(String),
        ttl: expect.any(Number),
        type: 'identitySub',
      });
    });

    it('should update identity session with entityUuid when device session entityUuid is presented', async () => {
      when(mockAuthService.getSubAndCustomerFromAccessToken(anything())).thenResolve({
        sub: 'user',
        customerUuid: 'customerUuid',
      } as any);

      const deviceCache = {
        id: uuidv4(),
        type: 'deviceUuid',
      };
      await saveCacheItem(deviceCache);
      await expect(sessionService.checkSdkDeviceAndIdentitySession(deviceCache.id, accessToken)).resolves.toEqual({
        auth0sub: 'user',
        customerUuid: 'customerUuid',
        deviceUuid: deviceCache.id,
        id: expect.any(String),
        ttl: expect.any(Number),
        type: 'identitySub',
      });

      const assignedDeviceCache = {
        ...deviceCache,
        entityUuid: uuidv4(),
      };
      await saveCacheItem(assignedDeviceCache);
      await expect(sessionService.checkSdkDeviceAndIdentitySession(deviceCache.id, accessToken)).resolves.toEqual({
        auth0sub: 'user',
        customerUuid: 'customerUuid',
        entityUuid: assignedDeviceCache.entityUuid,
        deviceUuid: deviceCache.id,
        id: expect.any(String),
        ttl: expect.any(Number),
        type: 'identitySub',
      });
    });
  });

  describe('validateSdkIdentitySession', () => {
    let accessToken: string;

    beforeEach(() => {
      accessToken = uuidv4();
    });

    it('should succeed when a session exist and status is valid', async () => {
      const item = {
        type: 'identitySub',
        id: accessToken,
        customerUuid: 'customerUuid',
        ttl: new Date().getTime() + 24 * 60 * 60,
      };
      await saveCacheItem(item);
      await expect(sessionService.checkSdkIdentitySession(accessToken)).resolves.toEqual(item);
    });

    it('should create sdk identity session when session not found in db', async () => {
      const customerUuid = uuidv4();
      when(mockAuthService.getSubAndCustomerFromAccessToken(anything())).thenResolve({
        sub: 'user',
        customerUuid,
      });
      await expect(sessionService.checkSdkIdentitySession(accessToken)).resolves.toEqual({
        auth0sub: 'user',
        customerUuid,
        id: expect.any(String),
        ttl: expect.any(Number),
        type: 'identitySub',
      });
    });
  });

  describe('updateAllCustomerSessionsInCache', () => {
    it('should successful update sessions', async () => {
      const customerUuid = uuidv4();
      const accessToken1 = uuidv4();
      const accessToken2 = uuidv4();
      await sessionService.updateAllCustomerSessionsInCache(entityUuid, customerUuid, {
        status: Status.ACTIVE,
      });
      const cache = {
        type: 'identitySub',
        role: 'ADMIN',
        entityUuid,
        id: accessToken1,
        ttl: new Date().getTime() + 24 * 60 * 60,
        customerUuid,
      };
      await saveCacheItem(cache);
      const cache2 = {
        type: 'identitySub',
        role: 'ADMIN',
        entityUuid,
        id: accessToken2,
        ttl: new Date().getTime() + 24 * 60 * 60,
        customerUuid,
      };
      await saveCacheItem(cache2);

      expect(await sessionService.checkIdentitySession(accessToken1, true)).toEqual(cache);
      expect(await sessionService.checkIdentitySession(accessToken2, true)).toEqual(cache2);
      await sessionService.updateAllCustomerSessionsInCache(entityUuid, customerUuid, {
        status: Status.DISABLED,
      });
      await expect(sessionService.checkIdentitySession(accessToken1, true)).rejects.toThrow(
        new Error('User is disabled.'),
      );
      await expect(sessionService.checkIdentitySession(accessToken2, true)).rejects.toThrow(
        new Error('User is disabled.'),
      );
    });
  });
  describe('checkOnboardingSession - onboarding flow', () => {
    let accessToken: string;
    beforeEach(() => {
      accessToken = uuidv4();
    });

    it('should successful get an onboarding session and update', async () => {
      when(mockAuthService.getSubFromAccessToken(anything())).thenResolve('user');
      await sessionService.updateOnboardingCachedSession(accessToken, {
        customerUuid: 'customer',
        entityUuid: 'eid',
        role: CustomerRole.ADMIN,
      });
      await expect(sessionService.checkOnboardingSession(accessToken)).resolves.toEqual({
        auth0sub: 'user',
        customerUuid: 'customer',
        entityUuid: 'eid',
        id: accessToken,
        role: 'ADMIN',
        ttl: expect.any(Number),
        type: 'onboarding',
      });
      const item = {
        type: 'onboarding',
        entityUuid,
        id: accessToken,
        ttl: new Date().getTime() + 24 * 60 * 60,
      };
      await saveCacheItem(item);
      await expect(sessionService.checkOnboardingSession(accessToken)).resolves.toEqual(item);
      await sessionService.updateOnboardingCachedSession(accessToken, {
        customerUuid: 'customerNew',
        entityUuid: 'entityNew',
        role: CustomerRole.MANAGER,
      });
      await expect(sessionService.checkOnboardingSession(accessToken)).resolves.toEqual({
        auth0sub: 'user',
        customerUuid: 'customerNew',
        entityUuid: 'entityNew',
        id: accessToken,
        role: 'MANAGER',
        ttl: expect.any(Number),
        type: 'onboarding',
      });
    });

    it('should get entity session when token not found in db', async () => {
      when(mockAuthService.getSubFromAccessToken(anything())).thenResolve('user');
      when(mockAuthService.getUserById(anything())).thenResolve({
        sub: 'user',
        nickname: '',
        name: '',
        picture: '',
        updated_at: '',
        email: '<EMAIL>',
        email_verified: true,
        user_metadata: {
          role: 'ADMIN',
          entityUuid: 'entityUuid',
          customerUuid: 'customerUuid',
        },
      });
      await expect(sessionService.checkOnboardingSession(accessToken)).resolves.toEqual({
        auth0sub: 'user',
        customerUuid: 'customerUuid',
        role: 'ADMIN',
        id: accessToken,
        ttl: expect.any(Number),
        type: 'onboarding',
      });
      await validateCache(accessToken, {
        auth0sub: 'user',
        customerUuid: 'customerUuid',
        role: 'ADMIN',
        id: accessToken,
        ttl: expect.any(Number),
        type: 'onboarding',
      });
    });

    it('should fail when token not found in db and no user_metadata', async () => {
      when(mockAuthService.getSubFromAccessToken(anything())).thenResolve('user');
      when(mockAuthService.getUserById(anything())).thenResolve({
        sub: 'user',
        nickname: '',
        name: '',
        picture: '',
        updated_at: '',
        email: '<EMAIL>',
        email_verified: true,
      });
      await expect(sessionService.checkOnboardingSession(accessToken)).resolves.toEqual({
        auth0sub: 'user',
        id: accessToken,
        ttl: expect.any(Number),
        type: 'onboarding',
      });
    });

    it('should throw an error on invalid token', async () => {
      when(mockAuthService.getUserById(anything())).thenReject(new Error('something went wrong'));
      await expect(sessionService.checkOnboardingSession(accessToken)).rejects.toThrow(
        new Error('Invalid access token.'),
      );
    });
  });

  describe('zeller session Id test suite', () => {
    const mockedLambdaService = {
      invokeCommand: jest.fn().mockResolvedValue({ status: true }),
    } as any;
    let zellerSessionId: string;
    let customerUuid: string;
    beforeEach(() => {
      zellerSessionId = uuidv4();
      customerUuid = uuidv4();
      sessionService = new InternalSessionService(
        dynamodb,
        { ...envService, zellerSessionIdCheckEnabled: true, zellerSessionIdTtlInSeconds: 2000 } as any,
        instance(mockAuthService),
        mockedLambdaService,
      );
      jest.resetAllMocks();
    });

    it('should return immediately with checkEnabled flag set to false', async () => {
      sessionService = new InternalSessionService(
        dynamodb,
        { ...envService, zellerSessionIdCheckEnabled: false } as any,
        instance(mockAuthService),
        mockedLambdaService,
      );
      await sessionService.checkZellerSessionIdentity({
        entityUuid: uuidv4(),
        customerUuid: uuidv4(),
        zellerSessionId: uuidv4(),
      });
    });

    it('should return immediately null as the zellerSessionId already exists', async () => {
      const item = {
        id: zellerSessionId,
        customerUuid,
        entityUuid,
        createdTimestamp: new Date().toJSON(),
        type: CacheType.zellerSession,
      };
      await saveCacheItem(item);
      const response = await sessionService.checkZellerSessionIdentity({
        entityUuid,
        customerUuid,
        zellerSessionId,
      });
      expect(response).toBeUndefined();
      await validateCache(item.id, {
        ...item,
      });
    });
    it('should invoke lambda as new event coming through', async () => {
      await sessionService.checkZellerSessionIdentity({
        entityUuid,
        customerUuid,
        zellerSessionId,
      });
      await validateCache(zellerSessionId, {
        id: zellerSessionId,
        customerUuid,
        entityUuid,
        createdTimestamp: expect.any(String),
        ttl: expect.any(Number),
        type: CacheType.zellerSession,
      });
    });

    it('should send a warning message, if no zellerSessionId passed through and check is enabled', async () => {
      await sessionService.checkZellerSessionIdentity({
        entityUuid,
        customerUuid,
      });
      expect(warn).toHaveBeenCalledWith('zellerSessionId missing', customerUuid);
    });

    it('should throw error with missing table details', async () => {
      sessionService = new InternalSessionService(
        dynamodb,
        { zellerSessionIdCheckEnabled: true, zellerSessionIdTtlInSeconds: 2000 } as any,
        instance(mockAuthService),
        mockedLambdaService,
      );
      await sessionService.checkZellerSessionIdentity({
        entityUuid,
        customerUuid,
        zellerSessionId,
      });
      expect(warn).toHaveBeenLastCalledWith('checkZellerSessionIdentity failed');
    });

    it('should throw log error with invokeLambda', async () => {
      sessionService = new InternalSessionService(
        dynamodb,
        { ...envService, zellerSessionIdCheckEnabled: true, zellerSessionIdTtlInSeconds: 2000 } as any,
        instance(mockAuthService),
        {} as any,
      );
      await sessionService.checkZellerSessionIdentity({
        entityUuid,
        customerUuid,
        zellerSessionId,
      });
      expect(warn).toHaveBeenCalledWith('this.lambdaService.invokeCommand is not a function');
      expect(warn).toHaveBeenCalledWith('checkZellerSessionIdentity failed');
    });
  });

  describe('device verification', () => {
    it('should be able verify device uuid in cache', async () => {
      const item = {
        id: uuidv4(),
        status: 'ACTIVE',
        type: CacheType.device,
      };
      await saveCacheItem(item);
      expect(await sessionService.isDeviceUuidInCache(item.id)).toBe(true);
    });

    it('should response false if device uuid doesnt exist in cache', async () => {
      expect(await sessionService.isDeviceUuidInCache('invalid id')).toBe(false);
    });

    it('should be able to query device based on tokens', async () => {
      const item = {
        id: uuidv4(),
        status: 'ACTIVE',
        type: CacheType.device,
        accessToken: uuidv4(),
        refreshToken: uuidv4(),
        idToken: uuidv4(),
      };
      await saveCacheItem(item);
      expect(await sessionService.getValidDeviceToken(item.id, item.accessToken, item.refreshToken)).toMatchObject({
        status: 'ACTIVE',
      });
    });

    it('should be able to query device based previous token', async () => {
      const item = {
        id: uuidv4(),
        status: 'ACTIVE',
        type: CacheType.device,
        accessToken: uuidv4(),
        previousAccessToken: uuidv4(),
        previousRefreshToken: uuidv4(),
        refreshToken: uuidv4(),
        idToken: uuidv4(),
      };
      await saveCacheItem(item);
      expect(
        await sessionService.getValidDeviceToken(item.id, item.previousAccessToken, item.previousRefreshToken),
      ).toMatchObject({
        status: 'ACTIVE',
      });
    });

    it('should not be able to query device based previous token and bad refresh token', async () => {
      const item = {
        id: uuidv4(),
        status: 'ACTIVE',
        type: CacheType.device,
        accessToken: uuidv4(),
        previousAccessToken: uuidv4(),
        previousRefreshToken: uuidv4(),
        refreshToken: uuidv4(),
        idToken: uuidv4(),
      };
      await saveCacheItem(item);
      expect(await sessionService.getValidDeviceToken(uuidv4(), item.accessToken, uuidv4())).toBe(null);
    });

    it('should response false if device uuid does not match', async () => {
      const item = {
        id: uuidv4(),
        status: 'ACTIVE',
        type: CacheType.device,
        accessToken: uuidv4(),
      };
      await saveCacheItem(item);
      expect(await sessionService.getValidDeviceToken(uuidv4(), item.accessToken, uuidv4())).toBe(null);
    });

    it('should response false if device token not found if INACTIVE', async () => {
      const item = {
        id: uuidv4(),
        status: 'INACTIVE',
        type: CacheType.device,
        accessToken: uuidv4(),
        refreshToken: uuidv4(),
      };
      await saveCacheItem(item);
      expect(await sessionService.getValidDeviceToken(item.id, item.accessToken, item.refreshToken)).toBe(null);
    });

    it('should response false if device token not found if DISABLED', async () => {
      const item = {
        id: uuidv4(),
        status: 'DISABLED',
        type: CacheType.device,
        accessToken: uuidv4(),
        refreshToken: uuidv4(),
      };
      await saveCacheItem(item);
      expect(await sessionService.getValidDeviceToken(item.id, item.accessToken, item.refreshToken)).toBe(null);
    });

    it('should be able to get refresh token', async () => {
      const item = {
        id: uuidv4(),
        status: 'ACTIVE',
        type: CacheType.device,
        refreshToken: uuidv4(),
      };
      await saveCacheItem(item);
      expect(await sessionService.getRefreshTokenByUuid(item.id)).toBe(item.refreshToken);
    });

    it('should not find refresh token', async () => {
      const item = {
        id: uuidv4(),
        status: 'INACTIVE',
        type: CacheType.device,
      };
      await saveCacheItem(item);
      await expect(sessionService.getRefreshTokenByUuid(item.id)).rejects.toThrowError(Error);
    });

    it('should not find device', async () => {
      await expect(sessionService.getRefreshTokenByUuid('unknown')).rejects.toThrowError(Error);
    });
  });

  describe('setEntityInIdentityCache Unit Test Suite', () => {
    it('should set entityUuid in the identity cache when no existing entityUuid is present', async () => {
      const record = {
        id: uuidv4(),
        type: 'identitySub',
        ttl: Math.floor(new Date().getTime() / 1000) + 86400,
      };

      await saveCacheItem(record);

      await sessionService.setEntityInIdentityCache(record.id, entityUuid);

      await validateCache(record.id, { ...record, entityUuid });
    });

    it('should not override the existing entityUuid if override is false', async () => {
      const record = {
        id: uuidv4(),
        type: 'identitySub',
        ttl: Math.floor(new Date().getTime() / 1000) + 86400,
        entityUuid: uuidv4(),
      };

      await saveCacheItem(record);

      await sessionService.setEntityInIdentityCache(record.id, entityUuid, false);

      // expected record should be same as the input if override is false
      await validateCache(record.id, record);
    });

    it('should override the existing entityUuid if override is true', async () => {
      const record = {
        id: uuidv4(),
        type: 'identitySub',
        ttl: Math.floor(new Date().getTime() / 1000) + 86400,
        entityUuid: uuidv4(),
      };

      await saveCacheItem(record);

      await sessionService.setEntityInIdentityCache(record.id, entityUuid, true);

      // overrides the existing identity session with the new entityUuid
      await validateCache(record.id, { ...record, entityUuid });
    });

    // this case should never exist in the actual scenario
    it('should handle cases when accessToken does not exist in cache', async () => {
      const record = {
        id: uuidv4(),
        type: 'identitySub',
        ttl: Math.floor(new Date().getTime() / 1000) + 86400,
      };

      const result = await sessionService.setEntityInIdentityCache(record.id, 'test-uuid');

      expect(result).toBeUndefined();
    });
  });
});
