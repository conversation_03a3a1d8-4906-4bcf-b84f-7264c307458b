import { Status, DbRecordType } from '@npco/component-dto-core';
import { CustomerLoginEventDto } from '@npco/component-dto-customer';
import { DeviceStatus } from '@npco/component-dto-device';
import type { AccountStatus } from '@npco/component-dto-entity';
import { OnboardingStatus } from '@npco/component-dto-entity';

import type { Auth0Service } from '../authzero/auth0Service';
import type { EnvironmentService } from '../config/envService';
import type { DynamodbService } from '../dynamodb/dynamodbService';
import type { DbJsonOject } from '../dynamodb/types';
import { NotFoundError, UnAuthorized, InvalidRequest } from '../error/graphQlError';
import { getAccountStatusMessage, getUserStatusMessage } from '../error/statusMessage';
import type { LambdaService } from '../lambda/lambdaService';
import type { MobileDeviceUuidResponse } from '../types';
import { CacheType } from '../types/cacheType';
import type { DeviceUuidResponse } from '../types/deviceUuidResponse';
import type { PublicPart } from '../types/publicPart';
import type {
  SessionCacheItem,
  SessionCache,
  IdentityCache,
  UserMetadata,
  DeviceCache,
  MobileDeviceCache,
  IdentityCacheItem,
  IdentityDeviceCacheItem,
  SdkIdentityDeviceCacheItem,
  DecodedMetadata,
  SdkIdentityDeviceCache,
  Auth0Metadata,
} from '../types/sessionCache';
import { getLogSafeSensitiveDataObject } from '../utils/dataMasking';
import { debug, info, warn, error } from '../utils/logger';

import { CacheDb } from './cacheDb';

export class InternalSessionService {
  private readonly sessionCacheDb: CacheDb;

  private readonly metadata: string;

  constructor(
    private readonly dynamodbService: PublicPart<DynamodbService>,
    private readonly envService: PublicPart<EnvironmentService>,
    private readonly auth0Service: Auth0Service,
    private readonly lambdaService: LambdaService,
  ) {
    this.sessionCacheDb = new CacheDb(envService, dynamodbService);
    this.metadata = 'user_metadata';
  }

  public async getEntityStatuses(
    entityUuid?: string,
  ): Promise<{ status?: Status; onboardingStatus?: OnboardingStatus; accountStatus?: AccountStatus }> {
    if (entityUuid) {
      const entity = await this.getEntityDbItem(entityUuid);
      if (entity) {
        const { status, onboardingStatus, accountStatus } = entity as {
          status?: Status;
          onboardingStatus?: OnboardingStatus;
          accountStatus?: AccountStatus;
        };
        return { status, onboardingStatus, accountStatus };
      }
    }
    return {};
  }

  public checkEntityOnboardingStatus = async (entityUuid: string): Promise<boolean> => {
    const entity = await this.dynamodbService.queryIdByType('', entityUuid, DbRecordType.ENTITY, {
      ignoreEntity: true,
    });
    if (!entity.Items || entity.Items.length === 0) {
      return Promise.reject(new NotFoundError('Account not found.', entityUuid));
    }
    const { onboardingStatus } = entity.Items[0];
    return onboardingStatus === OnboardingStatus.ONBOARDED || onboardingStatus === OnboardingStatus.RC_ONBOARDED;
  };

  public async updateEntityStatus(entityUuid: string, status: Status) {
    await this.sessionCacheDb.updateCacheForEntityStatus(entityUuid, status);
  }

  public async getDevicesForEntity(entityUuid: string) {
    const devicesForEntity = await this.sessionCacheDb.getAllByEntityUuid(entityUuid, CacheType.device);
    return devicesForEntity.map((deviceCache: any) => deviceCache.id);
  }

  public async getDeviceByModelSerial(model: string, serial: string): Promise<DeviceUuidResponse | null> {
    const response = await this.sessionCacheDb.getDeviceByModelSerial(model, serial);
    if (response.Items && response.Items.length > 0) {
      const item = response.Items[0];
      return {
        id: item.id as string,
        model: item.model as string,
        serial: item.serial as string,
      };
    }
    return null;
  }

  public async getDeviceByEntityModelSerial(
    entityUuid: string,
    model: string,
    serial: string,
  ): Promise<MobileDeviceUuidResponse | null> {
    const response = await this.sessionCacheDb.getDeviceByEntityModelSerial(entityUuid, model, serial);
    if (response.Items && response.Items.length > 0) {
      const item = response.Items[0];
      return {
        deviceUuid: item.id,
        model: item.model,
        serial: item.serial,
      };
    }
    return null;
  }

  public async checkDeviceSession(accessToken: string, validateStatus: boolean): Promise<IdentityDeviceCacheItem> {
    const existingSession = await this.sessionCacheDb.getDeviceByAccessToken(accessToken);
    debug(`checkDeviceSession: ${JSON.stringify(existingSession, null, 2)}`);
    if (!existingSession.Items || existingSession.Items.length === 0) {
      warn(`checkDeviceSession: Device check failed, no device for token was found.`);
      return Promise.reject(new UnAuthorized('Invalid access token - Device not found.'));
    }
    const device = existingSession.Items[0] as IdentityDeviceCacheItem;
    if (validateStatus) {
      const { status: deviceStatus, id } = device;
      if (deviceStatus !== DeviceStatus.ACTIVE) {
        warn(`checkDeviceSession:Device check failed - status: ${deviceStatus}`, id);
        return Promise.reject(new UnAuthorized('Device is disabled or inactive.'));
      }
    }
    debug('checkDeviceSession: OK', device.id);
    return device;
  }

  public async isDeviceUuidInCache(deviceUuid: string): Promise<boolean> {
    const response = await this.sessionCacheDb.getCachedItem(deviceUuid, CacheType.device);
    return Boolean(response.Items && response.Items.length > 0);
  }

  public async getValidDeviceToken(
    deviceUuid: string,
    accessToken: string,
    refreshToken: string,
  ): Promise<SessionCacheItem | null> {
    const items = await this.sessionCacheDb.findDeviceByTokens(deviceUuid, accessToken, refreshToken);
    if (items.Items && items.Items.length === 1) {
      const deviceStatus = items.Items[0].status;
      if (deviceStatus === DeviceStatus.ACTIVE) {
        return items.Items[0] as SessionCacheItem;
      }
    }
    return null;
  }

  public async getRefreshTokenByUuid(deviceUuid: string): Promise<string> {
    const cached = await this.sessionCacheDb.getCachedItem(deviceUuid, CacheType.device);
    debug(`getRefreshTokenByUuid: ${JSON.stringify(cached, null, 2)}`);
    let message = 'Invalid refresh token - Device not found.';
    if (cached.Items && cached.Items.length === 1) {
      const { refreshToken } = cached.Items[0];
      if (refreshToken) {
        return refreshToken as string;
      }
      message = 'Device is inactive - no refresh token found.';
    }
    error(message, deviceUuid);
    return Promise.reject(new InvalidRequest(message));
  }

  public async checkDeviceCache<T = IdentityDeviceCacheItem>(deviceUuid: string): Promise<T> {
    const device = await this.sessionCacheDb.getCachedItem(deviceUuid, CacheType.device);
    if (device.Items !== undefined && device.Items.length === 1) {
      return device.Items[0] as T;
    }
    warn('Device check failed', deviceUuid);
    return Promise.reject(new NotFoundError('Device not found', deviceUuid));
  }

  public async saveDeviceCache(deviceUuid: string, input: DeviceCache | MobileDeviceCache) {
    const logSafeInput = getLogSafeSensitiveDataObject(input);
    info(`Saving device cache ${JSON.stringify(logSafeInput)}`);
    await this.sessionCacheDb.saveDeviceCache(deviceUuid, input);
  }

  public async clearDeviceCache(deviceUuid: string, input: DeviceCache) {
    const logSafeInput = getLogSafeSensitiveDataObject(input);
    info(`Clearing device cache ${JSON.stringify(logSafeInput)}`);
    await this.sessionCacheDb.clearDeviceCache(deviceUuid, input);
  }

  public async clearIdentityDeviceEntityCache(deviceUuid: string) {
    await this.sessionCacheDb.clearIdentityDeviceEntityCache(deviceUuid);
  }

  // Merchant Portal
  public async updateOnboardingCachedSession(accessToken: string, input: Auth0Metadata): Promise<void> {
    try {
      const userId = await this.auth0Service.getSubFromAccessToken(accessToken);
      info(`user: ${userId}`);
      const session: IdentityCache = {
        ...input,
        auth0sub: userId,
      };
      info(`updated onboarding session to cache table ${JSON.stringify(session)}`);
      await this.sessionCacheDb.saveCachedSession(accessToken, session, CacheType.onboarding);
    } catch (e) {
      error(e);
      return Promise.reject(e); // NOSONAR
    }
    return Promise.resolve();
  }

  public async checkOnboardingSession(accessToken: string): Promise<SessionCacheItem> {
    return this.getCachedSessionOrCreateNew<SessionCacheItem>(accessToken, CacheType.onboarding);
  }

  public async checkZellerSessionIdentity(session: {
    entityUuid?: string;
    customerUuid: string;
    zellerSessionId?: string;
  }): Promise<void> {
    try {
      if (!this.envService.zellerSessionIdCheckEnabled) {
        return;
      }

      const { entityUuid, customerUuid, zellerSessionId } = session;
      debug(
        `checkZellerSessionIdentity called and enabled zellerSessionId: ${zellerSessionId} entityUuid: ${entityUuid}`,
        customerUuid,
      );
      if (!zellerSessionId || zellerSessionId.trim().length === 0) {
        warn('zellerSessionId missing', customerUuid);
        return;
      }

      const response = await this.sessionCacheDb.saveZellerSession(
        {
          zellerSessionId,
          customerUuid,
          entityUuid,
          createdTimestamp: new Date().toJSON(),
        },
        CacheType.zellerSession,
      );
      if (response === null) {
        debug(`checkZellerSessionIdentity: zellerSessionId already in db ${zellerSessionId}`, customerUuid);
        return;
      }
      const cqrsCommand = 'Customer.Login';
      const dto = new CustomerLoginEventDto({
        customerUuid,
        entityUuid,
        zellerSessionId,
      });
      debug(
        `checkZellerSessionIdentity: invoking cqrsCommand: ${cqrsCommand}, dto: ${JSON.stringify(dto)}`,
        customerUuid,
      );
      await this.lambdaService.invokeCommand(cqrsCommand, dto);
    } catch (err: any) {
      warn(err.message);
      warn('checkZellerSessionIdentity failed');
    }
  }

  public async checkIdentitySession(
    accessToken: string,
    validateStatus: boolean,
    zellerSessionId?: string,
  ): Promise<IdentityCacheItem> {
    const session = await this.getCachedSessionOrCreateNew<IdentityCacheItem>(accessToken, CacheType.identity);

    if (validateStatus) {
      const { accountStatus, status } = session;
      // remove this when accessControlMiddleware is enforced everywhere
      // as it is only required by legacy identity middleware
      if (accountStatus && accountStatus !== Status.ACTIVE) {
        warn(`Account is disabled or inactive. ${accountStatus}`);
        const { message, errorInfo } = getAccountStatusMessage(accountStatus);
        return Promise.reject(new UnAuthorized(message, undefined, errorInfo));
      }
      if (status && status !== Status.ACTIVE) {
        warn(`User is disabled or inactive. ${status}`);
        const { message, errorInfo } = getUserStatusMessage(status);
        return Promise.reject(new UnAuthorized(message, undefined, errorInfo));
      }
    }
    await this.checkZellerSessionIdentity({
      entityUuid: session.entityUuid,
      customerUuid: session.customerUuid,
      zellerSessionId,
    });
    return session;
  }

  public async checkSdkDeviceAndIdentitySession(
    deviceUuid: string,
    accessToken: string,
  ): Promise<SdkIdentityDeviceCacheItem> {
    const record = await this.sessionCacheDb.getCachedItem(accessToken, CacheType.identity);
    const session = record?.Items?.[0];

    if (session?.deviceUuid && session.deviceUuid !== deviceUuid) {
      warn(`deviceUuid ${deviceUuid} does not match session deviceUuid ${session.deviceUuid}`);
      throw new UnAuthorized('Action is not allowed');
    }

    if (session?.entityUuid) {
      return session as SdkIdentityDeviceCacheItem;
    }

    // identity session can exist without entityUuid when device is not assigned to an entity yet
    // once device is assigned to an entity, next query/mutation will update the identity session with entityUuid
    const deviceCache = await this.checkDeviceCache(deviceUuid);
    if (session && !deviceCache?.entityUuid) {
      return session as SdkIdentityDeviceCacheItem;
    }

    const userData = await this.auth0Service.getSubAndCustomerFromAccessToken(accessToken);
    if (!userData?.customerUuid) {
      warn('CustomerUuid is not provided in the Access Token');
      throw new InvalidRequest('CustomerUuid is not provided in the Access Token');
    }
    return this.sessionCacheDb.saveCachedSession<SdkIdentityDeviceCacheItem, SdkIdentityDeviceCache>(
      accessToken,
      {
        auth0sub: userData.sub,
        customerUuid: userData.customerUuid,
        deviceUuid,
        entityUuid: deviceCache.entityUuid,
      },
      CacheType.identity,
    );
  }

  public async checkSdkIdentitySession(accessToken: string): Promise<SdkIdentityDeviceCacheItem> {
    const record = await this.sessionCacheDb.getCachedItem(accessToken, CacheType.identity);
    const session = record?.Items?.[0];

    if (session) {
      return session as SdkIdentityDeviceCacheItem;
    }

    const userData = await this.auth0Service.getSubAndCustomerFromAccessToken(accessToken);
    if (!userData?.customerUuid) {
      warn('CustomerUuid is not provided in the Access Token');
      throw new InvalidRequest('CustomerUuid is not provided in the Access Token');
    }

    // save identity session without entityUuid
    return this.sessionCacheDb.saveCachedSession<SdkIdentityDeviceCacheItem, SdkIdentityDeviceCache>(
      accessToken,
      {
        auth0sub: userData.sub,
        customerUuid: userData.customerUuid,
      },
      CacheType.identity,
    );
  }

  public async updateAllCustomerSessionsInCache(
    entityUuid: string,
    customerUuid: string,
    update: Partial<IdentityCache>,
  ) {
    info(`updating sessions for customer ${customerUuid}`);
    await this.sessionCacheDb.updateAllCustomerSessionsInCache(entityUuid, customerUuid, update);
  }

  public async setEntityInIdentityCache(accessToken: string, entityUuid: string, override = false): Promise<void> {
    debug(`setting entityUuid in identity cache for ${entityUuid}`);
    const record = await this.sessionCacheDb.getCachedItem(accessToken, CacheType.identity);
    const identitySession = record?.Items?.[0] as IdentityCacheItem;

    if (!identitySession?.entityUuid || override) {
      await this.sessionCacheDb.saveCachedSession(accessToken, { entityUuid } as SessionCache, CacheType.identity);
    }
  }

  async createNewCachedSessionFromToken(accessToken: string, type: CacheType): Promise<SessionCache> {
    try {
      const userData = (await this.fetchAuth0UserMetadata(accessToken, type)) as SessionCache;
      const logSafeUserData = getLogSafeSensitiveDataObject(userData);
      info(`User data ${JSON.stringify(logSafeUserData)}`);
      return userData;
    } catch (e) {
      error(e);
      return Promise.reject(e); // NOSONAR
    }
  }

  private async fetchAuth0UserMetadata(
    accessToken: string,
    type: CacheType,
  ): Promise<UserMetadata | SessionCache | DecodedMetadata> {
    try {
      if (this.envService.auth0MetadataFromToken) {
        const result = await this.auth0Service.getSubAndCustomerFromAccessToken(accessToken);
        const data: DecodedMetadata = {
          auth0sub: result.sub,
          customerUuid: result.customerUuid,
        };
        return data;
      }
      const userId = await this.auth0Service.getSubFromAccessToken(accessToken);
      debug(`Getting user info for user: ${userId}`);
      const userInfo = await this.auth0Service.getUserById(userId);
      const logSafeUserInfo = getLogSafeSensitiveDataObject(userInfo);
      info(`user info: ${JSON.stringify(logSafeUserInfo)}`);

      const userMetadata = type === CacheType.onboarding ? userInfo[this.metadata] || {} : userInfo[this.metadata];

      const userData: UserMetadata | SessionCache = {
        auth0sub: userId,
        role: userMetadata.role,
        customerUuid: userMetadata.customerUuid,
      };
      info(`Valid userData: ${JSON.stringify(userData)}`);
      return userData;
    } catch (e) {
      error(e);
      return Promise.reject(new UnAuthorized('Invalid access token.'));
    }
  }

  private async getCachedSessionOrCreateNew<CacheResponse>(
    accessToken: string,
    type: CacheType,
  ): Promise<CacheResponse> {
    const existingSession = await this.sessionCacheDb.getCachedItem(accessToken, type);
    debug(`checkSession ${type}: ${JSON.stringify(existingSession, null, 2)}`);
    if (existingSession.Items !== undefined && existingSession.Items.length === 1) {
      return existingSession.Items[0] as CacheResponse;
    }
    const userEntityData = await this.createNewCachedSessionFromToken(accessToken, type);
    return this.sessionCacheDb.saveCachedSession(accessToken, userEntityData, type) as CacheResponse;
  }

  private readonly getEntityDbItem = async (entityUuid: string): Promise<DbJsonOject | null> => {
    const item = await this.dynamodbService.queryIdByType('', entityUuid, DbRecordType.ENTITY, {
      ignoreEntity: true,
      supportShortId: true,
    });
    if (item.Items && item.Items.length > 0) {
      return item.Items[0];
    }
    return null;
  };
}
