import type { CustomerRole, Status } from '@npco/component-dto-core';
import type { DeviceStatus } from '@npco/component-dto-device';

import type { CacheType } from './cacheType';

export interface SessionCacheItem extends SessionCache {
  id: string;
  type: CacheType;
}

export interface IdentityCacheItem extends IdentityCache {
  id: string;
  type: CacheType;
}

export interface SdkIdentityDeviceCacheItem extends SdkIdentityDeviceCache {
  id: string;
  type: CacheType;
}

// Devices can be cleared
export interface IdentityDeviceCacheItem extends MobileDeviceCache {
  id: string;
  type: CacheType;
}

export interface MobileDeviceCache extends DeviceCache {
  entityModelSerial?: string;
}

export interface DeviceCache extends Partial<Omit<IdentityCache, 'status'>> {
  status?: DeviceStatus;
  model?: string;
  serial?: string;
  modelSerial?: string;
  previousAccessToken?: string;
  previousRefreshToken?: string;
  accessToken?: string;
  refreshToken?: string;
  idToken?: string;
}

// onboarding
export interface SessionCache extends Partial<UserMetadata> {
  status?: Status;
  accountStatus?: Status;
  auth0sub: string;
}

export interface SdkIdentityDeviceCache extends DecodedMetadata {
  status?: Status;
  model?: string;
  serial?: string;
  accountStatus?: Status;
  deviceUuid?: string;
  role?: CustomerRole;
  entityUuid?: string;
}

// identity/device
export interface IdentityCache extends UserMetadata {
  status?: Status;
  accountStatus?: Status;
  deviceUuid?: string;
}

export interface DecodedMetadata {
  auth0sub: string;
  customerUuid: string;
}

export interface UserMetadata extends Auth0Metadata {
  auth0sub: string;
}

export interface Auth0Metadata {
  role: CustomerRole;
  entityUuid?: string;
  customerUuid: string;
}
