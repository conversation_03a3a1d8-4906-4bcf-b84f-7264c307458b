import { GST_TAX_NAME } from '../constants';
import type { Discount, Item, Order, ServiceCharge } from '../types';

import { calculateDiscount } from './discount';
import { calculateGst } from './gst';
import { calculateServiceCharge } from './serviceCharge';
import { calculateSubtotals } from './subtotal';

/**
 * @deprecated
 * @description Please use LineItemCalculator instead
 */
export class OrderAmountCalculator {
  discount = (price: number, discounts: Discount[] | undefined, quantity = 1) =>
    calculateDiscount(price, discounts, quantity);

  serviceCharge = (price: number, serviceCharges: ServiceCharge[] | undefined, quantity = 1) =>
    calculateServiceCharge(price, serviceCharges, quantity);

  readonly calculatePriceWithGstWithoutModifier = (
    item: Pick<Item, 'price' | 'taxes'> & { modifiers?: Pick<Item, 'price'>[] },
    itemsTaxInclusive: boolean,
  ) => {
    let itemPrice = Number(item.price) || 0;

    if (
      itemPrice > 0 &&
      itemsTaxInclusive &&
      (item.taxes ?? []).find((tax) => tax.name === GST_TAX_NAME && tax.enabled)
    ) {
      const gstInMill = itemPrice / 10;
      const priceInMill = itemPrice;
      const priceWithGst = gstInMill + priceInMill;
      itemPrice = Math.round(priceWithGst);
    }

    return itemPrice;
  };

  readonly calculatePriceWithGst = (
    item: Pick<Item, 'price' | 'taxes'> & { modifiers?: Pick<Item, 'price'>[] },
    itemsTaxInclusive: boolean,
  ) => {
    let itemPrice = this.calculatePriceWithGstWithoutModifier(item, itemsTaxInclusive);

    itemPrice = (item.modifiers ?? []).reduce((acc, modifier) => {
      return acc + (Number(modifier.price) || 0);
    }, itemPrice);

    return itemPrice;
  };

  calculateTaxes = (orderInput: Order) => {
    const { items: orderItems, catalogSettings } = orderInput;
    const order = { ...orderInput };
    const items = (orderItems ?? []).map((item) => ({
      ...item,
      price: Number(item.price),
      priceWithGst: this.calculatePriceWithGst(item, catalogSettings.itemsTaxInclusive),
    }));
    order.items = items;

    let totalGst = 0;
    let totalAmount = 0;
    let subtotal = 0;
    let totalAmountWithoutDiscount = 0;
    const paidAmount = 0;

    if (items && items.length > 0) {
      subtotal = calculateSubtotals(items);
      const invoiceLevelDiscount = this.discount(subtotal, order.discounts);
      const invoiceLevelServiceCharge = this.serviceCharge(subtotal, order.serviceCharges);
      totalGst = calculateGst(order);
      const totalGstExcl = catalogSettings.itemsTaxInclusive ? 0 : totalGst;
      totalAmountWithoutDiscount = subtotal + totalGstExcl;
      // If the totalAmount is < 0 due to discounts, we make totalAmount = 0
      totalAmount =
        subtotal <= invoiceLevelDiscount
          ? 0
          : subtotal - invoiceLevelDiscount + totalGstExcl + invoiceLevelServiceCharge;
    }

    return {
      totalGst,
      totalAmount,
      subtotalAmount: subtotal,
      paidAmount,
      totalAmountWithoutDiscount,
    };
  };
}
