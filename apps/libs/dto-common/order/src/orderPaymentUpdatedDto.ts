import type { Money } from '@npco/component-dto-core';

import type { OrderAmounts, OrderPayment } from './types';

export class OrderPaymentUpdatedDto {
  /**
   * orderUuid
   */
  id: string;

  entityUuid: string;

  referenceNumber: string;

  updatedPayment: OrderPayment;

  /**
   * @deprecated: UK currency refactor. Use `amounts.orderAmount` instead.
   */
  totalAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.orderAmount` instead.
   */
  orderAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.paidAmount` instead.
   */
  paidAmount: Money;

  updatedTimeInMilliseconds: number;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalTips` instead.
   */
  totalTips?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.orderDisplayAmount` instead.
   */
  orderDisplayAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalChange` instead.
   */
  totalChange?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalAmountTendered` instead.
   */
  totalAmountTendered?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.cashRoundingAdjustment` instead.
   */
  cashRoundingAdjustment?: Money;

  amounts?: OrderAmounts;

  constructor(params: OrderPaymentUpdatedDto) {
    this.id = params.id;
    this.entityUuid = params.entityUuid;
    this.referenceNumber = params.referenceNumber;
    this.updatedPayment = params.updatedPayment;
    this.totalAmount = params.totalAmount;
    this.updatedTimeInMilliseconds = params.updatedTimeInMilliseconds;
    this.totalTips = params.totalTips;
    this.totalChange = params.totalChange;
    this.totalAmountTendered = params.totalAmountTendered;
    this.orderDisplayAmount = params.orderDisplayAmount;
    this.orderAmount = params.orderAmount;
    this.paidAmount = params.paidAmount;
    this.cashRoundingAdjustment = params.cashRoundingAdjustment;
    this.orderAmount = params.orderAmount;
    this.amounts = params.amounts;
  }
}
