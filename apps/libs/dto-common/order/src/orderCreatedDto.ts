import type { Money } from '@npco/component-dto-core';

import type {
  OrderDiscount,
  OrderItem,
  OrderStatus,
  Order,
  CatalogSettingsSnapshot,
  OrderServiceCharge,
  OrderAmounts,
} from './types';

export class OrderCreatedDto implements Order {
  id: string;

  entityUuid: string;

  createdFromDeviceUuid?: string;

  status: OrderStatus;

  referenceNumber: string;

  siteUuid: string;

  siteName?: string;

  items?: OrderItem[];

  discounts?: OrderDiscount[];

  serviceCharges?: OrderServiceCharge[];

  /**
   * @deprecated: UK currency refactor. Use `amounts.paidAmount` instead.
   */
  paidAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.dueAmount` instead.
   */
  dueAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.subtotalAmount` instead.
   */
  subtotalAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.orderAmount` instead.
   */
  totalAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.orderAmount` instead.
   */
  orderAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalSurcharge` instead.
   */
  totalSurcharge?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalGst` instead.
   */
  totalGst?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.orderGst` instead.
   */
  orderGst?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalDiscount` instead.
   */
  totalDiscount?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalServiceCharge` instead.
   */
  totalServiceCharge?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalTips` instead.
   */
  totalTips?: Money;

  createdTime: number;

  updatedTime?: number;

  updatedTimeInMilliseconds?: number;

  paidTime?: number;

  catalogSettings: CatalogSettingsSnapshot;

  createdTimestampLocal?: string;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalChange` instead.
   */
  orderDisplayAmount?: Money;

  amounts?: OrderAmounts;

  currency?: string | undefined;

  constructor(params: OrderCreatedDto) {
    this.id = params.id;
    this.entityUuid = params.entityUuid;
    this.createdFromDeviceUuid = params.createdFromDeviceUuid;
    this.status = params.status;
    this.referenceNumber = params.referenceNumber;
    this.siteUuid = params.siteUuid;
    this.siteName = params.siteName;
    this.items = params.items;
    this.discounts = params.discounts;
    this.serviceCharges = params.serviceCharges;
    this.paidAmount = params.paidAmount;
    this.dueAmount = params.dueAmount;
    this.subtotalAmount = params.subtotalAmount;
    this.totalAmount = params.totalAmount;
    this.totalSurcharge = params.totalSurcharge;
    this.totalGst = params.totalGst;
    this.orderGst = params.orderGst;
    this.totalDiscount = params.totalDiscount;
    this.totalServiceCharge = params.totalServiceCharge;
    this.totalTips = params.totalTips;
    this.createdTime = params.createdTime;
    this.updatedTime = params.updatedTime;
    this.paidTime = params.paidTime;
    this.catalogSettings = params.catalogSettings;
    this.createdTimestampLocal = params.createdTimestampLocal;
    this.updatedTimeInMilliseconds = params.updatedTimeInMilliseconds;
    this.orderDisplayAmount = params.orderDisplayAmount;
    this.orderAmount = params.orderAmount;
    this.amounts = params.amounts;
    this.currency = params.currency;
  }
}
