import type {
  CatalogDiscountConfig,
  CatalogDiscountType,
  CatalogItem,
  CatalogModifier,
  CatalogServiceChargeConfig,
  CatalogServiceChargeType,
  CatalogTax,
  CatalogUnit,
} from '@npco/component-dto-catalog';
import type { Money, TransactionType } from '@npco/component-dto-core';

export enum OrderStatus {
  OPEN = 'OPEN',
  PAID = 'PAID',
  PART_PAID = 'PART_PAID',
  CANCELLED = 'CANCELLED',
}

export enum OrderItemType {
  SINGLE = 'SINGLE',
  VARIANT = 'VARIANT',
  MODIFIER = 'MODIFIER',
  ONE_TIME = 'ONE_TIME',
}

export type TaxAmount = { name: string; amount: number };

export type OrderItemAttribute = {
  attributeName: string;
  attributeValue: string;
};

export type OrderDiscount = {
  id: string;
  catalogDiscountUuid?: string;
  name?: string;
  type?: CatalogDiscountType;
  config: CatalogDiscountConfig;
  value: string;
  /**
   * @deprecated: UK currency refactor. Use `amount` instead.
   */
  discountedAmount: Money;
  amount?: number;
  ordinal: number;
};

export type OrderServiceCharge = {
  id: string;
  catalogServiceChargeUuid?: string;
  name?: string;
  type?: CatalogServiceChargeType;
  config: CatalogServiceChargeConfig;
  value: string;
  /**
   * @deprecated: UK currency refactor. Use `amount` instead.
   */
  serviceChargeAmount: Money;
  amount?: number;
  ordinal: number;
};

export type OrderItemModifier = {
  id: string;
  catalogModifierUuid?: string;
  catalogModifier?: CatalogModifier;
  name: string;
  /**
   * @deprecated: UK currency refactor. Use `amount` instead.
   */
  price: Money;
  amount?: number;
  currency?: string;
  ordinal: number;
  unit: CatalogUnit;
  quantity: number;
  /**
   * @deprecated: UK currency refactor. Use `subtotal` instead.
   */
  subtotalAmount?: Money;
  subtotal?: number;
};

export type OrderItem = {
  id: string;
  catalogItem?: CatalogItem;
  name: string;
  type: OrderItemType;
  /*
   * @deprecated: UK currency refactor. Use `amount` instead.
   */
  price: Money;
  amount?: number;
  currency?: string;
  ordinal: number;
  unit: CatalogUnit;
  quantity: number;
  discounts?: OrderDiscount[];
  serviceCharges?: OrderServiceCharge[];
  taxes: CatalogTax[];
  modifiers?: OrderItemModifier[];
  variantName?: string;
  /**
   * @deprecated: UK currency refactor. Use `subtotal` instead.
   */
  subtotalAmount?: Money;
  subtotal?: number;
  /**
   * @deprecated: UK currency refactor. Do not use this field.
   */
  totalAmount?: Money;
};

export enum TenderType {
  CASH = 'CASH',
  CARD = 'CARD',
  OTHER = 'OTHER',
}

export enum OrderPaymentStatus {
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
}

export enum NonCheckoutMethod {
  CASH = 'CASH',
  OTHER = 'OTHER',
}

export type OrderPayment = {
  id: string;
  entityUuid: string;
  transactionUuid: string;
  status: OrderPaymentStatus;
  timestamp: string;
  timestampLocal?: string;
  currency?: string;
  /**
   * @deprecated: UK currency refactor. Use `amounts.amount` instead.
   */
  amount: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.tips` instead.
   */
  tips?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.surchargeAmount` instead.
   */
  surchargeAmount?: Money;
  taxAmounts?: TaxAmount[];
  shortId?: string;
  note?: string;
  type: TransactionType;
  tenderType: TenderType;
  tenderSubType?: string;
  /**
   * @deprecated: UK currency refactor. Use `amounts.amountTendered` instead.
   */
  amountTendered?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.cashRoundingAdjustment` instead.
   */
  cashRoundingAdjustment?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.change` instead.
   */
  change?: Money;
  amounts?: OrderPaymentAmounts;
};

export type Order = {
  id: string;
  entityUuid: string;
  createdFromDeviceUuid?: string;
  status: OrderStatus;
  referenceNumber: string;
  siteUuid: string;
  siteName?: string;
  items?: OrderItem[];
  payments?: OrderPayment[];
  discounts?: OrderDiscount[];
  serviceCharges?: OrderServiceCharge[];
  /**
   * @deprecated: UK currency refactor. Use `amounts.paidAmount` instead.
   */
  paidAmount?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.dueAmount` instead.
   */
  dueAmount?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.subtotalAmount` instead.
   */
  subtotalAmount?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.totalAmount` instead.
   */
  totalAmount?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.orderAmount` instead.
   */
  orderAmount?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.totalSurcharge` instead.
   */
  totalSurcharge?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.totalGst` instead.
   */
  totalGst?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.orderGst` instead.
   */
  orderGst?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.totalDiscount` instead.
   */
  totalDiscount?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.totalServiceCharge` instead.
   */
  totalServiceCharge?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.totalTips` instead.
   */
  totalTips?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.totalChange` instead.
   */
  totalChange?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.totalAmountTendered` instead.
   */
  totalAmountTendered?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.orderDisplayAmount` instead.
   */
  orderDisplayAmount?: Money;
  /**
   * @deprecated: UK currency refactor. Use `amounts.cashRoundingAdjustment` instead.
   */
  cashRoundingAdjustment?: Money;
  createdTime: number;
  updatedTime?: number;
  sortCreatedTime?: string;
  paidTime?: number;
  catalogSettings?: CatalogSettingsSnapshot;
  createdTimestampLocal?: string;
  updatedTimeInMilliseconds?: number;
  amounts?: OrderAmounts;
  currency?: string;
};

// input types
export type CreateOrderDiscountInput = {
  id?: string;
  catalogDiscountUuid?: string; // undefined if one-time discount
  name?: string;
  config: CatalogDiscountConfig;
  value: string;
  ordinal: number;
};

export type CreateOrderServiceChargeInput = {
  id?: string;
  catalogServiceChargeUuid?: string; // undefined if one-time service charge
  name?: string;
  config: CatalogServiceChargeConfig;
  value: string;
  ordinal: number;
};

export type CreateOrderItemModifierInput = {
  id?: string;
  catalogModifierUuid?: string; // undefined if one-time modifier
  name: string;
  /**
   * @deprecated: UK currency refactor. Use `amount` instead.
   */
  price: string;
  amount?: number;
  currency?: string;
  ordinal: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
};

export type CreateOrderItemInput = {
  id: string;
  catalogItemUuid?: string; // undefined if one-time item
  name: string;
  /**
   * @deprecated: UK currency refactor. Use `amount` instead.
   */
  price: string;
  amount?: number;
  currency?: string;
  type: OrderItemType;
  ordinal: number;
  description?: string;
  unit: CatalogUnit;
  quantity: number;
  discounts?: CreateOrderDiscountInput[];
  serviceCharges?: CreateOrderServiceChargeInput[];
  taxes: CatalogTax[];
  modifiers?: CreateOrderItemModifierInput[];
  variantName?: string;
};

export type CreateOrderInput = {
  id: string;
  siteUuid: string;
  createdFromDeviceUuid?: string;
  createdTimestampLocal?: string;
  items: CreateOrderItemInput[];
  discounts: CreateOrderDiscountInput[];
  serviceCharges: CreateOrderServiceChargeInput[];
  currency?: string;
};

export type NonCardCheckoutInput = {
  id: string;
  orderUuid: string;
  type: NonCheckoutMethod;
  tenderType: string;
  amount?: string;
  surchargeAmount?: string;
  tips?: string;
  gst?: string;
  amountTendered?: string;
  change?: string;
};

export type CatalogSettingsSnapshot = { itemsTaxInclusive: boolean; itemsApplyTax: boolean; autoSkuEnabled: boolean };

/**
 * Order amounts in cents
 */
export type OrderAmounts = {
  orderAmount: number;
  paidAmount?: number;
  dueAmount?: number;
  subtotalAmount?: number;
  totalSurcharge?: number;
  totalGst?: number;
  orderGst?: number;
  totalDiscount?: number;
  totalServiceCharge?: number;
  totalTips?: number;
  totalChange?: number;
  totalAmountTendered?: number;
  cashRoundingAdjustment?: number;
  orderDisplayAmount?: number;
};

/**
 * Order payment amounts in cents
 */
export type OrderPaymentAmounts = {
  amount: number;
  surchargeAmount?: number;
  tips?: number;
  amountTendered?: number;
  change?: number;
  cashRoundingAdjustment?: number;
};
