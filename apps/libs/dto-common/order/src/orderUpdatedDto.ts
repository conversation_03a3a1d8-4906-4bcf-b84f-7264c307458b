import type { Money } from '@npco/component-dto-core';

import type { OrderCreatedDto } from './orderCreatedDto';
import type { CatalogSettingsSnapshot, OrderAmounts, OrderDiscount, OrderItem, OrderStatus } from './types';

export class OrderUpdatedDto implements Partial<OrderCreatedDto> {
  id: string;

  entityUuid: string;

  createdFromDeviceUuid?: string;

  status?: OrderStatus;

  referenceNumber?: string;

  siteUuid?: string;

  siteName?: string;

  paidAmount?: Money;

  subtotalAmount?: Money;

  dueAmount?: Money;

  totalSurcharge?: Money;

  totalAmount?: Money;

  totalDiscount?: Money;

  totalGst?: Money;

  orderGst?: Money;

  orderAmount?: Money;

  totalTips?: Money;

  createdTime: number;

  updatedTime?: number;

  updatedTimeInMilliseconds?: number;

  paidTime?: number;

  items?: OrderItem[];

  discounts?: OrderDiscount[];

  catalogSettings: CatalogSettingsSnapshot;

  createdTimestampLocal?: string;

  orderDisplayAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalChange` instead.
   */
  totalChange?: Money;

  /**
   * @deprecated: UK currency refactor. Use `amounts.totalAmountTendered` instead.
   */
  totalAmountTendered?: Money;

  amounts?: OrderAmounts;

  constructor(params: OrderUpdatedDto) {
    this.id = params.id;
    this.createdFromDeviceUuid = params.createdFromDeviceUuid;
    this.entityUuid = params.entityUuid;
    this.referenceNumber = params.referenceNumber;
    this.status = params.status;
    this.siteName = params.siteName;
    this.siteUuid = params.siteUuid;
    this.discounts = params.discounts;
    this.items = params.items;
    this.dueAmount = params.dueAmount;
    this.subtotalAmount = params.subtotalAmount;
    this.paidAmount = params.paidAmount;
    this.totalSurcharge = params.totalSurcharge;
    this.totalGst = params.totalGst;
    this.totalAmount = params.totalAmount;
    this.totalTips = params.totalTips;
    this.totalDiscount = params.totalDiscount;
    this.createdTime = params.createdTime;
    this.updatedTime = params.updatedTime;
    this.paidTime = params.paidTime;
    this.catalogSettings = params.catalogSettings;
    this.createdTimestampLocal = params.createdTimestampLocal;
    this.updatedTimeInMilliseconds = params.updatedTimeInMilliseconds;
    this.orderDisplayAmount = params.orderDisplayAmount;
    this.totalChange = params.totalChange;
    this.totalAmountTendered = params.totalAmountTendered;
    this.orderGst = params.orderGst;
    this.amounts = params.amounts;
  }
}
