import type { Money } from '@npco/component-dto-core';

import type { Order, OrderAmounts, OrderPayment, OrderStatus } from './types';

export class OrderPartPaidDto implements Partial<Order> {
  id: string;

  entityUuid: string;

  /**
   * The new payment that was made for the order. It's empty for Zero Dollar Sales
   */
  newPayment?: OrderPayment;

  status: OrderStatus;

  /**
   * @deprecated: UK currency refactor. Use amounts.paidAmount instead.
   * */
  paidAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.dueAmount instead.
   */
  dueAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.orderAmount instead.
   */
  totalAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.orderAmount instead.
   */
  orderAmount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalSurcharge instead.
   */
  totalSurcharge: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalGst instead.
   */
  totalGst: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalDiscount instead.
   */
  totalDiscount: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalServiceCharge instead.
   */
  totalServiceCharge: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalTips instead.
   */
  totalTips: Money;

  paidTime?: number;

  updatedTimeInMilliseconds?: number;

  updatedTime?: number;

  /**
   * @deprecated: UK currency refactor. Use amounts.orderDisplayAmount instead.
   */
  orderDisplayAmount?: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalChange instead.
   */
  totalChange?: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalAmountTendered instead.
   */
  totalAmountTendered?: Money;

  /**
   * @deprecated: UK currency refactor. Use amounts.totalChange instead.
   */
  cashRoundingAdjustment?: Money;

  amounts?: OrderAmounts;

  constructor(params: OrderPartPaidDto) {
    this.id = params.id;
    this.entityUuid = params.entityUuid;
    this.newPayment = params.newPayment;
    this.status = params.status;
    this.paidAmount = params.paidAmount;
    this.dueAmount = params.dueAmount;
    this.totalAmount = params.totalAmount;
    this.totalSurcharge = params.totalSurcharge;
    this.totalGst = params.totalGst;
    this.totalDiscount = params.totalDiscount;
    this.totalServiceCharge = params.totalServiceCharge;
    this.totalTips = params.totalTips;
    this.paidTime = params.paidTime;
    this.updatedTimeInMilliseconds = params.updatedTimeInMilliseconds;
    this.updatedTime = params.updatedTime;
    this.orderDisplayAmount = params.orderDisplayAmount;
    this.totalChange = params.totalChange;
    this.totalAmountTendered = params.totalAmountTendered;
    this.orderAmount = params.orderAmount;
    this.cashRoundingAdjustment = params.cashRoundingAdjustment;
    this.amounts = params.amounts;
  }
}
