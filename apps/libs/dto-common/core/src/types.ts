// Common types
export type AccountDetails = {
  bsb: string;
  account: string;
  name?: string;
};

// PA only supplies the type
// Dashboard id, type, entityUuid
export interface TerminalGqlKeySchema {
  id?: string;
  type: string;
  entityUuid?: string;
}

export enum DbRecordType {
  ENTITY = 'entity.core',
  ENTITY_BILLING_ACCOUNT = 'entity.billingaccount',
  ENTITY_BILLING_PLAN = 'entity.billingplan',
  ENTITY_REFERRAL = 'entity.referral.',
  ENTITY_DOCUMENT = 'entity.document',
  ENTITY_SAVINGS_ACCOUNT_PRODUCT_PROMOTION = 'entity.savingsAccountProductPromotion',

  DEVICE_SETTINGS = 'device.settings',
  DEVICE_INFO = 'device.information',
  DEVICE_METRICS = 'device.metrics.',
  DEVICE_SOFTWARE = 'device.software',
  DEVICE_DOMICILE = 'device.domicile',
  DEPOSIT = 'deposit.',
  TRANSACTION = 'transaction.',
  ISSUING_TRANSACTION = 'issuing.transaction.',
  PAYEE_DETAILS = 'payeedetails.core',
  SCHEDULED_TRANSFER = 'dca.scheduled.transfer.',
  MERCHANT = 'merchant.core',
  SUBCATEGORIES = 'subcategories.core',
  SITE = 'site.core',
  CUSTOMER = 'customer.core',
  CUSTOMER_ENTITY = 'customer.entity.',
  CUSTOMER_DOCUMENT = 'customer.document',
  SIM = 'sim.core',
  SIM_BILL = 'sim.bill.',
  THIRDPARTY_BANK_ACCOUNT = 'thirdpartyaccount.core',
  TAG = 'tag.core',
  TAG_LINK = 'tag#',
  STAND_IN = 'standin.',
  SUBCATEGORY = 'subcategory.core',
  PROMOTION = 'promotion.core',
  // issuing accounts
  DEBIT_CARD_ACCOUNT_TRANSACTION = 'dca.transaction.',
  DEBIT_CARD_ACCOUNT_CARD = 'dcac.core',
  DEBIT_CARD_ACCOUNT_CARD_DIGITAL_WALLET_TOKEN = 'dcac.digitalWalletToken',
  DEBIT_CARD_ACCOUNT_CARD_HISTORY_DIGITAL_WALLET_TOKEN = 'dcac.history.digitalWalletToken.',
  DEBIT_CARD_ACCOUNT = 'dca.core',
  DEBIT_CARD_ACCOUNT_BALANCE_HISTORY = 'dca.balanceHistory.',
  DEBIT_CARD_ACCOUNT_STATEMENT_TRANSCTION = 'dca.accountStatementTransaction.',
  DEBIT_CARD_ACCOUNT_STATEMENT_STORAGE = 'dca.statement.storage.',
  DEBIT_CARD_ACCOUNT_SAVINGS_ACCOUNT_PRODUCT = 'dca.savingsAccountProduct',
  CARD_LOGO = 'cardlogo.core',
  ISSUING_SAVINGS_ACCOUNT = 'issuing.account.savings',
  ISSUING_BALANCE_HISTORY = 'issuing.balanceHistory.',
  ISSUING_INTEREST_SUMMARY_STORAGE = 'issuing.interestSummary.storage.',
  // connection
  CONNECTION_POS_INTERFACE = 'connection.posinterface',
  CONNECTION_XERO_BANKFEED = 'connection.xerobankfeed',
  CONNECTION_XERO_ECOMMERCE = 'connection.xeroecommerce',
  // addressbook
  CONTACT = 'contact.core',
  CONTACT_LINK = 'contact#',
  CONTACT_EMAIL = 'contact.email.',
  CONTACT_PHONE = 'contact.phone.',
  CONTACT_APPLICANT_ID = 'contact.applicantId',
  LABEL = 'label.',
  CARDHOLDER_LINK = 'cardholder#',
  MERCHANT_LINK = 'merchant#',
  DCA_TRANSACTION_SENDER_LINK = 'dca.transactionsender#',
  PAYMENT_INSTRUMENT = 'paymentinstrument.core',
  PAYMENT_INSTRUMENT_COP_RESULT = 'paymentinstrument.copResult',
  // Hubspot Ticket (Crms)
  TICKET = 'ticket',
  // Items
  CATALOG_ITEM = 'catalog.item',
  CATALOG_ATTRIBUTE_SET = 'catalog.attributeset',
  CATALOG_ITEM_IMPORT = 'catalog.import.item',
  CATALOG_CATEGORY = 'catalog.category',
  CATALOG_MODIFIER_SET = 'catalog.modifierset',
  CATALOG_MODIFIER_SET_SITE_SETTINGS = 'catalogModifierSet#site',
  CATALOG_ITEM_MODIFIER = 'catalogItem#modifier',
  CATALOG_DISCOUNT = 'catalog.discount',
  CATALOG_SERVICE_CHARGE = 'catalog.servicecharge',
  CATALOG_ITEM_SITE_SETTINGS = 'catalogItem#site',
  INVOICE_SETTINGS = 'invoice.settings',
  INVOICE = 'invoice.core',
  INVOICE_LINE_ITEM = 'invoice.lineitem',
  INVOICE_CUSTOMER = 'invoice.customer',
  CATALOG_SETTINGS = 'catalog.settings',
  INVOICE_DRAFT_CONTACT_USAGE = 'invoice.draft.contact.usage',
  INVOICE_ACTIVITY = 'invoice.activity',
  INVOICE_PAYMENT = 'invoice.payment',
  // Ecommerce
  ECOMMERCE = 'ecommerce.core',
  CNP_ECOMMERCE = 'ecommerce.',
  // PosInterface
  POSINTERFACE_PAIR_SITE = 'posinterface.pair.site.',
  POSINTERFACE_PAIR_DEVICE = 'posinterface.pair.device.',
  POSINTERFACE_PAIR_POS = 'posinterface.pair.pos.',
  POSINTERFACE_PAIR = 'posinterface.pair.',

  // Notifications
  NOTIFICATION = 'notification.core',
  NOTIFICATION_SETTINGS = 'notification.settings',
  NOTIFICATION_COUNTER_UNREAD = 'notification.counter.unread',

  // ZPOS
  ORDER = 'order.core',
  ORDER_ITEM = 'order.item',
  ORDER_PAYMENT = 'order.payment',

  // SCHEDULED TRANSFER
  TRANSFER_SCHEDULE = 'transferSchedule.core',
  TRANSFER_SCHEDULE_SKIP = 'transferSchedule.skip',
  TRANSFER_SCHEDULE_EXECUTION = 'transferSchedule.execution',

  // DOMICILE Global Lookup Table
  ENTITY_DOMICILE = 'entity.domicile',
  CUSTOMER_DOMICILE = 'customer.domicile',
  ACCOUNT_ID_REGION_MAPPING = 'account.id.region.mapping',
}

export enum Status {
  INACTIVE = 'INACTIVE',
  ACTIVE = 'ACTIVE',
  DISABLED = 'DISABLED',
  DELETED = 'DELETED',
}

export enum AddressState {
  VIC = 'VIC',
  ACT = 'ACT',
  NSW = 'NSW',
  QLD = 'QLD',
  TAS = 'TAS',
  NT = 'NT',
  SA = 'SA',
  WA = 'WA',
}

export type Address = {
  street?: string;
  suburb?: string;
  state?: string;
  postcode?: string;
  country?: string;
};

export enum CustomerRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
}

/* Entity */
export enum EntityType {
  INDIVIDUAL = 'INDIVIDUAL',
  COMPANY = 'COMPANY',
  PARTNERSHIP = 'PARTNERSHIP',
  TRUST = 'TRUST',
  ASSOCIATION = 'ASSOCIATION',
  ASSOCIATION_UNINCORPORATED = 'ASSOCIATION_UNINCORPORATED',
  BENEFICIARY_CLASS = 'BENEFICIARY_CLASS',
  GOVERNMENT = 'GOVERNMENT',
  OTHER = 'OTHER',
}

export enum ScreeningRequestedType {
  MEDICARE = 'MEDICARE',
  PASSPORT = 'PASSPORT',
  DRIVERS_LICENCE = 'DRIVERS_LICENCE',
  DEFAULT = 'DEFAULT',
}

export interface CategorySubcategory {
  category: string;
  subcategoryUuid?: string;
  match: boolean;
}
/* Transaction */

export interface TaxAmount {
  name: string;
  amount: Money;
}

export enum Source {
  STANDALONE = 'STANDALONE',
  LINKLY = 'LINKLY',
  QUICKPOS = 'QUICKPOS',
  XERO_INVOICE = 'XERO_INVOICE',
  ZELLER_INVOICE = 'ZELLER_INVOICE',
  DASHBOARD = 'DASHBOARD',
  HL_POS = 'HL_POS',
  ORACLE_POS = 'ORACLE_POS',
  VIRTUAL_TERMINAL = 'VIRTUAL_TERMINAL',
  PAY_BY_LINK = 'PAY_BY_LINK',
  ZELLER_POS = 'ZELLER_POS',
  IMPOS = 'IMPOS',
  SDK = 'SDK',
  TEVALIS_POS = 'TEVALIS_POS',
}

export enum Channel {
  MOBILE_ANDROID = 'MOBILE_ANDROID',
  MOBILE_IOS = 'MOBILE_IOS',
  TERMINAL = 'TERMINAL',
  PAY_MYZELLER = 'PAY_MYZELLER',
  DASHBOARD = 'DASHBOARD',
  ZELLER_ADMIN = 'ZELLER_ADMIN',
}
export enum TransactionResponseCode {
  USER_CANCELLED = 1000,
  RESPONSE_TIMEOUT = 1001,
  CARD_DECLINED = 1002,
  SIGNATURE_MISMATCH = 1003,
  COMMUNICATIONS_FAILURE = 1004,
  INVALID_RESPONSE = 1005,
  PENDING_REVERSAL_FAILURE = 1006,
  SYSTEM_ERROR = 1007,
}

export enum TransactionType {
  PURCHASE = 'PURCHASE',
  REFUND = 'REFUND',
  PURCHASEADVICE = 'PURCHASEADVICE',
  REFUNDADVICE = 'REFUNDADVICE',
  PREAUTH = 'PREAUTH',
  ADJUSTMENT = 'ADJUSTMENT',
  CASHOUT = 'CASHOUT',
  DEPOSIT = 'DEPOSIT',
  ACCOUNTVERIFY = 'ACCOUNTVERIFY',
  REVERSAL = 'REVERSAL',
}

export enum TransactionCancelReason {
  USER_CANCELLED = 'USER_CANCELLED',
  RESPONSE_TIMEOUT = 'RESPONSE_TIMEOUT',
  CARD_DECLINED = 'CARD_DECLINED',
  SIGNATURE_MISMATCH = 'SIGNATURE_MISMATCH',
  COMMUNICATIONS_FAILURE = 'COMMUNICATIONS_FAILURE',
  INVALID_RESPONSE = 'INVALID_RESPONSE',
  PENDING_REVERSAL_FAILURE = 'PENDING_REVERSAL_FAILURE',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
}

export enum TransactionFraudScreeningOutcome {
  APPROVED = 'APPROVED',
  REVIEW_RECOMMENDED = 'REVIEW_RECOMMENDED',
  SKIPPED = 'SKIPPED',
  DECLINED = 'DECLINED',
}

export enum TransactionFraudScreeningSkippedReason {
  TMS_ERROR = 'TMS_UNREACHABLE',
  SYSTEM_ERROR = 'SYSTEM_ERROR',
}

export enum AcquirerApprovedDetailReason {
  FAILED_TO_PARSE_MESSAGE = 'FAILED_TO_PARSE_MESSAGE',
  ACQUIRED_DECLINED = 'ACQUIRED_DECLINED',
}

export type EmvTag = {
  tag: string;
  length: number;
  value: string;
};

export type Emv = {
  terminalType: string;
  terminalCapabilities: string;
  additionalTerminalCapabilities: string;
  tvrRequest: string;
  tsiRequest: string;
  aid: string;
  cid: string;
  cvmResult: string;
  authResponseCode: string;
  tranCurrencyCode: string;
  terminalCountryCode: string;
  appCurrencyCode: string;
  issuerCurrencyCode: string;
  additionalTags?: EmvTag[];
  appName?: string;
};

export type Location = {
  location: string;
  timestampLocal?: string;
  accuracy?: number;
};

export enum IsoProcessingCode {
  CODE_001000 = '001000',
  CODE_002000 = '002000',
  CODE_003000 = '003000',
  CODE_200010 = '200010',
  CODE_200020 = '200020',
  CODE_200030 = '200030',
  CODE_200000 = '200000',
}

export enum TransactionResponseType {
  APPROVED = 'APPROVED',
  DECLINED = 'DECLINED',
}

/* Other */
export enum KYCScreeningResult {
  POTENTIAL_MATCH = 'POTENTIAL_MATCH',
  NO_MATCH = 'NO_MATCH',
  OTHER = 'OTHER',
  ERROR = 'ERROR',
}

export enum ISO3166 {
  AFG = 'AFG',
  ALA = 'ALA',
  ALB = 'ALB',
  DZA = 'DZA',
  ASM = 'ASM',
  AND = 'AND',
  AGO = 'AGO',
  AIA = 'AIA',
  ATA = 'ATA',
  ATG = 'ATG',
  ARG = 'ARG',
  ARM = 'ARM',
  ABW = 'ABW',
  AUS = 'AUS',
  AUT = 'AUT',
  AZE = 'AZE',
  BHS = 'BHS',
  BHR = 'BHR',
  BGD = 'BGD',
  BRB = 'BRB',
  BLR = 'BLR',
  BEL = 'BEL',
  BLZ = 'BLZ',
  BEN = 'BEN',
  BMU = 'BMU',
  BTN = 'BTN',
  BOL = 'BOL',
  BES = 'BES',
  BIH = 'BIH',
  BWA = 'BWA',
  BVT = 'BVT',
  BRA = 'BRA',
  IOT = 'IOT',
  BRN = 'BRN',
  BGR = 'BGR',
  BFA = 'BFA',
  BDI = 'BDI',
  CPV = 'CPV',
  KHM = 'KHM',
  CMR = 'CMR',
  CAN = 'CAN',
  CYM = 'CYM',
  CAF = 'CAF',
  TCD = 'TCD',
  CHL = 'CHL',
  CHN = 'CHN',
  CXR = 'CXR',
  CCK = 'CCK',
  COL = 'COL',
  COM = 'COM',
  COG = 'COG',
  COD = 'COD',
  COK = 'COK',
  CRI = 'CRI',
  CIV = 'CIV',
  HRV = 'HRV',
  CUB = 'CUB',
  CUW = 'CUW',
  CYP = 'CYP',
  CZE = 'CZE',
  DNK = 'DNK',
  DJI = 'DJI',
  DMA = 'DMA',
  DOM = 'DOM',
  ECU = 'ECU',
  EGY = 'EGY',
  SLV = 'SLV',
  GNQ = 'GNQ',
  ERI = 'ERI',
  EST = 'EST',
  ETH = 'ETH',
  FLK = 'FLK',
  FRO = 'FRO',
  FJI = 'FJI',
  FIN = 'FIN',
  FRA = 'FRA',
  GUF = 'GUF',
  PYF = 'PYF',
  ATF = 'ATF',
  GAB = 'GAB',
  GMB = 'GMB',
  GEO = 'GEO',
  DEU = 'DEU',
  GHA = 'GHA',
  GIB = 'GIB',
  GRC = 'GRC',
  GRL = 'GRL',
  GRD = 'GRD',
  GLP = 'GLP',
  GUM = 'GUM',
  GTM = 'GTM',
  GGY = 'GGY',
  GIN = 'GIN',
  GNB = 'GNB',
  GUY = 'GUY',
  HTI = 'HTI',
  HMD = 'HMD',
  VAT = 'VAT',
  HND = 'HND',
  HKG = 'HKG',
  HUN = 'HUN',
  ISL = 'ISL',
  IND = 'IND',
  IDN = 'IDN',
  IRN = 'IRN',
  IRQ = 'IRQ',
  IRL = 'IRL',
  IMN = 'IMN',
  ISR = 'ISR',
  ITA = 'ITA',
  JAM = 'JAM',
  JPN = 'JPN',
  JEY = 'JEY',
  JOR = 'JOR',
  KAZ = 'KAZ',
  KEN = 'KEN',
  KIR = 'KIR',
  PRK = 'PRK',
  KOR = 'KOR',
  KWT = 'KWT',
  KGZ = 'KGZ',
  LAO = 'LAO',
  LVA = 'LVA',
  LBN = 'LBN',
  LSO = 'LSO',
  LBR = 'LBR',
  LBY = 'LBY',
  LIE = 'LIE',
  LTU = 'LTU',
  LUX = 'LUX',
  MAC = 'MAC',
  MKD = 'MKD',
  MDG = 'MDG',
  MWI = 'MWI',
  MYS = 'MYS',
  MDV = 'MDV',
  MLI = 'MLI',
  MLT = 'MLT',
  MHL = 'MHL',
  MTQ = 'MTQ',
  MRT = 'MRT',
  MUS = 'MUS',
  MYT = 'MYT',
  MEX = 'MEX',
  FSM = 'FSM',
  MDA = 'MDA',
  MCO = 'MCO',
  MNG = 'MNG',
  MNE = 'MNE',
  MSR = 'MSR',
  MAR = 'MAR',
  MOZ = 'MOZ',
  MMR = 'MMR',
  NAM = 'NAM',
  NRU = 'NRU',
  NPL = 'NPL',
  NLD = 'NLD',
  NCL = 'NCL',
  NZL = 'NZL',
  NIC = 'NIC',
  NER = 'NER',
  NGA = 'NGA',
  NIU = 'NIU',
  NFK = 'NFK',
  MNP = 'MNP',
  NOR = 'NOR',
  OMN = 'OMN',
  PAK = 'PAK',
  PLW = 'PLW',
  PSE = 'PSE',
  PAN = 'PAN',
  PNG = 'PNG',
  PRY = 'PRY',
  PER = 'PER',
  PHL = 'PHL',
  PCN = 'PCN',
  POL = 'POL',
  PRT = 'PRT',
  PRI = 'PRI',
  QAT = 'QAT',
  REU = 'REU',
  ROU = 'ROU',
  RUS = 'RUS',
  RWA = 'RWA',
  BLM = 'BLM',
  SHN = 'SHN',
  KNA = 'KNA',
  LCA = 'LCA',
  MAF = 'MAF',
  SPM = 'SPM',
  VCT = 'VCT',
  WSM = 'WSM',
  SMR = 'SMR',
  STP = 'STP',
  SAU = 'SAU',
  SEN = 'SEN',
  SRB = 'SRB',
  SYC = 'SYC',
  SLE = 'SLE',
  SGP = 'SGP',
  SXM = 'SXM',
  SVK = 'SVK',
  SVN = 'SVN',
  SLB = 'SLB',
  SOM = 'SOM',
  ZAF = 'ZAF',
  SGS = 'SGS',
  SSD = 'SSD',
  ESP = 'ESP',
  LKA = 'LKA',
  SDN = 'SDN',
  SUR = 'SUR',
  SJM = 'SJM',
  SWZ = 'SWZ',
  SWE = 'SWE',
  CHE = 'CHE',
  SYR = 'SYR',
  TWN = 'TWN',
  TJK = 'TJK',
  TZA = 'TZA',
  THA = 'THA',
  TLS = 'TLS',
  TGO = 'TGO',
  TKL = 'TKL',
  TON = 'TON',
  TTO = 'TTO',
  TUN = 'TUN',
  TUR = 'TUR',
  TKM = 'TKM',
  TCA = 'TCA',
  TUV = 'TUV',
  UGA = 'UGA',
  UKR = 'UKR',
  ARE = 'ARE',
  GBR = 'GBR',
  USA = 'USA',
  UMI = 'UMI',
  URY = 'URY',
  UZB = 'UZB',
  VUT = 'VUT',
  VEN = 'VEN',
  VNM = 'VNM',
  VGB = 'VGB',
  VIR = 'VIR',
  WLF = 'WLF',
  ESH = 'ESH',
  YEM = 'YEM',
  ZMB = 'ZMB',
  ZWE = 'ZWE',
}

/** Currency */
export enum ISO4217 {
  AED = 'AED',
  AFN = 'AFN',
  ALL = 'ALL',
  AMD = 'AMD',
  ANG = 'ANG',
  AOA = 'AOA',
  ARS = 'ARS',
  AUD = 'AUD',
  AWG = 'AWG',
  AZN = 'AZN',
  BAM = 'BAM',
  BBD = 'BBD',
  BDT = 'BDT',
  BGN = 'BGN',
  BHD = 'BHD',
  BIF = 'BIF',
  BMD = 'BMD',
  BND = 'BND',
  BOB = 'BOB',
  BOV = 'BOV',
  BRL = 'BRL',
  BSD = 'BSD',
  BTN = 'BTN',
  BWP = 'BWP',
  BYR = 'BYR',
  BZD = 'BZD',
  CAD = 'CAD',
  CDF = 'CDF',
  CHE = 'CHE',
  CHF = 'CHF',
  CHW = 'CHW',
  CLF = 'CLF',
  CLP = 'CLP',
  CNY = 'CNY',
  COP = 'COP',
  COU = 'COU',
  CRC = 'CRC',
  CUP = 'CUP',
  CVE = 'CVE',
  CYP = 'CYP',
  CZK = 'CZK',
  DJF = 'DJF',
  DKK = 'DKK',
  DOP = 'DOP',
  DZD = 'DZD',
  EEK = 'EEK',
  EGP = 'EGP',
  ERN = 'ERN',
  ETB = 'ETB',
  EUR = 'EUR',
  FJD = 'FJD',
  FKP = 'FKP',
  GBP = 'GBP',
  GEL = 'GEL',
  GHS = 'GHS',
  GIP = 'GIP',
  GMD = 'GMD',
  GNF = 'GNF',
  GTQ = 'GTQ',
  GWP = 'GWP',
  GYD = 'GYD',
  HKD = 'HKD',
  HNL = 'HNL',
  HRK = 'HRK',
  HTG = 'HTG',
  HUF = 'HUF',
  IDR = 'IDR',
  ILS = 'ILS',
  INR = 'INR',
  IQD = 'IQD',
  IRR = 'IRR',
  ISK = 'ISK',
  JMD = 'JMD',
  JOD = 'JOD',
  JPY = 'JPY',
  KES = 'KES',
  KGS = 'KGS',
  KHR = 'KHR',
  KMF = 'KMF',
  KPW = 'KPW',
  KRW = 'KRW',
  KWD = 'KWD',
  KYD = 'KYD',
  KZT = 'KZT',
  LAK = 'LAK',
  LBP = 'LBP',
  LKR = 'LKR',
  LRD = 'LRD',
  LSL = 'LSL',
  LTL = 'LTL',
  LVL = 'LVL',
  LYD = 'LYD',
  MAD = 'MAD',
  MDL = 'MDL',
  MGA = 'MGA',
  MKD = 'MKD',
  MMK = 'MMK',
  MNT = 'MNT',
  MOP = 'MOP',
  MRO = 'MRO',
  MTL = 'MTL',
  MUR = 'MUR',
  MVR = 'MVR',
  MWK = 'MWK',
  MXN = 'MXN',
  MXV = 'MXV',
  MYR = 'MYR',
  MZN = 'MZN',
  NAD = 'NAD',
  NGN = 'NGN',
  NIO = 'NIO',
  NOK = 'NOK',
  NPR = 'NPR',
  NZD = 'NZD',
  OMR = 'OMR',
  PAB = 'PAB',
  PEN = 'PEN',
  PGK = 'PGK',
  PHP = 'PHP',
  PKR = 'PKR',
  PLN = 'PLN',
  PYG = 'PYG',
  QAR = 'QAR',
  RON = 'RON',
  RSD = 'RSD',
  RUB = 'RUB',
  RUR = 'RUR',
  RWF = 'RWF',
  SAR = 'SAR',
  SBD = 'SBD',
  SCR = 'SCR',
  SDG = 'SDG',
  SEK = 'SEK',
  SGD = 'SGD',
  SHP = 'SHP',
  SKK = 'SKK',
  SLL = 'SLL',
  SOS = 'SOS',
  SRD = 'SRD',
  STD = 'STD',
  SVC = 'SVC',
  SYP = 'SYP',
  SZL = 'SZL',
  THB = 'THB',
  TJS = 'TJS',
  TMM = 'TMM',
  TND = 'TND',
  TOP = 'TOP',
  TRY = 'TRY',
  TTD = 'TTD',
  TWD = 'TWD',
  TZS = 'TZS',
  UAH = 'UAH',
  UGX = 'UGX',
  USD = 'USD',
  USN = 'USN',
  USS = 'USS',
  UYI = 'UYI',
  UYU = 'UYU',
  UZS = 'UZS',
  VEF = 'VEF',
  VND = 'VND',
  VUV = 'VUV',
  WST = 'WST',
  XAF = 'XAF',
  XAG = 'XAG',
  XAU = 'XAU',
  XBA = 'XBA',
  XBB = 'XBB',
  XBC = 'XBC',
  XBD = 'XBD',
  XCD = 'XCD',
  XDR = 'XDR',
  XOF = 'XOF',
  XPD = 'XPD',
  XPF = 'XPF',
  XPT = 'XPT',
  XTS = 'XTS',
  XXX = 'XXX',
  YER = 'YER',
  ZAR = 'ZAR',
  ZMK = 'ZMK',
  ZWD = 'ZWD',
}

export type Money = {
  /**
   * Amount in whole dollar in string e.g. $50.35 is repersented as "5035"
   */
  value: string;

  /**
   * ISO4217 currecny code
   */
  currency: ISO4217;
};

export type AdjustedFee = {
  fromDateTimeAEST: string;
  toDateTimeAEST: string;
  accumulatedGrossFees: string;
  accumulatedNetFees: string;
};

export type BankAccount = {
  bsb: string;
  account: string;
};

export enum ScreeningStatus {
  NOT_REQUIRED = 'NOT_REQUIRED',
  REQUIRED = 'REQUIRED',
  COMPLETED = 'COMPLETED',
}

export type ScreeningResult = {
  result?: string;
  error?: string;
  matchStatus?: string;
  searchIdentifier?: string;
  status?: ScreeningStatus;
  screeningType?: ScreeningRequestedType;
};

/* AMEX */

export type AmexSeller = {
  caid: string;
  url?: string;
  mcc: string;
  name: string;
  tradingName: string;
  phone: string;
  email: string;
  currency: ISO4217;
  address: AmexAddress;
};

export type AmexAuthorizedSigner = {
  firstName: string;
  lastName: string;
  dob: string;
  address: AmexAddress;
};

export type AmexAddress = {
  address_line_1: string;
  address_line_2?: string;
  city_name: string;
  region_code: string;
  postal_code: string;
  country_code: string;
};

export enum AmexAcquisitionStatus {
  REQUIRED = 'REQUIRED',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR',
}

export enum AmexSeStatusCodes {
  CANCELLED_DEROGATORY = 'CANCELLED_DEROGATORY',
  REINSTATEMENT = 'REINSTATEMENT',
  DEFAULT = 'DEFAULT',
}

export interface MaxMin {
  maximum?: string;
  minimum?: string;
}

export enum PaymentType {
  CP = 'CP',
  CNP = 'CNP',
  MOTO = 'MOTO',
  ZINVLOC = 'ZINVLOC',
  ZINVINT = 'ZINVINT',
  XINVLOC = 'XINVLOC',
  XINVINT = 'XINVINT',
  CPOC = 'CPOC',
  VT = 'VT',
  PBLLOC = 'PBLLOC',
  PBLINT = 'PBLINT',
  DEFAULT = 'default',
}

export enum FeePlanType {
  ADVALOREM = 'adValorem',
  FIXED = 'fixed',
  ADVALOREMPLUSFIXED = 'adValoremplusfixed',
  COSTPLUS = 'cost+',
}

export type FeeRate = {
  // FS2 only has subset of CNP and uses subPaymentType for cnp specific
  paymentType: PaymentType;
  subPaymentType?: CnpSubPaymentType;
  rates: Array<Fee>;
};

export enum FeeRateUpdateType {
  CURRENT_SCHEDULED = 'CURRENT_SCHEDULED',
  NEXT_SCHEDULED = 'NEXT_SCHEDULED',
}

export enum CnpSubPaymentType {
  ZINVINT = 'ZINVINT',
  ZINVLOC = 'ZINVLOC',
  XINVINT = 'XINVINT',
  XINVLOC = 'XINVLOC',
  VT = 'VT',
  PBLLOC = 'PBLLOC',
  PBLINT = 'PBLINT',
}

export type Fee = {
  type: FeePlanType;
  value: Money | string;
};

export type BatchContent = {
  provider: string;
  link: string;
};

export type Icon = {
  colour?: string;
  letter?: string;
  image?: string;
  images?: Image[];
  animation?: string;
};

export type Image = {
  url: string;
  size: ImageSize;
};

export enum ImageSize {
  Thumbnail = 'Thumbnail', // 100x
  Small = 'Small', // 300x
  Medium = 'Medium', // 400x
  Large = 'Large', // 800x
  Native = 'Native', // input
}

export enum EcommerceType {
  GENERIC = 'GENERIC',
  XERO_INVOICE = 'XERO_INVOICE',
  ZELLER_INVOICE = 'ZELLER_INVOICE',
  VIRTUAL_TERMINAL = 'VIRTUAL_TERMINAL',
  PAY_BY_LINK = 'PAY_BY_LINK',
}

export enum IsoPosEntryMode {
  CODE_000 = '000',
  CODE_001 = '001',
  CODE_002 = '002',
  CODE_003 = '003',
  CODE_004 = '004',
  CODE_012 = '012',
  CODE_013 = '013',
  CODE_014 = '014',
}

export enum IsoPosConditionCode {
  CODE_00 = '00',
  CODE_08 = '08',
  CODE_01 = '01',
}

export enum SplitPaymentType {
  PORTION = 'PORTION',
  AMOUNT = 'AMOUNT',
  ITEMS = 'ITEMS',
}

export type SplitPayment = {
  id: string;
  type: SplitPaymentType;
  targetAmount: Money;
  portions?: number;
};

export enum ConnectionType {
  XERO_BANKFEEDS = 'XERO_BANKFEEDS',
  XERO_PAYMENT_SERVICES = 'XERO_PAYMENT_SERVICES',
  HL_POS = 'HL_POS',
  ORACLE_POS = 'ORACLE_POS',
  IMPOS = 'IMPOS',
  SDK = 'SDK',
  TEVALIS_POS = 'TEVALIS_POS',
}

export enum ConnectionTypeParams {
  HL = 'hl',
  ORACLE = 'oracle',
  IMPOS = 'impos',
  SDK = 'sdk',
  TEVALIS = 'tevalis',
}

export enum MutationAttributionTokenGrant {
  USER_PASS = 'USER_PASS',
  MFA = 'MFA',
  ATTESTATION = 'ATTESTATION',
  SYSTEM = 'SYSTEM',
}

export enum MutationAttributionUserRole {
  MANAGER = 'MANAGER',
  ADMIN = 'ADMIN',
  HUBSPOT = 'HUBSPOT',
  ZELLER = 'ZELLER',
  SYSTEM = 'SYSTEM',
}

export enum MutationAttributionPlatform {
  APP = 'APP',
  TERMINAL = 'TERMINAL',
  DASHBOARD = 'DASHBOARD',
  ADMIN = 'ADMIN',
  SYSTEM = 'SYSTEM',
  SDK = 'SDK',
}

export type MutationAttribution = {
  /**
   * The user identifier (A merchant customerUuid, Hubspot userId, Zeller email or 'System')
   */
  userIdentifier: string;
  tokenGrant: MutationAttributionTokenGrant;
  userRole: MutationAttributionUserRole;
  platform: MutationAttributionPlatform;
  createdTimestamp: number;
  sessionKey?: string;
  reason?: string;
};

export enum ChannelType {
  'CP' = 'CP',
  'CNP' = 'CNP',
  'MOBILE_ANDROID' = 'MOBILE_ANDROID',
  'MOBILE_IOS' = 'MOBILE_IOS',
}

export interface PaymentSettings {
  cnpPaymentLimits?: MaxMin;
  paymentLimits?: MaxMin;
  motoPaymentLimits?: MaxMin;
  cpocPaymentLimits?: MaxMin;
}

export enum StandInOperation {
  BELOW = 'below',
  EQUAL = 'equal',
  ABOVE = 'above',
}

export enum StandInField {
  OFFLINE_AMOUNT = 'offline_amount',
  OFFLINE_COUNT = 'offline_count',
  TRANSACTION_AMOUNT = 'transaction_amount',
}

export interface StandInRule {
  operation: StandInOperation;
  field: StandInField;
  value: string;
}

export enum EntityCategories {
  PURCHASES = 'PURCHASES',
  COST_OF_GOODS_SOLD = 'COST_OF_GOODS_SOLD',
  ADVERTISING = 'ADVERTISING',
  BANK_FEES = 'BANK_FEES',
  CLEANING = 'CLEANING',
  CONSULTING_ACCOUNTING = 'CONSULTING_ACCOUNTING',
  ENTERTAINMENT = 'ENTERTAINMENT',
  FREIGHT_COURIER = 'FREIGHT_COURIER',
  GENERAL_EXPENSES = 'GENERAL_EXPENSES',
  INSURANCE = 'INSURANCE',
  INTEREST_EXPENSE = 'INTEREST_EXPENSE',
  LEGAL_EXPENSES = 'LEGAL_EXPENSES',
  LIGHT_POWER_HEATING = 'LIGHT_POWER_HEATING',
  MOTOR_VEHICLE_EXPENSES = 'MOTOR_VEHICLE_EXPENSES',
  OFFICE_EXPENSES = 'OFFICE_EXPENSES',
  PRINTING_STATIONERY = 'PRINTING_STATIONERY',
  RENT = 'RENT',
  WAGES_SALARIES = 'WAGES_SALARIES',
  SUPERANNUATION = 'SUPERANNUATION',
  COMMISSION = 'COMMISSION',
  SUBSCRIPTIONS = 'SUBSCRIPTIONS',
  TELEPHONE_INTERNET = 'TELEPHONE_INTERNET',
  TRAVEL_NATIONAL = 'TRAVEL_NATIONAL',
  TRAVEL_INTERNATIONAL = 'TRAVEL_INTERNATIONAL',
  INCOME_TAX_EXPENSE = 'INCOME_TAX_EXPENSE',
  OFFICE_EQUIPMENT = 'OFFICE_EQUIPMENT',
  COMPUTER_EQUIPMENT = 'COMPUTER_EQUIPMENT',
  HEALTH_FITNESS = 'HEALTH_FITNESS',
  SERVICES = 'SERVICES',
}

export type BankAccountDetailsInput = {
  bsb: string;
  account: string;
  name: string;
};
