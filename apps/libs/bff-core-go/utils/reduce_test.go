package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestReduce(t *testing.T) {
	sum := Reduce([]int{1, 2, 3, 4, 5}, func(acc int, v int) int {
		return acc + v
	}, 0)

	assert.Equal(t, 15, sum)
}
func TestReduce_EmptySlice(t *testing.T) {
	result := Reduce([]int{}, func(acc, v int) int {
		return acc + v
	}, 42)
	assert.Equal(t, 42, result)
}

func TestReduce_StringConcat(t *testing.T) {
	result := Reduce([]string{"a", "b", "c"}, func(acc, v string) string {
		return acc + v
	}, "")
	assert.Equal(t, "abc", result)
}

func TestReduce_Multiply(t *testing.T) {
	result := Reduce([]int{2, 3, 4}, func(acc, v int) int {
		return acc * v
	}, 1)
	assert.Equal(t, 24, result)
}

func TestReduce_Structs(t *testing.T) {
	type item struct{ v int }
	items := []item{{1}, {2}, {3}}
	sum := Reduce(items, func(acc int, it item) int {
		return acc + it.v
	}, 0)
	assert.Equal(t, 6, sum)
}

func TestReduce_BoolAnd(t *testing.T) {
	result := Reduce([]bool{true, true, false}, func(acc, v bool) bool {
		return acc && v
	}, true)
	assert.False(t, result)
}

func TestReduce_BoolOr(t *testing.T) {
	result := Reduce([]bool{false, false, true}, func(acc, v bool) bool {
		return acc || v
	}, false)
	assert.True(t, result)
}
