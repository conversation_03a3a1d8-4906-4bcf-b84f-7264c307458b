package utils

// Reduce is a helper function similar to a common pattern in functional programming.
// It takes a slice of any type T, a reducer function f, and an initial value of type M.
// It iterates over the slice, applying the reducer function to accumulate a result.
func Reduce[T, M any](s []T, f func(M, T) M, initValue M) M {
	acc := initValue
	for _, v := range s {
		acc = f(acc, v)
	}
	return acc
}
