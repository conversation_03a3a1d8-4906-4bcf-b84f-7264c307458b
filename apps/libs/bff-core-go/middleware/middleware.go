package middleware

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambdacontext"
	logger "github.com/zeller-engineering/component-bff/apps/libs/bff-core-go/logger"
)

type JsonEvent json.RawMessage
type CronJobInput struct {
	CronJobType string `json:"cronJobType"`
}

type HandlerFunc func(context.Context, json.RawMessage) (any, error)
type APIGatewayWebsocketEventHandler func(context.Context, events.APIGatewayWebsocketProxyRequest) (events.APIGatewayProxyResponse, error)
type APIGatewayProxyEventHandler func(context.Context, events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error)
type CloudWatchEventHandler func(context.Context, events.CloudWatchEvent) (any, error)
type ALBTargetGroupEventHandler func(context.Context, events.ALBTargetGroupRequest) (events.ALBTargetGroupResponse, error)
type DynamoDBStreamEventHandler func(ctx context.Context, e events.DynamoDBEvent) events.DynamoDBEventResponse
type SQSEventHandler func(context.Context, events.SQSEvent) (events.SQSEventResponse, error)
type APIGatewayCustomAuthorizerRequestHandler func(context.Context, events.APIGatewayCustomAuthorizerRequestTypeRequest) (events.APIGatewayCustomAuthorizerResponse, error)
type CronJobHandler func(context.Context, CronJobInput) (any, error)
type Middleware func(HandlerFunc) HandlerFunc

func chainMiddlewares(handler HandlerFunc, middlewares ...Middleware) HandlerFunc {
	for i := len(middlewares) - 1; i >= 0; i-- {
		handler = middlewares[i](handler)
	}
	return handler
}

func BaseMiddlewares(next HandlerFunc) HandlerFunc {
	baseMiddlewares := []Middleware{
		WarmupMiddleware,
		XrayTraceIdMiddleware,
		LogMiddleware,
	}

	return chainMiddlewares(next, baseMiddlewares...)
}

func LogMiddleware(next HandlerFunc) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, event json.RawMessage) (interface{}, error) {
		lc, ok := lambdacontext.FromContext(ctx)
		if ok {
			ctx = context.WithValue(ctx, logger.RequestId{}, lc.AwsRequestID)
			logger.Debug(ctx, fmt.Sprintf("get aws request id %s", lc.AwsRequestID))
		}
		return next(ctx, event)
	})
}

func APIGatewayProxyMiddleware(next APIGatewayProxyEventHandler) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, data json.RawMessage) (interface{}, error) {
		var event events.APIGatewayProxyRequest
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return next(ctx, event)
	})
}

func CloudWatchEventMiddleware(next CloudWatchEventHandler) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, data json.RawMessage) (any, error) {
		var event events.CloudWatchEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return next(ctx, event)
	})
}

func APIGatewayWebsocketProxyMiddleware(next APIGatewayWebsocketEventHandler) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, data json.RawMessage) (interface{}, error) {
		var event events.APIGatewayWebsocketProxyRequest
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return next(ctx, event)
	})
}

func ALBTargetGroupMiddleware(next ALBTargetGroupEventHandler) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, data json.RawMessage) (interface{}, error) {
		var event events.ALBTargetGroupRequest
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return next(ctx, event)
	})
}

func SQSMiddleware(next SQSEventHandler) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, data json.RawMessage) (interface{}, error) {
		var event events.SQSEvent
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return next(ctx, event)
	})
}

func CronJobMiddleware(next CronJobHandler) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, data json.RawMessage) (interface{}, error) {
		var cronJobInput CronJobInput
		if err := json.Unmarshal(data, &cronJobInput); err != nil {
			return nil, err
		}
		return next(ctx, cronJobInput)
	})
}

func WarmupMiddleware(next HandlerFunc) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, event json.RawMessage) (interface{}, error) {
		var data map[string]interface{}
		err := json.Unmarshal(event, &data)
		if err != nil {
			logger.Error(ctx, err)
		}
		if data["source"] == "keep.warm" {
			logger.Debug(ctx, "Received keep warm")
			return stopMiddleware(ctx, event)
		}
		return next(ctx, event)
	})
}

func APIGatewayCustomAuthorizerRequestHandlerMiddleWare(next APIGatewayCustomAuthorizerRequestHandler) HandlerFunc {
	return HandlerFunc(func(ctx context.Context, data json.RawMessage) (interface{}, error) {
		var event events.APIGatewayCustomAuthorizerRequestTypeRequest
		if err := json.Unmarshal(data, &event); err != nil {
			return nil, err
		}
		return next(ctx, event)
	})
}

func stopMiddleware(context.Context, json.RawMessage) (interface{}, error) {
	return events.APIGatewayProxyResponse{StatusCode: 200}, nil
}
