import { Domicile, getSupportedDomiciles } from '@npco/component-bff-core/dist/utils/domicile';
import { DbRecordType } from '@npco/component-dto-core/dist/types';

import type { DynamodbClient } from '../dynamodb/dynamodbClient';
import { retry } from '../utils/retry';

const getDomicileValue = () => {
  const region = process.env.AWS_REGION || 'ap-southeast-2';
  const domiciles = getSupportedDomiciles(region);
  if (domiciles.length === 0) {
    return Domicile.AU;
  }
  return domiciles[0];
};
const getDomicileLookUpTable = () => {
  const globalAccountId = process.env.SYDNEY_ACCOUNT_ID ?? '';
  const staticEnvName = process.env.STATIC_ENV_NAME ?? 'dev';
  const region = process.env.AWS_REGION ?? 'ap-southeast-2';
  return `arn:aws:dynamodb:${region}:${globalAccountId}:table/${staticEnvName}-ams-engine-DomicileLookup`;
};

const saveDomicileValue = async (
  dbClient: DynamodbClient,
  item: any,
  domicileLookUpTable = getDomicileLookUpTable(),
) => {
  try {
    await dbClient.put({
      TableName: domicileLookUpTable,
      Item: {
        ...item,
      },
    });
  } catch (error) {
    console.error('Error saving domicile value:', error);
    throw error;
  }
};

const validateDomicileItem = async (
  dbClient: DynamodbClient,
  id: string,
  type: DbRecordType,
  domicileLookUpTable = getDomicileLookUpTable(),
) => {
  const item = await dbClient.get({
    TableName: domicileLookUpTable,
    Key: {
      id,
      type,
    },
  });
  console.log(item);
  expect(item?.Item).toBeDefined();
};

const createSaveAndVerifyDomicile = async (
  id: string,
  type: DbRecordType,
  dbClient: DynamodbClient,
  domicileLookUpTable = getDomicileLookUpTable(),
) => {
  const item = {
    id,
    type,
    domicile: getDomicileValue(),
  };

  await saveDomicileValue(dbClient, item, domicileLookUpTable);
  await retry(async () => {
    console.log(`${JSON.stringify(item)}`, id);
    await validateDomicileItem(dbClient, id, type, domicileLookUpTable);
  });
};

export const createDomicileValue = async (
  dbClient: DynamodbClient,
  entityUuid?: string,
  customerUuid?: string,
  deviceUuid?: string,
  domicileLookUpTable = getDomicileLookUpTable(),
) => {
  if (entityUuid) {
    await createSaveAndVerifyDomicile(entityUuid, DbRecordType.ENTITY_DOMICILE, dbClient, domicileLookUpTable);
  }

  if (customerUuid) {
    await createSaveAndVerifyDomicile(customerUuid, DbRecordType.CUSTOMER_DOMICILE, dbClient, domicileLookUpTable);
  }

  if (deviceUuid) {
    await createSaveAndVerifyDomicile(deviceUuid, DbRecordType.DEVICE_DOMICILE, dbClient, domicileLookUpTable);
  }
};
