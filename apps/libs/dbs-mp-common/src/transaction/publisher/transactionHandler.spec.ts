import type { DbRecordStreamEvent, StreamDbRecord } from '@npco/bff-common/dist/publisher/types';
import { DbRecordType } from '@npco/component-dto-core';

import { v4 as uuid } from 'uuid';

import { TransactionHandler } from './transactionHandler';

const mockSendMessage = jest.fn();

jest.mock('@npco/component-bff-core/dist/aws/sqsClient', () => ({
  SqsClient: jest.fn(() => ({
    sendRawMessage: mockSendMessage,
  })),
}));

const createTransactionDto = () => ({
  transactionUuid: uuid(),
  entityUuid: uuid(),
});

const transaction = createTransactionDto();

const events = [
  {
    eventID: 'c98b908637fed06146a6cf7ca1cc6222',
    eventName: 'MODIFY',
    eventVersion: '1.1',
    eventSource: 'aws:dynamodb',
    awsRegion: 'ap-southeast-2',
    dynamodb: {
      ApproximateCreationDateTime: new Date(1594608723),
      Keys: {
        id: { S: transaction.transactionUuid },
        type: { S: `${DbRecordType.TRANSACTION}1594608723` },
      },
      NewImage: {
        id: { S: transaction.transactionUuid },
        entityUuid: { S: transaction.entityUuid },
      },
      OldImage: {
        id: { S: transaction.transactionUuid },
        entityUuid: { S: transaction.entityUuid },
      },
    },
  },
  {
    eventID: 'c98b908637fed06146a6cf7ca1cc6223',
    eventName: 'MODIFY',
    eventVersion: '1.1',
    eventSource: 'aws:dynamodb',
    awsRegion: 'ap-southeast-2',
    dynamodb: {
      ApproximateCreationDateTime: new Date(1594608723),
      Keys: {
        id: { S: transaction.transactionUuid },
        type: { S: `${DbRecordType.TRANSACTION}1594608723` },
      },
      NewImage: {
        id: { S: transaction.transactionUuid },
        entityUuid: { S: transaction.entityUuid },
      },
      OldImage: {
        id: { S: transaction.transactionUuid },
        entityUuid: { S: transaction.entityUuid },
      },
    },
  },
] as unknown as DbRecordStreamEvent<StreamDbRecord>[];

describe('TransactionHandler test suite', () => {
  let transactionHandler: TransactionHandler;

  beforeEach(() => {
    transactionHandler = new TransactionHandler({
      transactionTotalsUpdateSqsUrl: 'transactionTotalsUpdateSqsUrl',
    } as any);
    mockSendMessage.mockReset();
  });

  it('should be able to handle stream event', async () => {
    await transactionHandler.processDbstream(events);
    expect(mockSendMessage).toHaveBeenCalledTimes(2);
    expect(mockSendMessage.mock.calls[0][0]).toEqual({
      MessageBody: JSON.stringify({
        id: transaction.transactionUuid,
        oldItem: {
          id: transaction.transactionUuid,
          entityUuid: transaction.entityUuid,
        },
        newItem: {
          id: transaction.transactionUuid,
          entityUuid: transaction.entityUuid,
        },
      }),
      MessageDeduplicationId: expect.any(String),
      MessageGroupId: transaction.entityUuid,
      QueueUrl: 'transactionTotalsUpdateSqsUrl',
    });
  });

  it('should be able to skip stream event without messageGroupId', async () => {
    const eventsWithoutEntityUuid = [
      {
        eventID: 'c98b908637fed06146a6cf7ca1cc6222',
        eventName: 'MODIFY',
        eventVersion: '1.1',
        eventSource: 'aws:dynamodb',
        awsRegion: 'ap-southeast-2',
        dynamodb: {
          ApproximateCreationDateTime: new Date(1594608723),
          Keys: {
            id: { S: transaction.transactionUuid },
            type: { S: `${DbRecordType.TRANSACTION}1594608723` },
          },
          NewImage: {
            id: { S: transaction.transactionUuid },
          },
        },
      },
    ] as unknown as DbRecordStreamEvent<StreamDbRecord>[];

    await transactionHandler.processDbstream(eventsWithoutEntityUuid);
    expect(mockSendMessage).toHaveBeenCalledTimes(0);
  });
});
