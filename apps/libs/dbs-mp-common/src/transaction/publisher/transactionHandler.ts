import type { IDbstreamConsumer } from '@npco/bff-common/dist/publisher/dbstreamConsumer';
import { resolvePromisesSequentially } from '@npco/bff-common/dist/publisher/dbstreamUtil';
import { EventStreamHandler } from '@npco/bff-common/dist/publisher/eventStreamHandler';
import type { DbRecordStreamEvent } from '@npco/bff-common/dist/publisher/types';
import { SqsClient } from '@npco/component-bff-core/dist/aws/sqsClient';
import { info, error, debug } from '@npco/component-bff-core/dist/utils/logger';

import { unmarshall } from '@aws-sdk/util-dynamodb';
import { v4 as uuidv4 } from 'uuid';

import type { EnvironmentService } from '../../config/envService';

export class TransactionHandler extends EventStreamHandler implements IDbstreamConsumer {
  sqs: SqsClient;

  constructor(private readonly envService: EnvironmentService) {
    super();
    this.sqs = new SqsClient();
  }

  filterDbstream = () => true;

  handleEventStreamItem = async (item: any) => {
    const aggregateId = item.id;
    const messageGroupId = item.newItem.entityUuid;
    if (!messageGroupId) {
      info(`Skipping stream event without messageGroupId`, aggregateId);
      return;
    }
    info(`sqs message: ${JSON.stringify(item)}`, aggregateId);
    const queueUrl = this.envService.transactionTotalsUpdateSqsUrl;

    await this.sqs.sendRawMessage({
      MessageBody: JSON.stringify(item),
      QueueUrl: queueUrl,
      MessageDeduplicationId: uuidv4(),
      MessageGroupId: messageGroupId,
    });
    info(`sqs message:sent event to ${queueUrl}`, aggregateId);
  };

  processDbstream = async (records: DbRecordStreamEvent[]): Promise<void> => {
    info('processing transaction db stream event');

    let id: string | undefined;
    try {
      const filteredRecords = records.filter(
        (record: DbRecordStreamEvent) => typeof record.dynamodb?.NewImage !== 'undefined',
      );

      const result = await resolvePromisesSequentially(filteredRecords, (record: DbRecordStreamEvent) => {
        const { dynamodb } = record;
        const { NewImage: newImage, OldImage: oldImage } = dynamodb;
        const oldItem = oldImage ? unmarshall(oldImage) : null;
        const newItem = unmarshall(newImage);
        id = newItem.id;
        debug(`new item ${JSON.stringify(newItem)}`);
        debug(`old item ${JSON.stringify(oldItem)}`);
        const streamingItem = {
          id,
          oldItem,
          newItem,
        };
        return this.handleEventStreamItem(streamingItem);
      });
      info(`handle event result ${JSON.stringify(result)}`);
    } catch (err) {
      error('failed to process event', id);
      error(err, id);
    }
  };
}
