import { MutationAttributionService } from '@npco/component-bff-core/dist/attribution/mutationAttributionService';
import {
  CustomerEmailAlreadyExists,
  CustomerIdentityAlreadyExists,
  CustomerIdentityError,
  InvalidRequest,
  ResourceExistsError,
  ServerError,
} from '@npco/component-bff-core/dist/error/graphQlError';
import { HttpService } from '@npco/component-bff-core/dist/httpService/httpService';
import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger';

import AWSXRay from 'aws-xray-sdk-core';
import type { AxiosError, AxiosResponse } from 'axios';

import type { EnvironmentService } from '../config/envService';
import type { JsonObject } from '../types';

export abstract class ApiService {
  protected readonly httpService: HttpService;

  protected attributionService = MutationAttributionService.getInstance();

  protected externalEndpoint = true;

  private readonly config = {
    headers: { 'Content-Type': 'application/json' },
  };

  constructor(
    protected readonly envService: EnvironmentService,
    protected readonly endpointPath: string,
    protected readonly modelType: string,
  ) {
    this.httpService = new HttpService();
    this.httpService.enableRetryInterceptors();
    if (this.envService.isInLambda) {
      /* eslint-disable */
      AWSXRay.captureHTTPsGlobal(require('http'), true);
      AWSXRay.captureHTTPsGlobal(require('https'), true);
      AWSXRay.capturePromise();
      /* eslint-enable */
    }
  }

  public get = async <ResponseDto = void>(
    uuid: string,
    path = '',
    params?: JsonObject<any>,
    headers?: { [key: string]: string },
  ): Promise<ResponseDto> => {
    const url = this.getEndpoint();
    /* istanbul ignore next */
    const extraInfo = path ? `with path ${path}` : '';
    info(`send get ${this.modelType} request to ${url} ${extraInfo}`, uuid);
    return this.httpService
      .get(`${url}/${uuid}${path}`, this.constructConfig(params, headers))
      .then((res: AxiosResponse<ResponseDto>) => {
        this.checkApiError(res);
        return res.data;
      })
      .catch((e) => this.handleApiError(e, `Failed to get ${this.modelType} ${uuid}`, uuid));
  };

  public create = async <CreateDto = void>(uuid: string, dto: CreateDto): Promise<any> => {
    const url = this.getEndpoint();
    info(`send create ${this.modelType} request to ${url}`, uuid);
    return this.httpService
      .post(url, JSON.stringify(dto), this.constructConfig())
      .then((res: AxiosResponse<any>) => {
        info(`Response from create entity endpoint: ${JSON.stringify(res?.data)}`, uuid);
        this.checkApiError(res);
        const parsedRes = res?.data?.body ? JSON.parse(res.data.body) : {};
        info(`Returning response from create: ${JSON.stringify({ id: uuid, ...parsedRes })}`, uuid);
        return { id: uuid, ...parsedRes };
      })
      .catch((e) => this.handleApiError(e, `Failed to create ${this.modelType} ${uuid}`, uuid));
  };

  public delete = async (uuid: string, path = '', headers: { [key: string]: string } = {}): Promise<boolean> => {
    const url = this.getEndpoint();
    info(`send delete ${this.modelType} request to ${url}`, uuid);
    return this.httpService
      .delete(`${url}/${path}${uuid}`, this.constructConfig(undefined, headers))
      .then((res: AxiosResponse) => this.checkApiResponse(res, `Delete ${this.modelType} failed ${uuid}`, uuid))
      .catch((e) => this.handleApiError(e, `Failed to delete ${this.modelType} ${uuid}`, uuid));
  };

  /**
   * Update and suppress graphql error
   */
  public update = async <UpdateDto = void>(uuid: string, updateEventDto: UpdateDto, path = ''): Promise<boolean> => {
    const url = `${this.getEndpoint()}/${uuid}${path}`;
    info(`Send ${this.modelType} update event to ${url}.`, uuid);
    return this.httpService
      .patch(url, JSON.stringify(updateEventDto), this.constructConfig())
      .then((res: AxiosResponse<any>) => this.checkApiResponse(res, `Update ${this.modelType} failed ${uuid}`, uuid))
      .catch((e) => this.handleApiError(e, `Failed to update ${this.modelType} ${uuid}`, uuid));
  };

  public updateWithCustomRequest = async <UpdateDto = void>(
    uuid: string,
    updateEventDto: UpdateDto,
    path = '',
    customRequest?: Record<string, any>,
  ): Promise<boolean> => {
    const url = `${this.getEndpoint()}/${uuid}${path}`;
    info(`Send ${this.modelType} update event to ${url}.`, uuid);
    return this.httpService
      .patch(
        url,
        JSON.stringify({
          ...updateEventDto,
          ...(customRequest && { ...customRequest }),
        }),
        this.constructConfig(),
      )
      .then((res: AxiosResponse<any>) => this.checkApiResponse(res, `Update ${this.modelType} failed ${uuid}`, uuid))
      .catch((e) => this.handleApiError(e, `Failed to update ${this.modelType} ${uuid}`, uuid));
  };

  /**
   * Update and suppress graphql error
   */
  public updateWithDataResponse = async <UpdateDto = void, ResponseDto = any>(
    uuid: string,
    updateEventDto: UpdateDto,
    path = '',
  ): Promise<ResponseDto> => {
    const url = `${this.getEndpoint()}/${uuid}${path}`;
    info(`Send ${this.modelType} update event to ${url}.`, uuid);
    return this.httpService
      .patch(url, JSON.stringify(updateEventDto), this.constructConfig())
      .then((res: AxiosResponse<ResponseDto>) => {
        this.checkApiResponse(res, `Update ${this.modelType} failed ${uuid}`, uuid);
        return res.data;
      })
      .catch((e) => this.handleApiError(e, `Failed to update ${this.modelType} ${uuid}`, uuid));
  };

  protected constructConfig = (params?: JsonObject<any>, headers?: { [key: string]: string }) => {
    return {
      ...this.config,
      headers: {
        ...this.config.headers,
        ...headers,
        ...(!this.externalEndpoint ? this.attributionService.generateAttributionHeaders() : {}),
      },
      params,
    };
  };

  protected readonly readErrors = (response: AxiosResponse) => {
    debug(`${'read errors -> response'}: ${JSON.stringify(response.data, null, 2)}`);
    delete response.request;
    debug(response);
    if (response.status === 400 && response.data?.errorMessage) {
      if (response.data.errorType) {
        const err = this.getError(response);
        if (err.name !== 'Error') {
          return err;
        }
      }
      return new InvalidRequest(response.data.errorMessage);
    }
    if (response.status === 500 && response.data?.errorMessage) {
      return new ServerError(response.data.errorMessage);
    }
    if (response.status > 299 || response.data?.errorMessage) {
      return this.getError(response);
    }
    return null;
  };

  /**
   * Handle any rethrown errors from a failed axios response
   */
  protected handleApiError = (e: AxiosError, message: string, uuid?: string) => {
    if (e.response) {
      const err = this.readErrors(e.response);
      if (err) {
        error(`${message} -> ${JSON.stringify(e, null, 2)}`, uuid);
        return Promise.reject(err); // NOSONAR
      }
    }
    if (e instanceof Error && e.name !== 'Error') {
      const customError = this.handleCustomError(e) ?? e;
      warn(`${message} -> ${JSON.stringify(e, null, 2)}`, uuid);
      return Promise.reject(customError); // NOSONAR
    }
    error(`${message} -> ${JSON.stringify(e, null, 2)}`, uuid);
    return Promise.reject(new ServerError(message)); // NOSONAR
  };

  /**
   * Validate the axios resolved response for any errors and re-throw
   */
  protected checkApiError = (response: AxiosResponse) => {
    const err = this.readErrors(response);
    if (err) {
      error(`${err.message}: ${JSON.stringify(response.data, null, 2)}`);
      throw err;
    }
  };

  /**
   * Validate the axios resolved response for a mutation for any errors and throw it, otherwise return true
   */
  protected checkApiResponse = (response: AxiosResponse, message: string, uuid: string) => {
    info(`invoke ams endpoint response: ${JSON.stringify(response.data)} ${response.status}`, uuid);
    const err = this.readErrors(response);
    if (err) {
      error(`${message}: ${JSON.stringify(response.data, null, 2)}`, uuid);
      throw err;
    }
    debug('checkApiResponse: OK');
    return true;
  };

  // Custom errors from ams api for conflicts
  private readonly handleCustomError = (err: Error) => {
    switch (err.name) {
      case 'CustomerEmailAlreadyExistsError':
      case 'CustomerEmailAlreadyExists':
        return new CustomerEmailAlreadyExists('Customer email already exists.');
      case 'CustomerAuth0IdentityCreationError':
      case 'CustomerIdentityError':
        return new CustomerIdentityError('Customer failed to create identity.');
      case 'CustomerIdentityAlreadyExistsError':
      case 'CustomerIdentityAlreadyExists':
        return new CustomerIdentityAlreadyExists('Customer identity already exists.');
      case 'ResourceExistsError':
        return new ResourceExistsError(err.message);
      case 'BadRequestError':
        return new InvalidRequest(err.message);
      default:
        return null;
    }
  };

  private readonly getError = (response: AxiosResponse) => {
    const customError = new Error();
    if (response.data?.errorType) {
      customError.name = response.data.errorType;
    }

    if (response.data?.errorMessage) {
      customError.message = response.data.errorMessage;
    }
    const err = this.handleCustomError(customError);
    if (err) {
      return err;
    }
    return customError;
  };

  protected abstract getEndpoint(): string;
}
