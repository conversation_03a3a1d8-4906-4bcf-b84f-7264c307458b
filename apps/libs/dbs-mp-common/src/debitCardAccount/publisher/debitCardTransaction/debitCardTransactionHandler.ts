import type { IDbstreamConsumer } from '@npco/bff-common/dist/publisher/dbstreamConsumer';
import { EventStreamHandler } from '@npco/bff-common/dist/publisher/eventStreamHandler';
import type { DbRecordStreamEvent } from '@npco/bff-common/dist/publisher/types';
import { SqsClient } from '@npco/component-bff-core/dist/aws/sqsClient';
import { info } from '@npco/component-bff-core/dist/utils/logger';

import { v4 as uuidv4 } from 'uuid';

import type { EnvironmentService } from '../../../config/envService';

export class DebitCardTransactionHandler extends EventStreamHandler implements IDbstreamConsumer {
  sqs: SqsClient;

  constructor(private readonly envSerivce: EnvironmentService) {
    super();
    this.sqs = new SqsClient(this.envSerivce.awsRegion);
  }

  filterDbstream = () => true;

  handleEventStreamItem = async (debitCardTransactionItem: any) => {
    info(`sqs message: ${JSON.stringify(debitCardTransactionItem)}`);
    await this.sqs.sendRawMessage({
      MessageBody: JSON.stringify(debitCardTransactionItem),
      QueueUrl: this.envSerivce.statementsToSendSqsUrl,
      MessageGroupId: debitCardTransactionItem.id,
      MessageDeduplicationId: uuidv4(),
    });
    info(`sqs message:sent event to ${this.envSerivce.statementsToSendSqsUrl}`);
  };

  processDbstream = async (event: DbRecordStreamEvent[]): Promise<void> => {
    info(`contact stream event: ${JSON.stringify(event)}`);
    await this.handleStreamEvent(event);
  };
}
