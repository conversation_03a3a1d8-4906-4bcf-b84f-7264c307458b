import { EnvironmentService as EnvSettings } from '@npco/component-bff-core/dist/config';
import type {
  BaseQueryInput,
  BffDynamoDbClient,
  QueryOptions,
  QueryOptionsInput,
} from '@npco/component-bff-core/dist/dynamodb';
import { BaseDbService as CoreDbService } from '@npco/component-bff-core/dist/dynamodb';
import { debug, error, info, warn } from '@npco/component-bff-core/dist/utils/logger';
import type { DbRecordType } from '@npco/component-dto-core';

import type {
  BatchGetCommandInput,
  BatchGetCommandOutput,
  BatchWriteCommandInput,
  BatchWriteCommandOutput,
  QueryCommandOutput,
} from '@aws-sdk/lib-dynamodb';
import { BatchGetCommand, BatchWriteCommand } from '@aws-sdk/lib-dynamodb';
import type { NativeAttributeValue } from '@aws-sdk/util-dynamodb';

import type { EnvironmentService } from '../config/envService';

import { DynamodbUtils } from './dynamodbUtils';

const BATCH_THROUGHPUT_DELAY_MS = 200;

export class BaseDbService extends CoreDbService {
  utils = new DynamodbUtils();

  constructor(readonly envService$: EnvironmentService, public documentClient: BffDynamoDbClient) {
    super(new EnvSettings(), documentClient);
  }

  /**
   * Safe batchWrite that will write items > 25 limit
   * @param items array of id/type/...
   */
  batchWriteAll = async (items: any[], tableName = this.envService$.componentTableName): Promise<void> => {
    debug(`DynamoDbService:batchWriteAll: ${JSON.stringify(items)}`);
    if (!items.length) {
      return;
    }
    const processedItems = [...items];
    try {
      const promiseLimit = 50;
      const writeSize = 20; //  16mb limit
      do {
        const proms = [];
        do {
          const batch = processedItems.splice(0, writeSize);
          proms.push(this.batchWriteItem(batch, tableName));
        } while (proms.length < promiseLimit && processedItems.length);
        const result = await Promise.all(proms);
        debug(`batchWriteAll:proms: ${result.length}`);
        result.forEach((unprocessedItems) => {
          if (unprocessedItems?.length) {
            processedItems.push(...unprocessedItems.map((item) => item.PutRequest.Item));
          }
        });
        debug(`batchWriteAll:unprocessedItems: ${processedItems.length}`);
      } while (processedItems.length);
      info(`batchWriteAll:complete: ${items.length}`);
    } catch (e: any) {
      error(e);
      throw e;
    }
  };

  /**
   * Safe BatchGet that will fetch items > 100 limit, has a max batching size of 100 x 100
   * can return very large results - out of memory
   * @param keys array of pk id type
   * @param projectionExpression batch get item is limited to 16mb per request
   * @param projectionExpressionNames mapping reserved words, type, name etc
   * @returns items
   */
  batchGetAll = async (
    keys: { id: string; type: string }[],
    params?: { projectionExpression: string; projectionExpressionNames?: { [key: string]: string } },
    tableName = this.envService$.componentTableName,
  ): Promise<Record<string, NativeAttributeValue>[]> => {
    if (!keys.length) {
      return [];
    }
    debug(`DynamoDbService:batchGetAll: ${JSON.stringify(keys)}`);
    const processedKeys = [...keys];
    try {
      const promiseLimit = 50;
      const fetchSize = 100; // 100 Item - 16mb limit
      const response: Record<string, NativeAttributeValue>[] = [];
      do {
        const proms = [];
        do {
          const batch = processedKeys.splice(0, fetchSize);
          proms.push(this.batchGetItem(batch, params, tableName));
        } while (proms.length < promiseLimit && processedKeys.length);
        const result = await Promise.all(proms);
        debug(`batchGetAll:proms: ${result.length}`);
        const responses = result.reduce((acc: Record<string, NativeAttributeValue>[], batchResult) => {
          if (batchResult.UnprocessedKeys?.length) {
            processedKeys.push(...batchResult.UnprocessedKeys);
          }
          if (batchResult.Response) {
            return acc.concat(batchResult.Response);
          }
          return acc;
        }, []);
        response.push(...responses);
        debug(`batchWriteAll:unprocessedKeys: ${processedKeys.length}`);
      } while (processedKeys.length);
      debug(`batchGetAll:response: ${response.length}`);
      return response;
    } catch (e: any) {
      error(e);
      throw e;
    }
  };

  queryGsiByType = async (pk: string, type: string, pkName: string, indexName: string): Promise<QueryCommandOutput> =>
    this.query({
      TableName: this.envService.componentTableName,
      IndexName: indexName,
      KeyConditionExpression: '#pk = :pk AND #type = :type',
      ExpressionAttributeNames: {
        '#pk': pkName,
        '#type': 'type',
      },
      ExpressionAttributeValues: {
        ':type': type,
        ':pk': pk,
      },
    });

  queryIdByLessThanType = async (
    entityUuid: string,
    uuid: string,
    type: DbRecordType | string,
    params?: QueryOptionsInput,
  ): Promise<QueryCommandOutput> => {
    const options: QueryOptions = {
      ...this.defaultQueryIdByTypeOptions,
      ...params,
    };
    const baseQuery: BaseQueryInput = {
      KeyConditionExpression: '#id = :id AND #type < :type',
    };
    debug('DynamoDbService:queryIdByLessThanType', uuid);
    return this.handleQuery(uuid, type, entityUuid, options, baseQuery);
  };

  queryIdByLessThanTypeWithinPrefix = async (
    entityUuid: string,
    uuid: string,
    typePrefix: string,
    type: DbRecordType | string,
    params?: QueryOptionsInput,
  ): Promise<QueryCommandOutput> => {
    const options: QueryOptions = {
      ...this.defaultQueryIdByTypeOptions,
      ...params,
    };
    const baseQuery: BaseQueryInput = {
      KeyConditionExpression: '#id = :id AND #type between :typePrefix AND :type',
      ExpressionAttributeValues: {
        ':typePrefix': typePrefix,
        ':type': type,
      },
    };
    debug('DynamoDbService:queryIdByLessThanTypeWithinPrefix', uuid);
    return this.handleQuery(uuid, type, entityUuid, options, baseQuery);
  };

  queryIdByBetweenType = async (
    entityUuid: string,
    uuid: string,
    typeStart: DbRecordType | string,
    typeEnd: DbRecordType | string,
    params?: QueryOptionsInput,
  ): Promise<QueryCommandOutput> => {
    const ignoreEntity = Boolean(params?.ignoreEntity ?? params?.entityGsi);
    const options: QueryOptions = {
      ...this.defaultQueryIdByTypeOptions,
      ...params,
      ignoreEntity,
      ignoreDefaultType: true,
    };
    const baseQuery: BaseQueryInput = {
      KeyConditionExpression: '#id = :id AND #type between :typeStart AND :typeEnd',
      ExpressionAttributeValues: {
        ':typeStart': typeStart,
        ':typeEnd': typeEnd,
      },
    };
    debug('DynamoDbService:queryIdByBetweenType', uuid);
    return this.handleQuery(uuid, 'type', entityUuid, options, baseQuery);
  };

  /**
   * Exhuastive query on the main table
   * @param entityUuid can be empty to ignore entityUuid
   * @param uuid === primary id
   * @param type begins_with
   * @param filterDeleted true = filter DELETED status
   * @param ignoreEntity true will ignore using the entityUuid
   * @returns array of items
   */
  queryAllById = async (
    entityUuid: string,
    uuid: string,
    type: DbRecordType,
    filterDeleted = false,
    ignoreEntity = false,
  ) => {
    info(`queryAllById ${type} ${uuid}`, entityUuid);
    return this.fetchAllQueryWrapper((lastEvaluatedKey) =>
      this.queryIdByType(entityUuid, uuid, type, {
        ...this.defaultQueryIdByTypeOptions,
        filterDeleted,
        ignoreEntity,
        lastEvaluatedKey,
      }),
    );
  };

  queryAllByIdWithBetween = async (
    entityUuid: string,
    uuid: string,
    typeStart: DbRecordType | string,
    typeEnd: DbRecordType | string,
    filterDeleted = false,
    ignoreEntity = false,
    entityGsi?: boolean,
  ) => {
    info(`queryAllByIdWithBetween ${uuid} between ${typeStart} and ${typeEnd}`, entityUuid);
    return this.fetchAllQueryWrapper((lastEvaluatedKey) =>
      this.queryIdByBetweenType(entityUuid, uuid, typeStart, typeEnd, {
        ...this.defaultQueryIdByTypeOptions,
        filterDeleted,
        ignoreEntity,
        lastEvaluatedKey,
        entityGsi,
      }),
    );
  };

  queryOneByTypeWithID = async (
    uuid: string,
    type: DbRecordType,
    filterDeleted = false,
    tableName = this.envService$.componentTableName,
  ): Promise<QueryCommandOutput> =>
    this.queryIdByType('', uuid, type, {
      ignoreEntity: true,
      filterDeleted,
      tableName,
    });

  private readonly batchWriteItem = async (items: any[], tableName: string): Promise<any[]> => {
    let unprocessedItems;
    try {
      debug(`DynamoDbService:batchWriteItem: ${JSON.stringify(items)}`);
      const param: BatchWriteCommandInput = {
        RequestItems: {
          [tableName]: items.map((item) => ({ PutRequest: { Item: item } })),
        },
      };
      const result: BatchWriteCommandOutput = await this.documentClient.send(new BatchWriteCommand(param));
      if (result.UnprocessedItems?.[tableName]?.length) {
        debug(`UnprocessedItems: ${JSON.stringify(result.UnprocessedItems[tableName]?.length)}`);
        unprocessedItems = result.UnprocessedItems[tableName];
      }
      return unprocessedItems ?? [];
    } catch (e: any) {
      if (e.name === 'ProvisionedThroughputExceededException') {
        warn(`batchWriteItem:ProvisionedThroughputExceededException:retrying ${JSON.stringify(e)}`);
        await new Promise((resolve) => {
          setTimeout(resolve, BATCH_THROUGHPUT_DELAY_MS);
        });
        return unprocessedItems ?? [];
      }
      error(e);
      throw e;
    }
  };

  private readonly batchGetItem = async (
    keys: { id: string; type: string }[],
    params?: { projectionExpression: string; projectionExpressionNames?: { [key: string]: string } },
    tableName = this.envService$.componentTableName,
  ): Promise<{
    Response: Record<string, NativeAttributeValue>[];
    UnprocessedKeys?: { id: string; type: string }[];
  }> => {
    if (!keys.length) {
      return { Response: [] };
    }
    try {
      debug(`batchGetItem: ${JSON.stringify(keys)}`);
      const param: BatchGetCommandInput = {
        RequestItems: {
          [tableName]: {
            Keys: keys,
            ProjectionExpression: params?.projectionExpression,
            ...(params?.projectionExpressionNames
              ? { ExpressionAttributeNames: params?.projectionExpressionNames }
              : {}),
          },
        },
      };
      const result: BatchGetCommandOutput = await this.documentClient.send(new BatchGetCommand(param));
      info(`batchGetItem:result: ${result.Responses?.length}`);
      let unprocessed;
      if (result.UnprocessedKeys?.[tableName]) {
        debug(`UnprocessedKeys: ${JSON.stringify(result.UnprocessedKeys)}`);
        const unprocessedKeys = result.UnprocessedKeys[tableName];
        if (unprocessedKeys?.Keys?.length) {
          unprocessed = unprocessedKeys.Keys.map((key) => ({ id: key.id.S, type: key.type.S }));
        }
      }
      if (result.Responses?.[tableName]) {
        const response = result.Responses[tableName];
        info(`batchGetItem:response: ${response.length}`);
        return { Response: response, UnprocessedKeys: unprocessed };
      }
      return { Response: [], UnprocessedKeys: unprocessed };
    } catch (e: any) {
      if (e.name === 'ProvisionedThroughputExceededException') {
        warn(`batchGetItem:ProvisionedThroughputExceededException:retrying ${JSON.stringify(e)}`);
        await new Promise((resolve) => {
          setTimeout(resolve, BATCH_THROUGHPUT_DELAY_MS);
        });
        return { Response: [], UnprocessedKeys: keys };
      }
      error(e);
      throw e;
    }
  };
}
