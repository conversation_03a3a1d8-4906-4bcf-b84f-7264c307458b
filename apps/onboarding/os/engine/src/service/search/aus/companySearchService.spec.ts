import type { Address } from '@npco/component-dto-core';
import { AddressState, EntityType } from '@npco/component-dto-core';
import type { EntityAddress, EntityFullSearchRequestedEventDto, EntityMember } from '@npco/component-dto-entity';

import { jest } from '@jest/globals';
import { v4 } from 'uuid';

import { EnvironmentService } from '../../../config/envService.js';
import type { FullCompanyProfileSearchResult, InitialCompanySearchResult } from '../../../types.js';

import type { CompanyFullProfileSearchDbRecord, CompanyInitialSearchDbRecord } from './types.js';

const equifaxFullCompanyProfileSearch = jest.fn();
const equifaxInitialCompanySearch = jest.fn();
const createFullCompanyProfileCache = jest.fn();
const lookupFullCompanyProfileCacheByEntityUuid = jest.fn();
const createInitialSearchCache = jest.fn();
const lookupInitialSearchCacheByBusinessIdentifier = jest.fn();
const lookupInitialSearchCacheByCustomerUuid = jest.fn();
const lookupInitialSearchCacheByEntityUuid = jest.fn();

jest.unstable_mockModule('../../../equifax/index', () => ({
  __esModule: true,
  fullCompanyProfileSearch: equifaxFullCompanyProfileSearch,
  initialCompanySearch: equifaxInitialCompanySearch,
}));

jest.unstable_mockModule('./db/companyInitialSearchDb', () => ({
  __esModule: true,
  createInitialSearchCache,
  lookupInitialSearchCacheByBusinessIdentifier,
  lookupInitialSearchCacheByCustomerUuid,
  lookupInitialSearchCacheByEntityUuid,
}));

jest.unstable_mockModule('./db/companyFullProfileSearchDb', () => ({
  __esModule: true,
  createFullCompanyProfileCache,
  lookupFullCompanyProfileCacheByEntityUuid,
}));

jest.mock('aws-sdk');

describe('initial company search test suite', () => {
  let initialCompanySearch: any;

  beforeAll(async () => {
    const companySearchService = await import('./companySearchService.js');
    initialCompanySearch = companySearchService.initialCompanySearch;
  });

  beforeEach(jest.clearAllMocks);

  it('returns not found if invalid business identifier input', async () => {
    const mockInput = {
      entityUuid: 'uuid',
      customerUuid: 'customer_uuid',
      businessIdentifier: '123456',
    };

    equifaxInitialCompanySearch.mockResolvedValue(null as never);
    createInitialSearchCache.mockResolvedValue(null as never);
    lookupInitialSearchCacheByBusinessIdentifier.mockResolvedValue(null as never);
    expect(equifaxInitialCompanySearch).not.toHaveBeenCalled();
    expect(createInitialSearchCache).not.toHaveBeenCalled();
    expect(lookupInitialSearchCacheByBusinessIdentifier).not.toHaveBeenCalled();
    const result = await initialCompanySearch(mockInput);
    expect(equifaxInitialCompanySearch).toHaveBeenCalledTimes(0);
    expect(createInitialSearchCache).toHaveBeenCalledTimes(0);
    expect(lookupInitialSearchCacheByBusinessIdentifier).toHaveBeenCalledTimes(0);
    expect(result).toBeDefined();
    expect(result?.entityUuid).toBe(mockInput.entityUuid);
    expect(result?.customerUuid).toBe(mockInput.customerUuid);
    expect(result?.businessIdentifier).toBe(mockInput.businessIdentifier);
    expect(result?.country).toBe('AUS');
    expect(result?.name).toBeUndefined();
    expect(result?.businessDetails).toBeUndefined();
    expect(result?.acn).toBeUndefined();
    expect(result?.abn).toBeUndefined();
    expect(result?.type).toBeUndefined();
    expect(result?.found).toBe(false);
    expect(result?.error).toEqual('Error: Invalid Business Identifier');
  });

  it('searches the api and returns the values if the business identifier is not in cache', async () => {
    const mockInput = {
      entityUuid: 'uuid',
      customerUuid: 'customer_uuid',
      businessIdentifier: '79*********',
    };
    const mockAPIReturn = {
      aggregateId: 'customer_uuid',
      businessIdentifier: '79*********',
      name: 'test',
      abn: '79*********',
      acn: '*********',
      type: EntityType.COMPANY,
      active: true,
      requestXml: '',
      responseXml: '',
    } as InitialCompanySearchResult;

    equifaxInitialCompanySearch.mockReturnValue(mockAPIReturn);
    createInitialSearchCache.mockImplementation(() => Promise.resolve());
    lookupInitialSearchCacheByBusinessIdentifier.mockResolvedValue(null as never);
    expect(equifaxInitialCompanySearch).not.toHaveBeenCalled();
    expect(createInitialSearchCache).not.toHaveBeenCalled();
    expect(lookupInitialSearchCacheByBusinessIdentifier).not.toHaveBeenCalled();
    const result = await initialCompanySearch(mockInput);
    expect(equifaxInitialCompanySearch).toHaveBeenCalledTimes(1);
    expect(createInitialSearchCache).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByBusinessIdentifier).toHaveBeenCalledTimes(1);
    expect(result).toBeDefined();
    expect(result?.customerUuid).toBe(mockAPIReturn.aggregateId);
    expect(result?.country).toBe('AUS');
    expect(result?.businessIdentifier).toBe(mockAPIReturn.businessIdentifier);
    expect(result?.name).toBe(mockAPIReturn.name);
    expect(result?.businessDetails).toEqual({
      ausBusiness: {
        acn: mockAPIReturn.acn,
        abn: mockAPIReturn.abn,
      },
    });
    expect(result?.acn).toBe(mockAPIReturn.acn);
    expect(result?.abn).toBe(mockAPIReturn.abn);
    expect(result?.type).toBe(mockAPIReturn.type);
    expect(result?.found).toBe(true);
    expect(result?.error).toBeUndefined();
  });

  it('returns the cache if the business identifier is in cache', async () => {
    const mockInput = {
      entityUuid: 'uuid',
      customerUuid: 'customer_uuid',
      businessIdentifier: '79*********',
    };

    const mockCache = {
      id: '79*********',
      entityUuid: 'other_uuid',
      customerUuid: 'other_customer_uuid',
      expirationTime: 123,
      name: 'name',
      acn: 'acn',
      abn: 'abn',
      active: true,
      type: EntityType.COMPANY,
      requestXml: 'requestXml',
      responseXml: 'responseXml',
    } as CompanyInitialSearchDbRecord;

    equifaxInitialCompanySearch.mockReturnValue('');
    createInitialSearchCache.mockImplementation(() => Promise.resolve());
    lookupInitialSearchCacheByBusinessIdentifier.mockResolvedValue(mockCache as never);
    expect(equifaxInitialCompanySearch).not.toHaveBeenCalled();
    expect(createInitialSearchCache).not.toHaveBeenCalled();
    expect(lookupInitialSearchCacheByBusinessIdentifier).not.toHaveBeenCalled();
    const result = await initialCompanySearch(mockInput);
    expect(equifaxInitialCompanySearch).not.toHaveBeenCalled();
    expect(lookupInitialSearchCacheByBusinessIdentifier).toHaveBeenCalledTimes(1);
    expect(createInitialSearchCache).toHaveBeenCalledTimes(1);
    expect(result).toBeDefined();
    expect(result?.entityUuid).toBe(mockInput.entityUuid);
    expect(result?.customerUuid).toBe(mockInput.customerUuid);
    expect(result?.businessIdentifier).toBe(mockCache.id);
    expect(result?.country).toBe('AUS');
    expect(result?.name).toBe(mockCache.name);
    expect(result?.acn).toBe(mockCache.acn);
    expect(result?.abn).toBe(mockCache.abn);
    expect(result?.businessDetails).toEqual({
      ausBusiness: {
        acn: mockCache.acn,
        abn: mockCache.abn,
      },
    });
    expect(result?.type).toBe(mockCache.type);
    expect(result?.found).toBe(true);
    expect(result?.error).toBeUndefined();
  });

  it('returns not found if the business Identifier is not existed', async () => {
    const mockInput = {
      entityUuid: 'uuid',
      customerUuid: 'customer_uuid',
      businessIdentifier: '***********',
    };

    const mockAPIReturn = {
      aggregateId: 'customer_uuid',
      businessIdentifier: '***********',
      requestXml: '',
      responseXml: '',
    } as InitialCompanySearchResult;

    equifaxInitialCompanySearch.mockReturnValue(mockAPIReturn);
    createInitialSearchCache.mockImplementation(() => Promise.resolve());
    lookupInitialSearchCacheByBusinessIdentifier.mockResolvedValue(null as never);
    expect(equifaxInitialCompanySearch).not.toHaveBeenCalled();
    expect(createInitialSearchCache).not.toHaveBeenCalled();
    expect(lookupInitialSearchCacheByBusinessIdentifier).not.toHaveBeenCalled();
    const result = await initialCompanySearch(mockInput);
    expect(equifaxInitialCompanySearch).toHaveBeenCalledTimes(1);
    expect(createInitialSearchCache).toHaveBeenCalledTimes(0);
    expect(lookupInitialSearchCacheByBusinessIdentifier).toHaveBeenCalledTimes(1);
    expect(result).toBeDefined();
    expect(result?.entityUuid).toBe(mockInput.entityUuid);
    expect(result?.customerUuid).toBe(mockAPIReturn.aggregateId);
    expect(result?.businessIdentifier).toBe(mockAPIReturn.businessIdentifier);
    expect(result?.country).toBe('AUS');
    expect(result?.name).toBeUndefined();
    expect(result?.businessDetails).toBeUndefined();
    expect(result?.acn).toBeUndefined();
    expect(result?.abn).toBeUndefined();
    expect(result?.type).toBeUndefined();
    expect(result?.found).toBe(false);
    expect(result?.error).toBeUndefined();
  });

  it('returns not found if the Entity type is not supported', async () => {
    const mockInput = {
      entityUuid: 'uuid',
      customerUuid: 'customer_uuid',
      businessIdentifier: '***********',
    };

    const mockAPIReturn = {
      aggregateId: 'customer_uuid',
      businessIdentifier: '***********',
      name: 'test',
      abn: '79*********',
      acn: '*********',
      type: EntityType.OTHER,
      requestXml: '',
      responseXml: '',
    } as InitialCompanySearchResult;

    equifaxInitialCompanySearch.mockReturnValue(mockAPIReturn);
    createInitialSearchCache.mockImplementation(() => Promise.resolve());
    lookupInitialSearchCacheByBusinessIdentifier.mockResolvedValue(null as never);
    expect(equifaxInitialCompanySearch).not.toHaveBeenCalled();
    expect(createInitialSearchCache).not.toHaveBeenCalled();
    expect(lookupInitialSearchCacheByBusinessIdentifier).not.toHaveBeenCalled();
    const result = await initialCompanySearch(mockInput);
    expect(equifaxInitialCompanySearch).toHaveBeenCalledTimes(1);
    expect(createInitialSearchCache).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByBusinessIdentifier).toHaveBeenCalledTimes(1);
    expect(result).toBeDefined();
    expect(result?.entityUuid).toBe(mockInput.entityUuid);
    expect(result?.customerUuid).toBe(mockAPIReturn.aggregateId);
    expect(result?.businessIdentifier).toBe(mockAPIReturn.businessIdentifier);
    expect(result?.country).toBe('AUS');
    expect(result?.name).toBeUndefined();
    expect(result?.businessDetails).toBeUndefined();
    expect(result?.acn).toBeUndefined();
    expect(result?.abn).toBeUndefined();
    expect(result?.type).toBeUndefined();
    expect(result?.found).toBe(false);
    expect(result?.error).toBe('Entity Type is not supported');
  });

  it('returns not found if entity business is not active', async () => {
    const mockInput = {
      entityUuid: 'uuid',
      customerUuid: 'customer_uuid',
      businessIdentifier: '***********',
    };

    const mockAPIReturn = {
      aggregateId: 'customer_uuid',
      businessIdentifier: '***********',
      name: 'test',
      abn: '79*********',
      acn: '*********',
      type: EntityType.COMPANY,
      active: false,
      requestXml: '',
      responseXml: '',
    } as InitialCompanySearchResult;

    equifaxInitialCompanySearch.mockReturnValue(mockAPIReturn);
    createInitialSearchCache.mockImplementation(() => Promise.resolve());
    lookupInitialSearchCacheByBusinessIdentifier.mockResolvedValue(null as never);
    expect(equifaxInitialCompanySearch).not.toHaveBeenCalled();
    expect(createInitialSearchCache).not.toHaveBeenCalled();
    expect(lookupInitialSearchCacheByBusinessIdentifier).not.toHaveBeenCalled();
    const result = await initialCompanySearch(mockInput);
    expect(equifaxInitialCompanySearch).toHaveBeenCalledTimes(1);
    expect(createInitialSearchCache).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByBusinessIdentifier).toHaveBeenCalledTimes(1);
    expect(result).toBeDefined();
    expect(result?.entityUuid).toBe(mockInput.entityUuid);
    expect(result?.customerUuid).toBe(mockAPIReturn.aggregateId);
    expect(result?.businessIdentifier).toBe(mockAPIReturn.businessIdentifier);
    expect(result?.country).toBe('AUS');
    expect(result?.name).toBeUndefined();
    expect(result?.businessDetails).toBeUndefined();
    expect(result?.acn).toBeUndefined();
    expect(result?.abn).toBeUndefined();
    expect(result?.type).toBeUndefined();
    expect(result?.found).toBe(false);
    expect(result?.error).toBe('NOT_ACTIVE: Business Identifier is DE-REGISTER or UNDER EXTERNAL ADMINISTRATION');
  });

  it('returns not found if there is an error in initialCompanySearch', async () => {
    const mockInput = {
      entityUuid: 'uuid',
      customerUuid: 'customer_uuid',
      businessIdentifier: '***********',
    };

    const errorMessage = 'Equifax Error';

    equifaxInitialCompanySearch.mockRejectedValue(errorMessage as never);
    createInitialSearchCache.mockImplementation(() => Promise.resolve());
    lookupInitialSearchCacheByBusinessIdentifier.mockResolvedValue(null as never);
    expect(equifaxInitialCompanySearch).not.toHaveBeenCalled();
    expect(createInitialSearchCache).not.toHaveBeenCalled();
    expect(lookupInitialSearchCacheByBusinessIdentifier).not.toHaveBeenCalled();
    const result = await initialCompanySearch(mockInput);
    expect(equifaxInitialCompanySearch).toHaveBeenCalledTimes(1);
    expect(createInitialSearchCache).toHaveBeenCalledTimes(0);
    expect(lookupInitialSearchCacheByBusinessIdentifier).toHaveBeenCalledTimes(1);
    expect(result).toBeDefined();
    expect(result?.entityUuid).toBe(mockInput.entityUuid);
    expect(result?.customerUuid).toBe(mockInput.customerUuid);
    expect(result?.businessIdentifier).toBe(mockInput.businessIdentifier);
    expect(result?.country).toBe('AUS');
    expect(result?.name).toBeUndefined();
    expect(result?.businessDetails).toBeUndefined();
    expect(result?.acn).toBeUndefined();
    expect(result?.abn).toBeUndefined();
    expect(result?.type).toBeUndefined();
    expect(result?.found).toBe(false);
    expect(result?.error).toBe(errorMessage);
  });
});

describe('full company profile search test suite', () => {
  const mockEntityUuid = 'uuid';
  const mockCustomerUuid = 'customer_uuid';
  const mockBusinessAddress = {
    street1: 'business_street1',
    street2: 'business_street2',
    suburb: 'business_suburb',
    state: AddressState.NSW,
    postcode: '2000',
  } as EntityAddress;
  const mockRegisteredAddress = {
    street1: 'street1',
    street2: 'street2',
    suburb: 'suburb',
    state: AddressState.VIC,
    postcode: '3000',
  } as EntityAddress;
  const mockAddress = {
    street: '123 Spring Street',
    suburb: 'Melbourne',
    state: AddressState.VIC,
    postcode: '3000',
  } as Address;
  const mockMembers = [
    {
      firstname: 'firstname',
      lastname: 'lastname',
      address: mockAddress,
      director: true,
    },
    {
      firstname: 'firstname',
      middlename: 'middlename',
      lastname: 'lastname',
      address: mockAddress,
      secretary: true,
    },
  ] as EntityMember[];
  const mockDynamoDBInitialCompanySearchRecord = {
    id: v4(),
    acn: 'acn',
    abn: 'abn',
    name: 'name',
    type: EntityType.COMPANY,
  } as CompanyInitialSearchDbRecord;
  const mockDynamoDBFullCompanyProfileSearchRecord = {
    entityUuid: mockEntityUuid,
    acn: 'acn',
    registeredAddress: mockRegisteredAddress,
    businessAddress: mockBusinessAddress,
    members: mockMembers,
  } as CompanyFullProfileSearchDbRecord;

  let fullCompanyProfileSearch: any;

  beforeAll(async () => {
    const companySearchService = await import('./companySearchService.js');
    fullCompanyProfileSearch = companySearchService.fullCompanyProfileSearch;
  });

  beforeEach(jest.clearAllMocks);

  it('returns record based on the initial company search and Equifax API with EntityUuid (old format)', async () => {
    const mockAcn = 'acn';

    const mockInput = {
      entityUuid: mockEntityUuid,
      acn: mockAcn,
    } as EntityFullSearchRequestedEventDto;

    const mockFullCompanyProfileSearch = {
      aggregateId: mockEntityUuid,
      acn: mockAcn,
      found: true,
      registeredAddress: mockRegisteredAddress,
      businessAddress: mockBusinessAddress,
      companyFile: {},
    } as FullCompanyProfileSearchResult;

    (lookupInitialSearchCacheByCustomerUuid as jest.Mock).mockResolvedValue(null as never);
    (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBInitialCompanySearchRecord as never,
    );
    (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
    (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(mockFullCompanyProfileSearch as never);
    (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

    expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
    const result = await fullCompanyProfileSearch(mockInput);
    expect(lookupInitialSearchCacheByCustomerUuid).toHaveBeenCalledTimes(0);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
      mockEntityUuid,
      expect.any(EnvironmentService),
      mockEntityUuid,
    );
    expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(1);
    expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(1);
    const { entityUuid, found, acn, registeredAddress, businessAddress, companyFile, error } = result;
    expect(entityUuid).toBe(mockEntityUuid);
    expect(found).toBe(true);
    expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
    expect(registeredAddress).toBe(mockRegisteredAddress);
    expect(businessAddress).toBe(mockBusinessAddress);
    expect(companyFile).toBeDefined();
    expect(error).toBeUndefined();
  });

  describe('test with the customerUuid in the full request dto', () => {
    it('return based on customerUuid', async () => {
      const mockAcn = 'acn';

      const mockInput = {
        entityUuid: mockEntityUuid,
        customerUuid: mockCustomerUuid,
        acn: mockAcn,
      } as EntityFullSearchRequestedEventDto;

      const mockFullCompanyProfileSearch = {
        aggregateId: mockEntityUuid,
        acn: mockAcn,
        found: true,
        registeredAddress: mockRegisteredAddress,
        businessAddress: mockBusinessAddress,
        companyFile: {},
      } as FullCompanyProfileSearchResult;

      (lookupInitialSearchCacheByCustomerUuid as jest.Mock).mockResolvedValue(
        mockDynamoDBInitialCompanySearchRecord as never,
      );
      (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
      (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
      (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(mockFullCompanyProfileSearch as never);
      (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

      expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
      expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
      expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
      expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
      const result = await fullCompanyProfileSearch(mockInput);
      expect(lookupInitialSearchCacheByCustomerUuid).toHaveBeenCalledTimes(1);
      expect(lookupInitialSearchCacheByCustomerUuid).toHaveBeenLastCalledWith(
        mockCustomerUuid,
        expect.any(EnvironmentService),
        mockEntityUuid,
      );
      expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(0);
      expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(1);
      expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
      expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(1);
      const { entityUuid, found, acn, registeredAddress, businessAddress, companyFile, error } = result;
      expect(entityUuid).toBe(mockEntityUuid);
      expect(found).toBe(true);
      expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
      expect(registeredAddress).toBe(mockRegisteredAddress);
      expect(businessAddress).toBe(mockBusinessAddress);
      expect(companyFile).toBeDefined();
      expect(error).toBeUndefined();
    });

    it('able to handle if throw in the customerGSI search', async () => {
      const mockAcn = 'acn';

      const mockInput = {
        entityUuid: mockEntityUuid,
        customerUuid: mockCustomerUuid,
        acn: mockAcn,
      } as EntityFullSearchRequestedEventDto;

      const mockFullCompanyProfileSearch = {
        aggregateId: mockEntityUuid,
        acn: mockAcn,
        found: true,
        registeredAddress: mockRegisteredAddress,
        businessAddress: mockBusinessAddress,
        companyFile: {},
      } as FullCompanyProfileSearchResult;

      (lookupInitialSearchCacheByCustomerUuid as jest.Mock).mockRejectedValue(new Error('error message') as never);
      (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(
        mockDynamoDBInitialCompanySearchRecord as never,
      );
      (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
      (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(mockFullCompanyProfileSearch as never);
      (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

      expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
      expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
      expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
      expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
      const result = await fullCompanyProfileSearch(mockInput);
      expect(lookupInitialSearchCacheByCustomerUuid).toHaveBeenCalledTimes(1);
      expect(lookupInitialSearchCacheByCustomerUuid).toHaveBeenLastCalledWith(
        mockCustomerUuid,
        expect.any(EnvironmentService),
        mockEntityUuid,
      );
      expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
      expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
        mockEntityUuid,
        expect.any(EnvironmentService),
        mockEntityUuid,
      );
      expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(1);
      expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
      expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(1);
      const { entityUuid, found, acn, registeredAddress, businessAddress, companyFile, error } = result;
      expect(entityUuid).toBe(mockEntityUuid);
      expect(found).toBe(true);
      expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
      expect(registeredAddress).toBe(mockRegisteredAddress);
      expect(businessAddress).toBe(mockBusinessAddress);
      expect(companyFile).toBeDefined();
      expect(error).toBeUndefined();
    });

    it('customerUuid not found, fall back with entityUuid', async () => {
      const mockAcn = 'acn';

      const mockInput = {
        entityUuid: mockEntityUuid,
        customerUuid: mockCustomerUuid,
        acn: mockAcn,
      } as EntityFullSearchRequestedEventDto;

      const mockFullCompanyProfileSearch = {
        aggregateId: mockEntityUuid,
        acn: mockAcn,
        found: true,
        registeredAddress: mockRegisteredAddress,
        businessAddress: mockBusinessAddress,
        companyFile: {},
      } as FullCompanyProfileSearchResult;

      (lookupInitialSearchCacheByCustomerUuid as jest.Mock).mockResolvedValue(null as never);
      (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(
        mockDynamoDBInitialCompanySearchRecord as never,
      );
      (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
      (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(mockFullCompanyProfileSearch as never);
      (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

      expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
      expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
      expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
      expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
      const result = await fullCompanyProfileSearch(mockInput);
      expect(lookupInitialSearchCacheByCustomerUuid).toHaveBeenCalledTimes(1);
      expect(lookupInitialSearchCacheByCustomerUuid).toHaveBeenLastCalledWith(
        mockCustomerUuid,
        expect.any(EnvironmentService),
        mockEntityUuid,
      );
      expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
      expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
        mockEntityUuid,
        expect.any(EnvironmentService),
        mockEntityUuid,
      );
      expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(1);
      expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
      expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(1);
      const { entityUuid, found, acn, registeredAddress, businessAddress, companyFile, error } = result;
      expect(entityUuid).toBe(mockEntityUuid);
      expect(found).toBe(true);
      expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
      expect(registeredAddress).toBe(mockRegisteredAddress);
      expect(businessAddress).toBe(mockBusinessAddress);
      expect(companyFile).toBeDefined();
      expect(error).toBeUndefined();
    });
  });

  it('returns record as found even the registered and business address are empty', async () => {
    const mockAcn = 'acn';

    const mockInput = {
      entityUuid: mockEntityUuid,
      acn: mockAcn,
    } as EntityFullSearchRequestedEventDto;

    const mockFullCompanyProfileSearch = {
      aggregateId: mockEntityUuid,
      acn: mockAcn,
      found: true,
    } as FullCompanyProfileSearchResult;

    (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBInitialCompanySearchRecord as never,
    );
    (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
    (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(mockFullCompanyProfileSearch as never);
    (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

    expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
    const result = await fullCompanyProfileSearch(mockInput);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
      mockEntityUuid,
      expect.any(EnvironmentService),
      mockEntityUuid,
    );
    expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(1);
    expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(1);
    const { entityUuid, found, acn, registeredAddress, businessAddress, error } = result;
    expect(entityUuid).toBe(mockEntityUuid);
    expect(found).toBe(true);
    expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
    expect(registeredAddress).toBeUndefined();
    expect(businessAddress).toBeUndefined();
    expect(error).toBeUndefined();
  });

  it('returns record based on the full company profile search cache', async () => {
    const mockAcn = 'acn';

    const mockInput = {
      entityUuid: mockEntityUuid,
      acn: mockAcn,
    } as EntityFullSearchRequestedEventDto;

    (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBInitialCompanySearchRecord as never,
    );
    (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBFullCompanyProfileSearchRecord as never,
    );
    (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(null as never);
    (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

    expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
    const result = await fullCompanyProfileSearch(mockInput);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
      mockEntityUuid,
      expect.any(EnvironmentService),
      mockEntityUuid,
    );
    expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(0);
    expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(0);
    const { entityUuid, found, acn, registeredAddress, businessAddress, members, error } = result;
    expect(entityUuid).toBe(mockEntityUuid);
    expect(found).toBe(true);
    expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
    expect(registeredAddress).toBe(mockRegisteredAddress);
    expect(businessAddress).toBe(mockBusinessAddress);
    expect(members).toEqual(expect.arrayContaining(mockMembers));
    expect(error).toBeUndefined();
  });

  it('returns record based on the full company profile search cache - empty attribute', async () => {
    const mockAcn = 'acn';

    const mockInput = {
      entityUuid: mockEntityUuid,
      acn: mockAcn,
    } as EntityFullSearchRequestedEventDto;

    const mockDynamoDBFullCompanyProfileSearchEmptyRecord = {
      entityUuid: mockEntityUuid,
      acn: 'acn',
    } as CompanyFullProfileSearchDbRecord;

    (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBInitialCompanySearchRecord as never,
    );
    (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBFullCompanyProfileSearchEmptyRecord as never,
    );
    (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(null as never);
    (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

    expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
    const result = await fullCompanyProfileSearch(mockInput);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
      mockEntityUuid,
      expect.any(EnvironmentService),
      mockEntityUuid,
    );
    expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(0);
    expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(0);
    const { entityUuid, found, acn, registeredAddress, businessAddress, members, companyFile, error } = result;
    expect(entityUuid).toBe(mockEntityUuid);
    expect(found).toBe(true);
    expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
    expect(registeredAddress).toBeUndefined();
    expect(businessAddress).toBeUndefined();
    expect(members).toBeUndefined();
    expect(companyFile).toBeUndefined();
    expect(error).toBeUndefined();
  });

  it('return not found if no record in Equifax API', async () => {
    const mockAcn = 'acn';

    const mockInput = {
      entityUuid: mockEntityUuid,
    } as EntityFullSearchRequestedEventDto;

    const mockFullCompanyProfileSearch = {
      aggregateId: mockEntityUuid,
      acn: mockAcn,
      found: false,
    } as FullCompanyProfileSearchResult;

    (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBInitialCompanySearchRecord as never,
    );
    (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
    (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(mockFullCompanyProfileSearch as never);
    (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

    expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
    const result = await fullCompanyProfileSearch(mockInput);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
      mockEntityUuid,
      expect.any(EnvironmentService),
      mockEntityUuid,
    );
    expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(1);
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();

    const { entityUuid, found, acn, registeredAddress, businessAddress, members, companyFile, error } = result;
    expect(entityUuid).toBe(mockEntityUuid);
    expect(found).toBe(false);
    expect(acn).toBe(mockAcn);
    expect(registeredAddress).toBeUndefined();
    expect(businessAddress).toBeUndefined();
    expect(members).toBeUndefined();
    expect(companyFile).toBeUndefined();
    expect(error).toBeUndefined();
  });

  it('return not found with error message if the initial cache expired and the dto without acn', async () => {
    const mockInput = {
      entityUuid: mockEntityUuid,
    } as EntityFullSearchRequestedEventDto;

    (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
    (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
    (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(null as never);
    (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

    expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
    const result = await fullCompanyProfileSearch(mockInput);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
      mockEntityUuid,
      expect.any(EnvironmentService),
      mockEntityUuid,
    );
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();

    const { entityUuid, found, acn, registeredAddress, businessAddress, members, companyFile, error } = result;
    expect(entityUuid).toBe(mockEntityUuid);
    expect(found).toBe(false);
    expect(acn).toBeUndefined();
    expect(registeredAddress).toBeUndefined();
    expect(businessAddress).toBeUndefined();
    expect(members).toBeUndefined();
    expect(companyFile).toBeUndefined();
    expect(error).toEqual('Missing ACN');
  });

  it('return the correct record if the initial search cache acn is not matched with full search cache acn', async () => {
    const mockAcn = 'acn';

    const mockInput = {
      entityUuid: mockEntityUuid,
    } as EntityFullSearchRequestedEventDto;

    const mockDynamoDBFullCompanyProfileSearchRecordWithDifferentAcn = {
      entityUuid: mockEntityUuid,
      acn: 'different_acn',
      registeredAddress: {
        street1: 'street1',
        suburb: 'Melbourne',
        state: 'VIC',
        postcode: '3000',
      } as EntityAddress,
      businessAddress: {
        street1: 'street1',
        suburb: 'Sydney',
        state: 'NSW',
        postcode: '2000',
      } as EntityAddress,
    } as CompanyFullProfileSearchDbRecord;

    const mockFullCompanyProfileSearch = {
      aggregateId: mockEntityUuid,
      acn: mockAcn,
      found: true,
      registeredAddress: mockRegisteredAddress,
      businessAddress: mockBusinessAddress,
      members: mockMembers,
      companyFile: {},
    } as FullCompanyProfileSearchResult;

    (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBInitialCompanySearchRecord as never,
    );
    (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(
      mockDynamoDBFullCompanyProfileSearchRecordWithDifferentAcn as never,
    );
    (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(mockFullCompanyProfileSearch as never);
    (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

    expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
    const result = await fullCompanyProfileSearch(mockInput);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
      mockEntityUuid,
      expect.any(EnvironmentService),
      mockEntityUuid,
    );
    expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(1);
    expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(1);

    const { entityUuid, found, acn, registeredAddress, businessAddress, members, companyFile, error } = result;
    expect(entityUuid).toBe(mockEntityUuid);
    expect(found).toBe(true);
    expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
    expect(registeredAddress).toBe(mockRegisteredAddress);
    expect(businessAddress).toBe(mockBusinessAddress);
    expect(members).toEqual(expect.arrayContaining(mockMembers));
    expect(companyFile).toBeDefined();
    expect(error).toBeUndefined();
  });

  it('return record if the initial cache expired, but the dto with acn', async () => {
    const mockAcn = 'acn';

    const mockInput = {
      entityUuid: mockEntityUuid,
      acn: mockAcn,
    } as EntityFullSearchRequestedEventDto;

    const mockFullCompanyProfileSearch = {
      aggregateId: mockEntityUuid,
      acn: mockAcn,
      found: true,
      registeredAddress: mockRegisteredAddress,
      businessAddress: mockBusinessAddress,
      members: mockMembers,
      companyFile: {},
    } as FullCompanyProfileSearchResult;

    (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
    (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
    (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue(mockFullCompanyProfileSearch as never);
    (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

    expect(lookupInitialSearchCacheByEntityUuid).not.toHaveBeenCalled();
    expect(equifaxFullCompanyProfileSearch).not.toHaveBeenCalled();
    expect(lookupFullCompanyProfileCacheByEntityUuid).not.toHaveBeenCalled();
    expect(createFullCompanyProfileCache).not.toHaveBeenCalled();
    const result = await fullCompanyProfileSearch(mockInput);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(lookupInitialSearchCacheByEntityUuid).toHaveBeenLastCalledWith(
      mockEntityUuid,
      expect.any(EnvironmentService),
      mockEntityUuid,
    );
    expect(lookupFullCompanyProfileCacheByEntityUuid).toHaveBeenCalledTimes(1);
    expect(equifaxFullCompanyProfileSearch).toHaveBeenCalledTimes(1);
    expect(createFullCompanyProfileCache).toHaveBeenCalledTimes(1);

    const { entityUuid, found, acn, registeredAddress, businessAddress, members, companyFile, error } = result;
    expect(entityUuid).toBe(mockEntityUuid);
    expect(found).toBe(true);
    expect(acn).toBe(mockDynamoDBInitialCompanySearchRecord.acn);
    expect(registeredAddress).toBe(mockRegisteredAddress);
    expect(businessAddress).toBe(mockBusinessAddress);
    expect(members).toEqual(expect.arrayContaining(mockMembers));
    expect(companyFile).toBeDefined();
    expect(error).toBeUndefined();
  });
});

describe('System test throwing', () => {
  let fullCompanyProfileSearch: any;
  let initialCompanySearch: any;

  beforeAll(async () => {
    const companySearchService = await import('./companySearchService.js');
    fullCompanyProfileSearch = companySearchService.fullCompanyProfileSearch;
    initialCompanySearch = companySearchService.initialCompanySearch;
  });

  beforeEach(jest.clearAllMocks);

  describe('Initial Search', () => {
    it('throw if it is in the testing environment', async () => {
      const mockInput = {
        entityUuid: 'uuid',
        businessIdentifier: '1',
      };

      process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE = 'test';
      await expect(initialCompanySearch(mockInput)).rejects.toThrow('Testing error throw');
    });

    it('not throw if it is not in the testing environment', async () => {
      const mockInput = {
        entityUuid: 'uuid',
        businessIdentifier: '1',
      };

      process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE = 'production';
      const result = await initialCompanySearch(mockInput);
      expect(result).toBeDefined();
      expect(result?.entityUuid).toBe(mockInput.entityUuid);
      expect(result?.businessIdentifier).toBe(mockInput.businessIdentifier);
      expect(result?.name).toBeUndefined();
      expect(result?.acn).toBeUndefined();
      expect(result?.abn).toBeUndefined();
      expect(result?.type).toBeUndefined();
      expect(result?.found).toBe(false);
      expect(result?.error).toBe('Error: Invalid Business Identifier');
    });
  });

  describe('Full Company Profile Search', () => {
    it('throw if it is in the testing environment', async () => {
      const mockInput = {
        entityUuid: 'uuid',
        acn: '1',
      };

      process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE = 'test';
      await expect(fullCompanyProfileSearch(mockInput)).rejects.toThrow('Testing error throw');
    });

    it('not throw if it is not in the testing environment', async () => {
      const mockInput = {
        entityUuid: 'uuid',
        acn: '1',
      };

      (lookupInitialSearchCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
      (lookupFullCompanyProfileCacheByEntityUuid as jest.Mock).mockResolvedValue(null as never);
      (equifaxFullCompanyProfileSearch as jest.Mock).mockResolvedValue({
        aggregateId: 'uuid',
        acn: '1',
        found: false,
      } as never);
      (createFullCompanyProfileCache as jest.Mock).mockResolvedValue(null as never);

      process.env.EQUIFAX_INITIAL_COMPANY_SEARCH_MODE = 'production';
      const result = await fullCompanyProfileSearch(mockInput);
      expect(result).toBeDefined();
      expect(result?.entityUuid).toBe(mockInput.entityUuid);
      expect(result?.acn).toBe(mockInput.acn);
      expect(result?.found).toBe(false);
      expect(result?.error).toBeUndefined();
    });
  });
});
