import { debug, info } from '@npco/component-bff-core/dist/utils/logger.js';
import { EntityType } from '@npco/component-dto-core';
import type {
  EntityFullSearchCompletedEventDto,
  EntityFullSearchRequestedEventDto,
  EntityInitialSearchCompletedEventDto,
  EntityInitialSearchRequestedEventDto,
} from '@npco/component-dto-entity';

import { EnvironmentService } from '../../../config/envService.js';
import {
  fullCompanyProfileSearch as equifaxFullCompanyProfileSearch,
  initialCompanySearch as equifaxInitialCompanySearch,
} from '../../../equifax/index.js';
import { isValidAbn, isValidAcn } from '../../../lib/index.js';
import type { FullCompanyProfileSearchResult, InitialCompanySearchResult } from '../../../types.js';

import {
  createFullCompanyProfileCache,
  lookupFullCompanyProfileCacheByEntityUuid,
} from './db/companyFullProfileSearchDb.js';
import {
  createInitialSearchCache,
  lookupInitialSearchCacheByBusinessIdentifier,
  lookupInitialSearchCacheByCustomerUuid,
  lookupInitialSearchCacheByEntityUuid,
} from './db/companyInitialSearchDb.js';
import type { CompanyFullProfileSearchDbRecord } from './types.js';

const testErrorMessage = 'Testing error throw';

const convertDynamoDBFullCompanyProfileRecordToEntityDto = (
  record: CompanyFullProfileSearchDbRecord,
): EntityFullSearchCompletedEventDto =>
  ({
    entityUuid: record.entityUuid,
    acn: record.acn,
    found: true,
    ...(record.registeredAddress ? { registeredAddress: record.registeredAddress } : {}),
    ...(record.businessAddress ? { businessAddress: record.businessAddress } : {}),
    ...(record.members && record.members.length > 0 ? { members: record.members } : {}),
  } as EntityFullSearchCompletedEventDto);

const createEntityInitialSearchCompletedEventDto = (
  input: EntityInitialSearchRequestedEventDto,
  record?: InitialCompanySearchResult | null,
  err?: Error,
): EntityInitialSearchCompletedEventDto => {
  const { entityUuid, customerUuid, businessIdentifier } = input;
  const result: EntityInitialSearchCompletedEventDto = {
    entityUuid,
    customerUuid,
    businessIdentifier,
    country: 'AUS',
    found: false,
  };

  if (err) {
    result.error = err.toString();
    return result;
  }

  if (record?.name) {
    if (record.type === EntityType.OTHER) {
      info(`${record.type} is not supported`, customerUuid);
      result.error = 'Entity Type is not supported';
      return result;
    }

    if (!record.active) {
      info(`${businessIdentifier} is not in REGISTER status`, customerUuid);
      result.error = 'NOT_ACTIVE: Business Identifier is DE-REGISTER or UNDER EXTERNAL ADMINISTRATION';
      return result;
    }
    result.type = record.type;
    result.found = true;
    result.name = record.name;
    result.businessDetails = {
      ausBusiness: {
        abn: record.abn,
        acn: record.acn,
      },
    };
    result.acn = record.acn; // NoSonar - backward compatibility
    result.abn = record.abn; // NoSonar - backward compatibility
  }
  return result;
};

const lookupInitialSearchCacheByGsi = async (
  entityUuid: string,
  envService: EnvironmentService,
  customerUuid?: string,
) => {
  try {
    if (customerUuid) {
      const record = await lookupInitialSearchCacheByCustomerUuid(customerUuid, envService, entityUuid);
      if (record) {
        return record;
      }
    }
  } catch (error: any) {
    info(`Error looking up Initial Search Cache for CustomerUuid: ${customerUuid} - ${error.message}`, entityUuid);
  }
  return lookupInitialSearchCacheByEntityUuid(entityUuid, envService, entityUuid);
};

const generateFullCompanySearchEntityDto = (
  record: FullCompanyProfileSearchResult,
): EntityFullSearchCompletedEventDto =>
  ({
    entityUuid: record.aggregateId,
    acn: record.acn,
    found: record.found,
    ...(record.registeredAddress ? { registeredAddress: record.registeredAddress } : {}),
    ...(record.businessAddress ? { businessAddress: record.businessAddress } : {}),
    ...(record.members && record.members.length > 0 ? { members: record.members } : {}),
    companyFile: record.companyFile,
  } as EntityFullSearchCompletedEventDto);

/**
 * Initial Company Search Result
 * @param {EntityInitialSearchRequestedEventDto} input - Initial Search Request Parameter
 * @returns {Promise<EntityInitialSearchCompletedEventDto>} - Return the Initial Company Search Complete DTO
 */
export const initialCompanySearch = async (
  input: EntityInitialSearchRequestedEventDto,
): Promise<EntityInitialSearchCompletedEventDto> => {
  const envService = new EnvironmentService();
  const { entityUuid, customerUuid, businessIdentifier } = input;
  if (envService.equifaxInitialCompanySearchMode === 'test' && businessIdentifier === '1') {
    throw new Error(testErrorMessage);
  }
  info(`Initial Company Search with ${businessIdentifier}`);
  if (!isValidAbn(businessIdentifier) && !isValidAcn(businessIdentifier)) {
    debug(`${businessIdentifier} is not a valid Business Identifier`);
    info(`Complete with Equifax Error: Initial Company Search with ${businessIdentifier}`);
    return createEntityInitialSearchCompletedEventDto(input, null, new Error('Invalid Business Identifier'));
  }
  debug(`${businessIdentifier} is a valid Business Identifier`);
  const cache = await lookupInitialSearchCacheByBusinessIdentifier(businessIdentifier, envService);
  let companyInformation: InitialCompanySearchResult;
  if (cache) {
    debug(`Using Cache - ${businessIdentifier}`);
    companyInformation = {
      businessIdentifier,
      aggregateId: customerUuid,
      name: cache.name,
      abn: cache.abn,
      acn: cache.acn,
      type: cache.type,
      active: cache.active,
      abrEntityType: cache.abrEntityType,
      asicType: cache.asicType,
      requestXml: cache.requestXml,
      responseXml: cache.responseXml,
    } as InitialCompanySearchResult;
  } else {
    try {
      debug(`Using Equifax API - ${businessIdentifier}`);
      companyInformation = await equifaxInitialCompanySearch(businessIdentifier, envService, customerUuid);
    } catch (e: any) {
      debug('Generate return data with Equifax Error');
      info(`Complete with Equifax Error: Initial Company Search with ${businessIdentifier}`);
      return createEntityInitialSearchCompletedEventDto(input, null, e);
    }
  }

  let record: EntityInitialSearchCompletedEventDto;
  if (companyInformation.name) {
    debug('Generate return data with the Company Information', customerUuid);
    record = createEntityInitialSearchCompletedEventDto(input, companyInformation);
    debug('Record is found, generating cache', customerUuid);
    await createInitialSearchCache(companyInformation, envService, customerUuid, entityUuid);
  } else {
    debug('Generate return data without the Company Information', customerUuid);
    record = createEntityInitialSearchCompletedEventDto(input);
  }

  info(`Complete: Initial Company Search with ${businessIdentifier}`, customerUuid);
  return record;
};

export const fullCompanyProfileSearch = async (
  input: EntityFullSearchRequestedEventDto,
): Promise<EntityFullSearchCompletedEventDto> => {
  const envService = new EnvironmentService();
  const { entityUuid, customerUuid } = input;
  info(`Full Company Profile Search - EntityID ${entityUuid} with customerUuid ${customerUuid}`, entityUuid);
  const initialCompanySearchCache = await lookupInitialSearchCacheByGsi(entityUuid, envService, customerUuid);

  const acn = initialCompanySearchCache?.acn ?? input.acn;
  if (envService.equifaxInitialCompanySearchMode === 'test' && acn === '1') {
    throw new Error(testErrorMessage);
  }
  debug(`ACN - ${acn}`, entityUuid);
  if (!acn) {
    info('Return not found due to missing ACN', entityUuid);
    return {
      entityUuid,
      found: false,
      error: 'Missing ACN',
    } as EntityFullSearchCompletedEventDto;
  }

  const cache = await lookupFullCompanyProfileCacheByEntityUuid(entityUuid, envService, entityUuid);
  if (cache && cache.acn === acn) {
    info('Return with the cache', entityUuid);
    return convertDynamoDBFullCompanyProfileRecordToEntityDto(cache);
  }

  const result = await equifaxFullCompanyProfileSearch(acn, entityUuid, envService);
  debug('Return from the API', entityUuid);
  debug(result, entityUuid);
  if (result.found) {
    await createFullCompanyProfileCache(result, envService, entityUuid);
  }
  info('Return the result based on the Equifax API', entityUuid);
  return generateFullCompanySearchEntityDto(result);
};
