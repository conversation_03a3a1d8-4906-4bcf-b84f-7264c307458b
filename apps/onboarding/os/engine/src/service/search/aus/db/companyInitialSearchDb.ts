import { debug, error, info } from '@npco/component-bff-core/dist/utils/logger.js';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import AWSXray from 'aws-xray-sdk';

import type { EnvironmentService } from '../../../../config/envService.js';
import type { InitialCompanySearchResult } from '../../../../types.js';
import type { CompanyInitialSearchDbRecord } from '../types.js';

/**
 * Lookup Initial Company Cache by Business Identifier
 * @param {string} businessIdentifier - Business Identifier
 * @param {EnvironmentService} envService - EnvironmentService contains the initial company service dynamodb table name
 * @param {string} [aggregateId] - AggregateID for Log only
 * @returns {Promise<CompanyInitialSearchDbRecord | null>} returns the company details if found
 */
const lookupInitialSearchCacheByBusinessIdentifier = async (
  businessIdentifier: string,
  envService: EnvironmentService,
  aggregateId?: string,
): Promise<CompanyInitialSearchDbRecord | null> => {
  info(`Initial Company DynamoDB Table lookup - ${businessIdentifier}`, aggregateId);
  const dynamodb = DynamoDBDocumentClient.from(
    new DynamoDBClient({
      region: envService.region,
    }),
  );

  if (envService.isInLambda) {
    AWSXray.captureAWSv3Client(dynamodb);
  }

  const params = {
    TableName: envService.initialCompanySearchTable,
    KeyConditionExpression: 'id = :id',
    ExpressionAttributeValues: {
      ':id': businessIdentifier.replace(/ /g, ''),
    },
    ScanIndexForward: false,
    Limit: 1,
  };

  try {
    const command = new QueryCommand(params);
    const result = await dynamodb.send(command);
    debug(result);
    if (result.Count && result.Count > 0 && result.Items) {
      info(`Initial Search DynamoDB Table - record found: ${businessIdentifier}`);
      return result.Items[0] as CompanyInitialSearchDbRecord;
    }
    info(`Initial Search DynamoDB Table - record not found: ${businessIdentifier}`);
    return null;
  } catch (dynamodbError: any) {
    error(dynamodbError.toString(), aggregateId);
    throw dynamodbError;
  }
};

/**
 * Create the Cache in the Dynamodb for the Company Initial search record
 * @param {InitialCompanySearchResult} companyRecord - Company Record
 * @param {EnvironmentService} envService - Environment Service
 * @param {string} [aggregateId] - AggregateID for Log only
 * @param {string} [entityUuid] - Entity UUID for the record
 */
const createInitialSearchCache = async (
  companyRecord: InitialCompanySearchResult,
  envService: EnvironmentService,
  aggregateId?: string,
  entityUuid?: string,
): Promise<void> => {
  info(`Creating record - ${companyRecord.businessIdentifier}`, aggregateId);
  const dynamodb = DynamoDBDocumentClient.from(
    new DynamoDBClient({
      region: envService.region,
    }),
    {
      marshallOptions: {
        removeUndefinedValues: true,
      },
    },
  );

  if (envService.isInLambda) {
    AWSXray.captureAWSv3Client(dynamodb);
  }

  const now = new Date();
  const expiredTime = new Date(now);
  expiredTime.setDate(now.getDate() + envService.initialCompanySearchCacheLifeTime);
  const cache = {
    id: companyRecord.businessIdentifier,
    entityUuid,
    customerUuid: companyRecord.aggregateId,
    expirationTime: Math.round(expiredTime.getTime() / 1e3),
    name: companyRecord.name,
    acn: companyRecord.acn,
    abn: companyRecord.abn,
    type: companyRecord.type,
    active: companyRecord.active,
    asicType: companyRecord.asicType,
    abrEntityType: companyRecord.abrEntityType,
    requestXml: companyRecord.requestXml,
    responseXml: companyRecord.responseXml,
  } as CompanyInitialSearchDbRecord;
  debug('Cache data:', aggregateId);
  debug(cache, aggregateId);
  const params = {
    TableName: envService.initialCompanySearchTable,
    Item: cache,
  };

  try {
    debug('Put the records into Initial Search DynamoDB Table with following parameters', aggregateId);
    debug(params, aggregateId);
    const command = new PutCommand(params);
    await dynamodb.send(command);
  } catch (dynamodbError: any) {
    error(dynamodbError.toString(), aggregateId);
    throw new Error(dynamodbError.toString());
  }
  info(`Initial Search DynamoDB Table - record created: ${companyRecord.businessIdentifier}`, aggregateId);
};

const lookupInitialSearchCacheByGsi = async (
  id: string,
  envService: EnvironmentService,
  gsiName: 'ENTITY' | 'CUSTOMER',
  aggregateId?: string,
): Promise<CompanyInitialSearchDbRecord | null> => {
  const dynamodb = DynamoDBDocumentClient.from(
    new DynamoDBClient({
      region: envService.region,
    }),
  );
  if (envService.isInLambda) {
    AWSXray.captureAWSv3Client(dynamodb);
  }

  let indexName = '';
  let condition = '';

  switch (gsiName) {
    case 'ENTITY':
      indexName = envService.initialCompanySearchTableGSI;
      condition = 'entityUuid = :id';
      info(`Initial Search DynamoDB Table lookup by EntityUuid - ${id}`, aggregateId);
      break;
    case 'CUSTOMER':
      indexName = envService.initialCompanySearchTableCustomerGSI;
      condition = 'customerUuid = :id';
      info(`Initial Search DynamoDB Table lookup by CustomerUuid - ${id}`, aggregateId);
      break;
    default:
      throw new Error(`Invalid GSI name: ${gsiName}`);
  }

  const params = {
    TableName: envService.initialCompanySearchTable,
    IndexName: indexName,
    KeyConditionExpression: condition,
    ExpressionAttributeValues: {
      ':id': id,
    },
    ScanIndexForward: false,
    Limit: 1,
  };

  try {
    const command = new QueryCommand(params);
    const result = await dynamodb.send(command);
    debug(`Initial Search DynamoDB Table - Get GSI with ${id}`);
    debug(result, aggregateId);
    if (result.Count && result.Count > 0 && result.Items) {
      info(`Initial Search DynamoDB Table - GSI record found: ${id}`, aggregateId);
      return result.Items[0] as CompanyInitialSearchDbRecord;
    }
    info(`Initial Search DynamoDB Table - GSI record not found: ${id}`, aggregateId);
    return null;
  } catch (dynamodbError: any) {
    error(dynamodbError.toString(), aggregateId);
    throw dynamodbError;
  }
};

const lookupInitialSearchCacheByEntityUuid = async (
  entityUuid: string,
  envService: EnvironmentService,
  aggregateId?: string,
): Promise<CompanyInitialSearchDbRecord | null> =>
  lookupInitialSearchCacheByGsi(entityUuid, envService, 'ENTITY', aggregateId);

const lookupInitialSearchCacheByCustomerUuid = async (
  customerUuid: string,
  envService: EnvironmentService,
  aggregateId?: string,
): Promise<CompanyInitialSearchDbRecord | null> =>
  lookupInitialSearchCacheByGsi(customerUuid, envService, 'CUSTOMER', aggregateId);

export {
  createInitialSearchCache,
  lookupInitialSearchCacheByBusinessIdentifier,
  lookupInitialSearchCacheByCustomerUuid,
  lookupInitialSearchCacheByEntityUuid,
};
