import { ConfigService } from '@npco/component-bff-core/dist/config/index.js';
import type { DomainURI } from '@npco/component-dto-core';
import { DomainURImap } from '@npco/component-dto-core';

export class EnvironmentService {
  amsEndpoint = '';

  amsEndpointVersion = '';

  equifaxInitialCompanySearchUrl = '';

  equifaxInitialCompanySearchMode = '';

  equifaxInitialCompanySearchUsername = '';

  equifaxInitialCompanySearchPassword = '';

  equifaxInitialCompanySearchSubscriberIdentifier = '';

  equifaxInitialCompanySearchSecurityCode = '';

  equifaxFullCompanyProfileSearchUsername = '';

  equifaxFullCompanyProfileSearchPassword = '';

  equifaxIdentityDocumentVerificationUsername = '';

  equifaxIdentityDocumentVerificationPassword = '';

  equifaxIdentitySafeHarbourUsername = '';

  equifaxIdentitySafeHarbourPassword = '';

  equifaxIdvSafeHarbourDvsConfig = '';

  safeharbourConfig = '';

  equifaxFullCompanyProfileSearchProductionEnvironment = false;

  equifaxDocumentVerificationSearchProductionEnvironment = false;

  initialCompanySearchTable = '';

  initialCompanySearchTableGSI = '';

  initialCompanySearchTableCustomerGSI = '';

  initialCompanySearchCacheLifeTime = 1;

  fullCompanyProfileSearchTable = '';

  customerScreeningSearchTable = '';

  entityScreeningSearchTable = '';

  screeningFuzzinessLevel = 0;

  screeningApiKey = '';

  identityDocumentVerificationTable = '';

  idvSafeHarbourTable = '';

  idvTable = '';

  equifaxIdvDvsConfig = '';

  safeHarbourSearchTable = '';

  osCommandHandler = '';

  isInLambda!: boolean;

  region = '';

  documentEncryptionKeyArn = '';

  amexApiUrl = '';

  amexClientId = '';

  amexClientSecret = '';

  amexKeyFilename = '';

  amexCertFilename = '';

  amexKeyPassword = '';

  onfidoApiToken = '';

  onfidoWebhookToken = '';

  selfieVerificationSearchTable = '';

  selfieVerificationSearchTableGSI = '';

  documentsVerificationSessionTable = '';

  lambdaHttpTimeout = 5000;

  lambdaConnectTimeout = 2000;

  maxRetries = 3;

  cqrsCommandHandler = '';

  cqrsCmds: DomainURI = DomainURImap;

  private readonly configService: ConfigService = new ConfigService();

  constructor() {
    this.setupAwsEnv();
    this.setupAms();
    this.setupEquifaxEnv();
    this.setupScreeningEnv();
    this.updateAWSConfig();
    this.setupAmexEnv();
    this.setupSelfVerificationCheckEnv();
  }

  setupAwsEnv(): void {
    this.initialCompanySearchTable = this.configService.get('DYNAMODB_INITIAL_COMPANY_SEARCH_TABLE', '');
    this.initialCompanySearchTableGSI = this.configService.get('INITIAL_COMPANY_SEARCH_TABLE_ENTITY_UUID_GSI', '');
    this.initialCompanySearchTableCustomerGSI = this.configService.get(
      'INITIAL_COMPANY_SEARCH_TABLE_CUSTOMER_UUID_GSI',
      '',
    );
    this.fullCompanyProfileSearchTable = this.configService.get('DYNAMODB_FULL_COMPANY_PROFILE_SEARCH_TABLE', '');
    this.customerScreeningSearchTable = this.configService.get('CUSTOMER_SCREENING_SEARCH_TABLE', '');
    this.entityScreeningSearchTable = this.configService.get('ENTITY_SCREENING_SEARCH_TABLE', '');
    this.identityDocumentVerificationTable = this.configService.get(
      'DYNAMODB_IDENTITY_DOCUMENT_VERIFICATION_SEARCH_TABLE',
      '',
    );
    this.idvSafeHarbourTable = this.configService.get('DYNAMODB_IDV_SAFE_HARBOUR_SEARCH_TABLE', '');
    this.idvTable = this.configService.get('DYNAMODB_IDV_SEARCH_TABLE', '');
    this.safeHarbourSearchTable = this.configService.get('SAFE_HARBOUR_SEARCH_TABLE', '');
    this.selfieVerificationSearchTable = this.configService.get('SELFIE_VERIFICATION_SEARCH_TABLE', '');
    this.selfieVerificationSearchTableGSI = this.configService.get(
      'SELFIE_VERIFICATION_SEARCH_TABLE_APPLICANT_ID_GSI',
      '',
    );
    this.initialCompanySearchCacheLifeTime = Number(
      this.configService.get('INITIAL_COMPANY_SEARCH_CACHE_LIFETIME', '1'),
    );
    this.isInLambda = !!this.configService.get('LAMBDA_TASK_ROOT', '');
    this.osCommandHandler = this.configService.get('OS_COMMAND_HANDLER', '');
    this.cqrsCommandHandler = this.configService.get('OS_COMMAND_HANDLER', '');
    this.documentsVerificationSessionTable = this.configService.get('DOCUMENTS_VERIFICATION_SESSION_TABLE_NAME', '');
  }

  setupEquifaxEnv(): void {
    this.equifaxInitialCompanySearchUrl = this.configService.get('EQUIFAX_INITIAL_COMPANY_SEARCH_URL', '');
    this.equifaxInitialCompanySearchMode = this.configService.get('EQUIFAX_INITIAL_COMPANY_SEARCH_MODE', '');
    this.equifaxInitialCompanySearchUsername = this.configService.get('EQUIFAX_INITIAL_COMPANY_SEARCH_USERNAME', '');
    this.equifaxInitialCompanySearchPassword = this.configService.get('EQUIFAX_INITIAL_COMPANY_SEARCH_PASSWORD', '');
    this.equifaxInitialCompanySearchSubscriberIdentifier = this.configService.get(
      'EQUIFAX_INITIAL_COMPANY_SEARCH_SUBSCRIBER_IDENTIFIER',
      '',
    );
    this.equifaxInitialCompanySearchSecurityCode = this.configService.get(
      'EQUIFAX_INITIAL_COMPANY_SEARCH_SECURITY_CODE',
      '',
    );
    this.equifaxFullCompanyProfileSearchUsername = this.configService.get(
      'EQUIFAX_FULL_COMPANY_PROFILE_SEARCH_USERNAME',
      '',
    );
    this.equifaxFullCompanyProfileSearchPassword = this.configService.get(
      'EQUIFAX_FULL_COMPANY_PROFILE_SEARCH_PASSWORD',
      '',
    );
    this.safeharbourConfig = this.configService.get('EQUIFAX_SAFE_HARBOUR_CONFIG', '');
    this.equifaxIdvSafeHarbourDvsConfig = this.configService.get('EQUIFAX_IDV_SAFE_HARBOUR_DVS_CONFIG', '');
    this.equifaxIdentityDocumentVerificationUsername = this.configService.get('EQUIFAX_IDV_USERNAME', '');
    this.equifaxIdentityDocumentVerificationPassword = this.configService.get('EQUIFAX_IDV_PASSWORD', '');
    this.equifaxIdentitySafeHarbourUsername = this.configService.get('EQUIFAX_IDV_SAFE_HARBOUR_USERNAME', '');
    this.equifaxIdentitySafeHarbourPassword = this.configService.get('EQUIFAX_IDV_SAFE_HARBOUR_PASSWORD', '');
    this.equifaxFullCompanyProfileSearchProductionEnvironment =
      this.configService
        .get('EQUIFAX_FULL_COMPANY_PROFILE_SEARCH_PRODUCTION_ENVIRONMENT', false)
        .toString()
        .toLowerCase() === 'true';
    this.equifaxDocumentVerificationSearchProductionEnvironment =
      this.configService
        .get('EQUIFAX_DOCUMENT_VERIFICATION_SEARCH_PRODUCTION_ENVIRONMENT', false)
        .toString()
        .toLowerCase() === 'true';
    this.equifaxIdvDvsConfig = this.configService.get('EQUIFAX_IDV_DVS_CONFIG', '');
  }

  setupScreeningEnv(): void {
    this.screeningFuzzinessLevel = this.configService.get('SCREENING_FUZZINESS_SEARCH_LEVEL', 0);
    this.screeningApiKey = this.configService.get('SCREENING_API_KEY', '');
  }

  updateAWSConfig(): void {
    this.documentEncryptionKeyArn = this.configService.get('DOCUMENT_ENCRYPTION_KEY_ARN', '');
    this.region = this.configService.get('AWS_REGION', 'ap-southeast-2');
  }

  setupAmexEnv(): void {
    this.amexApiUrl = this.configService.get('AMEX_API_URL', '');
    this.amexClientId = this.configService.get('AMEX_CLIENT_ID', '');
    this.amexClientSecret = this.configService.get('AMEX_CLIENT_SECRET', '');
    this.amexKeyFilename = this.configService.get('AMEX_KEY_FILENAME', '');
    this.amexCertFilename = `/${this.configService.get('AMEX_CERT_FILENAME', '')}`;
    this.amexKeyPassword = this.configService.get('AMEX_KEY_PASSWORD', '');
  }

  setupSelfVerificationCheckEnv(): void {
    this.onfidoApiToken = this.configService.get('ONFIDO_API_TOKEN', '');
    this.onfidoWebhookToken = this.configService.get('ONFIDO_WEBHOOK_TOKEN', '');
  }

  setupAms(): void {
    this.amsEndpoint = this.configService.get('AMS_API_ENDPOINT', '');
    this.amsEndpointVersion = this.configService.get('AMS_API_ENDPOINT_VERSION', 'v2');
  }
}
