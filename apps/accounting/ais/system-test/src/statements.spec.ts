import { testIf } from '@npco/bff-systemtest-utils';
import { isRegionAP } from '@npco/bff-systemtest-utils/dist/helper/baseTestHelper';
import { ConnectionStatus, XeroAccountLinkStatus, ConnectionErrorType } from '@npco/component-dto-connection';
import type {
  XeroAccount,
  ConnectionXeroBankfeedConfigCreatedEventDto,
  ConnectionXeroBankfeedConfigUpdatedEventDto,
} from '@npco/component-dto-connection';
import { DbRecordType } from '@npco/component-dto-core';
import { DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';
import type { XeroToken } from '@npco/component-events-core';

import { v4 as uuidv4 } from 'uuid';
import type { Statement } from 'xero-node/dist/gen/model/bankfeeds/statement';

import {
  createTestSavingsAccountTransactionCreateEventDto,
  createTestTransactionCreateEventDto,
} from './__testData__/createTestTransactionCreateEventDto';
import type { IDebitCardAccountTransactionDbModel, TableKeys } from './__testData__/iDebitCardAccountRansactionDbModel';
import { TransctionSendAttemptStatus } from './__testData__/iDebitCardAccountRansactionDbModel';
import { allAtOnceOutputKeyed } from './utils/bulkAsync/allAtOnceOutputKeyed';
import { doXTimesAllAtOnce } from './utils/bulkAsync/doXTimesAllAtOnce';
import { AisEnvService } from './utils/envService';
import { retry } from './utils/retry';
import { setupBankfeedsConnection } from './utils/setupBankfeedsConnection';
import { sleep } from './utils/sleep';
import { getGlobalProjectionSqsService, getSqsService } from './utils/sqs/sqsService';
import type { SqsService } from './utils/sqs/sqsService';
import { ApiTestHelper } from './utils/testHelper';
import { getCountryCodeByRegion, getCurrencyByRegion } from './utils/utils';

describe('Statement test suites', () => {
  const env = new AisEnvService();
  const context = new ApiTestHelper(env);
  let projectionSqs: SqsService;
  let statementRetrySqs: any;
  let connection: ConnectionXeroBankfeedConfigCreatedEventDto;
  let xeroClient: any;

  const createTransactionDbItem = async (): Promise<IDebitCardAccountTransactionDbModel> => {
    const txn = createTestTransactionCreateEventDto(
      uuidv4(),
      context.testData.debitCardAccountUuid,
      context.testData.entityUuid,
    );

    // set the xeroId so it does not get picked up by the dbstream handler
    (txn as any).xeroId = uuidv4();
    (txn as any).transactionType = txn.type;
    (txn as any).type = `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}${txn.timestamp}`;
    txn.timestamp = new Date(Number(txn.timestamp)).toISOString();
    await context.putItemToDb({ ...txn });
    (txn as any).xeroId = undefined;
    await sleep(1000);
    await context.putItemToDb({ ...txn });
    return txn as any as IDebitCardAccountTransactionDbModel;
  };

  const sendFailedStatementMessage = async (transactionUuid: string, error: any) => {
    const failedStatementMessage = {
      debitCardAccountUuid: context.testData.debitCardAccountUuid,
      entityUuid: context.testData.entityUuid,
      connectionUuid: context.testData.bankfeedConnectionUuid,
      transactionUuid,
      statements: [
        {
          id: uuidv4(),
          feedConnectionId: uuidv4(), // c7e524da-4149-451c-a41b-daeb14a5d8ce
          status: 'REJECTED',
          errors: [error],
        },
      ],
      attempts: 1,
    };
    await statementRetrySqs.sendFifoSqsMessage(JSON.stringify(failedStatementMessage));
    return failedStatementMessage.statements[0].id;
  };

  const sendErrorRecoveryEvent = async (connectionToSend: ConnectionXeroBankfeedConfigCreatedEventDto) => {
    if (!connectionToSend) {
      throw new Error('Connection not set');
    }
    const payload: ConnectionXeroBankfeedConfigUpdatedEventDto = {
      ...connectionToSend,
    };
    const sqsMessage = {
      'detail-type': 'ais.Connection.XeroBankfeedConfigRestored',
      detail: {
        aggregateId: connectionToSend.connectionUuid,
        version: 1,
        createdTimestamp: new Date().getTime(),
        source: 'ais',
        payload,
      },
    };
    await (await getGlobalProjectionSqsService()).sendSqsMessage(JSON.stringify(sqsMessage));
  };

  beforeAll(async () => {
    const entityUuid = context.getEntityUuid();
    const connectionUuid = await setupBankfeedsConnection(context, entityUuid, env, 1);
    context.setBankFeedConnectionUuid(connectionUuid);
    await sleep();
    connection = await context.getKnownItemFromDb<ConnectionXeroBankfeedConfigCreatedEventDto>(
      context.testData.bankfeedConnectionUuid,
      DbRecordType.CONNECTION_XERO_BANKFEED,
    );
    console.log('connection', connection);
    xeroClient = await context.getXeroBankFeedClientWithToken(connection.xeroToken.xeroTokenSet);
  });

  // eslint-disable-next-line sonarjs/cognitive-complexity
  describe('transaction to statements test suites', () => {
    beforeAll(async () => {
      projectionSqs = await getGlobalProjectionSqsService();
      statementRetrySqs = await getSqsService('StatementsFailedSendQueueURL');
    });

    it('should send statements from dbStream handler from create and update debit card account transaction events', async () => {
      const aggregateId = uuidv4();
      const dcaTxnDto = createTestTransactionCreateEventDto(
        aggregateId,
        context.testData.debitCardAccountUuid,
        context.testData.entityUuid,
      );
      await projectionSqs.sendTransactionProjection(dcaTxnDto);
      await retry(async () => {
        const txn = await context.getItemFromDb(aggregateId, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
        console.log('txn', txn?.id);
        expect(txn.id).toEqual(aggregateId);
        expect(txn.statementId).toBeDefined();
        expect(String(txn.amount.value)).toEqual(dcaTxnDto.amount.value);
        const { body: statement } = await xeroClient.bankFeedsApi.getStatement(
          connection.xeroOrganisationId,
          txn.statementId,
        );
        expect(txn.statementId).toEqual(statement.id);
        expect(statement.status).toEqual('DELIVERED');
        expect(statement.endBalance.amount).toEqual('1.0000');
        expect(statement.endBalance.creditDebitIndicator).toEqual('CREDIT');
      }, 15);
    });

    const statementTransactions = [
      {
        amountValue: '100',
        balanceValue: '200',
        transactionType: DebitCardTransactionTypeV2.NPP_OUT,
        expectedStartBalance: {
          amount: '3.0000',
          creditDebitIndicator: 'CREDIT',
        },
        expectedEndBalance: {
          amount: '2.0000',
          creditDebitIndicator: 'CREDIT',
        },
      },
      {
        amountValue: '100',
        balanceValue: '50',
        transactionType: DebitCardTransactionTypeV2.NPP_OUT,
        expectedStartBalance: {
          amount: '1.5000',
          creditDebitIndicator: 'CREDIT',
        },
        expectedEndBalance: {
          amount: '0.5000',
          creditDebitIndicator: 'CREDIT',
        },
      },
      {
        amountValue: '100',
        balanceValue: '200',
        transactionType: DebitCardTransactionTypeV2.NPP_IN,
        expectedStartBalance: {
          amount: '1.0000',
          creditDebitIndicator: 'CREDIT',
        },
        expectedEndBalance: {
          amount: '2.0000',
          creditDebitIndicator: 'CREDIT',
        },
      },
      {
        amountValue: '100',
        balanceValue: '50',
        transactionType: DebitCardTransactionTypeV2.NPP_IN,
        expectedStartBalance: {
          amount: '-0.5000',
          creditDebitIndicator: 'CREDIT',
        },
        expectedEndBalance: {
          amount: '0.5000',
          creditDebitIndicator: 'CREDIT',
        },
      },
      {
        amountValue: '100',
        balanceValue: undefined,
        transactionType: DebitCardTransactionTypeV2.NPP_OUT,
        expectedStartBalance: {
          amount: '0.0000',
          creditDebitIndicator: 'DEBIT',
        },
        expectedEndBalance: {
          amount: '1.0000',
          creditDebitIndicator: 'DEBIT',
        },
      },
      {
        amountValue: '100',
        balanceValue: undefined,
        transactionType: DebitCardTransactionTypeV2.NPP_IN,
        expectedStartBalance: {
          amount: '0.0000',
          creditDebitIndicator: 'CREDIT',
        },
        expectedEndBalance: {
          amount: '1.0000',
          creditDebitIndicator: 'CREDIT',
        },
      },
    ];
    it.each(statementTransactions)(
      'should send statements to xero correctly %p',
      async ({ amountValue, balanceValue, transactionType, expectedStartBalance, expectedEndBalance }) => {
        const aggregateId = uuidv4();
        const dcaTxnDto = createTestTransactionCreateEventDto(
          aggregateId,
          context.testData.debitCardAccountUuid,
          context.testData.entityUuid,
          {
            amount: {
              value: amountValue,
              currency: getCurrencyByRegion(),
            },
            type: transactionType,
            ...(balanceValue
              ? {
                  balance: { currency: getCurrencyByRegion(), value: balanceValue },
                }
              : {}),
          },
        );
        await projectionSqs.sendTransactionProjection(dcaTxnDto);
        await retry(async () => {
          const txn = await context.getItemFromDb(aggregateId, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
          console.log('txn', txn?.id);
          expect(txn.id).toEqual(aggregateId);
          expect(txn.statementId).toBeDefined();
          expect(String(txn.amount.value)).toEqual(dcaTxnDto.amount.value);
          const { body: statement } = await xeroClient.bankFeedsApi.getStatement(
            connection.xeroOrganisationId,
            txn.statementId,
          );
          expect(txn.statementId).toEqual(statement.id);
          expect(statement.status).toEqual('DELIVERED');
          expect(statement.startBalance.amount).toEqual(expectedStartBalance.amount);
          expect(statement.startBalance.creditDebitIndicator).toEqual(expectedStartBalance.creditDebitIndicator);
          expect(statement.endBalance.amount).toEqual(expectedEndBalance.amount);
          expect(statement.endBalance.creditDebitIndicator).toEqual(expectedEndBalance.creditDebitIndicator);
        }, 15);
      },
    );

    xit('should send statements from dbStream handler from create and update savings account transaction events', async () => {
      const aggregateId = uuidv4();
      const savingsAccountTxnDto = createTestSavingsAccountTransactionCreateEventDto(
        aggregateId,
        context.testData.debitCardAccountUuid,
        context.testData.entityUuid,
      );
      await projectionSqs.sendSavingsAccountTransactionProjection(savingsAccountTxnDto);
      await retry(async () => {
        const txn = await context.getItemFromDb(aggregateId, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
        console.log('txn', txn?.id);
        expect(txn.id).toEqual(aggregateId);
        expect(txn.statementId).toBeDefined();
        expect(String(txn.amount.value)).toEqual(savingsAccountTxnDto.amount.value);
        const { body: statement } = await xeroClient.bankFeedsApi.getStatement(
          connection.xeroOrganisationId,
          txn.statementId,
        );
        expect(txn.statementId).toEqual(statement.id);
        expect(statement.status).toEqual('DELIVERED');
        expect(statement.endBalance.amount).toEqual('1.0000');
        expect(statement.endBalance.creditDebitIndicator).toEqual('CREDIT');
      }, 15);
    });

    it('should send statement from dbStream handler for update event', async () => {
      const aggregateId = uuidv4();
      const dcaTxnDto = createTestTransactionCreateEventDto(
        aggregateId,
        context.testData.debitCardAccountUuid,
        context.testData.entityUuid,
      );

      const dcaTxnUpdateDto = createTestTransactionCreateEventDto(
        aggregateId,
        context.testData.debitCardAccountUuid,
        context.testData.entityUuid,
      );
      dcaTxnUpdateDto.amount.value = '1234';
      dcaTxnUpdateDto.timestamp = dcaTxnDto.timestamp;
      await projectionSqs.sendTransactionProjection(
        dcaTxnUpdateDto,
        'ais.ProjectionDebitCardAccountTransaction.update',
      );
      await retry(async () => {
        const txn = await context.getItemFromDb(
          aggregateId,
          `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}${dcaTxnDto.timestamp}`,
        );
        console.log('txn', txn?.id);
        expect(txn.id).toEqual(aggregateId);
        expect(txn.statementId).toBeDefined();
        expect(String(txn.amount.value)).toEqual(dcaTxnUpdateDto.amount.value);
        const { body: statement } = await xeroClient.bankFeedsApi.getStatement(
          connection.xeroOrganisationId,
          txn.statementId,
        );
        expect(txn.statementId).toEqual(statement.id);
        expect(statement.status).toEqual('DELIVERED');
        expect(statement.endBalance.amount).toEqual('12.3400');
        expect(statement.endBalance.creditDebitIndicator).toEqual('CREDIT');
      }, 15);
    });

    it('will mark as duplicate on second send', async () => {
      const aggregateId = uuidv4();
      const dcaTxnDto = createTestTransactionCreateEventDto(
        aggregateId,
        context.testData.debitCardAccountUuid,
        context.testData.entityUuid,
      );

      const dcaTxnUpdateDto = createTestTransactionCreateEventDto(
        aggregateId,
        context.testData.debitCardAccountUuid,
        context.testData.entityUuid,
      );
      dcaTxnUpdateDto.amount.value = '1235';
      dcaTxnUpdateDto.timestamp = dcaTxnDto.timestamp;
      await projectionSqs.sendTransactionProjection(
        dcaTxnUpdateDto,
        'ais.ProjectionDebitCardAccountTransaction.update',
      );
      await retry(async () => {
        const initialTransactionStateAfterFirstSend = await context.getItemFromDb(
          aggregateId,
          `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}${dcaTxnDto.timestamp}`,
        );
        console.log('txn', initialTransactionStateAfterFirstSend?.id);
        expect(initialTransactionStateAfterFirstSend.id).toEqual(aggregateId);
        expect(initialTransactionStateAfterFirstSend.statementId).toBeDefined();
        expect(String(initialTransactionStateAfterFirstSend.amount.value)).toEqual(dcaTxnUpdateDto.amount.value);
        const { body: statement } = await xeroClient.bankFeedsApi.getStatement(
          connection.xeroOrganisationId,
          initialTransactionStateAfterFirstSend.statementId,
        );
        expect(initialTransactionStateAfterFirstSend.statementId).toEqual(statement.id);
        expect(statement.status).toEqual('DELIVERED');
        expect(statement.endBalance.amount).toEqual('12.3500');
        expect(statement.endBalance.creditDebitIndicator).toEqual('CREDIT');
      }, 15);

      const transactionToClearSentStatus = await context.getItemFromDb(
        aggregateId,
        `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}${dcaTxnDto.timestamp}`,
      );
      transactionToClearSentStatus.xeroId = undefined;
      transactionToClearSentStatus.statementId = undefined;
      transactionToClearSentStatus.statementStatus = undefined;
      transactionToClearSentStatus.sentToXeroBankAccountId = undefined;
      transactionToClearSentStatus.sentToXeroOrganisationId = undefined;
      transactionToClearSentStatus.sentToXeroFeedConnectionId = undefined;
      await context.putItemToDb(transactionToClearSentStatus);

      await sendFailedStatementMessage(transactionToClearSentStatus.id, {
        status: 500,
        type: 'internal-error',
        title: 'Intermittent Internal Xero Error',
        detail: 'The request should be retried. If the error persists, a Xero support issue should be raised.',
      });

      await retry(async () => {
        const finalTransactionState = await context.getItemFromDb(
          aggregateId,
          `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}${dcaTxnDto.timestamp}`,
        );
        console.log('txn', finalTransactionState?.id);
        expect(finalTransactionState.statementStatus).toEqual('DUPLICATE');
      }, 15);
    });

    it('should be able to retry failed statement and send to xero', async () => {
      const error = {
        status: 500,
        type: 'internal-error',
        title: 'Intermittent Internal Xero Error',
        detail: 'The request should be retried. If the error persists, a Xero support issue should be raised.',
      };
      const item = await createTransactionDbItem();

      await sendFailedStatementMessage(item.id, error);
      await retry(async () => {
        const txn = await context.getItemFromDb(item.id, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
        expect(txn.id).toEqual(item.id);
        expect(txn.statementId).toBeDefined();
        const result: {
          body: Statement;
        } = await xeroClient.bankFeedsApi.getStatement(connection.xeroOrganisationId, txn.statementId);
        expect(txn.statementId).toEqual(result.body.id);
        expect(result.body.status).toEqual('DELIVERED');
      }, 30);
    });

    it('will pickup missed transactions and send them after connection restore', async () => {
      const defaultSendConfig: XeroAccount = connection.accounts.find(
        (x) => x.zellerDebitCardAccountUuid === context.testData.debitCardAccountUuid,
      ) as XeroAccount;
      if (!defaultSendConfig) {
        throw new Error('missing configuration for test entity');
      }

      const numberToAdd = 31;

      const testItems = await doXTimesAllAtOnce(numberToAdd, async () => {
        const item = await createTransactionDbItem();
        item.statementStatus = TransctionSendAttemptStatus.MISSED_SEND_DUE_TO_CONNECTION_ERROR;
        item.missedSendToXeroOrganisationId = connection.xeroOrganisationId;
        item.missedSendToXeroBankAccountId = defaultSendConfig.xeroId;
        await context.putItemToDb({ ...item });
        item.xeroId = undefined;
        item.statementId = undefined;
        await context.putItemToDb({ ...item });
        return item;
      });

      await sendErrorRecoveryEvent(connection);

      await retry(
        async () => {
          const itemKeys = testItems.map((x) => [x.id] as [string]);
          const itemFinalResultPromises: Record<string, IDebitCardAccountTransactionDbModel> =
            await allAtOnceOutputKeyed(
              itemKeys,
              (id: string) => context.getKnownItemFromDb<IDebitCardAccountTransactionDbModel>(id, 'dca.transaction.'),
              (result) => result.id,
            );
          const itemFinalResults = Object.values(itemFinalResultPromises);

          const amountSent = itemFinalResults.filter(
            (x) => x.statementStatus === TransctionSendAttemptStatus.SENT,
          ).length;

          const statmentStatusCounts = itemFinalResults.reduce((acc, x) => {
            const name = x.statementStatus as string;
            if (!acc[name]) {
              acc[name] = 0;
            }
            // eslint-disable-next-line no-plusplus
            acc[name]++;
            return acc;
          }, {} as any);

          console.log('statmentStatusCounts', statmentStatusCounts);
          console.log(`from ${numberToAdd} Transactions set to retry, ${amountSent} have been marked as SENT`);

          for (const itemFromInitialTestData of testItems) {
            const matchingItemFromDb = itemFinalResultPromises[itemFromInitialTestData.id];

            if (!matchingItemFromDb) {
              throw new Error('data query error');
            }
            console.log(matchingItemFromDb.id, matchingItemFromDb.statementStatus);
            expect(matchingItemFromDb.statementStatus).toEqual(TransctionSendAttemptStatus.SENT);
          }
        },
        30,
        5000,
      );
    }, 380000);
  });

  describe('savings account transaction to statements test suites', () => {
    beforeAll(async () => {
      projectionSqs = await getGlobalProjectionSqsService();
      statementRetrySqs = await getSqsService('StatementsFailedSendQueueURL');
    });

    testIf(
      isRegionAP,
      'should send statements from dbStream handler from create and update savings account transaction events',
      async () => {
        const aggregateId = uuidv4();
        const savingsAccountTxnDto = createTestSavingsAccountTransactionCreateEventDto(
          aggregateId,
          context.testData.debitCardAccountUuid,
          context.testData.entityUuid,
        );
        await projectionSqs.sendSavingsAccountTransactionProjection(savingsAccountTxnDto);
        await retry(async () => {
          const txn = await context.getItemFromDb(aggregateId, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
          console.log('txn', txn?.id);
          expect(txn.id).toEqual(aggregateId);
          expect(txn.statementId).toBeDefined();
          expect(String(txn.amount.value)).toEqual(savingsAccountTxnDto.amount.value);
          const { body: statement } = await xeroClient.bankFeedsApi.getStatement(
            connection.xeroOrganisationId,
            txn.statementId,
          );
          expect(txn.statementId).toEqual(statement.id);
          expect(statement.status).toEqual('DELIVERED');
          expect(statement.endBalance.amount).toEqual('1.0000');
          expect(statement.endBalance.creditDebitIndicator).toEqual('CREDIT');
        }, 15);
      },
    );
  });
});

describe('mark as missed send', () => {
  let projectionSqs: SqsService;
  let context: ApiTestHelper;
  beforeAll(async () => {
    projectionSqs = await getGlobalProjectionSqsService();
    context = new ApiTestHelper(new AisEnvService());
  });

  async function createAndPersistConnectionWithError(entityUuid: string, linkedDebitCardAccounts: string[]) {
    const lastChangeTime = new Date(Date.now() - 1000);
    const xeroToken: XeroToken = {
      lastTokenRefreshStartTimestamp: 0,
      lastTokenRefreshTimestamp: 0,
      xeroTokenSet: { access_token: '', expires_at: 0, refresh_token: '', scope: '', token_type: 'Bearer' },
    };
    const accountConfigs = linkedDebitCardAccounts.map((x): XeroAccount => {
      return {
        accountName: uuidv4(),
        countryCodeISO3166: getCountryCodeByRegion(),
        linkStatus: XeroAccountLinkStatus.LINKED,
        sharedWithXeroTimestamp: lastChangeTime.getTime(),
        xeroAccountType: 'BANK',
        xeroFeedConnectionId: uuidv4(),
        xeroId: uuidv4(),
        zellerDebitCardAccountUuid: x,
        zellerGeneratedXeroAccountToken: uuidv4(),
      };
    });
    const id = uuidv4();
    const connection: ConnectionXeroBankfeedConfigCreatedEventDto & TableKeys = {
      accounts: accountConfigs,
      connectionTimestamp: lastChangeTime.getTime(),
      connectionUuid: id,
      customerUuid: uuidv4(),
      entityUuid,
      error: { type: ConnectionErrorType.DATA_SYNC_ERROR, message: 'mocked data error' },
      status: ConnectionStatus.CONNECTED_WITH_ERROR,
      updatedTime: lastChangeTime.getTime(),
      xeroOrganisationId: uuidv4(),
      xeroOrganisationName: uuidv4(),
      xeroToken,
      id,
      type: `${DbRecordType.CONNECTION_XERO_BANKFEED}`,
    };

    await context.putItemToDb(connection);

    return connection;
  }

  it('mark as missed if transaction processed while connection has error', async () => {
    const entityUuid = uuidv4();
    const debitCardAccountUuid = uuidv4();

    const connection = await createAndPersistConnectionWithError(entityUuid, [debitCardAccountUuid]);
    const accountConfig = connection.accounts.find((x) => x.zellerDebitCardAccountUuid === debitCardAccountUuid);
    if (!accountConfig) {
      throw new Error('Date not set up correctly');
    }

    const transactionEvent = createTestTransactionCreateEventDto(uuidv4(), debitCardAccountUuid, entityUuid);
    await projectionSqs.sendTransactionProjection(transactionEvent);

    await retry(async () => {
      const transaction = await context.tryGetItemFromDb<IDebitCardAccountTransactionDbModel>(
        transactionEvent.id,
        DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION,
      );
      if (!transaction) {
        throw new Error('Transaction not found yet');
      }
      expect(transaction.statementStatus).toEqual(TransctionSendAttemptStatus.MISSED_SEND_DUE_TO_CONNECTION_ERROR);
      expect(transaction.missedSendToXeroBankAccountId).toEqual(accountConfig.xeroId);
      expect(transaction.missedSendToXeroOrganisationId).toEqual(connection.xeroOrganisationId);
    }, 30);
  });
});
