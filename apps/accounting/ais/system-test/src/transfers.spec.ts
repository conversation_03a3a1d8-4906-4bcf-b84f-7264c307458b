import type {
  ConnectionXeroBankfeedConfigCreatedEventDto,
  ConnectionXeroEcommerceConfigCreatedEventDto,
} from '@npco/component-dto-connection';
import { ConnectionStatus, ConnectionErrorType, XeroAccountLinkStatus } from '@npco/component-dto-connection';
import { DbRecordType } from '@npco/component-dto-core';
import type { XeroToken } from '@npco/component-events-core';

import { v4 as uuidv4 } from 'uuid';
import type { BankTransfer, BankTransfers, XeroClient } from 'xero-node';

import { createTestTransactionDepositCreateEventDto } from './__testData__/createTestTransactionDepositCreateEventDto';
import type { IDebitCardAccountTransactionDbModel, TableKeys } from './__testData__/iDebitCardAccountRansactionDbModel';
import { TransctionSendAttemptStatus } from './__testData__/iDebitCardAccountRansactionDbModel';
import { AisEnvService } from './utils/envService';
import { retry } from './utils/retry';
import { setupBankfeedsConnection } from './utils/setupBankfeedsConnection';
import { setupEcommerceConnection } from './utils/setupEcommerceConnection';
import { sleep } from './utils/sleep';
import { getSqsService } from './utils/sqs/sqsService';
import { ApiTestHelper } from './utils/testHelper';
import { getCountryCodeByRegion } from './utils/utils';

describe('BankTransfer test suites', () => {
  const env = new AisEnvService();
  const context = new ApiTestHelper(env);
  let bankTransferRetrySqs: any;
  let connection: ConnectionXeroBankfeedConfigCreatedEventDto;

  let xeroClient: XeroClient;

  const createTransactionDbItem = async (
    data: { siteUuid: string; entityUuid: string; debitCardAccountUuid: string } = {
      siteUuid: context.testData.siteUuid,
      entityUuid: context.testData.entityUuid,
      debitCardAccountUuid: context.testData.debitCardAccountUuid,
    },
  ): Promise<IDebitCardAccountTransactionDbModel> => {
    const txn = createTestTransactionDepositCreateEventDto(
      uuidv4(),
      data.siteUuid,
      data.entityUuid,
      data.debitCardAccountUuid,
    );

    // set the xeroId so it does not get picked up by the dbstream handler
    (txn as any).xeroId = uuidv4();
    (txn as any).transactionType = txn.type;
    (txn as any).type = `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}${txn.timestamp}`;
    txn.timestamp = new Date(Number(txn.timestamp)).toISOString();
    await context.putItemToDb({ ...txn });
    return txn as any as IDebitCardAccountTransactionDbModel;
  };

  const sendFailedBankTransferMessage = async (transactionUuid: string, error: any) => {
    const failedBankTransferMessage = {
      siteUuid: context.testData.siteUuid,
      entityUuid: context.testData.entityUuid,
      connectionUuid: context.testData.ecommerceConnectionUuid,
      targetXeroBankAccountId: context.testData.xeroBankAccountId,
      transactionUuid,
      bankTransfers: [
        {
          bankTransferID: uuidv4(),
          validationErrors: [error],
        },
      ],
      attempts: 1,
    };
    await bankTransferRetrySqs.sendFifoSqsMessage(JSON.stringify(failedBankTransferMessage));
    return failedBankTransferMessage.bankTransfers[0].bankTransferID;
  };

  beforeAll(async () => {
    try {
      const entityUuid = context.getEntityUuid();
      context.setFixedXeroClearingAccountId();
      context.setFixedXeroBankAccountId();
      const bankfeedConnectionUuid = await setupBankfeedsConnection(context, entityUuid, env, 1);
      const ecommerceConnectionUuid = await setupEcommerceConnection(context, entityUuid, env, 1);
      context.setBankFeedConnectionUuid(bankfeedConnectionUuid);
      context.setEcommerceConnectionUuid(ecommerceConnectionUuid);
      await sleep();
      connection = await context.getKnownItemFromDb<ConnectionXeroBankfeedConfigCreatedEventDto>(
        context.testData.bankfeedConnectionUuid,
        DbRecordType.CONNECTION_XERO_BANKFEED,
      );
      console.log('connection', connection);
      expect(connection.xeroToken?.xeroTokenSet).toBeDefined();
      xeroClient = await context.getXeroPaymentServiceClientWithToken(connection.xeroToken!.xeroTokenSet);
    } catch (e) {
      console.error(e);
    }
  });

  describe('transaction to bankTransfers test suites', () => {
    beforeAll(async () => {
      bankTransferRetrySqs = await getSqsService('BankTransfersFailedSendQueueURL');
    });

    it('should send transaction deposit bank transfers to xero', async () => {
      const txn = await createTransactionDbItem();
      await retry(async () => {
        const item = await context.getItemFromDb(txn.id, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
        console.log('txn', txn?.id);
        expect(item.id).toEqual(txn.id);
      }, 15);
      const date = new Date().toISOString();
      await context.invokeAsync(`${env.stage}-ais-engine-transfer-sendBankTransfer`, {
        transactionUuid: txn.id,
        entityUuid: context.testData.entityUuid,
        date,
        amount: 1234,
        triggeringTargetXeroBankAccountId: context.testData.xeroBankAccountId,
      });
      await retry(async () => {
        const item = await context.getItemFromDb(txn.id, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
        console.log('txn', txn?.id);
        expect(item.id).toEqual(txn.id);
        expect(item.bankTransferId).toBeDefined();
        expect(String(item.amount.value)).toEqual(txn.amount.value);
        const response = await xeroClient.accountingApi.getBankTransfer(
          connection.xeroOrganisationId,
          item.bankTransferId as string,
        );
        const bankTransfer: BankTransfer | undefined = response.body.bankTransfers?.[0];
        console.log(bankTransfer);
        expect(item.bankTransferId).toEqual(bankTransfer?.bankTransferID);
        expect(bankTransfer?.validationErrors).toEqual(undefined);
        expect(bankTransfer?.amount).toEqual(12.34);
        expect(bankTransfer?.date).toBeDefined();
        expect(bankTransfer?.toBankAccount.accountID).toEqual(context.testData.xeroBankAccountId);
        expect(bankTransfer?.fromBankAccount.accountID).toEqual(context.testData.xeroClearingAccountId);
      }, 15);
    });

    it('should be able to retry failed bankTransfer and send to xero', async () => {
      const error = {
        message: 'The request should be retried. If the error persists, a Xero support issue should be raised.',
      };
      const item = await createTransactionDbItem();

      await sendFailedBankTransferMessage(item.id, error);
      await retry(async () => {
        const txn = await context.getItemFromDb(item.id, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
        expect(txn.id).toEqual(item.id);
        expect(txn.bankTransferId).toBeDefined();
        const result: {
          body: BankTransfers;
        } = await xeroClient.accountingApi.getBankTransfer(connection.xeroOrganisationId, txn.bankTransferId);
        expect(txn.bankTransferId).toEqual(result.body.bankTransfers?.[0].bankTransferID);
        // expect(result.body.bankTransfers?.[0].validationErrors).toEqual('DELIVERED');
      }, 90);
    });
  });

  describe('mark as missed send', () => {
    // eslint-disable-next-line @typescript-eslint/no-shadow
    let context: ApiTestHelper;
    beforeAll(async () => {
      context = new ApiTestHelper(new AisEnvService());
    });

    async function createAndPersistConnectionWithError(
      entityUuid: string,
      siteUuid: string,
      debitCardAccountUuid: string,
    ) {
      const lastChangeTime = new Date(Date.now() - 1000);
      const xeroToken: XeroToken = {
        lastTokenRefreshStartTimestamp: 0,
        lastTokenRefreshTimestamp: 0,
        xeroTokenSet: { access_token: '', expires_at: 0, refresh_token: '', scope: '', token_type: 'Bearer' },
      };
      const id = uuidv4();
      const xeroOrganisationId = uuidv4();
      // eslint-disable-next-line @typescript-eslint/no-shadow
      const connection: ConnectionXeroEcommerceConfigCreatedEventDto & TableKeys = {
        siteUuid,
        xeroClearingAccountId: uuidv4(),
        xeroExpenseAccountCode: uuidv4(),
        xeroExpenseAccountId: uuidv4(),
        xeroOrganisationShortCode: uuidv4(),
        enabledThemes: [],
        paymentServiceUuid: uuidv4(),
        connectionTimestamp: lastChangeTime.getTime(),
        connectionUuid: id,
        customerUuid: uuidv4(),
        entityUuid,
        error: { type: ConnectionErrorType.DATA_SYNC_ERROR, message: 'mocked data error' },
        status: ConnectionStatus.CONNECTED_WITH_ERROR,
        updatedTime: lastChangeTime.getTime(),
        xeroOrganisationId,
        xeroOrganisationName: uuidv4(),
        xeroToken,
        id,
        type: `${DbRecordType.CONNECTION_XERO_ECOMMERCE}`,
      };
      const accountConfigs = [
        {
          accountName: uuidv4(),
          countryCodeISO3166: getCountryCodeByRegion() as any,
          linkStatus: XeroAccountLinkStatus.LINKED,
          sharedWithXeroTimestamp: lastChangeTime.getTime(),
          xeroAccountType: 'BANK',
          xeroFeedConnectionId: uuidv4(),
          xeroId: uuidv4(),
          zellerDebitCardAccountUuid: debitCardAccountUuid,
          zellerGeneratedXeroAccountToken: uuidv4(),
        },
      ];
      const connectionFeeds: ConnectionXeroBankfeedConfigCreatedEventDto & TableKeys = {
        accounts: accountConfigs,
        connectionTimestamp: lastChangeTime.getTime(),
        connectionUuid: uuidv4(),
        customerUuid: uuidv4(),
        entityUuid,
        error: { type: ConnectionErrorType.NO_ERROR, message: '' },
        status: ConnectionStatus.CONNECTED,
        updatedTime: lastChangeTime.getTime(),
        xeroOrganisationId,
        xeroOrganisationName: uuidv4(),
        xeroToken,
        id,
        type: `${DbRecordType.CONNECTION_XERO_BANKFEED}`,
      };

      await context.putItemToDb(connection);
      await context.putItemToDb(connectionFeeds);
      return connection;
    }

    it('mark as missed if transaction processed while connection has error', async () => {
      const entityUuid = uuidv4();
      const siteUuid = uuidv4();
      const debitCardAccountUuid = uuidv4();

      const conn = await createAndPersistConnectionWithError(entityUuid, siteUuid, debitCardAccountUuid);

      const txn = await createTransactionDbItem({ debitCardAccountUuid, siteUuid, entityUuid });
      await retry(async () => {
        const item = await context.getItemFromDb(txn.id, `${DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION}`);
        console.log('txn', txn?.id);
        expect(item.id).toEqual(txn.id);
      }, 15);
      const date = new Date().toISOString();
      await context.invokeAsync(`${env.stage}-ais-engine-transfer-sendBankTransfer`, {
        transactionUuid: txn.id,
        entityUuid: context.testData.entityUuid,
        date,
        amount: 1234,
        triggeringTargetXeroBankAccountId: context.testData.xeroBankAccountId,
      });
      await retry(async () => {
        const transaction = await context.tryGetItemFromDb<IDebitCardAccountTransactionDbModel>(
          txn.id,
          DbRecordType.DEBIT_CARD_ACCOUNT_TRANSACTION,
        );
        if (!transaction) {
          throw new Error('Transaction not found yet');
        }
        expect(transaction.bankTransferStatus).toEqual(TransctionSendAttemptStatus.MISSED_SEND_DUE_TO_CONNECTION_ERROR);
        expect(transaction.missedSendToXeroBankAccountId).toEqual(context.testData.xeroBankAccountId);
        expect(transaction.missedSendToXeroOrganisationId).toEqual(conn.xeroOrganisationId);
      }, 30);
    });
  });
});
