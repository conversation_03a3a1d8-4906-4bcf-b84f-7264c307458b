import { ISO4217 } from '@npco/component-dto-core';
import type {
  DebitCardAccountTransactionCreatedEventDto,
  SavingsAccountTransactionCreatedEventDto,
} from '@npco/component-dto-issuing-transaction';
import { DebitCardTransactionStatusV2, DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import { v4 as uuidv4 } from 'uuid';

import { getCurrencyByRegion } from '../utils/utils';

import { randomAccountNumber, randomBsb } from './randomData';

export const createTestTransactionCreateEventDto = (
  id: string,
  debitCardAccountUuid: string,
  entityUuid: string,
  overrides?: Partial<DebitCardAccountTransactionCreatedEventDto>,
): DebitCardAccountTransactionCreatedEventDto => ({
  id,
  debitCardAccountUuid,
  entityUuid,
  type: DebitCardTransactionTypeV2.DE_IN,
  status: DebitCardTransactionStatusV2.APPROVED,
  amount: {
    value: '100',
    currency: getCurrencyByRegion(),
  },
  debitCardMaskedPan: 'debitCardMaskedPan',
  referencePayee: `referencePayee${uuidv4()}`,
  payeeDetailsUuid: `payeeDetailsUuid${uuidv4()}`,
  payeeDetails: {
    nppDetails: {
      payId: 'payId',
    },
    bpayDetails: {
      billerCode: 'billerCode',
      crn: 'crn',
      billerName: 'billerName',
      nickname: 'nickname',
    },
    accountDetails: {
      bsb: randomBsb(),
      account: randomAccountNumber(),
      name: `name-${id}`,
    },
    debitCardAccountUuid: uuidv4(),
  },
  payerDetails: {
    nppDetails: {
      payId: 'payId',
    },
    bpayDetails: {
      billerCode: 'billerCode',
      crn: 'crn',
      billerName: 'billerName',
      nickname: 'nickname',
    },
    accountDetails: {
      bsb: randomBsb(),
      account: randomAccountNumber(),
      name: 'name',
    },
    debitCardAccountUuid: uuidv4(),
  },
  updatedTime: new Date().getTime() / 1000,
  timestamp: new Date().getTime().toString(),
  ...overrides,
});

export const createTestSavingsAccountTransactionCreateEventDto = (
  id: string,
  debitCardAccountUuid: string,
  entityUuid: string,
): SavingsAccountTransactionCreatedEventDto => ({
  id,
  accountUuid: debitCardAccountUuid,
  entityUuid,
  type: DebitCardTransactionTypeV2.TRANSFER_IN,
  status: DebitCardTransactionStatusV2.APPROVED,
  amount: {
    value: '100',
    currency: ISO4217.AUD,
  },
  balance: {
    value: '100',
    currency: ISO4217.AUD,
  },
  referencePayee: `referencePayee${uuidv4()}`,
  payeeDetails: {
    accountDetails: {
      bsb: randomBsb(),
      account: randomAccountNumber(),
      name: `name-${id}`,
    },
    debitCardAccountUuid: uuidv4(),
  },
  payerDetails: {
    nppDetails: {
      payId: 'payId',
    },
    bpayDetails: {
      billerCode: 'billerCode',
      crn: 'crn',
      billerName: 'billerName',
      nickname: 'nickname',
    },
    accountDetails: {
      bsb: randomBsb(),
      account: randomAccountNumber(),
      name: 'name',
    },
    debitCardAccountUuid: uuidv4(),
  },
  updatedTime: new Date().getTime() / 1000,
  timestamp: new Date().getTime(),
});
