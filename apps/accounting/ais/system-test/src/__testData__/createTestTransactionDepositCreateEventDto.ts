import type { DebitCardAccountTransactionCreatedEventDto } from '@npco/component-dto-issuing-transaction';
import { DebitCardTransactionStatusV2, DebitCardTransactionTypeV2 } from '@npco/component-dto-issuing-transaction';

import { v4 as uuidv4 } from 'uuid';

import { getCurrencyByRegion } from '../utils/utils';

import { randomAccountNumber, randomBsb } from './randomData';

export const createTestTransactionDepositCreateEventDto = (
  id: string,
  siteUuid: string,
  entityUuid: string,
  debitCardAccountUuid: string,
): DebitCardAccountTransactionCreatedEventDto => ({
  id,
  debitCardAccountUuid,
  entityUuid,
  depositId: `${entityUuid}${siteUuid}${new Date().toISOString()}`,
  type: DebitCardTransactionTypeV2.DEPOSIT,
  status: DebitCardTransactionStatusV2.APPROVED,
  amount: {
    value: '1234',
    currency: getCurrencyByRegion(),
  },
  debitCardMaskedPan: 'debitCardMaskedPan',
  referencePayee: `referencePayee${uuidv4()}`,
  payeeDetailsUuid: `payeeDetailsUuid${uuidv4()}`,
  payeeDetails: {
    nppDetails: {
      payId: 'payId',
    },
    bpayDetails: {
      billerCode: 'billerCode',
      crn: 'crn',
      billerName: 'billerName',
      nickname: 'nickname',
    },
    accountDetails: {
      bsb: randomBsb(),
      account: randomAccountNumber(),
      name: `name-${id}`,
    },
    debitCardAccountUuid: uuidv4(),
  },
  payerDetails: {
    nppDetails: {
      payId: 'payId',
    },
    bpayDetails: {
      billerCode: 'billerCode',
      crn: 'crn',
      billerName: 'billerName',
      nickname: 'nickname',
    },
    accountDetails: {
      bsb: randomBsb(),
      account: randomAccountNumber(),
      name: 'name',
    },
    debitCardAccountUuid: uuidv4(),
  },
  updatedTime: new Date().getTime() / 1000,
  timestamp: new Date().getTime().toString(),
});
