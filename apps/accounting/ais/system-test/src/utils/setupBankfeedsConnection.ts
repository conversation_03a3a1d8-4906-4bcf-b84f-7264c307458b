import { ConnectionStatus } from '@npco/component-dto-connection';
import { ConnectionType, DbRecordType } from '@npco/component-dto-core/dist';
import type { XeroTenant } from '@npco/component-dto-xero';

import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { XeroClient } from 'xero-node/dist';

import { completeXeroConnectionOAuthFlow } from './completeXeroConnectionOAuthFlow';
import type { AisEnvService } from './envService';
import { generateXeroSingleUseTokenGenerationCode } from './generateToken';
import type { XeroUserNumber } from './getXeroUsersFromEnvVars';
import { getXeroUsersFromEnvVars } from './getXeroUsersFromEnvVars';
import { ProgressLogger } from './progressLogger';
import { SsmService, ZellerComponent } from './ssmService';
import type { ApiTestHelper } from './testHelper';
import {
  getXeroOrganisationName,
  getXeroOrganisationShortCode,
  getCountryCodeByRegion,
  getXeroFeedConnectionId,
  getXeroId,
  getZellerGeneratedXeroAccountToken,
  getSharedWithXeroTimestamp,
} from './utils';

export const getXeroCallbackUrl = async () => {
  const ssmService = new SsmService();
  let xeroCallbackUrl = process.env.XERO_CALLBACK_URL;
  if (!xeroCallbackUrl) {
    xeroCallbackUrl = await ssmService.getSsmParameterValue('xero-callback-url', ZellerComponent.AisEngine);
  }
  return xeroCallbackUrl;
};

export const getXeroBankFeedsClientId = async () => {
  const ssmService = new SsmService();
  let xeroBankFeedsClientId = process.env.XERO_BANKFEED_CLIENT_ID;
  if (!xeroBankFeedsClientId) {
    xeroBankFeedsClientId = await ssmService.getSsmParameterValue('xero-bankfeeds-clientid', ZellerComponent.AisEngine);
  }
  return xeroBankFeedsClientId;
};

export async function completeBankfeedsOAuthFlow(
  apiTestHelper: ApiTestHelper,
  entityUuid: string,
  xeroUserNumber: XeroUserNumber,
  calledFrom: string,
) {
  console.log('entityUuid', entityUuid);
  const xeroUser = await getXeroUsersFromEnvVars(xeroUserNumber);
  const progressLogger = new ProgressLogger();
  let callbackUrl: string;
  try {
    const xeroCallbackUrl = await getXeroCallbackUrl();
    const xeroBankFeedsClientId = await getXeroBankFeedsClientId();
    callbackUrl = await generateXeroSingleUseTokenGenerationCode(
      {
        xeroEmailUsername: xeroUser.username,
        xeroPassword: xeroUser.password,
        xeroTotpSecret: xeroUser.totp,
        clientId: xeroBankFeedsClientId ?? apiTestHelper.env.XERO_BANKFEED_CLIENT_ID ?? '',
        callbackUrl: xeroCallbackUrl ?? 'http://localhost:5000/callback',
        scopes: [
          'email',
          'profile',
          'openid',
          'bankfeeds',
          'accounting.settings',
          'accounting.transactions',
          'accounting.contacts',
          'offline_access',
        ],
      },
      'bankfeeds',
      progressLogger,
    );
  } catch (e: any) {
    progressLogger.outputQueuedLogs();
    throw e;
  }

  await completeXeroConnectionOAuthFlow(
    callbackUrl,
    ConnectionType.XERO_BANKFEEDS,
    entityUuid,
    apiTestHelper,
    `${calledFrom}->completeBankfeedsOAuthFlow.ts`,
  );

  const connection = await apiTestHelper.getConnectionItemByEntityUuid(
    entityUuid,
    DbRecordType.CONNECTION_XERO_BANKFEED,
  );
  const client = new XeroClient({
    clientId: apiTestHelper.env.XERO_BANKFEED_CLIENT_ID,
    scopes: connection.xeroToken.xeroTokenSet.scope.split(' '),
    clientSecret: '',
    state: '',
    grantType: 'Bearer',
    redirectUris: ['http://localhost:5000'],
  });
  client.setTokenSet(connection.xeroToken.xeroTokenSet);
  await client.updateTenants(true);
  return client.tenants as XeroTenant[];
}

export async function setupBankfeedsConnection(
  apiTestHelper: ApiTestHelper,
  entityUuid: string,
  env: AisEnvService,
  xeroUserNumber: XeroUserNumber,
) {
  const availableOrgs = await completeBankfeedsOAuthFlow(
    apiTestHelper,
    entityUuid,
    xeroUserNumber,
    'setupBankfeedsConnection.ts->setupBankfeedsConnection',
  );
  const connection = await apiTestHelper.getConnectionItemByEntityUuid(
    entityUuid,
    DbRecordType.CONNECTION_XERO_BANKFEED,
  );
  connection.status = ConnectionStatus.CONNECTED;
  connection.xeroOrganisationId = availableOrgs[0].tenantId;
  connection.xeroOrganisationName = getXeroOrganisationName();
  connection.xeroOrganisationShortCode = getXeroOrganisationShortCode();
  connection.zellerDebitCardAccountUuid = apiTestHelper.testData.debitCardAccountUuid;
  connection.accounts = [
    {
      accountName: 'FEED_TEST_DO_NOT_DELETE',
      countryCodeISO3166: getCountryCodeByRegion(),
      linkStatus: 'LINKED',
      sharedWithXeroTimestamp: getSharedWithXeroTimestamp(),
      xeroAccountType: 'BANK',
      xeroFeedConnectionId: getXeroFeedConnectionId(),
      xeroId: getXeroId(),
      zellerDebitCardAccountUuid: apiTestHelper.testData.debitCardAccountUuid,
      zellerGeneratedXeroAccountToken: getZellerGeneratedXeroAccountToken(),
    },
  ];
  const putCommand = new PutCommand({ Item: connection, TableName: `${env.entityTable}` });
  await apiTestHelper.dbClient.send(putCommand);
  return connection.id;
}
