import type { DbRecordType } from '@npco/component-dto-core';

import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { Lambda } from '@aws-sdk/client-lambda';
import { DynamoDBDocumentClient, PutCommand, QueryCommand } from '@aws-sdk/lib-dynamodb';
import type { NativeAttributeValue } from '@aws-sdk/util-dynamodb';
import type { TokenSetParameters } from 'openid-client';
import { v4 as uuidv4 } from 'uuid';
import type { IXeroClientConfig } from 'xero-node';
import { XeroClient } from 'xero-node';

// eslint-disable-next-line import/no-relative-packages
import { KnownXeroOAuthScopeValues } from '../../../engine/runtime/src/xero/types';

import { dynamoConfig } from './dynamoConfig';
import type { AisEnvService } from './envService';
import { SsmService, ZellerComponent } from './ssmService';
import { getXeroClearingAccountId, getXeroDepositAccountId } from './utils';

/**
 * Fixed xero clearing account used for a persistent bankfeed with the Xero API.
 */
export const defaultXeroClearingAccountId = getXeroClearingAccountId();
/**
 * Fixed xeroBankAccount used for a persistent bankfeed with the Xero API.
 */
export const defaultXeroBankAccountUuid = getXeroDepositAccountId();

class TestData {
  entityUuid = uuidv4();

  debitCardAccountUuid = uuidv4();

  xeroClearingAccountId = uuidv4();

  xeroBankAccountId = uuidv4();

  // cnp invoice site
  siteUuid = uuidv4();

  ecommerceConnectionUuid = uuidv4();

  bankfeedConnectionUuid = uuidv4();
}

export class ApiTestHelper {
  public readonly testData = new TestData();

  public dbClient: DynamoDBDocumentClient;

  public ssmClient = new SsmService();

  private lambdaClient;

  constructor(readonly env: AisEnvService) {
    const client = new DynamoDBClient({ region: env.region });
    this.dbClient = DynamoDBDocumentClient.from(client, dynamoConfig);
    this.lambdaClient = new Lambda({ region: env.region });
  }

  isSystemTestStage = () => {
    const stStageRegex = /st\d+/g;
    return stStageRegex.test(this.env.stage);
  };

  getEntityUuid = () => {
    return this.testData.entityUuid;
  };

  setEntityUuid = (entityUuid: string) => {
    this.testData.entityUuid = entityUuid;
  };

  setFixedXeroClearingAccountId = () => {
    this.testData.xeroClearingAccountId = defaultXeroClearingAccountId;
  };

  setFixedXeroBankAccountId = () => {
    this.testData.xeroBankAccountId = defaultXeroBankAccountUuid;
  };

  setBankFeedConnectionUuid = (connectionUuid: string) => {
    this.testData.bankfeedConnectionUuid = connectionUuid;
  };

  setEcommerceConnectionUuid = (connectionUuid: string) => {
    this.testData.ecommerceConnectionUuid = connectionUuid;
  };

  async invokeAsync(lambdaName: string, payload: any): Promise<void> {
    await this.lambdaClient.invoke({
      FunctionName: lambdaName,
      InvocationType: 'Event',
      Payload: Buffer.from(JSON.stringify(payload)),
    });
  }

  getItemFromDb = async <T = any>(id: string, type: string, tableName = this.env.entityTable): Promise<Partial<T>> => {
    const queryParams = {
      TableName: tableName,
      KeyConditionExpression: `id = :id AND begins_with(#type, :type)`,
      ExpressionAttributeValues: {
        ':type': type,
        ':id': id,
      },
      ExpressionAttributeNames: { '#type': 'type' },
    };
    const queryCommand = new QueryCommand(queryParams);
    const { Items } = await this.dbClient.send(queryCommand);

    if (!Items) {
      throw new Error('No Items returned');
    }

    return Items[0] as Partial<T>;
  };

  getKnownItemFromDb = async <T = any>(id: string, type: string): Promise<T> => {
    const queryParams = {
      TableName: this.env.entityTable,
      KeyConditionExpression: `id = :id AND begins_with(#type, :type)`,
      ExpressionAttributeValues: {
        ':type': type,
        ':id': id,
      },
      ExpressionAttributeNames: { '#type': 'type' },
    };

    const command = new QueryCommand(queryParams);
    const { Items } = await this.dbClient.send(command);

    if (!Items) {
      throw new Error('No Items returned');
    }

    return Items[0] as T;
  };

  tryGetItemFromDb = async <T = any>(id: string, type: string): Promise<Partial<T> | undefined> => {
    const queryParams = {
      TableName: this.env.entityTable,
      KeyConditionExpression: `id = :id AND begins_with(#type, :type)`,
      ExpressionAttributeValues: {
        ':type': type,
        ':id': id,
      },
      ExpressionAttributeNames: { '#type': 'type' },
    };

    try {
      const command = new QueryCommand(queryParams);
      const { Items } = await this.dbClient.send(command);

      if (!Items || Items.length === 0) {
        return undefined;
      }

      return Items[0] as Partial<T>;
    } catch (e: any) {
      throw new Error(`dynamo db query error${JSON.stringify(e)}`);
    }
  };

  putItemToDb = async (item: any): Promise<any> => {
    try {
      const params = {
        TableName: this.env.entityTable,
        Item: item,
      };
      const putCommand = new PutCommand(params);
      await this.dbClient.send(putCommand);
    } catch (e: any) {
      throw new Error(`dynamo db put error${JSON.stringify(e)}`);
    }
  };

  getConnectionItemByEntityUuid = async (entityUuid: string, dbRecordType: DbRecordType) => {
    let items: Record<string, NativeAttributeValue>[] | undefined;

    try {
      const queryParams = {
        TableName: this.env.entityTable,
        IndexName: 'entityGsi',
        KeyConditionExpression: `entityUuid = :entityUuid AND begins_with(#type, :type)`,
        ExpressionAttributeValues: {
          ':type': dbRecordType,
          ':entityUuid': entityUuid,
        },
        ExpressionAttributeNames: { '#type': 'type' },
      };
      const queryCommand = new QueryCommand(queryParams);
      items = (await this.dbClient.send(queryCommand)).Items;
    } catch (e: any) {
      throw new Error(`dynamo db query error${JSON.stringify(e)}`);
    }

    if (!items) {
      throw new Error('No Items returned');
    }
    return items[0];
  };

  getConnectionItemById = async (connectionUuid: string, dbRecordType: DbRecordType) => {
    let items: Record<string, NativeAttributeValue>[] | undefined;

    try {
      const queryParams = {
        TableName: this.env.entityTable,
        KeyConditionExpression: `id = :id AND begins_with(#type, :type)`,
        ExpressionAttributeValues: {
          ':type': dbRecordType,
          ':id': connectionUuid,
        },
        ExpressionAttributeNames: { '#type': 'type' },
      };
      const queryCommand = new QueryCommand(queryParams);
      items = (await this.dbClient.send(queryCommand)).Items;
    } catch (e: any) {
      throw new Error(`dynamo db query error${JSON.stringify(e)}`);
    }

    if (!items) {
      throw new Error('No Items returned');
    }
    return items[0];
  };

  getXeroBankFeedClientWithToken = async (xeroTokenSet: TokenSetParameters): Promise<XeroClient> => {
    const clientSecret = await this.ssmClient.getSsmParameterValue('XeroBankfeedClientSecret', ZellerComponent.MpApi);

    const xeroConfig: IXeroClientConfig = {
      clientId: this.env.XERO_BANKFEED_CLIENT_ID,
      clientSecret,
      httpTimeout: this.env.XERO_API_HTTP_TIMEOUT_MS,
      redirectUris: [this.env.XERO_AUTH_CALLBACK_URL],
      scopes: [
        KnownXeroOAuthScopeValues.BANKFEEDS,
        KnownXeroOAuthScopeValues.OFFLINE_ACCESS,
        KnownXeroOAuthScopeValues.ACCOUNTING_SETTINGS,
      ],
    };

    const xeroClient = new XeroClient(xeroConfig);
    xeroClient.setTokenSet(xeroTokenSet);

    return xeroClient;
  };

  getXeroPaymentServiceClientWithToken = async (xeroTokenSet: TokenSetParameters): Promise<XeroClient> => {
    const clientSecret = await this.ssmClient.getSsmParameterValue('XeroEcommerceClientSecret', ZellerComponent.MpApi);

    const xeroConfig: IXeroClientConfig = {
      clientId: this.env.XERO_PAYMENT_SERVICE_CLIENT_ID,
      clientSecret,
      httpTimeout: this.env.XERO_API_HTTP_TIMEOUT_MS,
      redirectUris: [this.env.XERO_AUTH_CALLBACK_URL],
      scopes: [
        KnownXeroOAuthScopeValues.PAYMENT_SERVICES,
        KnownXeroOAuthScopeValues.OFFLINE_ACCESS,
        KnownXeroOAuthScopeValues.ACCOUNTING_TRANSACTIONS,
        KnownXeroOAuthScopeValues.ACCOUNTING_SETTINGS,
        KnownXeroOAuthScopeValues.ACCOUNTING_CONTACTS,
      ],
    };

    const xeroClient = new XeroClient(xeroConfig);
    xeroClient.setTokenSet(xeroTokenSet);

    return xeroClient;
  };
}
