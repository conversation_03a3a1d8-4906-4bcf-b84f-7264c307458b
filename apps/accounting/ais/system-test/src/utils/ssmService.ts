import type { GetParameterCommandInput, Parameter } from '@aws-sdk/client-ssm';
import { GetParameterCommand, SSMClient } from '@aws-sdk/client-ssm';

import { AisEnvService } from './envService';
import { region } from './globalVariables';

export enum ZellerComponent {
  AisEngine = 'ais-engine',
  MpApi = 'mp-api',
}

export class SsmService {
  client: SSMClient;

  env: AisEnvService;

  constructor() {
    this.env = new AisEnvService();
    this.client = new SSMClient({ region });
  }

  public async getSsmParameter(parameter: string, stack: ZellerComponent): Promise<Parameter | null> {
    const fullparameterName = `/${this.env.dbStage}-${stack}/${parameter}`;
    console.log('parameter name', fullparameterName);
    console.info(`Get SSM Value for parameter ${fullparameterName} called `);
    const input: GetParameterCommandInput = {
      Name: fullparameterName,
      WithDecryption: true,
    };
    const command = new GetParameterCommand(input);

    const result = await this.client.send(command, {});

    if (!result.Parameter) {
      console.warn(`SSM parameter ${parameter} not found`);
      return null;
    }

    if (!result.Parameter.Value) {
      console.warn(`SSM value for parameter ${parameter} not found`);
      return null;
    }

    console.info(`SSM parameter ${fullparameterName} resolved`);

    return result.Parameter;
  }

  public async getSsmParameterValue(parameter: string, stack: ZellerComponent): Promise<string> {
    const result = await this.getSsmParameter(parameter, stack);

    if (!result?.Value) {
      return '';
    }

    return result.Value;
  }
}
