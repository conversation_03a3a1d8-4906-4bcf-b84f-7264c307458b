import { ISO4217 } from '@npco/component-dto-core';

import { region } from './globalVariables';

export const getCurrencyByRegion = () => {
  switch (region) {
    case 'eu-west-2': {
      return ISO4217.GBP;
    }
    case 'ap-southeast-2':
    default: {
      return ISO4217.AUD;
    }
  }
};

export const getXeroOrganisationName = () => {
  switch (region) {
    case 'eu-west-2': {
      return 'Zeller Test 7 (UK-2025-05)';
    }
    case 'ap-southeast-2':
    default: {
      return 'Zeller Test (AU-2022-05)';
    }
  }
};

export const getXeroOrganisationShortCode = () => {
  switch (region) {
    case 'eu-west-2': {
      return '!bdT7W';
    }
    case 'ap-southeast-2':
    default: {
      return '!-Rn6!';
    }
  }
};

export const getCountryCodeByRegion = () => {
  switch (region) {
    case 'eu-west-2': {
      return 'GB';
    }
    case 'ap-southeast-2':
    default: {
      return 'AU';
    }
  }
};

export const getXeroId = () => {
  switch (region) {
    case 'eu-west-2': {
      return '9a9a8701-0224-42e2-8767-e314af936dc4';
    }
    case 'ap-southeast-2':
    default: {
      return '41a8da72-0d1d-4d7a-bf76-c9dda99c678f';
    }
  }
};

export const getXeroClearingAccountId = () => {
  switch (region) {
    case 'eu-west-2': {
      return '00f820e0-7d96-486e-b503-5e3d865d606a';
    }
    case 'ap-southeast-2':
    default: {
      return '0e26c14c-781e-4e79-9602-************';
    }
  }
};

export const getXeroDepositAccountId = () => {
  switch (region) {
    case 'eu-west-2': {
      return '902bc28f-d4c8-4272-87ad-7e70324a0439';
    }
    case 'ap-southeast-2':
    default: {
      return 'e4e412a2-d515-4f61-aa83-6e98f1f7eb92';
    }
  }
};

export const getXeroFeedConnectionId = () => {
  switch (region) {
    case 'eu-west-2': {
      return 'bce64f51-ab3d-4e8a-801f-38169b194d93';
    }
    case 'ap-southeast-2':
    default: {
      return 'c7e524da-4149-451c-a41b-daeb14a5d8ce';
    }
  }
};

export const getZellerGeneratedXeroAccountToken = () => {
  switch (region) {
    case 'eu-west-2': {
      return '3087a896-b1ea-421d-a41c-04cb320d35e6';
    }
    case 'ap-southeast-2':
    default: {
      return '31a8da72-0d1d-4d7a-bf76-c9dda99c678a';
    }
  }
};

export const getSharedWithXeroTimestamp = () => {
  switch (region) {
    case 'eu-west-2': {
      return *************;
    }
    case 'ap-southeast-2':
    default: {
      return *************;
    }
  }
};
