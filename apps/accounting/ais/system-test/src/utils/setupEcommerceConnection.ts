import { ConnectionStatus } from '@npco/component-dto-connection';
import { ConnectionType, DbRecordType } from '@npco/component-dto-core';
import type { XeroTenant } from '@npco/component-dto-xero';

import { PutCommand } from '@aws-sdk/lib-dynamodb';
import { XeroClient } from 'xero-node';

import { completeXeroConnectionOAuthFlow } from './completeXeroConnectionOAuthFlow';
import type { AisEnvService } from './envService';
import { generateXeroSingleUseTokenGenerationCode } from './generateToken';
import type { XeroUserNumber } from './getXeroUsersFromEnvVars';
import { getXeroUsersFromEnvVars } from './getXeroUsersFromEnvVars';
import { ProgressLogger } from './progressLogger';
import { SsmService, ZellerComponent } from './ssmService';
import type { ApiTestHelper } from './testHelper';
import { getXeroOrganisationName, getXeroOrganisationShortCode } from './utils';

export const getXeroCallbackUrl = async () => {
  const ssmService = new SsmService();
  let xeroCallbackUrl = process.env.XERO_CALLBACK_URL;
  if (!xeroCallbackUrl) {
    xeroCallbackUrl = await ssmService.getSsmParameterValue('xero-callback-url', ZellerComponent.AisEngine);
  }
  return xeroCallbackUrl;
};

export const getXeroEcommerceClientId = async () => {
  const ssmService = new SsmService();
  let ecommerceClientId = process.env.XERO_ECOMMERCE_CLIENT_ID;
  if (!ecommerceClientId) {
    ecommerceClientId = await ssmService.getSsmParameterValue('xero-ecommerce-clientid', ZellerComponent.AisEngine);
  }
  return ecommerceClientId;
};

export async function completeEcommerceOAuthFlow(
  apiTestHelper: ApiTestHelper,
  entityUuid: string,
  xeroUserNumber: XeroUserNumber,
  calledFrom: string,
) {
  console.log('entityUuid', entityUuid);
  const xeroUser = await getXeroUsersFromEnvVars(xeroUserNumber);
  const progressLogger = new ProgressLogger();
  let callbackUrl: string;
  try {
    const xeroCallbackUrl = await getXeroCallbackUrl();
    const xeroEcommerceClientId = await getXeroEcommerceClientId();
    callbackUrl = await generateXeroSingleUseTokenGenerationCode(
      {
        xeroEmailUsername: xeroUser.username,
        xeroPassword: xeroUser.password,
        xeroTotpSecret: xeroUser.totp,
        clientId: xeroEcommerceClientId ?? apiTestHelper.env.XERO_PAYMENT_SERVICE_CLIENT_ID ?? '',
        callbackUrl: xeroCallbackUrl ?? 'http://localhost:5000/callback',
        scopes: [
          'email',
          'profile',
          'openid',
          'paymentservices',
          'accounting.settings',
          'accounting.transactions',
          'accounting.contacts',
          'offline_access',
        ],
      },
      'paymentServices',
      progressLogger,
    );
  } catch (e: any) {
    progressLogger.outputQueuedLogs();
    throw e;
  }

  await completeXeroConnectionOAuthFlow(
    callbackUrl,
    ConnectionType.XERO_PAYMENT_SERVICES,
    entityUuid,
    apiTestHelper,
    `${calledFrom}->completeEcommerceOAuthFlow.ts`,
  );

  const connection = await apiTestHelper.getConnectionItemByEntityUuid(
    entityUuid,
    DbRecordType.CONNECTION_XERO_ECOMMERCE,
  );
  const client = new XeroClient({
    clientId: apiTestHelper.env.XERO_PAYMENT_SERVICE_CLIENT_ID,
    scopes: connection.xeroToken.xeroTokenSet.scope.split(' '),
    clientSecret: '',
    state: '',
    grantType: 'Bearer',
    redirectUris: ['http://localhost:5000'],
  });
  client.setTokenSet(connection.xeroToken.xeroTokenSet);
  await client.updateTenants(true);

  return client.tenants as XeroTenant[];
}

export async function setupEcommerceConnection(
  apiTestHelper: ApiTestHelper,
  entityUuid: string,
  env: AisEnvService,
  xeroUserNumber: XeroUserNumber,
) {
  const availableOrgs = await completeEcommerceOAuthFlow(
    apiTestHelper,
    entityUuid,
    xeroUserNumber,
    'setupEcommerceConnection.ts->setupEcommerceConnection',
  );
  const initialConnection = await apiTestHelper.getConnectionItemByEntityUuid(
    entityUuid,
    DbRecordType.CONNECTION_XERO_ECOMMERCE,
  );

  const connection = {
    ...initialConnection,
    siteUuid: apiTestHelper.testData.siteUuid,
    status: ConnectionStatus.CONNECTED,
    xeroOrganisationId: availableOrgs[0].tenantId,
    enabledThemes: [{ xeroThemeId: '9ee56736-bf5d-4a27-a7dc-faf74e90189c' }],
    paymentServiceUuid: '33d9f7af-missing',
    xeroClearingAccountId: apiTestHelper.testData.xeroClearingAccountId,
    xeroExpenseAccountCode: 'ZLREXPST-missing',
    xeroExpenseAccountId: '1d05b78a-missing',
    xeroOrganisationName: getXeroOrganisationName(),
    xeroOrganisationShortCode: getXeroOrganisationShortCode(),
  };
  const putCommand = new PutCommand({ Item: connection, TableName: `${env.entityTable}` });
  await apiTestHelper.dbClient.send(putCommand);
  return initialConnection.id;
}
