import type { GetSecretValueCommandInput } from '@aws-sdk/client-secrets-manager';
import { GetSecretValueCommand, SecretsManagerClient } from '@aws-sdk/client-secrets-manager';

import { AisEnvService } from './envService';
import { region } from './globalVariables';

export class SecretManagerService {
  client: SecretsManagerClient;

  env: AisEnvService;

  constructor() {
    this.env = new AisEnvService();
    this.client = new SecretsManagerClient({ region });
  }

  public async getSmParameter(parameter: string): Promise<string | null> {
    const fullparameterName = `/${this.env.dbStage}-${this.env.COMPONENT_NAME}/${parameter}`;
    console.info(`Get SM Value for parameter ${fullparameterName} called `);
    const input: GetSecretValueCommandInput = {
      SecretId: fullparameterName,
    };
    const command = new GetSecretValueCommand(input);

    const result = await this.client.send(command, {});

    if (!result.SecretString) {
      console.warn(`SM parameter ${parameter} not found`);
      return null;
    }

    console.info(`SM parameter ${fullparameterName} resolved`);

    return result.SecretString;
  }
}
