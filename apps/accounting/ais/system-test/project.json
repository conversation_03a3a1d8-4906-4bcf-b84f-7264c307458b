{"name": "ais-engine-system-test", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "tags": ["separate-yarn-install", "tests-require-infrastructure"], "targets": {"yarninstall": {"executor": "nx:run-commands", "options": {"command": "yarn install", "cwd": "{projectRoot}"}}, "no-sonar": {"executor": "nx:noop"}, "system:test": {"executor": "nx:run-commands", "options": {"command": "yarn jest --forceExit --config ./jest.config.js", "cwd": "{projectRoot}"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["{projectRoot}/src/**/*.ts"]}}}}