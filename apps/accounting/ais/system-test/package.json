{"name": "ais-engine-system-test", "version": "1.0.0", "main": "index.js", "author": "<PERSON>", "license": "MIT", "scripts": {"build": "rm -fr dist && yarn tsc --build tsconfig.json", "test": "echo no unit test", "run-audit": "echo no audit", "clean": "rm -rf dist", "system:test": "yarn jest --forceExit --config ./jest.config.js"}, "devDependencies": {"@nx/jest": "18.2.4", "@rushstack/eslint-patch": "^1.6.0", "@types/totp-generator": "^0.0.4", "typescript": "^5.3.3"}, "dependencies": {"@aws-sdk/client-cloudformation": "3.435.0", "@aws-sdk/client-dynamodb": "3.435.0", "@aws-sdk/client-lambda": "3.435.0", "@aws-sdk/client-secrets-manager": "3.435.0", "@aws-sdk/client-sqs": "3.435.0", "@aws-sdk/client-ssm": "3.435.0", "@aws-sdk/lib-dynamodb": "3.435.0", "@aws-sdk/types": "3.413.0", "@aws-sdk/util-dynamodb": "3.435.0", "@npco/bff-systemtest-utils": "^1.0.38", "@npco/component-bff-core": "1.3.103", "@npco/component-dto-connection": "^1.0.4", "@npco/component-dto-core": "^3.0.18", "@npco/component-dto-entity": "1.0.1", "@npco/component-dto-issuing-transaction": "^1.0.3", "@npco/component-dto-xero": "^1.0.1", "@npco/component-events-core": "^1.0.36", "@smithy/smithy-client": "^2.3.1", "@types/aws-lambda": "^8.10.95", "@types/chance": "^1.1.3", "@types/dotenv": "^8.2.0", "@types/graphql": "^14.5.0", "@types/jest": "^29.5.11", "@types/node": "^14.14.22", "@types/random-number-csprng": "^1.0.0", "@types/request": "^2.48.8", "@types/uuid": "^8.3.4", "@types/ws": "^8.5.10", "axios": "^0.21.1", "chance": "^1.1.9", "dotenv": "^16.0.0", "firebase": "^8.6.8", "isomorphic-fetch": "^3.0.0", "jest": "^29.7.0", "jest-html-reporter": "^3.5.0", "jest-junit": "^13.2.0", "jest-sonar-reporter": "^2.0.0", "openid-client": "^5.3.1", "puppeteer": "^17.1.3", "random-number-csprng": "^1.0.2", "request": "^2.88.2", "totp-generator": "^0.0.13", "ts-jest": "^28.0.2", "ts-node": "^10.9.2", "uuid": "^8.3.2", "ws": "^8.17.1", "xero-node": "4.21.0"}, "jestSonar": {"reportPath": "dist", "reportFile": "test-reporter.xml", "indent": 2}, "resolutions": {"@npco/component-bff-core": "file:../../../libs/bff-core", "@npco/bff-systemtest-utils": "file:../../../libs/bff-systemtest-utils", "bff-permission-interface": "file:../../../libs/bff-permission-interface", "@npco/component-dto-issuing-transaction": "file:../../../libs/dto-common/issuing-transaction", "@npco/component-dto-entity": "file:../../../libs/dto-common/entity", "@npco/component-dto-connection": "file:../../../libs/dto-common/connection", "@npco/component-dto-core": "file:../../../libs/dto-common/core", "@npco/component-events-core": "file:../../../libs/bff-events/core", "@npco/component-dto-richdata": "file:../../../libs/dto-common/richdata", "@npco/component-dto-customer": "file:../../../libs/dto-common/customer", "@npco/component-dto-device": "file:../../../libs/dto-common/device", "@npco/component-dto-site": "file:../../../libs/dto-common/site", "@npco/component-dto-xero": "file:../../../libs/dto-common/xero"}, "dependenciesMeta": {"nx": {"built": false}}, "husky": {"hooks": {"pre-push": "yarn lint && yarn test --silent"}}, "prettier": "@npco/eslint-config-backend/prettier"}