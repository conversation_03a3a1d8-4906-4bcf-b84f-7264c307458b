import { ConfigService } from '@npco/component-bff-core/dist/config/configService';
import { DomainURImap } from '@npco/component-dto-core';
import type { DomainURI } from '@npco/component-dto-core';

export class EnvironmentService {
  /**
   * The number of milliseconds a dynamodb request can take
   * before automatically being terminated. Defaults to two minutes (120000).
   */
  public dynamodbQueryTimeout = 5000;

  /**
   * The maximum time in milliseconds that the connection phase of the request
   * should be allowed to take. This only limits the connection phase and has
   * no impact once the socket has established a connection.
   * Used in node.js environments only.
   */
  public dynamodbConnectTimeout = 2000;

  /**
   * The maximum amount of retries to perform for a dynamodb service request.
   */
  public dynamoMaxRetries = 3;

  /**
   * Inferred by checking if LAMBDA_TASK_ROOT is defined, which should be when a lambda is executing
   */
  public isInLambda!: boolean;

  public lambdaHttpTimeout = 5000;

  public lambdaConnectTimeout = 2000;

  public maxRetries = 3;

  public cqrsCommandHandler: string;

  public cqrsCmds: DomainURI = DomainURImap;

  /**
   *  URL of the message queue where projection messages are stored
   */
  public projectionSqsUrl: string;

  /**
   *  URL of the message queue where statement messages are queued and rate limited
   */
  public statementsToSendSqsUrl: string;

  /**
   *  URL of the message queue where statement messages are queued and rate limited
   */
  public connectionErrorRecoverHandlerSqsUrl: string;

  /**
   *  URL of the message queue where failed statement messages are queued and retried
   */
  public statementsRetryOrFailSqsUrl: string;

  /**
   *  URL of the message queue where failed bank transfer messages are queued and retried
   */
  public bankTransfersRetryOrFailSqsUrl: string;

  /**
   * The time to wait in seconds for the circuit break to restart sending
   * when the xero connection is failing
   */
  public xeroConnectionLambdaFailureTimeout: number;

  /**
   * AWS region
   */
  public region: string;

  public awsRegion: string;

  /**
   * Xero API HTTP Timeout in ms
   */
  public xeroApiHttpTimeoutMs: number;

  public bffConnectionApiEndpoint: string;

  public bffConnectionApiEndpointVersion: string;

  public XERO_BANKFEED_CLIENT_ID: string;

  public XERO_PAYMENT_SERVICE_CLIENT_ID: string;

  public STATIC_ENV_NAME: string;

  public XERO_AUTH_CALLBACK_URL: string;

  public AWS_LAMBDA_FUNCTION_NAME = 'lambda_name_not_defined';

  public maxSendsAllowedInOneMinuteForOrganisation = 30;

  public settlementReconciliationLambda: string;

  public COMPONENT_NAME: string;

  private readonly componentTableName: string;

  private readonly merchantTableName: string;

  constructor(private readonly configService: ConfigService = new ConfigService()) {
    this.isInLambda = !!this.configService.get('LAMBDA_TASK_ROOT', '');
    this.cqrsCommandHandler = this.configService.get('CQRS_COMMAND_HANDLER', '');
    this.projectionSqsUrl = this.configService.get('PROJECTION_SQS_URL', '');
    this.statementsToSendSqsUrl = this.configService.get('STATEMENTS_TO_SEND_SQS_URL', '');
    this.statementsRetryOrFailSqsUrl = this.configService.get('STATEMENTS_FAILED_SEND_SQS_URL', '');
    this.bankTransfersRetryOrFailSqsUrl = this.configService.get('BANK_TRANSFERS_FAILED_SEND_SQS_URL', '');
    this.connectionErrorRecoverHandlerSqsUrl = this.configService.get('CONNECTION_RECOVERY_SQS_URL', '');
    this.xeroConnectionLambdaFailureTimeout = this.configService.get<number>('XERO_CONNECTION_FAILURE_TIMEOUT', 10);
    this.dynamodbQueryTimeout = this.configService.get('DYNAMODB_QUERY_TIMEOUT_IN_MS', 5000);
    this.dynamodbConnectTimeout = this.configService.get('DYNAMO_CONNECTION_TIMEOUT_IN_MS', 2000);
    this.dynamoMaxRetries = this.configService.get('DYNAMO_MAX_RETRIES', 3);
    this.componentTableName = this.configService.get<string>('COMPONENT_TABLE_NAME', '');
    this.merchantTableName = this.configService.get<string>('MERCHANT_TABLE_NAME', '');
    this.region = this.configService.get<string>('AWS_REGION', '');
    this.awsRegion = this.configService.get<string>('AWS_REGION', '');
    this.xeroApiHttpTimeoutMs = this.configService.get<number>('XERO_API_HTTP_TIMEOUT_MS', 2000);
    this.bffConnectionApiEndpoint = this.configService.get<string>('BFF_CONNECTION_API_ENDPOINT', '');
    this.bffConnectionApiEndpointVersion = this.configService.get<string>('BFF_CONNECTION_API_ENDPOINT_VERSION', 'v1');
    this.settlementReconciliationLambda = this.configService.get<string>('SETTLEMENT_RECONCILIATION_LAMBDA', '');
    this.XERO_BANKFEED_CLIENT_ID = this.configService.get<string>('XERO_BANKFEED_CLIENT_ID', '');
    this.XERO_PAYMENT_SERVICE_CLIENT_ID = this.configService.get<string>('XERO_PAYMENT_SERVICE_CLIENT_ID', '');
    this.STATIC_ENV_NAME = this.configService.get<string>('STATIC_ENV_NAME', 'not_defined');
    this.XERO_AUTH_CALLBACK_URL = this.configService.get<string>('XERO_AUTH_CALLBACK_URL', 'not_defined');
    this.maxSendsAllowedInOneMinuteForOrganisation = this.configService.get<number>(
      'MAX_XERO_STATEMENTS_PER_TENANT_PER_MINUTE',
      50,
    );
    this.AWS_LAMBDA_FUNCTION_NAME = this.configService.get<string>(
      'AWS_LAMBDA_FUNCTION_NAME',
      'lambda_name_not_defined',
    );
    this.COMPONENT_NAME = this.configService.get<string>('COMPONENT_NAME', 'ais');
  }

  // required for compatibility with bff-core env
  public get dynamodbTimeout() {
    return this.dynamodbQueryTimeout;
  }

  public getComponentTableName() {
    if (this.componentTableName) {
      return this.componentTableName;
    }
    throw new Error(
      'COMPONENT_TABLE_NAME has not been defined as an env variable. ' +
        'Can`t connect to ComponentTable without this definition, ' +
        'ensure that it`s configured in the serverless for all lambda`s that require it',
    );
  }

  public getMerchantTableName() {
    if (this.merchantTableName) {
      return this.merchantTableName;
    }
    throw new Error(
      'MERCHANT_TABLE_NAME has not been defined as an env variable. ' +
        'Can`t connect to MerchantTable without this definition, ' +
        'ensure that it`s configured in the serverless for all lambda`s that require it',
    );
  }
}
