import { BffDynamoDbClient } from '@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient';
import type { ConnectionPosInterfaceConfigCreatedEventDto } from '@npco/component-dto-connection/dist/connectionPosInterfaceConfigCreatedEventDto';
import { PosConnectionStatus } from '@npco/component-dto-connection/dist/types';
import { ConnectionType, DbRecordType } from '@npco/component-dto-core/dist/types';

import { v4 as uuidv4 } from 'uuid';

import { BffEnvironmentService } from '../config/envService';

import { PosInterfaceDb } from './posInterfaceDb';
import type { HlPosConfiguration, HlPosVenue, ImposConfiguration, PosVenue, TevalisPosConfiguration } from './types';

jest.mock('aws-xray-sdk');
jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');
jest.mock('../config/envService');

describe('Connection Pos Interface db test suite', () => {
  let documentClient: BffDynamoDbClient;
  let env: BffEnvironmentService;
  let posInterfaceDb: PosInterfaceDb;
  const entityUuid = uuidv4();

  beforeAll(async () => {
    env = new BffEnvironmentService({} as any);
    documentClient = new BffDynamoDbClient(env);
    posInterfaceDb = new PosInterfaceDb(env, documentClient);
  });

  it('should return null on getPosInterfaceDbItem when no existing item found', async () => {
    const connectionUuid = uuidv4();
    const connection: any = await posInterfaceDb.getPosInterfaceDbItem(entityUuid, connectionUuid);
    expect(connection).toBeNull();
  });

  it('should save connection event projection to db', async () => {
    const connectionUuid = uuidv4();

    const posInterfaceConfig: ConnectionPosInterfaceConfigCreatedEventDto = {
      entityUuid,
      connectionUuid,
      provider: ConnectionType.HL_POS,
      status: PosConnectionStatus.CONNECTED,
      clientId: 'mockClientId',
      encryptedClientSecret: 'mockEncryptedClientSecret',
      organisationId: 'mockOrganisationId',
      organisationName: 'mockOrganisationName',
      venues: [
        {
          id: 'mockVenueId',
          name: 'mockVenueName',
          locations: [
            {
              id: 'mockLocationId',
              name: 'mockLocationName',
              number: 'mockTableNumber',
            },
          ],
        },
      ],
    };

    await posInterfaceDb.savePosInterfaceProjection(posInterfaceConfig);

    const connection: any = await posInterfaceDb.getPosInterfaceDbItem(entityUuid, connectionUuid);
    const expectedConnection = {
      ...posInterfaceConfig,
      id: connectionUuid,
      type: DbRecordType.CONNECTION_POS_INTERFACE,
    };
    expect(connection).toEqual(expectedConnection);
  });

  it('should return null on getPosInterfaceDbItemByEntityGsi when no existing item found', async () => {
    const connection: any = await posInterfaceDb.getPosInterfaceDbItemByEntityGsi(uuidv4(), {
      provider: ConnectionType.HL_POS,
    });
    expect(connection).toBeNull();
  });

  it('should return the existing item on getPosInterfaceDbItemByEntityGsi', async () => {
    const testEntityUuid = uuidv4();
    const posInterfaceConfig: ConnectionPosInterfaceConfigCreatedEventDto = {
      entityUuid: testEntityUuid,
      connectionUuid: uuidv4(),
      provider: ConnectionType.HL_POS,
      status: PosConnectionStatus.CONNECTED,
      clientId: 'mockClientId',
      encryptedClientSecret: 'mockEncryptedClientSecret',
      organisationId: 'mockOrganisationId',
      organisationName: 'mockOrganisationName',
      venues: [
        {
          id: 'mockVenueId',
          name: 'mockVenueName',
          locations: [
            {
              id: 'mockLocationId',
              name: 'mockLocationName',
              number: 'mockTableNumber',
            },
          ],
        },
      ],
    };

    await posInterfaceDb.savePosInterfaceProjection(posInterfaceConfig);

    const connection: any = await posInterfaceDb.getPosInterfaceDbItemByEntityGsi(testEntityUuid, {
      provider: ConnectionType.HL_POS,
    });
    const expectedConnection = {
      ...posInterfaceConfig,
      id: posInterfaceConfig.connectionUuid,
      entityUuid: testEntityUuid,
      type: DbRecordType.CONNECTION_POS_INTERFACE,
    };
    expect(connection).toEqual(expectedConnection);
  });

  it('should return null on getHlPosConfiguration when no existing item found', async () => {
    const connection: any = await posInterfaceDb.getHlPosConfiguration(uuidv4());
    expect(connection).toBeNull();
  });

  it('should return the existing item with HlPosConfiguration shape on getHlPosConfiguration', async () => {
    const testEntityUuid = uuidv4();
    const posInterfaceConfig: ConnectionPosInterfaceConfigCreatedEventDto = {
      entityUuid: testEntityUuid,
      connectionUuid: uuidv4(),
      provider: ConnectionType.HL_POS,
      status: PosConnectionStatus.CONNECTED,
      clientId: 'mockClientId',
      encryptedClientSecret: 'mockEncryptedClientSecret',
      organisationId: 'mockOrganisationId',
      organisationName: 'mockOrganisationName',
      venues: [
        {
          id: 'mockVenueId',
          name: 'mockVenueName',
          locations: [
            {
              id: 'mockLocationId',
              name: 'mockLocationName',
              number: 'mockTableNumber',
            },
          ],
        },
      ],
    };

    await posInterfaceDb.savePosInterfaceProjection(posInterfaceConfig);

    const hlPosConfiguration = await posInterfaceDb.getHlPosConfiguration(testEntityUuid);
    const expectedHlPosConfig: HlPosConfiguration = {
      organisationId: posInterfaceConfig.organisationId as string,
      organisationName: posInterfaceConfig.organisationName,
      clientId: posInterfaceConfig.clientId as string,
      venues: posInterfaceConfig.venues as HlPosVenue[],
    };
    expect(hlPosConfiguration).toEqual(expectedHlPosConfig);
  });

  it('should return null on getImposConfiguration when no existing item found', async () => {
    const connection: any = await posInterfaceDb.getImposConfiguration(uuidv4());
    expect(connection).toBeNull();
  });

  it('should return the existing item with ImposConfiguration shape on getImposConfiguration', async () => {
    const testEntityUuid = uuidv4();
    const posInterfaceConfig = {
      entityUuid: testEntityUuid,
      connectionUuid: uuidv4(),
      provider: ConnectionType.IMPOS,
      status: PosConnectionStatus.CONNECTED,
      encryptedClientSecret: 'mockEncryptedClientSecret',
      hostIp: 'mockHostIp',
      clientApiKey: 'mockclientApiKey',
      venues: [
        {
          id: 'mockVenueId',
          name: 'mockVenueName',
          locations: [
            {
              id: 'mockLocationId',
              name: 'mockLocationName',
              number: 'mockTableNumber',
            },
          ],
        },
      ],
    };

    await posInterfaceDb.savePosInterfaceProjection(posInterfaceConfig);

    const imposConfiguration = await posInterfaceDb.getImposConfiguration(testEntityUuid);
    const expectedImposConfig: ImposConfiguration = {
      hostIp: posInterfaceConfig.hostIp as string,
      clientApiKey: posInterfaceConfig.clientApiKey,
      venues: posInterfaceConfig.venues as PosVenue[],
    };
    expect(imposConfiguration).toEqual(expectedImposConfig);
  });

  it('should return null on getTevaliPosConfiguration when no existing item found', async () => {
    const connection: any = await posInterfaceDb.getTevalisPosConfiguration(uuidv4());
    expect(connection).toBeNull();
  });

  it('should return the existing item with TevaliPosConfiguration shape on getTevalisPosConfiguration', async () => {
    const testEntityUuid = uuidv4();
    const posInterfaceConfig = {
      entityUuid: testEntityUuid,
      connectionUuid: uuidv4(),
      provider: ConnectionType.TEVALIS_POS,
      status: PosConnectionStatus.CONNECTED,
      encryptedClientSecret: 'mockEncryptedClientSecret',
      hostIp: 'mockHostIp',
      credentials: {
        companyId: 'mockCompanyId',
        guid: 'mockGuid',
        guid2: 'mockGuid2',
      },
      venues: [
        {
          id: 'mockVenueId',
          name: 'mockVenueName',
          locations: [
            {
              id: 'mockLocationId',
              name: 'mockLocationName',
              number: 'mockTableNumber',
            },
          ],
        },
      ],
    };

    await posInterfaceDb.savePosInterfaceProjection(posInterfaceConfig);

    const tevalisPosConfiguration = await posInterfaceDb.getTevalisPosConfiguration(testEntityUuid);
    const expectedTevalisPosConfig: TevalisPosConfiguration = {
      companyId: posInterfaceConfig.credentials.companyId,
      guid: posInterfaceConfig.credentials.guid,
      guid2: posInterfaceConfig.credentials.guid2,
      venues: posInterfaceConfig.venues ?? [],
    };
    expect(tevalisPosConfiguration).toEqual(expectedTevalisPosConfig);
  });

  it('should return null on getTevalisPosConfiguration when only DISCONNECTED records exist', async () => {
    const testEntityUuid = uuidv4();
    const disconnectedConfig = {
      entityUuid: testEntityUuid,
      connectionUuid: uuidv4(),
      provider: ConnectionType.TEVALIS_POS,
      status: PosConnectionStatus.DISCONNECTED,
      encryptedClientSecret: 'mockEncryptedClientSecret',
      hostIp: 'mockHostIp',
      credentials: null, // this is how we remove the credentials in disconnected state
      venues: [], // no venues in disconnected state
    };
    await posInterfaceDb.savePosInterfaceProjection(disconnectedConfig as any);
    const tevalisPosConfiguration = await posInterfaceDb.getTevalisPosConfiguration(testEntityUuid);
    expect(tevalisPosConfiguration).toBeNull();
  });
});
