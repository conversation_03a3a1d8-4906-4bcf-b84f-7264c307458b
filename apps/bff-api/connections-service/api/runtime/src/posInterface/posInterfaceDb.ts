import type { BffDynamoDbClient } from '@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient';
import { debug } from '@npco/component-bff-core/dist/utils/logger';
import type { ConnectionPosInterfaceConfigCreatedEventDto } from '@npco/component-dto-connection/dist/connectionPosInterfaceConfigCreatedEventDto';
import type { ConnectionPosInterfaceConfigUpdatedEventDto } from '@npco/component-dto-connection/dist/connectionPosInterfaceConfigUpdatedEventDto';
import { PosConnectionStatus } from '@npco/component-dto-connection/dist/types';
import { ConnectionType, DbRecordType } from '@npco/component-dto-core/dist/types';

import type { BffEnvironmentService } from '../config/envService';
import { EntityDbService } from '../framework/dynamoDb/entityDbService';

import type {
  ConnectionPosInterfaceRecord,
  HlPosConfiguration,
  HlPosInterfaceRecord,
  PosConfiguration,
  ImposInterfaceRecord,
  ImposConfiguration,
  TevalisInterfaceRecord,
  TevalisPosConfiguration,
} from './types';

export class PosInterfaceDb extends EntityDbService<ConnectionPosInterfaceRecord> {
  type = DbRecordType.CONNECTION_POS_INTERFACE;

  constructor(envService: BffEnvironmentService, documentClient: BffDynamoDbClient) {
    super(envService, documentClient, envService.COMPONENT_TABLE);
  }

  getPosInterfaceDbItem = async (entityUuid: string, connectionUuid: string) => {
    const existing = await this.queryIdByType(entityUuid, connectionUuid, this.type);
    debug(`PosInterfaceDb:getPosInterfaceDbItem existing: ${JSON.stringify(existing)}`);
    if (existing.Items && existing.Items.length > 0) {
      return existing.Items[0];
    }

    return null;
  };

  getPosInterfaceDbItemByEntityGsi = async (
    entityUuid: string,
    filters: { provider?: ConnectionType; status?: PosConnectionStatus } = {},
  ): Promise<ConnectionPosInterfaceRecord | null> => {
    const result = await this.queryUsingEntityGsi(
      {
        entityUuid,
        typeStartsWith: DbRecordType.CONNECTION_POS_INTERFACE,
      },
      { provider: filters.provider, status: filters.status },
    );
    debug(`PosInterfaceDb:getPosInterfaceDbItemByEntityGsi existing item: ${JSON.stringify(result)}`);

    if (result && result.length > 0) {
      return result[0];
    }
    return null;
  };

  getHlPosConfiguration = async (entityUuid: string): Promise<HlPosConfiguration | null> => {
    const result = await this.queryUsingEntityGsi(
      {
        entityUuid,
        typeStartsWith: DbRecordType.CONNECTION_POS_INTERFACE,
      },
      { provider: ConnectionType.HL_POS },
    );
    debug(`PosInterfaceDb:getHlPosConfiguration result: ${JSON.stringify(result)}`);

    if (result?.length === 0) {
      return null;
    }
    return this.convertRecordToHlPosConfiguration(result[0] as HlPosInterfaceRecord);
  };

  getImposConfiguration = async (entityUuid: string): Promise<ImposConfiguration | null> => {
    const result = await this.queryUsingEntityGsi(
      {
        entityUuid,
        typeStartsWith: DbRecordType.CONNECTION_POS_INTERFACE,
      },
      { provider: ConnectionType.IMPOS },
    );
    debug(`PosInterfaceDb:getImposConfiguration result: ${JSON.stringify(result)}`);

    if (result?.length === 0) {
      return null;
    }
    return this.convertRecordToImposConfiguration(result[0] as ImposInterfaceRecord);
  };

  getTevalisPosConfiguration = async (entityUuid: string): Promise<TevalisPosConfiguration | null> => {
    const result = await this.queryUsingEntityGsi(
      {
        entityUuid,
        typeStartsWith: DbRecordType.CONNECTION_POS_INTERFACE,
      },
      { provider: ConnectionType.TEVALIS_POS, status: PosConnectionStatus.CONNECTED },
    );
    debug(`PosInterfaceDb:getTevalisPosConfiguration result: ${JSON.stringify(result)}`);

    if (result?.length === 0) {
      return null;
    }
    return this.convertRecordToTevalisPosConfiguration(result[0] as TevalisInterfaceRecord);
  };

  savePosInterfaceProjection = async (
    dto: ConnectionPosInterfaceConfigCreatedEventDto | ConnectionPosInterfaceConfigUpdatedEventDto,
  ) => {
    debug(`PosInterfaceDb:savePosInterfaceProjection dto: ${JSON.stringify(dto)}`);
    await this.saveConditionallyByUpdatedTime(
      { id: dto.connectionUuid, type: DbRecordType.CONNECTION_POS_INTERFACE },
      dto,
    );
  };

  getPosConfiguration = async (
    entityUuid: string,
    provider: ConnectionType,
    convertRecordToPosConfiguration: (record: ConnectionPosInterfaceRecord) => PosConfiguration,
  ) => {
    const result = await this.queryUsingEntityGsi(
      {
        entityUuid,
        typeStartsWith: DbRecordType.CONNECTION_POS_INTERFACE,
      },
      { provider },
    );
    debug(`PosInterfaceDb:getPosConfiguration result: ${JSON.stringify(result)}`);

    if (result?.length === 0) {
      return null;
    }
    return convertRecordToPosConfiguration(result[0]);
  };

  private readonly convertRecordToHlPosConfiguration = (record: HlPosInterfaceRecord): HlPosConfiguration => {
    const hlPosConfiguration: HlPosConfiguration = {
      organisationId: record.organisationId as string,
      organisationName: record.organisationName,
      clientId: record.clientId as string,
      venues: record.venues ?? [],
    };
    return hlPosConfiguration;
  };

  private readonly convertRecordToImposConfiguration = (record: ImposInterfaceRecord): ImposConfiguration => {
    const imposConfiguration: ImposConfiguration = {
      hostIp: record.hostIp as string,
      clientApiKey: record.clientApiKey as string,
      venues: record.venues ?? [],
    };
    return imposConfiguration;
  };

  private readonly convertRecordToTevalisPosConfiguration = (
    record: TevalisInterfaceRecord,
  ): TevalisPosConfiguration => {
    const tevalisPosConfiguration: TevalisPosConfiguration = {
      companyId: record.credentials?.companyId,
      guid: record.credentials?.guid,
      guid2: record.credentials?.guid2,
      venues: record.venues ?? [],
    };
    return tevalisPosConfiguration;
  };
}
