import { retry } from '@npco/bff-systemtest-utils';
import type { DeviceCreatedEventDto } from '@npco/component-dto-device';
import { DeviceStatus } from '@npco/component-dto-device';
import type { SiteCreatedEventDto } from '@npco/component-dto-site';
import { SiteType } from '@npco/component-dto-site';
import { ComponentClients } from '@npco/bff-systemtest-utils/dist/helper';

import gql from 'graphql-tag';
import { v4 as uuidv4 } from 'uuid';

import { defaultSiteSurchargesTaxes } from './testUtils';
import { ApiTestHelper } from '../apiTestHelper';
import { describeIf } from '../testIf';
import { timezone } from '@npco/component-dto-site/dist/mockTestData/mockdata.timezone';

const { STAGE: stage } = process.env;
describeIf(stage === 'dev', 'role based access control system tests', () => {
  const apiTestHelper = new ApiTestHelper(ComponentClients.DeviceBackend);
  const createSiteCreatedDto = (siteUuid: string): SiteCreatedEventDto => ({
    entityUuid: apiTestHelper.getEntityUuid(),
    siteUuid,
    name: uuidv4(),
    pin: uuidv4(),
    refundRequiresPin: true,
    type: SiteType.FIXED,
    address: {
      street: 'street1',
      suburb: 'suburb',
      postcode: 'postcode',
    },
    surchargesTaxes: defaultSiteSurchargesTaxes,
    timezone,
  });

  const assignedSiteId = uuidv4();
  const unassignedSiteId = uuidv4();

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
    await apiTestHelper.createDomicileValue();
    const site = createSiteCreatedDto(assignedSiteId);

    await apiTestHelper.sendSiteProjectionEvent('dbs.Site.Created', site);
    const site2 = createSiteCreatedDto(unassignedSiteId);

    await apiTestHelper.sendSiteProjectionEvent('dbs.Site.Created', site2);
    const zellerSite = {
      siteUuid: uuidv4(),
      entityUuid: apiTestHelper.getEntityUuid(),
      name: 'test',
      type: SiteType.CNP_ZELLER_INVOICE,
      address: {
        state: 'state',
        street: 'street1',
        suburb: '',
        postcode: '',
      },
    };

    await apiTestHelper.sendSiteProjectionEvent('dbs.Site.Created', zellerSite);
    const xeroSite = {
      siteUuid: uuidv4(),
      pin: uuidv4(),
      entityUuid: apiTestHelper.getEntityUuid(),
      name: uuidv4(),
      type: SiteType.CNP_XERO_INVOICE,
      address: {
        state: 'state',
        street: 'street1',
        suburb: '',
        postcode: '',
      },
      customers: [apiTestHelper.getCustomerUuid()],
    };

    await apiTestHelper.sendSiteProjectionEvent('dbs.Site.Created', xeroSite);
    // assignCustomerToSite
    await apiTestHelper.updateCustomer({
      customerUuid: apiTestHelper.getCustomerUuid(),
      entityUuid: apiTestHelper.getEntityUuid(),
      sites: [assignedSiteId, xeroSite.siteUuid],
    });
    // assignDeviceToSite
    const device: DeviceCreatedEventDto = {
      siteUuid: assignedSiteId,
      entityUuid: apiTestHelper.getEntityUuid(),
      deviceUuid: uuidv4(),
      model: 'model',
      serial: 'serial',
      status: DeviceStatus.ACTIVE,
    };
    await apiTestHelper.createDevice(device);
    await retry(async () => {
      const result = await apiTestHelper.api.getSite(assignedSiteId);
      expect(result.getSite.id).toEqual(assignedSiteId);
      const result2 = await apiTestHelper.api.getSite(unassignedSiteId);
      expect(result2.getSite.id).toEqual(unassignedSiteId);
    }, 15);
    await apiTestHelper.setTestCustomerAsRole('MANAGER');
  });

  it('should be able to query individual site', async () => {
    await retry(async () => {
      const result = await apiTestHelper.api.getSite(assignedSiteId);
      expect(result.getSite.id).toEqual(assignedSiteId);
    }, 15);
  });

  it('should fail to query unassigned site', async () => {
    await apiTestHelper.api
      .getSite(unassignedSiteId)
      .then(() => expect('fail').toBe('shouldNotGetHere'))
      .catch((e) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
  });

  it('should be able to query list of sites', async () => {
    await retry(async () => {
      const data = await apiTestHelper.api.getSites(1);
      console.log('getSites result: ', data.getSites);
      expect(data.getSites.sites.length).toBe(1);
    }, 15);
  });

  const updateSite = async (input: any) =>
    (await apiTestHelper.getOpenIdClient()).mutate({
      mutation: gql`
        mutation updateSite($input: UpdateSiteInput!) {
          updateSite(input: $input)
        }
      `,
      variables: {
        input,
      },
    });

  it('should be able to update Site', async () => {
    await updateSite({
      name: 'name',
      id: assignedSiteId,
    })
      .then(() => expect('fail').toBe('shouldNotGetHere'))
      .catch((e) => {
        expect(
          e.message.includes("GraphQL error: [400] Sites's id") ||
            e.message.includes('GraphQL error: Failed to update site'),
        ).toBe(true);
      });
  });

  it('should failed to update unassigned site', async () => {
    await updateSite({
      name: 'name',
      id: unassignedSiteId,
    })
      .then(() => expect('fail').toBe('shouldNotGetHere'))
      .catch((e) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
  });

  const deleteSite = async (siteUuid: string) =>
    (await apiTestHelper.getOpenIdClient()).mutate({
      mutation: gql`
        mutation deleteSite($siteUuid: ID!) {
          deleteSite(siteUuid: $siteUuid)
        }
      `,
      variables: {
        siteUuid,
      },
    });

  it('should be able to delete a site', async () => {
    await deleteSite(assignedSiteId)
      .then(() => expect('fail').toBe('shouldNotGetHere'))
      .catch((e) => {
        expect(e.message.includes('GraphQL error: [400] Site') || e.message.includes('Failed to delete site')).toBe(
          true,
        );
      });
  });

  it('should not be able to delete a site', async () => {
    await deleteSite(unassignedSiteId)
      .then(() => expect('fail').toBe('shouldNotGetHere'))
      .catch((e) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
  });

  describe('assign customer to site', () => {
    const assignCustomerToSite = async (siteUuid: string) =>
      (await apiTestHelper.getOpenIdClient()).mutate({
        mutation: gql`
          mutation assignCustomerToSite($customerUuid: ID!, $siteUuid: ID!) {
            assignCustomerToSite(customerUuid: $customerUuid, siteUuid: $siteUuid)
          }
        `,
        variables: {
          customerUuid: uuidv4(),
          siteUuid,
        },
      });

    it('should be able to assign a customer to site', async () => {
      await assignCustomerToSite(assignedSiteId)
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e) => {
          expect(
            e.message.includes('[400] Cant find customer') || e.message.includes('Failed to assign customer'),
          ).toBe(true);
        });
    });

    it('should not be able to remove a customer from a site', async () => {
      await assignCustomerToSite(unassignedSiteId)
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
    });

    const removeCustomerFromSite = async (siteUuid: string) =>
      (await apiTestHelper.getOpenIdClient()).mutate({
        mutation: gql`
          mutation removeCustomerFromSite($customerUuid: ID!, $siteUuid: ID!) {
            removeCustomerFromSite(customerUuid: $customerUuid, siteUuid: $siteUuid)
          }
        `,
        variables: {
          customerUuid: uuidv4(),
          siteUuid,
        },
      });

    it('should be able to remove a customer to site', async () => {
      await removeCustomerFromSite(assignedSiteId)
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e) => {
          expect(
            e.message.includes('[400] Cant find customer') || e.message.includes('Failed to unassign customer'),
          ).toBe(true);
        });
    });

    it('should not be able to remove a customer from a site', async () => {
      await removeCustomerFromSite(unassignedSiteId)
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
    });
  });

  describe('assign device to site', () => {
    const assignDeviceToSite = async (siteUuid: string) =>
      (await apiTestHelper.getOpenIdClient()).mutate({
        mutation: gql`
          mutation assignDeviceToSite($deviceUuid: ID!, $siteUuid: ID!) {
            assignDeviceToSite(deviceUuid: $deviceUuid, siteUuid: $siteUuid)
          }
        `,
        variables: {
          deviceUuid: uuidv4(),
          siteUuid,
        },
      });

    it('should be able to assign a device to site', async () => {
      await assignDeviceToSite(assignedSiteId)
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e) => {
          expect(e.message.includes('[400] Cant find site') || e.message.includes('Failed to assign device')).toBe(
            true,
          );
        });
    });

    it('should not be able to remove a device from a site', async () => {
      await assignDeviceToSite(unassignedSiteId)
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
    });

    const removeDeviceFromSite = async (siteUuid: string) =>
      (await apiTestHelper.getOpenIdClient()).mutate({
        mutation: gql`
          mutation removeDeviceFromSite($deviceUuid: ID!, $siteUuid: ID!) {
            removeDeviceFromSite(deviceUuid: $deviceUuid, siteUuid: $siteUuid)
          }
        `,
        variables: {
          deviceUuid: uuidv4(),
          siteUuid,
        },
      });

    it('should be able to remove a device to site', async () => {
      await removeDeviceFromSite(assignedSiteId)
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e) => {
          expect(e.message.includes('[400] Cant find site') || e.message.includes('Failed to unassign device')).toBe(
            true,
          );
        });
    });

    it('should not be able to remove a device from a site', async () => {
      await removeDeviceFromSite(unassignedSiteId)
        .then(() => expect('fail').toBe('shouldNotGetHere'))
        .catch((e) => expect(e.message).toEqual('GraphQL error: Not allowed.'));
    });
  });
});
