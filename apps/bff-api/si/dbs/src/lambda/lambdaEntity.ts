import { appEntityMiddleware, clientIpMiddleWare } from '@npco/component-bff-core/dist/middleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import type { Entity, EntityDetails, NestAppEntityContext } from '@npco/component-dbs-mp-common/dist/types';
import type { EntityOnboardingDetailsDto, EntityOnboardingErrorDto } from '@npco/component-dto-entity';

import type { Context, Handler } from 'aws-lambda';
import { EntityModule } from 'bff-entity-api/src/services/entityModule';
import { EntityService } from 'bff-entity-api/src/services/entityService';
import { OnboardingService } from 'bff-entity-api/src/services/onboarding/onboardingService';

import type { NestAppClientContext } from './middleware';
import { bootstrapNestJSMiddleware, disableCallbackWaitForEmptyEventLoop } from './middleware';

export const entityParams = { getAggregateId: (event: any) => event.detail.entityUuid };

export const createSoletraderEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { name: string } }, context: Context) => {
    const { entityUuid, app, clientIpAddress = '' } = context as NestAppClientContext;
    return app.get(EntityService).createSoletraderEntity(entityUuid, event.args.name, clientIpAddress);
  },
  [
    bootstrapNestJSMiddleware(EntityModule),
    disableCallbackWaitForEmptyEventLoop,
    appEntityMiddleware(false),
    clientIpMiddleWare,
  ],
);

export const confirmEntityDetailsInitialHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { input: EntityDetails } }, context: Context) => {
    const { app, entityUuid, clientIpAddress = '' } = context as NestAppClientContext;
    return app.get(EntityService).confirmEntityDetailsInitial(entityUuid, event.args.input, clientIpAddress);
  },
  [
    bootstrapNestJSMiddleware(EntityModule),
    disableCallbackWaitForEmptyEventLoop,
    appEntityMiddleware(false),
    clientIpMiddleWare,
  ],
);

export const getEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (_event: any, context: Context) => {
    const { entityUuid, app } = context as NestAppEntityContext;
    return app.get(EntityService).getEntity(entityUuid);
  },
  [bootstrapNestJSMiddleware(EntityModule), appEntityMiddleware(false)],
);

export const updateEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { entity: Entity } }, context: Context) => {
    const { entityUuid, app } = context as NestAppEntityContext;
    return entityUuid === event.args.entity.id ? app.get(EntityService).updateEntity(event.args.entity) : false;
  },
  [bootstrapNestJSMiddleware(EntityModule), appEntityMiddleware(false)],
);

export const searchEntityDetailsInitialHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { businessIdentifier: string } }, context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    await app.get(EntityService).requestSearchEntityDetailsInitial(entityUuid, event.args.businessIdentifier);
  },
  [bootstrapNestJSMiddleware(EntityModule), appEntityMiddleware(false)],
);

export const searchEntityDetailsFullHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (_, context) => {
    const { app, entityUuid, customerUuid } = context as NestAppEntityContext;
    await app.get(EntityService).requestFullSearchEntityDetails(entityUuid, customerUuid);
  },
  [bootstrapNestJSMiddleware(EntityModule), appEntityMiddleware(false)],
);

export const saveEntityOnboardingDetailsHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  (event: { args: { input: EntityOnboardingDetailsDto } }, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(OnboardingService).saveEntityOnboardingDetails(entityUuid, event.args.input);
  },
  [bootstrapNestJSMiddleware(EntityModule), appEntityMiddleware(false)],
);

export const retrieveOnboardingDetails: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  (_event: any, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(OnboardingService).retrieveEntityOnboardingDetails(entityUuid);
  },
  [bootstrapNestJSMiddleware(EntityModule), appEntityMiddleware(false)],
);

export const saveEntityOnboardingErrorsHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  (event: { args: { input: EntityOnboardingErrorDto[] } }, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(OnboardingService).saveEntityOnboardingErrors(entityUuid, event.args.input);
  },
  [bootstrapNestJSMiddleware(EntityModule), appEntityMiddleware(false)],
);
