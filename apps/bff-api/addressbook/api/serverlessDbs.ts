import {
  ApiAppServerlessStack,
  vpcImport,
  DbsApiAppEnvConfig,
  esbuildNestCommon,
  pluginsAppDefault,
  lambdasCrossAccountInvocationPolicy,
} from '@npco/component-bff-serverless';
import { eventBridgeRule } from './resources/dbs/projection/eventBridgeRule';
import { policy } from './resources/common/policy';
import { lambdas as contactCommon } from './resources/common/contact/lambdas';
import { lambdas as contactEmail } from './resources/common/contactEmail/lambdas';
import { lambdas as contactPhone } from './resources/common/contactPhone/lambdas';
import { lambdas as contactMutation } from './resources/common/contactMutation/lambdas';
import { lambdas as label } from './resources/common/label/lambdas';
import { lambdas as projection } from './resources/common/projection/lambdas';

const envConfig = new DbsApiAppEnvConfig('resources/dbs/config/', true);
const { AWS_REGION: region } = process.env;

const lambdas = {
  ...contactCommon,
  ...contactEmail,
  ...contactPhone,
  ...contactMutation,
  ...label,
  ...projection,
};

const sls = new ApiAppServerlessStack('addrbook', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    ...envConfig.getDynamoDb(),
    ...envConfig.getAuth0(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    vpcImport,
    esbuild: esbuildNestCommon,
    dbsApiStackName: '${opt:stage}-dbs-api',
    sessionCacheTableName: envConfig.sessionCacheTableName,
    entityGsi: '${cf:${self:custom.dynamodbStackName}.entityGsi}',
    typeGsi: '${cf:${self:custom.dynamodbStackName}.typeGsi}',
    sortKeyGsi: 'notRequired',
    entityCacheGsi: '${cf:${self:custom.dynamodbStackName}.entityCacheGsi}',
    source: '${env:COMPONENT_NAME}',
    cqrsStackName: '${opt:stage}-dbs-cqrs',
    cqrsEventBusStackName: {
      dev: '${self:custom.cqrsStackName}-iac-eventBridge',
      staging: '${self:custom.cqrsStackName}-iac-eventBridge',
      prod: '${self:custom.cqrsStackName}-iac-eventBridge',
      st: '${self:custom.cqrsStackName}-iac-eventBridge-mock-adbk',
    },
    cqrsEventBus:
      '${cf:${self:custom.cqrsEventBusStackName.${opt:stage}, "${self:custom.cqrsEventBusStackName.st}"}.EventBusProjectionArn}',
    lambdaPath: 'src/lambdas/dbs',
    contactImageBaseUrl: '${env:CONTACT_IMAGE_BASE_URL}',
    contactImageDownloadBucket: '${ssm:${env:STATIC_ENV_NAME}-s3-bucket-contact-image}',
    accessLogsBucketName: '${self:provider.stackName}-s3-logs-${self:provider.region}-${self:custom.accountId}',
    domicileLookupTableReadRolePolicyArn: envConfig.domicileLookupTableReadRolePolicyArn,
    sessionCacheTableDBPolicyArn: {
      'Fn::ImportValue': '${self:custom.serviceName}-sessionCacheTableDBPolicyArn',
    },
    permissionsTableReadRolePolicyArn: envConfig.permissionsTableReadRolePolicyArn,
  },
  functions: lambdas,
  environment: {
    ...envConfig.dotenvConfig,
    OPENID_ISSUER_URL: '${self:custom.auth0IssuerUrl}',
    ENTITY_GSI: '${self:custom.entityGsi}',
    TYPE_GSI: '${self:custom.typeGsi}',
    ENTITY_CACHE_GSI: '${self:custom.entityCacheGsi}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
    STAGE: '${opt:stage}',
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    AUTH0_TENANT: '${self:custom.auth0Tenant}',
  },
  resources: {
    ...policy.Resources,
    ...eventBridgeRule.Resources,
    ...lambdasCrossAccountInvocationPolicy(lambdas, region, false),
  },
});

module.exports = sls.build();
