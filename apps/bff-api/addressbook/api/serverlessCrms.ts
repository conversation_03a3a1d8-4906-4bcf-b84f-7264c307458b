import {
  ApiAppServerlessStack,
  ssmSharedVpcImport as vpcImport,
  CrmsApiAppEnvConfig,
  esbuildNestCommon,
  pluginsAppDefault,
  lambdasCrossAccountInvocationPolicy,
} from '@npco/component-bff-serverless';
import { policy } from './resources/common/policy';
import { lambdas as contactCommon } from './resources/common/contact/lambdas';
import { lambdas as contactEmail } from './resources/common/contactEmail/lambdas';
import { lambdas as contactPhone } from './resources/common/contactPhone/lambdas';
import { lambdas as contactMutation } from './resources/common/contactMutation/lambdas';
import { lambdas as tag } from './resources/common/tag/lambdas';
import { lambdas as subcategory } from './resources/common/subcategory/lambdas';
import { lambdas as label } from './resources/common/label/lambdas';
import { lambdas as paymentInstrument } from './resources/crms/paymentInstrument/lambdas';
import { lambdas as contactDcaTransaction } from './resources/crms/contactDcaTransaction/lambdas';
import { lambdas as contact } from './resources/crms/contact/lambdas';
import { lambdas as projection } from './resources/crms/projection/lambdas';
import { projectionSqs } from './resources/crms/projection/projectionSqs';

export const envConfig = new CrmsApiAppEnvConfig('resources/crms/config', true);
const { AWS_REGION: region } = process.env;

const lambdas = {
  ...contactCommon,
  ...contactEmail,
  ...contactPhone,
  ...contactMutation,
  ...tag,
  ...subcategory,
  ...label,
  ...paymentInstrument,
  ...contactDcaTransaction,
  ...contact,
  ...projection,
};

const sls = new ApiAppServerlessStack('addrbook', envConfig, {
  plugins: pluginsAppDefault,
  custom: {
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    componentName: 'crms',
    partName: 'engine',
    vpcImport,
    esbuild: esbuildNestCommon,
    entityGsi: '${cf:${self:custom.serviceName}-dynamodb.entityGsi}',
    typeGsi: '${cf:${self:custom.serviceName}-dynamodb.typeGsi}',
    sortKeyGsi: '${cf:${self:custom.serviceName}-dynamodb.sortKeyGsi}',
    sessionCacheTableName: 'none',
    auth0Tenant: 'none',
    globalEventBusName: '${env:GLOBAL_EVENT_BUS_NAME}',
    globalEventBusArn:
      'arn:aws:events:${self:provider.region}:${self:custom.accountId}:event-bus/${self:custom.globalEventBusName}',
    lambdaPath: 'src/lambdas/crms',
    sessionCacheTableDBPolicyArn: { Ref: 'AWS::NoValue' },
    permissionsTableReadRolePolicyArn: { Ref: 'AWS::NoValue' },
    domicileLookupTableReadRolePolicyArn: { Ref: 'AWS::NoValue' },
  },
  functions: lambdas,
  resources: {
    ...policy.Resources,
    ...projectionSqs.Resources,
    ...lambdasCrossAccountInvocationPolicy(lambdas, region, false),
  },
  outputs: {
    ...projectionSqs.Outputs,
  },
  environment: {
    ...envConfig.dotenvConfig,
    ENTITY_GSI: '${self:custom.entityGsi}',
    TYPE_GSI: '${self:custom.typeGsi}',
    SORT_KEY_GSI: '${self:custom.sortKeyGsi}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: envConfig.globalAccountId,
  },
});
module.exports = sls.build();
