import { ManagedPolicy } from '@npco/component-bff-serverless';

export const lambdas = {
  getLabelsHandler: {
    handler: '${self:custom.lambdaPath}.getLabelsHandler',
    name: 'getLabels',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        '${self:custom.sessionCacheTableDBPolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  addLabelHandler: {
    handler: '${self:custom.lambdaPath}.addLabelHandler',
    name: 'addLabel',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  updateLabelHandler: {
    handler: '${self:custom.lambdaPath}.updateLabelHandler',
    name: 'updateLabel',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  deleteLabelHandler: {
    handler: '${self:custom.lambdaPath}.deleteLabelHandler',
    name: 'deleteLabel',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
};
