import { ManagedPolicy } from '@npco/component-bff-serverless';

export const lambdas = {
  createContactHandler: {
    handler: '${self:custom.lambdaPath}.createContactHandler',
    name: 'createContactHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  linkContactHandler: {
    handler: '${self:custom.lambdaPath}.linkContactHandler',
    name: 'linkContactHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  unlinkContactHandler: {
    handler: '${self:custom.lambdaPath}.unlinkContactHandler',
    name: 'unlinkContactHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  linkCardholderHandler: {
    handler: '${self:custom.lambdaPath}.linkCardholderHandler',
    name: 'linkCardholderHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  unlinkCardholderHandler: {
    handler: '${self:custom.lambdaPath}.unlinkCardholderHandler',
    name: 'unlinkCardholderHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
};
