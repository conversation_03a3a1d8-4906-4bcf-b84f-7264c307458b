import { Arn, Action, ManagedPolicy } from '@npco/component-bff-serverless';
export const lambdas = {
  getContactHandler: {
    handler: '${self:custom.lambdaPath}.getContactHandler',
    name: 'getContactHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  getContactResolverHandler: {
    handler: '${self:custom.lambdaPath}.getContactResolverHandler',
    name: 'getContactResolverHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  getContactsHandler: {
    handler: '${self:custom.lambdaPath}.getContactsHandler',
    name: 'getContactsHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        getContactsDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.componentTableName}', '${self:custom.sortKeyGsi}')],
          },
        ],
      },
    },
    timeout: 30,
  },
  getLinkedContactsHandler: {
    handler: '${self:custom.lambdaPath}.getLinkedContactsHandler',
    name: 'getLinkedContactsHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  getContactByCardholder: {
    handler: '${self:custom.lambdaPath}.getContactByCardholder',
    name: 'getContactByCardholder',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  linkPaymentInstrumentWithContactHandler: {
    handler: '${self:custom.lambdaPath}.linkPaymentInstrumentWithContactHandler',
    name: 'linkPIWithContactHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  unlinkPaymentInstrumentFromContactHandler: {
    handler: '${self:custom.lambdaPath}.unlinkPaymentInstrumentFromContactHandler',
    name: 'unlinkPIFromContactHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
};
