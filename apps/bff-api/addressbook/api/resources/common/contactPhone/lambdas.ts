import { ManagedPolicy } from '@npco/component-bff-serverless';

export const lambdas = {
  getContactPhonesHandler: {
    handler: '${self:custom.lambdaPath}.getContactPhonesHandler',
    name: 'getContactPhones',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: {
      managed: [
        '${self:custom.permissionsTableReadRolePolicyArn}',
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        '${self:custom.sessionCacheTableDBPolicyArn}',
        { Ref: 'componentTableQueryRolePolicy' },
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
};
