import { ManagedPolicy } from '@npco/component-bff-serverless';

export const lambdas = {
  getContactForDcaTransactionResolverHandler: {
    handler: '${self:custom.lambdaPath}.getContactForDcaTransactionResolverHandler',
    name: 'getContactForDcaTransactionResolver',
    tracing: true,
    environment: { COMPONENT_TABLE: '${self:custom.componentTableName}' },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
  linkContactDcaTransactionSenderHandler: {
    handler: '${self:custom.lambdaPath}.linkContactDcaTransactionSenderHandler',
    name: 'linkContactDcaTransactionSender',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
  unlinkContactDcaTransactionSenderHandler: {
    handler: '${self:custom.lambdaPath}.unlinkContactDcaTransactionSenderHandler',
    name: 'unlinkContactDcaTransactionSender',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
};
