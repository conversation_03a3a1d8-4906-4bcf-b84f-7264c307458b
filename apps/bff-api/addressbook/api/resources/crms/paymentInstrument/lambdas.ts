import { ManagedPolicy } from '@npco/component-bff-serverless';
import { policy } from '../../common/policy';

export const lambdas = {
  getPaymentInstrumentHandler: {
    handler: '${self:custom.lambdaPath}.getPaymentInstrument',
    name: 'getPaymentInstrument',
    tracing: true,
    environment: { COMPONENT_TABLE: '${self:custom.componentTableName}' },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
  getPaymentInstrumentsHandler: {
    handler: '${self:custom.lambdaPath}.getPaymentInstruments',
    name: 'getPaymentInstruments',
    tracing: true,
    environment: { COMPONENT_TABLE: '${self:custom.componentTableName}' },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
  getPaymentInstrumentsWithoutContactHandler: {
    handler: '${self:custom.lambdaPath}.getPaymentInstrumentsWithoutContact',
    name: 'getPaymentInstrumentsWithoutContact',
    tracing: true,
    environment: { COMPONENT_TABLE: '${self:custom.componentTableName}' },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
  createPaymentInstrumentHandler: {
    handler: '${self:custom.lambdaPath}.createPaymentInstrument',
    name: 'createPaymentInstrument',
    tracing: true,
    environment: {
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: { managed: [ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
  updatePaymentInstrumentHandler: {
    handler: '${self:custom.lambdaPath}.updatePaymentInstrument',
    name: 'updatePaymentInstrument',
    tracing: true,
    environment: {
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: { managed: [ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
};
