import { ManagedPolicy } from '@npco/component-bff-serverless';

export const lambdas = {
  createContactsHandler: {
    handler: '${self:custom.lambdaPath}.createContactsHandler',
    name: 'createContactsHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
  updateContactHandler: {
    handler: '${self:custom.lambdaPath}.updateContactHandler',
    name: 'update<PERSON>ontactHand<PERSON>',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
  deleteContactHandler: {
    handler: '${self:custom.lambdaPath}.deleteContactHandler',
    name: 'deleteContactHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    policy: { managed: [{ Ref: 'componentTableQueryRolePolicy' }, ManagedPolicy.crossAccountInvocationPolicyArn] },
    timeout: 30,
  },
};
