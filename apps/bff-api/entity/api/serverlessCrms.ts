import {
  ApiAppServerlessStack,
  ServerlessPlugin,
  lambdasCrossAccountInvocationPolicy,
  ssmSharedVpcImport as vpcImport,
} from '@npco/component-bff-serverless';

import { CrmsApiAppEnvConfig } from './resources/common/crmsConfig';
import { pluginsApp } from './resources/common';
import { lambdas } from './resources/crms/lambdas';
import { resolvers } from './resources/crms/resolvers';
import { resources } from './resources/crms/resources';

type envVars = {
  [name: string]: string;
};

const envVarsToIgnore = [
  'METRIC_OUTSTANDING_AMOUNT_GSI',
  'METRIC_AMOUNT_GSI',
  'METRIC_LIFETIME_OUTSTANDING_AMOUNT_GSI',
  'METRIC_LIFETIME_AMOUNT_GSI',
  'METRIC_LIFETIME_GPV_GSI',
  'METRIC_AVERAGE_VALUE_GSI',
  'PGS_API_ENDPOINT_SSM',
];

const { AWS_REGION: region } = process.env;

const ignoreEnvVarsFromList = (fullEnvVarList: envVars, envVarsToIgnore: string[]) => {
  const filteredVars: envVars = {};

  Object.keys(fullEnvVarList).forEach((key) => {
    if (envVarsToIgnore.includes(key)) {
      return;
    }
    filteredVars[key] = fullEnvVarList[key];
  });

  return filteredVars;
};
export const envConfig = new CrmsApiAppEnvConfig('resources/crms/config', true);

const bffBankingServiceStackName = `${envConfig.service}-banking`;

const sls = new ApiAppServerlessStack('entity', envConfig, {
  plugins: [...pluginsApp, ServerlessPlugin.CanaryDeployments, 'serverless-dependson-plugin'],
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    vpcImport,
    siteNameGsi: envConfig.siteNameGsi,
    typeGsi: envConfig.typeGsi,
    siteGsi: envConfig.siteGsi,
    entityGsi: envConfig.entityGsi,
    iamUserKey: envConfig.iamUserKey,
    iamUserSecret: envConfig.iamUserSecret,
    fsApiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-fs2-fee-rate-api-url}',
    fsApiEndpointVersion: '${env:FS_API_ENDPOINT_VERSION}',
    riskRuleApiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5-endpoint}',
    riskRuleEndpointCategoryPath: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5-endpoint-path}',
    onboardingRiskRuleApiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule22-endpoint}',
    onboardingRiskRuleEndpointCategoryPath: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule22-endpoint-path}',
    riskRuleEndpointProhibitedMCCPath: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5a-endpoint-path}',
    cmsStackName: '${env:STATIC_ENV_NAME}-cms-cqrs-apiHandlers',
    cmsEndpoint: '${ssm:${env:STATIC_ENV_NAME}-bankingwrapper-engine-api-endpoint}',
    documentUploadsBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-document-uploads}',
    merchantPortalDocumentUploadBucketName: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-mp-doc-uploads}',
    tableStreamArn: '${cf:${self:custom.serviceName}-dynamodb.DeviceTableStreamArn}',
    merchantTableName: '${self:custom.serviceName}-${env:MERCHANT_TABLE}',
    merchantTableQueryRolePolicyArn: envConfig.merchantTableQueryRolePolicyArn,
    hubspotApiGatewayEndpoint: 'https://${env:CRMS_API_ENDPOINT}',
    cqrsCommandHandler: '${self:custom.cqrsStackName}-commandHandlers-handler',
    refundOverdraftEndpoint: '${ssm:${env:STATIC_ENV_NAME}-risk-cqrs-commandHndls-endpoint, ""}',
    refundOverdraftEndpointPath: '${ssm:${env:STATIC_ENV_NAME}-risk-cqrs-commandHndls-endpoint-path, ""}',
    getEntityDailyLimitLambda: `${bffBankingServiceStackName}-getEntityDailyLimit`,
    getEntityDailyLimitConfigLambda: `${bffBankingServiceStackName}-getEntityDailyLimitConfig`,
    updateEntityDailyLimitLambda: `${bffBankingServiceStackName}-updateEntityDailyLimit`,
    getSavingsAccountProductByEntityLambda: `${bffBankingServiceStackName}-getSavingsAccountProductByEntity`,
    selfieVerificationEnabled: '${env:SELFIE_CHECK_VERIFICATION_ENABLED}',
    dcaTransactionLookupSqsArn:
      'arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.service}-banking-sqs-dcaTransactionLookup',
    dcaTransactionLookupSqsUrl:
      'https://sqs.${self:provider.region}.amazonaws.com/${self:custom.accountId}/${self:custom.service}-banking-sqs-dcaTransactionLookup',
    dependsOn: {
      // Optional. Defaults to true, set to false to disable the plugin
      enabled: true,
      // Optional. Sets amount of lambda deployment parallelization plugin will attempt to create. Defaults to 1
      chains: 3,
    },
  },
  functions: lambdas,
  resources: {
    ...resolvers,
    ...resources,
    ...lambdasCrossAccountInvocationPolicy(lambdas, region, false),
  },
  environment: {
    ...ignoreEnvVarsFromList(envConfig.dotenvConfig, envVarsToIgnore),
    HUBSPOT_API_ENDPOINT: '${env:HUBSPOT_API_ENDPOINT}',
    HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
    COMPONENT_TABLE: envConfig.componentTableName,
    MULTI_ENTITY_ENABLED: '${env:MULTI_ENTITY_ENABLED}',
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}',
  },
});

module.exports = sls.build();
