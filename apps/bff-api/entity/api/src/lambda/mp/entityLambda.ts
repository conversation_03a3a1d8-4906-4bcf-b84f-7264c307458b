import { clientIpMiddleWare, appIdentityMiddleware } from '@npco/component-bff-core/dist/middleware';
import { PermissionRoot } from '@npco/component-bff-core/dist/middleware/lambdaMetadata';
import { mpMutationAttributionMiddleware } from '@npco/component-bff-core/dist/middleware/mpMutationAttributionMiddleware';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { debug, error } from '@npco/component-bff-core/dist/utils/logger';
import { createProjectionHandler } from '@npco/component-dbs-mp-common/dist/lambda';
import { type NestAppClientContext } from '@npco/component-dbs-mp-common/dist/middleware';
import { bootstrapNestJsMiddleware } from '@npco/component-dbs-mp-common/dist/middleware/bootstrapNestJsMiddleware';
import type {
  NestAppEntityContext,
  Entity,
  EntityDetails,
  RegisteredBusinessInput,
  UnregSoleTradBusinessInput,
  UnregBusinessInput,
  ConfirmBusinessInitDetailsInput,
} from '@npco/component-dbs-mp-common/dist/types';
import { type EntityOnboardingDetailsDto, type EntityOnboardingErrorDto } from '@npco/component-dto-entity';

import type { Context, Handler } from 'aws-lambda';

import { EntityModule, entityProjectionEvents, EntityService, OnboardingService } from '../../services';

import { entityCacheMiddleware, entityOnboardingStatusCheckMiddleware } from './middleware';

export const entityParams = { getAggregateId: (event: any) => event.detail.entityUuid };

export const checkForAdditionalEntityInfoHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (_event: { args: { entityUuid: string } }, context: Context) => {
    const { entityUuid, app } = context as NestAppEntityContext;
    return app.get(EntityService).checkForAdditionalEntityInfo(entityUuid);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);

export const requiredOnboardingDocumentsUploadHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (_event: { args: { entityUuid: string } }, context: Context) => {
    const { entityUuid, app } = context as NestAppEntityContext;
    return app.get(EntityService).requiredOnboardingDocumentsUpload(entityUuid);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);

export const regulatedMCCOnboardingHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (_event: { args: { entityUuid: string } }, context: Context) => {
    const { entityUuid, app } = context as NestAppEntityContext;
    return app.get(EntityService).regulatedMCCOnboarding(entityUuid);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);

export const createSoletraderEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { name: string } }, context: Context) => {
    const { entityUuid, app, clientIpAddress = '' } = context as NestAppClientContext;
    console.log('get context', context);
    return app.get(EntityService).createSoletraderEntity(entityUuid, event.args.name, clientIpAddress);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false), clientIpMiddleWare],
);

export const confirmUnregSoleTradInitDetailsHandler: Handler = withMiddlewaresV2(
  {
    component: ZellerComponent.MP,
    eventType: LambdaEventSource.APPSYNC,
    permissionsDerivedFrom: PermissionRoot.CUSTOMER_OWN_DATA,
  },
  async (event: { args: { input: UnregSoleTradBusinessInput } }, context: Context) => {
    const { entityUuid, customerUuid, app, clientIpAddress = '' } = context as NestAppClientContext;
    const input: ConfirmBusinessInitDetailsInput = {
      entityUuid,
      registeringIndividual: customerUuid,
      sourceIp: clientIpAddress,
    };
    return app.get(EntityService).confirmUnregisteredSoleTraderInitialDetails(input, event.args.input);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false), clientIpMiddleWare],
);

export const confirmUnregInitDetailsHandler: Handler = withMiddlewaresV2(
  {
    component: ZellerComponent.MP,
    eventType: LambdaEventSource.APPSYNC,
    permissionsDerivedFrom: PermissionRoot.CUSTOMER_OWN_DATA,
  },
  async (event: { args: { input: UnregBusinessInput } }, context: Context) => {
    const { entityUuid, customerUuid, app, clientIpAddress = '' } = context as NestAppClientContext;
    const input: ConfirmBusinessInitDetailsInput = {
      entityUuid,
      registeringIndividual: customerUuid,
      sourceIp: clientIpAddress,
    };
    return app.get(EntityService).confirmUnregisteredBusinessInitialDetails(input, event.args.input);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false), clientIpMiddleWare],
);

export const confirmEntityDetailsInitialHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { input: EntityDetails } }, context: Context) => {
    const { app, entityUuid, clientIpAddress = '' } = context as NestAppClientContext;
    return app.get(EntityService).confirmEntityDetailsInitial(entityUuid, event.args.input, clientIpAddress);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false), clientIpMiddleWare],
);

export const confirmRegBusInitDetailsHandler: Handler = withMiddlewaresV2(
  {
    component: ZellerComponent.MP,
    eventType: LambdaEventSource.APPSYNC,
    permissionsDerivedFrom: PermissionRoot.CUSTOMER_OWN_DATA,
  },
  async (event: { args: { input: RegisteredBusinessInput } }, context: Context) => {
    const { app, entityUuid, customerUuid, clientIpAddress = '' } = context as NestAppClientContext;
    const input: ConfirmBusinessInitDetailsInput = {
      entityUuid,
      registeringIndividual: customerUuid,
      sourceIp: clientIpAddress,
    };
    return app.get(EntityService).confirmRegisteredBusinessInitialDetails(input, event.args.input);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false), clientIpMiddleWare],
);

export const getEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  // Muilt-entity additional resolver
  async (_event, context: Context) => {
    const { entityUuid, app } = context as NestAppEntityContext;
    try {
      // this try catch is to unblock FE onboarding process, need to remove once they are fixed.
      // eslint-disable-next-line @typescript-eslint/return-await
      return await app.get(EntityService).getEntity(entityUuid);
    } catch (err: any) {
      /* istanbul ignore next */
      error(`get error call get entity: ${err?.stack}`);
      return { id: entityUuid };
    }
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);

export const updateEntityHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { entity: Entity } }, context: any) => {
    const { entityUuid, app } = context as NestAppEntityContext;
    return entityUuid === event.args.entity.id ? app.get(EntityService).updateEntity(event.args.entity) : false;
  },
  [
    bootstrapNestJsMiddleware(EntityModule),
    appIdentityMiddleware(true),
    entityOnboardingStatusCheckMiddleware,
    mpMutationAttributionMiddleware(),
  ],
);

export const searchEntityDetailsInitialHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { businessIdentifier: string } }, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    await app.get(EntityService).requestSearchEntityDetailsInitial(entityUuid, event.args.businessIdentifier);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false)],
);

export const searchEntityDetailsFullHandler: Handler = withMiddlewaresV2(
  {
    component: ZellerComponent.MP,
    eventType: LambdaEventSource.APPSYNC,
    permissionsDerivedFrom: PermissionRoot.CUSTOMER_OWN_DATA,
  },
  async (_, context: Context) => {
    const { app, entityUuid, customerUuid } = context as NestAppEntityContext;
    await app.get(EntityService).requestFullSearchEntityDetails(entityUuid, customerUuid);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false)],
);

export const saveEntityOnboardingDetailsHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  (event: { args: { input: EntityOnboardingDetailsDto } }, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(OnboardingService).saveEntityOnboardingDetails(entityUuid, event.args.input as any);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false)],
);

export const retrieveEntityOnboardingDetailsHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  (_event: any, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(OnboardingService).retrieveEntityOnboardingDetails(entityUuid);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false)],
);

export const saveEntityOnboardingErrorsHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  (event: { args: { input: EntityOnboardingErrorDto[] } }, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(OnboardingService).saveEntityOnboardingErrors(entityUuid, event.args.input);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(false)],
);

export const finaliseEntityOnboardingHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  (_event: any, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(EntityService).finaliseEntityOnboarding(entityUuid);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);

export const getEntityDocumentUploadUrlHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { entityUuid: string } }, context: Context) => {
    const { app } = context as NestAppEntityContext;
    return app.get(EntityService).getEntityDocumentUploadUrl(event.args.entityUuid);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);

export const getEntityDocumentUploadUrlsHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { fileNames: Array<string>; subject?: string } }, context: Context) => {
    const { app, entityUuid, customerUuid } = context as NestAppEntityContext;
    return app
      .get(EntityService)
      .getEntityDocumentUploadUrls(entityUuid, event.args.fileNames, customerUuid, event.args.subject);
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);

export const entityProjectionHandler: Handler = createProjectionHandler(entityProjectionEvents, EntityModule, [
  entityCacheMiddleware,
]);

export const getReferrerNameHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC, isUnauthenticatedAccessAllowed: true },
  async (event: { args: { referralCode: string } }, context: Context) => {
    const { app } = context as NestAppEntityContext;
    try {
      const { name } = await app.get(EntityService).getEntity(event.args.referralCode);
      return name;
    } catch (err: any) {
      debug(`get error call get referrer name.  error: ${JSON.stringify(err)}`);
      return null;
    }
  },
  [bootstrapNestJsMiddleware(EntityModule)],
);

export const getBankingMigrationStateHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (_event: any, _context: Context) => {
    return 'COMPLETED';
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);

export const setEztaTermsOfServiceHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.MP, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { input: { accepted: boolean } } }, context: Context) => {
    const { app, entityUuid, customerUuid } = context as NestAppEntityContext;
    return app.get(EntityService).setAcceptTermsOfService({
      accepted: event.args.input.accepted,
      entityUuid,
      customerUuid,
    });
  },
  [bootstrapNestJsMiddleware(EntityModule), appIdentityMiddleware(true)],
);
