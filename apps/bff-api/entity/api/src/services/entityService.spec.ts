import { S3Client } from '@npco/component-bff-core/dist/aws/s3Client';
import { NotFoundError } from '@npco/component-bff-core/dist/error';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';
import { mockFeeRates } from '@npco/component-dbs-mp-common/dist/entity/testcases/feeUtils';
import type { Entity, ValidateFinaliseEntityOnboardingResult } from '@npco/component-dbs-mp-common/dist/types';
import { EntityOnboardingResultStatus } from '@npco/component-dbs-mp-common/dist/types';
import { DomainURImap, EntityType, PaymentType } from '@npco/component-dto-core';
import type { Customer } from '@npco/component-dto-customer';
import { Category, CategoryGroup, OnboardingStatus, type EntityPaymentSettingsDto } from '@npco/component-dto-entity';

import axios from 'axios';
import { anything, capture, deepEqual, instance, mock, resetCalls, verify, when } from 'ts-mockito';
import { v4 as uuidv4 } from 'uuid';

import { ModifiedEntityService } from './__mocks__/modifiedEntityService';
import type { EntityDb } from './entityDb';
import { EntityService } from './entityService';
import { ReferralService } from './referral';

jest.mock('@npco/component-dbs-mp-common/dist/utils/retryUtil');
jest.mock('@npco/component-dbs-mp-common/dist/utils/sleep');
jest.mock('@npco/component-dbs-mp-common/dist/dynamodb/dynamodbService');
jest.mock('@npco/component-bff-core/dist/dynamodb/bffDynamoDbClient');

const ESTIMATED_ANNUAL_REVENUE_THRESHOLD = 50000;

jest.mock('axios');

jest.mock('@npco/component-bff-core/dist/utils/logger', () => {
  const originalModule = jest.requireActual('@npco/component-bff-core/dist/utils/logger');
  return {
    __esModule: true, // Use it when dealing with esModules
    ...originalModule,
    debug: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
  };
});

jest.mock('@npco/component-bff-core/dist/aws/s3Client', () => {
  const mockS3 = {
    getSignedUrl: jest.fn(),
    headObject: jest.fn(),
  };
  return { S3Client: jest.fn(() => mockS3) };
});
const getRegisteringIndividual = jest.fn();
jest.mock('@npco/component-dbs-mp-common/dist/customer/getRegisteringIndividual', () => ({
  getRegisteringIndividual: () => getRegisteringIndividual(),
}));
const mockGetCustomerDbItemOrThrow = jest.fn();
jest.mock('@npco/component-dbs-mp-common/dist/customer/getCustomerDbItem', () => ({
  getCustomerDbItemOrThrow: () => mockGetCustomerDbItemOrThrow(),
}));

const setTestEnvironmentVariables = () => {
  process.env.ESTIMATED_ANNUAL_REVENUE_THRESHOLD = ESTIMATED_ANNUAL_REVENUE_THRESHOLD as unknown as string;
};

describe('Entity service test suite', () => {
  const mockEntityDb = mock<EntityDb>();
  const mockReferralService: ReferralService = mock(ReferralService);
  const mockLambdaResponse = jest.fn().mockReturnValue(Promise.resolve('invoke success'));
  const mockLambdaService = {
    lambda: {
      invokeCommand: jest.fn().mockReturnValue({ promise: mockLambdaResponse }),
    },
  } as any;
  const mockPrimaryAccountHolder = uuidv4();
  let entityService: EntityService;
  const entityUuid = uuidv4();
  const registeringIndividual = uuidv4();
  const entity = {
    name: 'test entity',
    type: EntityType.INDIVIDUAL,
    paymentSettings: {
      cnpPaymentLimits: {
        maximum: 444,
        minimum: 333,
      },
      motoPaymentLimits: {
        maximum: 666,
        minimum: 555,
      },
      paymentLimits: {
        maximum: 222,
        minimum: 111,
      },
      cpocPaymentLimits: {
        maximum: 888,
        minimum: 777,
      },
    },
    country: 'AUS',
  };
  const sourceIp = '127.0.0.1';
  const notFound = new NotFoundError('Account not found.', entityUuid);
  setTestEnvironmentVariables();
  const envService = new EnvironmentService();

  let modifiedEntityService: ModifiedEntityService;
  beforeEach(() => {
    entityService = new EntityService({} as any, envService, mockLambdaService, instance(mockReferralService));
    entityService.entityDb = instance(mockEntityDb);
    (axios as any).post.mockResolvedValue({ status: 200 } as any);
    (axios as any).patch.mockResolvedValue({ status: 200 } as any);
    modifiedEntityService = new ModifiedEntityService({} as any, envService, mockLambdaService, {} as any);
    modifiedEntityService.entityDb = instance(mockEntityDb);
    getRegisteringIndividual.mockResolvedValue({ customerUuid: mockPrimaryAccountHolder });
  });

  it('should handle createSoletraderEntity', async () => {
    when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
      Promise.resolve({ entityUuid, id: entityUuid, ...entity } as any),
    );
    const res = (await entityService.createSoletraderEntity(entityUuid, entity.name, sourceIp)) as any;
    expect(res.id).toBe(entityUuid);
  });

  it('should handle createSoletraderEntity error response', async () => {
    (axios as any).post.mockResolvedValue({ status: 400 } as any);
    when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
      Promise.resolve({ entityUuid, id: entityUuid, ...entity } as any),
    );
    await expect(entityService.createSoletraderEntity(entityUuid, entity.name, sourceIp)).rejects.toThrowError();
  });

  describe('should handle confirmUnregisteredSoleTraderInitialDetails', () => {
    let createEntitySpy: jest.SpyInstance;
    beforeEach(() => {
      createEntitySpy = jest.spyOn(modifiedEntityService.amsApiService, 'createEntity');
    });
    it('should handle confirmUnregisteredSoleTraderInitialDetails error response', async () => {
      (axios as any).post.mockResolvedValue({ status: 400 } as any);
      const mockEntity = {
        ...entity,
      };
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          entityUuid,
          id: entityUuid,
          ...mockEntity,
        } as any),
      );
      await expect(
        entityService.confirmUnregisteredSoleTraderInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        ),
      ).rejects.toThrowError();
    });
    it('should handle correctly if entity uuid not exist when confirmUnregisteredSoleTraderInitialDetails', async () => {
      const mockEntity = {
        type: EntityType.INDIVIDUAL,
        country: 'AUS',
        businessDetails: {
          ausBusiness: {
            abn: 'abn',
          },
        },
        registeringIndividual: 'cust-uuid',
      };
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          ...mockEntity,
        } as any),
      );
      expect(createEntitySpy).not.toBeCalled();
      await modifiedEntityService.confirmUnregisteredSoleTraderInitialDetails(
        { sourceIp, registeringIndividual: 'cust-uuid' },
        mockEntity as any,
      );
      expect(createEntitySpy).toBeCalledTimes(1);
      expect(createEntitySpy).toBeCalledWith(
        expect.not.objectContaining({ entityUuid: expect.anything() }),
        Domicile.AU,
      );
      expect(createEntitySpy).toBeCalledWith(
        expect.objectContaining({
          type: EntityType.INDIVIDUAL,
          registeringIndividual: 'cust-uuid',
        }),
        Domicile.AU,
      );
      createEntitySpy.mockClear();
    });
  });

  describe('should handle confirmUnregisteredBusinessInitialDetails', () => {
    let spy: jest.SpyInstance;
    let createEntitySpy: jest.SpyInstance;
    beforeEach(() => {
      spy = jest.spyOn(modifiedEntityService.amsApiService, 'create');
      createEntitySpy = jest.spyOn(modifiedEntityService.amsApiService, 'createEntity');
    });

    it('should handle confirmUnregisteredBusinessInitialDetails if country is GBR and type is TRUST', async () => {
      const mockEntity = {
        name: 'test entity',
        type: EntityType.TRUST,
        country: 'GBR',
      };

      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          entityUuid,
          id: entityUuid,
          ...mockEntity,
        } as any),
      );
      expect(spy).not.toBeCalled();
      const res = await modifiedEntityService.confirmUnregisteredBusinessInitialDetails(
        { entityUuid, sourceIp, registeringIndividual },
        mockEntity as any,
      );
      expect(spy).toBeCalledTimes(1);
      expect(spy).toBeCalledWith(
        entityUuid,
        expect.objectContaining({
          name: 'test entity',
          type: EntityType.TRUST,
        }),
        Domicile.GB,
      );
      spy.mockClear();
      expect(res.id).toBe(entityUuid);
    });
    it('should handle confirmUnregisteredBusinessInitialDetails if country is not GBR', async () => {
      try {
        const mockEntity = {
          name: 'test entity',
          type: EntityType.TRUST,
          country: 'AUS',
        };
        await entityService.confirmUnregisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        );
      } catch (e: any) {
        expect(e.errorMessage).toEqual('Only GBR country is supported.');
      }
    });
    it('should handle confirmUnregisteredBusinessInitialDetails if entity type is not TRUST', async () => {
      try {
        const mockEntity = {
          name: 'test entity',
          type: EntityType.INDIVIDUAL,
          country: 'GBR',
        };
        await entityService.confirmUnregisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        );
      } catch (e: any) {
        expect(e.errorMessage).toEqual('Only TRUST entity type is supported.');
      }
    });
    it('should handle confirmUnregisteredBusinessInitialDetails error response', async () => {
      (axios as any).post.mockResolvedValue({ status: 400 } as any);
      const mockEntity = {
        ...entity,
      };
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          entityUuid,
          id: entityUuid,
          ...mockEntity,
        } as any),
      );
      await expect(
        entityService.confirmUnregisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        ),
      ).rejects.toThrowError();
    });
    it('should handle correctly if entity uuid not exist when confirmUnregisteredBusinessInitialDetails', async () => {
      const mockEntity = {
        name: 'test entity',
        type: EntityType.TRUST,
        country: 'GBR',
        businessDetails: {
          gbrBusiness: {
            crn: 'crn',
          },
        },
        registeringIndividual: 'cust-uuid',
      };
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          ...mockEntity,
        } as any),
      );
      expect(createEntitySpy).not.toBeCalled();
      await modifiedEntityService.confirmUnregisteredBusinessInitialDetails(
        { sourceIp, registeringIndividual: 'cust-uuid' },
        mockEntity as any,
      );
      expect(createEntitySpy).toBeCalledTimes(1);
      expect(createEntitySpy).toBeCalledWith(
        expect.not.objectContaining({ entityUuid: expect.anything() }),
        Domicile.GB,
      );
      expect(createEntitySpy).toBeCalledWith(
        expect.objectContaining({
          name: 'test entity',
          type: EntityType.TRUST,
          registeringIndividual: 'cust-uuid',
        }),
        Domicile.GB,
      );
      createEntitySpy.mockClear();
    });
  });

  it('should handle selectDepositAccount', async () => {
    when(mockEntityDb.getDebitCardAccount).thenReturn(() => Promise.resolve({ entityUuid, ...entity } as any));
    (axios as any).patch.mockResolvedValue({ status: 200 } as any);
    expect(await entityService.selectDepositAccount('entityUuid', 'accountId', true)).toBe(true);
  });

  it('should handle selectDepositAccount error response', async () => {
    when(mockEntityDb.getDebitCardAccount).thenReturn(() => Promise.resolve({ entityUuid, ...entity } as any));
    (axios as any).patch.mockResolvedValue({ status: 400 } as any);
    await expect(entityService.selectDepositAccount('entityUuid', 'accountId', false)).rejects.toThrow(
      'select deposit account failed accountId',
    );
  });

  it('should throw error if debit card account not found', async () => {
    when(mockEntityDb.getDebitCardAccount).thenReturn(() => {
      throw new Error('');
    });
    (axios as any).patch.mockResolvedValue({ status: 200 } as any);
    await expect(entityService.selectDepositAccount('entityUuid', 'accountId', true)).rejects.toThrow('');
  });

  describe('should handle confirmEntityDetailsInitial', () => {
    let spy: jest.SpyInstance;
    beforeEach(() => {
      spy = jest.spyOn(modifiedEntityService.amsApiService, 'create');
    });

    describe('should handle the manualEntry parameter correctly', () => {
      it('should handle without manualEntry', async () => {
        const mockEntity = {
          name: 'test entity',
          type: EntityType.INDIVIDUAL,
          abn: 'abn',
          acn: 'acn',
        };

        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        expect(spy).not.toBeCalled();
        const res = await modifiedEntityService.confirmEntityDetailsInitial(entityUuid, mockEntity as any, sourceIp);
        expect(spy).toBeCalledTimes(1);
        expect(spy).toBeCalledWith(entityUuid, expect.objectContaining({ ...mockEntity }));
        spy.mockClear();
        expect(res.id).toBe(entityUuid);
      });

      it('should handle with manualEntry', async () => {
        const mockEntity = {
          name: 'test entity',
          type: EntityType.INDIVIDUAL,
          abn: 'abn',
          acn: 'acn',
          manualEntry: true,
        };

        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        expect(spy).not.toBeCalled();
        const res = await modifiedEntityService.confirmEntityDetailsInitial(entityUuid, mockEntity as any, sourceIp);
        expect(spy).toBeCalledTimes(1);
        expect(spy).toBeCalledWith(entityUuid, expect.objectContaining({ ...mockEntity }));
        spy.mockClear();
        expect(res.id).toBe(entityUuid);
      });
    });

    it('should handle confirmEntityDetailsInitial with ABN and ACN', async () => {
      const mockEntity = {
        name: 'test entity',
        type: EntityType.INDIVIDUAL,
        abn: 'abn',
        acn: 'acn',
      };
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          entityUuid,
          id: entityUuid,
          ...mockEntity,
        } as any),
      );
      const res = (await entityService.confirmEntityDetailsInitial(entityUuid, mockEntity as any, sourceIp)) as any;
      expect(res.id).toBe(entityUuid);
    });

    it('should handle confirmEntityDetailsInitial with ABN only', async () => {
      const mockEntity = {
        name: 'test entity',
        type: EntityType.INDIVIDUAL,
        abn: 'abn',
      };
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          entityUuid,
          id: entityUuid,
          ...mockEntity,
        } as any),
      );
      const res = (await entityService.confirmEntityDetailsInitial(entityUuid, mockEntity as any, sourceIp)) as any;
      expect(res.id).toBe(entityUuid);
    });

    it('should handle confirmEntityDetailsInitial with ACN only', async () => {
      const mockEntity = {
        name: 'test entity',
        type: EntityType.INDIVIDUAL,
        acn: 'acn',
      };
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          entityUuid,
          id: entityUuid,
          ...mockEntity,
        } as any),
      );
      const res = (await entityService.confirmEntityDetailsInitial(entityUuid, mockEntity as any, sourceIp)) as any;
      expect(res.id).toBe(entityUuid);
    });

    it('should handle confirmEntityDetailsInitial error message if ABN and ACN is missing', async () => {
      try {
        await entityService.confirmEntityDetailsInitial(entityUuid, entity as any, sourceIp);
      } catch (e: any) {
        expect(e.errorMessage).toEqual('ABN or ACN is required.');
      }
    });

    it('should handle confirmEntityDetailsInitial error response', async () => {
      (axios as any).post.mockResolvedValue({ status: 400 } as any);
      const mockEntity = {
        name: 'test entity',
        type: EntityType.INDIVIDUAL,
        acn: 'acn',
      };
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          entityUuid,
          id: entityUuid,
          ...mockEntity,
        } as any),
      );
      await expect(
        entityService.confirmEntityDetailsInitial(entityUuid, mockEntity as any, sourceIp),
      ).rejects.toThrowError();
    });
  });

  describe('should handle confirmRegisteredBusinessInitialDetails', () => {
    let spy: jest.SpyInstance;
    let createEntitySpy: jest.SpyInstance;
    beforeEach(() => {
      spy = jest.spyOn(modifiedEntityService.amsApiService, 'create');
      createEntitySpy = jest.spyOn(modifiedEntityService.amsApiService, 'createEntity');
    });

    describe('should handle the manualEntry parameter correctly', () => {
      it('should handle without manualEntry', async () => {
        const mockEntity = {
          ...entity,
          businessDetails: {
            ausBusiness: {
              abn: 'abn',
              acn: 'acn',
            },
          },
        };

        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        expect(spy).not.toBeCalled();
        const res = await modifiedEntityService.confirmRegisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        );
        expect(spy).toBeCalledTimes(1);
        expect(spy).toBeCalledWith(
          entityUuid,
          expect.objectContaining({
            name: 'test entity',
            type: EntityType.INDIVIDUAL,
          }),
          Domicile.AU,
        );
        spy.mockClear();
        expect(res.id).toBe(entityUuid);
      });

      it('should handle with manualEntry', async () => {
        const mockEntity = {
          ...entity,
          businessDetails: {
            ausBusiness: {
              abn: 'abn',
              acn: 'acn',
            },
          },
          manualEntry: true,
        };

        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        expect(spy).not.toBeCalled();
        const res = await modifiedEntityService.confirmRegisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        );
        expect(spy).toBeCalled();
        expect(res.id).toBe(entityUuid);
      });
    });

    describe('should handle the abn/acn parameter correctly', () => {
      it('should handle confirmRegisteredBusinessInitialDetails with ABN and ACN', async () => {
        const mockEntity = {
          ...entity,
          businessDetails: {
            ausBusiness: {
              abn: 'abn',
              acn: 'acn',
            },
          },
        };
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        const res = (await entityService.confirmRegisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        )) as any;
        expect(res.id).toBe(entityUuid);
      });

      it('should handle confirmRegisteredBusinessInitialDetails with ABN only', async () => {
        const mockEntity = {
          ...entity,
          businessDetails: {
            ausBusiness: {
              abn: 'abn',
            },
          },
        };
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        const res = (await entityService.confirmRegisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        )) as any;
        expect(res.id).toBe(entityUuid);
      });

      it('should handle confirmRegisteredBusinessInitialDetails with ACN only', async () => {
        const mockEntity = {
          ...entity,
          businessDetails: {
            ausBusiness: {
              acn: 'acn',
            },
          },
        };
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        const res = (await entityService.confirmRegisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        )) as any;
        expect(res.id).toBe(entityUuid);
      });

      it('should handle confirmRegisteredBusinessInitialDetails error message if ABN and ACN is missing', async () => {
        try {
          await entityService.confirmRegisteredBusinessInitialDetails(
            { entityUuid, sourceIp, registeringIndividual },
            entity as any,
          );
        } catch (e: any) {
          expect(e.errorMessage).toEqual('ABN or ACN is required.');
        }
      });

      it('should handle correctly if entity uuid not exist when confirmRegisteredBusinessInitialDetails', async () => {
        const mockEntity = {
          name: 'test entity',
          type: EntityType.INDIVIDUAL,
          country: 'AUS',
          businessDetails: {
            ausBusiness: {
              abn: 'abn',
            },
          },
          registeringIndividual: 'cust-uuid',
        };
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...mockEntity,
          } as any),
        );
        expect(createEntitySpy).not.toBeCalled();
        await modifiedEntityService.confirmRegisteredBusinessInitialDetails(
          { sourceIp, registeringIndividual: 'cust-uuid' },
          mockEntity as any,
        );
        expect(createEntitySpy).toBeCalledTimes(1);
        expect(createEntitySpy).toBeCalledWith(
          expect.not.objectContaining({ entityUuid: expect.anything() }),
          Domicile.AU,
        );
        expect(createEntitySpy).toBeCalledWith(
          expect.objectContaining({
            name: 'test entity',
            type: EntityType.INDIVIDUAL,
            registeringIndividual: 'cust-uuid',
          }),
          Domicile.AU,
        );
        createEntitySpy.mockClear();
      });

      it('should handle confirmRegisteredBusinessInitialDetails error response', async () => {
        (axios as any).post.mockResolvedValue({ status: 400 } as any);
        const mockEntity = {
          ...entity,
          businessDetails: {
            ausBusiness: {
              acn: 'acn',
            },
          },
        };
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        await expect(
          entityService.confirmRegisteredBusinessInitialDetails(
            { entityUuid, sourceIp, registeringIndividual },
            mockEntity as any,
          ),
        ).rejects.toThrowError();
      });

      it('should handle confirmRegisteredBusinessInitialDetails error message if CRN is present', async () => {
        const mockEntity = {
          name: 'test entity',
          type: EntityType.INDIVIDUAL,
          country: 'GBR',
          businessDetails: {
            gbrBusiness: {
              crn: 'crn',
            },
          },
        };
        const res = await modifiedEntityService.confirmRegisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        );
        expect(spy).toBeCalled();
        expect(res.id).toBe(entityUuid);
      });

      it('should handle confirmRegisteredBusinessInitialDetails error message if CRN is absent', async () => {
        try {
          const mockEntity = {
            name: 'test entity',
            type: EntityType.INDIVIDUAL,
            country: 'GBR',
          };
          await entityService.confirmRegisteredBusinessInitialDetails(
            { entityUuid, sourceIp, registeringIndividual },
            mockEntity as any,
          );
        } catch (e: any) {
          expect(e.errorMessage).toEqual('CRN is required.');
        }
      });
    });

    describe('should handle the createEntity function correctly', () => {
      it('should call createEntity correclty when cohort is absent', async () => {
        const mockEntity = {
          ...entity,
          businessDetails: {
            ausBusiness: { abn: 'abn', acn: 'acn' },
          },
        };

        when(mockEntityDb.getEntityOrThrow).thenReturn(
          await Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );

        const res = await modifiedEntityService.confirmRegisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        );
        expect(spy).toBeCalled();
        expect(res.id).toBe(entityUuid);
      });

      it('should call createEntity correctly when cohort is present', async () => {
        const mockEntity = {
          ...entity,
          businessDetails: {
            ausBusiness: { abn: 'abn', acn: 'acn' },
          },
          cohort: ['STARTUP'],
        };

        when(mockEntityDb.getEntityOrThrow).thenReturn(
          await Promise.resolve({
            entityUuid,
            id: entityUuid,
            ...mockEntity,
          } as any),
        );
        const res = await modifiedEntityService.confirmRegisteredBusinessInitialDetails(
          { entityUuid, sourceIp, registeringIndividual },
          mockEntity as any,
        );
        expect(spy).toBeCalled();
        expect(res.id).toBe(entityUuid);
      });
    });

    describe('should handle the prepareEntityDetails function correctly', () => {
      it('should handle if category and categoryGroup is undefined', () => {
        const mockEntity = {
          ...entity,
        };
        const result = entityService.prepareEntityDetails(mockEntity as any);
        expect(result).not.toHaveProperty('category');
        expect(result).not.toHaveProperty('categoryGroup');
      });

      it('should map valid category and categoryGroup correctly', () => {
        const mockEntity = {
          ...entity,
          category: Category.BEAUTYSALON,
          categoryGroup: CategoryGroup.BEAUTY,
        };
        const result = entityService.prepareEntityDetails(mockEntity as any);
        expect(result.category).toEqual('001');
        expect(result.categoryGroup).toEqual('01');
      });
    });

    describe('should handle the updateEntity function correctly', () => {
      it('should handle updateEntity with cohort', async () => {
        const entityDto = {
          entityUuid: uuidv4(),
          cohort: ['STARTUP'],
        };

        await expect(entityService.updateEntity(entityDto as any)).resolves.toBe(true);
      });
    });
  });

  it('should handle get entity', async () => {
    when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(entity as any));
    const res = await entityService.getEntity('entityUuid');
    expect(res).toBe(entity);
  });

  it('should handle get entity with cohort conversion', async () => {
    when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
      Promise.resolve({
        ...entity,
        cohort: [{ code: 'STARTUP' }],
      } as any),
    );
    const res = await entityService.getEntity('entityUuid');
    expect(res).toStrictEqual({
      ...entity,
      cohort: ['STARTUP'],
    });
  });

  it('should handle get entity when the entity record not created', async () => {
    when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.reject(notFound));
    await expect(entityService.getEntity('mock')).rejects.toThrowError();
  });

  it.each([
    [PaymentType.CNP, 'cnpPaymentLimits'],
    [PaymentType.PBLINT, 'cnpPaymentLimits'],
    [PaymentType.PBLLOC, 'cnpPaymentLimits'],
    [PaymentType.XINVINT, 'cnpPaymentLimits'],
    [PaymentType.XINVLOC, 'cnpPaymentLimits'],
    [PaymentType.ZINVINT, 'cnpPaymentLimits'],
    [PaymentType.ZINVLOC, 'cnpPaymentLimits'],
    [PaymentType.MOTO, 'motoPaymentLimits'],
    [PaymentType.VT, 'motoPaymentLimits'],
    [PaymentType.CPOC, 'cpocPaymentLimits'],
    [PaymentType.DEFAULT, 'paymentLimits'],
    [PaymentType.CP, 'paymentLimits'],
  ])('Should get entity payment limit for %s', async (paymentType, limitField) => {
    when(mockEntityDb.getEntity).thenReturn(() => Promise.resolve(entity as any));
    const res = await entityService.getPaymentLimits('entityUuid', paymentType);
    expect(res).toBe((entity.paymentSettings as any)[limitField]);
  });

  it('should throw error if not able to get payment limits', async () => {
    when(mockEntityDb.getEntity).thenReturn(() => Promise.resolve(null));
    const paymentLimits = await entityService.getPaymentLimits('entityUuid', PaymentType.CNP);
    expect(paymentLimits).toBeUndefined();
  });

  describe('lambda invoke', () => {
    const invokeCommand = jest.fn().mockReturnValue(Promise.resolve({ status: true }));
    beforeEach(() => {
      entityService = new EntityService(
        {} as any,
        { cqrsCmds: DomainURImap } as any,
        { invokeCommand } as any,
        {} as any,
      );
    });

    it('should call initialSearchRequestedCommandHandler via lambdaService.invokeCommand', async () => {
      const res = await entityService.requestSearchEntityDetailsInitial('', '');
      expect(res.status).toBe(true);
    });

    it('should handle failed invocation of initialSearchRequestedCommandHandler', async () => {
      invokeCommand.mockReturnValue(Promise.resolve({ status: false }));
      const res = await entityService.requestSearchEntityDetailsInitial('', '');
      expect(res.status).toBe(false);
    });

    it('should handle failed invocation of fullSearchRequestedCommandHandler', async () => {
      invokeCommand.mockReturnValue(Promise.resolve({ status: false }));
      const res = await entityService.requestFullSearchEntityDetails('', '');
      expect(res.status).toBe(false);
    });

    it('should call fullSearchRequestedCommandHandler via lambdaService.invokeCommand', async () => {
      invokeCommand.mockReturnValue(Promise.resolve({ status: true }));
      const res = await entityService.requestFullSearchEntityDetails('', '');
      expect(res.status).toBe(true);
    });
  });

  it('should handle prepare dto with the account Status', async () => {
    let entityDto: any = {
      entityUuid: uuidv4(),
      type: EntityType.INDIVIDUAL,
      name: 'name',
      canAcquire: true,
    };
    expect(entityService.prepareUpdateDto(entityDto as any).accountStatus).toEqual({
      canAcquire: true,
      canAcquireMoto: undefined,
      canAcquireCnp: undefined,
      canAcquireMobile: undefined,
      canRefund: undefined,
      canStandIn: undefined,
      hadDirectDebitFailure: undefined,
      hadForcedRefund: undefined,
      hasChargeback: undefined,
      hasDirectDebitRequest: undefined,
    });
    entityDto = {
      entityUuid: uuidv4(),
      type: EntityType.INDIVIDUAL,
      name: 'name',
      canAcquire: false,
    };
    expect(entityService.prepareUpdateDto(entityDto as any).accountStatus).toEqual({
      canAcquire: false,
      canAcquireMoto: undefined,
      canAcquireCnp: undefined,
      canAcquireMobile: undefined,
      canRefund: undefined,
      canStandIn: undefined,
      hadDirectDebitFailure: undefined,
      hadForcedRefund: undefined,
      hasChargeback: undefined,
      hasDirectDebitRequest: undefined,
    });
    entityDto = {
      entityUuid: uuidv4(),
      type: EntityType.INDIVIDUAL,
      name: 'name',
    };
    expect(entityService.prepareUpdateDto(entityDto as any).accountStatus).toBeUndefined();
  });

  it('should handle prepare dto with the category group enum changes', async () => {
    const entityDto = {
      entityUuid: uuidv4(),
      type: EntityType.INDIVIDUAL,
      name: 'name',
      categoryGroup: 'BEAUTY',
    };
    const prepareUpdate = entityService.prepareUpdateDto(entityDto as any);
    expect(prepareUpdate.categoryGroup).toEqual('01');
  });

  it('should handle prepare dto with incorrect category group enum changes', async () => {
    const entityDto = {
      entityUuid: uuidv4(),
      type: EntityType.INDIVIDUAL,
      name: 'name',
      categoryGroup: 'INCORRECT',
    };
    const prepareUpdate = entityService.prepareUpdateDto(entityDto as any);
    expect(prepareUpdate.categoryGroup).toEqual('INCORRECT');
  });

  it('should handle prepare dto with the category enum changes', async () => {
    const entityDto = {
      entityUuid: uuidv4(),
      type: EntityType.INDIVIDUAL,
      name: 'name',
      category: 'FESTIVALS',
    };
    const prepareUpdate = entityService.prepareUpdateDto(entityDto as any);
    expect(prepareUpdate.category).toEqual('001');
  });

  it('should handle prepare dto with incorrect category enum changes', async () => {
    const entityDto = {
      entityUuid: uuidv4(),
      type: EntityType.INDIVIDUAL,
      name: 'name',
      category: 'INCORRECT',
    };
    const prepareUpdate = entityService.prepareUpdateDto(entityDto as any);
    expect(prepareUpdate.category).toEqual('INCORRECT');
  });

  describe('finaliseOnboarding', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return COMPLETED when evaluateOnboardingDetails returns "OnboardingStatus.ONBOARDED"', async () => {
      // mocking PATCH request to /finaliseEntityOnboarding response to 'OnboardingStatus.ONBOARDED'
      (axios as any).patch.mockResolvedValue({ data: OnboardingStatus.ONBOARDED });
      await expect(modifiedEntityService.finaliseEntityOnboarding(entityUuid)).resolves.toEqual({
        entityUuid,
        result: EntityOnboardingResultStatus.COMPLETED,
      });
    });

    it('should return IN_REVIEW when evaluateOnboardingDetails returns "OnboardingStatus.REVIEW"', async () => {
      // mocking PATCH request to /finaliseEntityOnboarding response to 'OnboardingStatus.REVIEW'
      (axios as any).patch.mockResolvedValue({ data: OnboardingStatus.REVIEW });
      await expect(modifiedEntityService.finaliseEntityOnboarding(entityUuid)).resolves.toEqual({
        entityUuid,
        result: EntityOnboardingResultStatus.IN_REVIEW,
      });
    });

    it('should throw an error when evaluateOnboardingDetails throws an error', async () => {
      (axios as any).patch.mockRejectedValue(new Error('[400] No registering individual found'));
      await expect(modifiedEntityService.finaliseEntityOnboarding(entityUuid)).rejects.toThrow(
        'Failed to evaluate onboarding details',
      );
    });

    it('should call saveEntityUpdatedProjection if the component is mp', async () => {
      const mockedEntityService = new ModifiedEntityService(
        {} as any,
        { componentName: 'mp' } as any, // Set env variable
        mockLambdaService,
        {} as any,
      );
      mockedEntityService.entityDb = instance(mockEntityDb);
      const saveEntityUpdatedProjectionSpy = jest.spyOn(mockedEntityService, 'saveEntityUpdatedProjection');
      await expect(mockedEntityService.finaliseEntityOnboarding(entityUuid)).resolves.toEqual({
        entityUuid,
        result: EntityOnboardingResultStatus.IN_REVIEW,
      });
      expect(saveEntityUpdatedProjectionSpy).toHaveBeenCalled();
    });
  });

  describe('validateFinaliseEntityOnboarding', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    const validEntity = {
      id: uuidv4(),
      name: 'name',
      registeredAddress: {
        street1: 'street1',
        suburb: 'sunshine',
        state: 'state',
        postcode: '3020',
        country: 'Australia',
      },
      businessAddress: {
        street1: 'street1',
        suburb: 'sunshine',
        state: 'state',
        postcode: '3020',
        country: 'Australia',
      },
      onboardingStatus: OnboardingStatus.NONE,
    } as Entity;
    const validFinaliseEntityOnboardingState: ValidateFinaliseEntityOnboardingResult = {
      missingIndividualName: false,
      missingDob: false,
      missingIndividualAddress: false,
      missingPhone: false,
      missingEmail: false,
      missingEntityName: false,
      missingEntityAddress: false,
      invalidOnboardingStatus: false,
      onboardingStatus: validEntity.onboardingStatus,
    };
    const validIndividual = {
      id: uuidv4(),
      firstname: 'firstname',
      lastname: 'lastname',
      dob: '01/01/1999',
      phone: '0400000000',
      email: '<EMAIL>',
      address: {
        street: 'street',
        suburb: 'suburb',
        postcode: '0000',
        state: 'state',
        country: 'country',
      },
    } as Customer;

    it.each([
      [
        'return valid finalise entity onboarding state',
        {
          testEntity: validEntity,
          testIndividual: validIndividual,
          expected: validFinaliseEntityOnboardingState,
        },
      ],
      [
        'flag missing entity name',
        {
          testEntity: {
            ...validEntity,
            name: undefined,
          },
          testIndividual: validIndividual,
          expected: {
            ...validFinaliseEntityOnboardingState,
            missingEntityName: true,
          },
        },
      ],
      [
        'flag missing entity address',
        {
          testEntity: {
            ...validEntity,
            registeredAddress: undefined,
            businessAddress: undefined,
          },
          testIndividual: validIndividual,
          expected: {
            ...validFinaliseEntityOnboardingState,
            missingEntityAddress: true,
          },
        },
      ],
      [
        'flag partial entity address',
        {
          testEntity: {
            ...validEntity,
            registeredAddress: undefined,
            businessAddress: {
              street1: 'street',
              state: 'state',
            },
          },
          testIndividual: validIndividual,
          expected: {
            ...validFinaliseEntityOnboardingState,
            missingEntityAddress: true,
          },
        },
      ],
      [
        'flag invalid onboarding status',
        {
          testEntity: {
            ...validEntity,
            onboardingStatus: OnboardingStatus.RC_DEPLATFORMED,
          } as Entity,
          testIndividual: validIndividual,
          expected: {
            ...validFinaliseEntityOnboardingState,
            onboardingStatus: OnboardingStatus.RC_DEPLATFORMED,
            invalidOnboardingStatus: true,
          },
        },
      ],
      [
        'flag missing individual name',
        {
          testEntity: validEntity,
          testIndividual: {
            ...validIndividual,
            lastname: undefined,
          },
          expected: {
            ...validFinaliseEntityOnboardingState,
            missingIndividualName: true,
          },
        },
      ],
      [
        'flag missing dob',
        {
          testEntity: validEntity,
          testIndividual: {
            ...validIndividual,
            dob: undefined,
          },
          expected: {
            ...validFinaliseEntityOnboardingState,
            missingDob: true,
          },
        },
      ],
      [
        'flag missing phone',
        {
          testEntity: validEntity,
          testIndividual: {
            ...validIndividual,
            phone: undefined,
          },
          expected: {
            ...validFinaliseEntityOnboardingState,
            missingPhone: true,
          },
        },
      ],
      [
        'flag missing email',
        {
          testEntity: validEntity,
          testIndividual: {
            ...validIndividual,
            email: undefined,
          },
          expected: {
            ...validFinaliseEntityOnboardingState,
            missingEmail: true,
          },
        },
      ],
    ])('should %p', async (_, { testEntity, testIndividual, expected }) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(testEntity as Entity));

      getRegisteringIndividual.mockResolvedValue(testIndividual);
      const result = await modifiedEntityService.validateFinaliseEntityOnboarding(testEntity.id);
      expect(expected).toEqual(result);
    });

    it('should throw not found error if unable to get registering individual', async () => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(validEntity));
      getRegisteringIndividual.mockResolvedValue(null);

      await expect(modifiedEntityService.validateFinaliseEntityOnboarding(validEntity.id)).rejects.toThrowError(
        new NotFoundError('Unable to get registering individual.'),
      );
    });
  });

  it('should handle updateEntity api request', async () => {
    const entityDto = {
      entityUuid: uuidv4(),
      type: EntityType.INDIVIDUAL,
      name: 'name',
    };
    await expect(entityService.updateEntity(entityDto as any)).resolves.toBe(true);
  });

  it('should handle updateEntity api request error case', async () => {
    (axios as any).patch.mockResolvedValue({ status: 400 } as any);
    const entityDto = {
      id: 'uuid',
      entityUuid: 'uuid',
      type: EntityType.INDIVIDUAL,
      name: 'name',
    };
    await expect(entityService.updateEntity(entityDto as any)).rejects.toThrowError(
      'Failed to update "entity" with id: "uuid"',
    );
  });

  it('should handle updateEntity api throw error case', async () => {
    (axios as any).patch.mockRejectedValue(new Error('error') as any);
    const entityDto = {
      id: 'uuid',
      entityUuid: 'uuid',
      type: EntityType.INDIVIDUAL,
      name: 'name',
    };
    await expect(entityService.updateEntity(entityDto as any)).rejects.toThrowError(
      'Failed to update "entity" with id: "uuid"',
    );
  });

  it('should handle saving entity create projection response to db', async () => {
    resetCalls(mockEntityDb);
    when(mockEntityDb.saveEntityProjection(anything())).thenResolve();
    const entityDto = { entityUuid };
    await entityService.saveEntityCreateResponse(entityDto as any);
    verify(mockEntityDb.saveEntityProjection(anything())).once();
    expect(capture(mockEntityDb.saveEntityProjection).first()).toEqual([
      {
        ...entityDto,
        transactionMetaData: {
          yetToMakeTransaction: true,
        },
      },
    ]);
  });

  it('should handle updating entity projection in db', async () => {
    resetCalls(mockEntityDb);
    when(mockEntityDb.saveEntityProjection).thenReturn(() => Promise.resolve());
    const entityDto = { entityUuid };
    await entityService.saveEntityUpdatedProjection(entityDto as any);
    verify(mockEntityDb.saveEntityProjection).once();
  });

  it('should handle fee rate effective entity projection in db', async () => {
    resetCalls(mockEntityDb);
    when(mockEntityDb.saveEntity(anything(), anything())).thenResolve();
    const entityDto = {
      entityUuid,
      feeRates: mockFeeRates,
    };
    await entityService.saveEntityFeeRatesEffectiveProjection(entityDto as any);
    verify(
      mockEntityDb.saveEntity(
        entityUuid,
        deepEqual({
          feeRateSettings: {
            feeFixedCpoc: 4100,
            feeFixedIntlXinv: 6300,
            feeFixedIntlZinv: 5300,
            feeFixedIntlPbl: 5300,
            feeFixedXinv: 6100,
            feeFixedZinv: 5100,
            feeFixedPbl: 5100,
            feeFixed: 4,
            feePercent: 300,
            feePercentCpoc: 4000,
            feePercentIntlXinv: 6200,
            feePercentIntlZinv: 5200,
            feePercentIntlPbl: 5200,
            feePercentMoto: 100,
            feeFixedMoto: 4,
            feePercentVt: 100,
            feeFixedVt: 400,
            feePercentXinv: 6000,
            feePercentZinv: 5000,
            feePercentPbl: 5000,
          },
        }),
      ),
    ).once();
  });

  it('should handle entity referred projection', async () => {
    resetCalls(mockEntityDb);
    resetCalls(mockReferralService);
    when(mockEntityDb.saveEntity(anything(), anything())).thenResolve();
    when(mockReferralService.saveReferralProjection).thenReturn(() => Promise.resolve());
    const entityDto = { referred: entityUuid };
    await entityService.saveEntityReferredProjection(entityDto as any);
    verify(mockEntityDb.saveEntity(entityUuid, anything())).once();
    verify(mockReferralService.saveReferralProjection).once();
  });

  it('should handle entity first transaction created projection', async () => {
    resetCalls(mockEntityDb);
    when(mockEntityDb.saveEntityFirstTransactionCreatedProjection).thenReturn(() => Promise.resolve());
    const entityDto = { entityUuid };
    await entityService.saveEntityFirstTransactionCreatedProjection(entityDto as any);
    verify(mockEntityDb.saveEntityFirstTransactionCreatedProjection).once();
  });

  it('should save payment settings projection', async () => {
    const entityDto: EntityPaymentSettingsDto = {
      entityUuid: uuidv4(),
      paymentLimits: [
        {
          paymentType: PaymentType.CP,
          maximum: '222',
          minimum: '111',
        },
        {
          paymentType: PaymentType.CNP,
          maximum: '444',
          minimum: '333',
        },
        {
          paymentType: PaymentType.MOTO,
          maximum: '666',
          minimum: '555',
        },
        {
          paymentType: PaymentType.CPOC,
          maximum: '888',
          minimum: '777',
        },
      ],
    };

    when(mockEntityDb.saveEntity(anything(), anything())).thenResolve();
    await entityService.savePaymentSettingsProjection(entityDto as any);

    verify(
      mockEntityDb.saveEntity(
        entityDto.entityUuid,
        deepEqual({
          paymentSettings: {
            cnpPaymentLimits: {
              maximum: 444,
              minimum: 333,
            },
            motoPaymentLimits: {
              maximum: 666,
              minimum: 555,
            },
            paymentLimits: {
              maximum: 222,
              minimum: 111,
            },
            cpocPaymentLimits: {
              maximum: 888,
              minimum: 777,
            },
          },
        }),
      ),
    ).once();
  });

  describe('checkForAdditionalEntityInfo', () => {
    it('return Error it the corresponding entity is not found', async () => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.reject(notFound));
      await entityService.checkForAdditionalEntityInfo(entityUuid).catch((e) => {
        expect(e).toEqual(notFound);
      });
    });

    it('return true if entity type is not a company', async () => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(entity as any));
      const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
      expect(result).toBe(true);
    });

    describe('if entity type is company with estimate annual revenue is smaller than ESTIMATED_ANNUAL_REVENUE_THRESHOLD ', () => {
      it('return false if it is not less than ESTIMATED_ANNUAL_REVENUE_THRESHOLD + 1', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD + 1,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(false);
      });

      it('return true if same as ESTIMATED_ANNUAL_REVENUE_THRESHOLD', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(true);
      });

      it('return true if it is less than ESTIMATED_ANNUAL_REVENUE_THRESHOLD', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD - 0.01,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(true);
      });
    });

    it('return true if the entity category is in other', async () => {
      (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          ...entity,
          estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD + 1,
          category: '000',
          type: EntityType.COMPANY,
        } as any),
      );
      const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
      expect(result).toBe(true);
    });

    describe('if the entity is in the risk category', () => {
      it('return false if it is not in risk', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD + 1,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(false);
      });

      it('return true if it is not in risk', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD + 1,
            type: EntityType.COMPANY,
            category: Category.PAINTING,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(false);
      });

      it('return true if it is in risk', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: true } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(true);
      });

      it('return true if the risk Service is throwing error (e.g. Network error)', async () => {
        (axios as any).get.mockRejectedValue({ status: 504 } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(true);
      });

      it('call the risk engine with the correct parameters', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
        const spy = jest.spyOn(modifiedEntityService.riskService, 'isInRiskCategory');
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD + 1,
            type: EntityType.COMPANY,
            categoryGroup: '01',
            category: '001',
          } as any),
        );
        await modifiedEntityService.checkForAdditionalEntityInfo(entityUuid);
        expect(spy).toHaveBeenCalledWith({
          categoryGroup: '01',
          category: '001',
        });
      });
    });

    describe('Prohibited MCC', () => {
      it('return false if it is not prohibited', async () => {
        (axios as any).get
          .mockResolvedValueOnce({ status: 200, data: false } as any)
          .mockResolvedValue({ status: 200, data: false } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD + 1,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(false);
      });

      it('return true if its prohibited', async () => {
        (axios as any).get
          .mockResolvedValueOnce({ status: 200, data: false } as any)
          .mockResolvedValue({ status: 200, data: true } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD - 1,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(true);
      });

      it('return true if the risk Service prohibited MCC is throwing error (e.g. Network error)', async () => {
        (axios as any).get
          .mockResolvedValueOnce({ status: 200, data: false } as any)
          .mockRejectedValue({ status: 504 } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD,
            type: EntityType.COMPANY,
          } as any),
        );
        const result = await entityService.checkForAdditionalEntityInfo(entityUuid);
        expect(result).toBe(true);
      });

      it('call the risk engine prohibited MCC with the correct parameters', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: true } as any);
        const spy = jest.spyOn(modifiedEntityService.riskService, 'isOnboardingProhibitedMCC');
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            estimatedAnnualRevenue: ESTIMATED_ANNUAL_REVENUE_THRESHOLD + 1,
            type: EntityType.COMPANY,
            categoryGroup: '01',
            category: '001',
          } as any),
        );
        await modifiedEntityService.checkForAdditionalEntityInfo(entityUuid);
        expect(spy).toHaveBeenCalledWith({
          categoryGroup: '01',
          category: '001',
        });
      });
    });
  });

  describe('requiredOnboardingDocumentsUpload', () => {
    it('return Error it the corresponding entity is not found', async () => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.reject(notFound));
      await entityService.requiredOnboardingDocumentsUpload(entityUuid).catch((e) => {
        expect(e).toEqual(notFound);
      });
    });

    describe('check entity is in the onboarding risk category', () => {
      it('return true if the risk Service is throwing error (e.g. Network error)', async () => {
        (axios as any).get.mockRejectedValue({ status: 504 } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
          } as any),
        );
        const result = await entityService.requiredOnboardingDocumentsUpload(entityUuid);
        expect(result).toBe(true);
      });

      it('call the risk engine with the correct parameters, return api response as false', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
        const spy = jest.spyOn(modifiedEntityService.riskService, 'isInOnboardingRiskCategory');
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            type: EntityType.COMPANY,
            categoryGroup: '01',
            category: '001',
          } as any),
        );
        const result = await modifiedEntityService.requiredOnboardingDocumentsUpload(entityUuid);
        expect(spy).toHaveBeenCalledWith({
          categoryGroup: '01',
          category: '001',
        });
        expect(result).toBe(false);
      });

      it('call the risk engine with the correct parameters, return api response as true', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: true } as any);
        const spy = jest.spyOn(modifiedEntityService.riskService, 'isInOnboardingRiskCategory');
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            type: EntityType.COMPANY,
            categoryGroup: '01',
            category: '001',
          } as any),
        );
        const result = await modifiedEntityService.requiredOnboardingDocumentsUpload(entityUuid);
        expect(spy).toHaveBeenCalledWith({
          categoryGroup: '01',
          category: '001',
        });
        expect(result).toBe(true);
      });
    });
  });

  describe('regulatedMCCOnboarding', () => {
    it('returns an error if the corresponding entity is not found', async () => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.reject(notFound));
      await entityService.regulatedMCCOnboarding(entityUuid).catch((e) => {
        expect(e).toEqual(notFound);
      });
    });

    describe('check entity is onboarding regulated MCC', () => {
      it('returns true if the risk service throws an error (e.g., network error)', async () => {
        (axios as any).get.mockRejectedValue({ status: 504 } as any);
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
          } as any),
        );
        const result = await entityService.regulatedMCCOnboarding(entityUuid);
        expect(result).toBe(true);
      });

      it('calls the risk engine with the correct parameters, returning API response as false', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: false } as any);
        const spy = jest.spyOn(modifiedEntityService.riskService, 'isOnboardingRegulatedMCC');
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            type: EntityType.COMPANY,
            categoryGroup: '01',
            category: '001',
            businessAddress: { state: 'NSW' },
          } as any),
        );
        const result = await modifiedEntityService.regulatedMCCOnboarding(entityUuid);
        expect(spy).toHaveBeenCalledWith({
          categoryGroup: '01',
          category: '001',
          state: 'NSW',
        });
        expect(result).toBe(false);
      });

      it('calls the risk engine with the correct parameters, returning API response as true', async () => {
        (axios as any).get.mockResolvedValue({ status: 200, data: true } as any);
        const spy = jest.spyOn(modifiedEntityService.riskService, 'isOnboardingRegulatedMCC');
        when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
          Promise.resolve({
            ...entity,
            type: EntityType.COMPANY,
            categoryGroup: '01',
            category: '001',
            registeredAddress: { state: 'NSW' },
          } as any),
        );
        const result = await modifiedEntityService.regulatedMCCOnboarding(entityUuid);
        expect(spy).toHaveBeenCalledWith({
          categoryGroup: '01',
          category: '001',
          state: 'NSW',
        });
        expect(result).toBe(true);
      });
    });
  });

  it('should generate document upload url', async () => {
    const url = 'url';
    const s3 = new S3Client();
    (s3 as any).getSignedUrl.mockReturnValueOnce(url);

    await expect(entityService.getEntityDocumentUploadUrl('entityUuid')).resolves.toEqual(url);
  });

  it('should generate upload urls with metadata', async () => {
    const url = 'url';
    const s3 = new S3Client();
    const specialChar = 'ĄŚŁÓŻĆ.png';
    (s3 as any).getSignedUrl.mockResolvedValueOnce(url);
    const spy = jest.spyOn(entityService, 'getPreSignedUrlForUpload');
    await expect(
      entityService.getEntityDocumentUploadUrls('entityUuid', [specialChar], 'customerUuid', 'subjectTest', {
        extraMetaData: 'extraMetaData',
      }),
    ).resolves.toStrictEqual([{ uploadUrl: url, fileName: specialChar }]);
    expect(spy).toHaveBeenCalledWith('', expect.any(String), {
      extraMetaData: 'extraMetaData',
      entityUuid: 'entityUuid',
      customerUuid: 'customerUuid',
      componentName: '',
      fileName: encodeURIComponent(specialChar),
      subject: 'subjectTest',
      uniqueFileUploadReference: expect.any(String),
    });
    spy.mockReset();
  });

  it('should encode max 100 characters', async () => {
    const url = 'url';
    const s3 = new S3Client();
    (s3 as any).getSignedUrl.mockResolvedValue(url);
    const spy = jest.spyOn(entityService, 'getPreSignedUrlForUpload');
    let a = '';
    while (a.length < 150) {
      a += 'a';
    }
    await expect(
      entityService.getEntityDocumentUploadUrls(
        'entityUuid',
        [`${a}.png`, `${a}.pdf`, `${a}.jpg`, `${a}.jpeg`],
        'customerUuid',
        'subjectTest',
      ),
    ).resolves.toStrictEqual([
      { uploadUrl: url, fileName: `${a}.png` },
      { uploadUrl: url, fileName: `${a}.pdf` },
      { uploadUrl: url, fileName: `${a}.jpg` },
      { uploadUrl: url, fileName: `${a}.jpeg` },
    ]);
    expect(spy).toHaveBeenNthCalledWith(1, '', expect.any(String), {
      entityUuid: 'entityUuid',
      customerUuid: 'customerUuid',
      componentName: '',
      fileName: `${a.slice(0, 96)}.png`,
      subject: 'subjectTest',
      uniqueFileUploadReference: expect.any(String),
    });
    expect(spy).toHaveBeenNthCalledWith(2, '', expect.any(String), {
      entityUuid: 'entityUuid',
      customerUuid: 'customerUuid',
      componentName: '',
      fileName: `${a.slice(0, 96)}.pdf`,
      subject: 'subjectTest',
      uniqueFileUploadReference: expect.any(String),
    });
    expect(spy).toHaveBeenNthCalledWith(3, '', expect.any(String), {
      entityUuid: 'entityUuid',
      customerUuid: 'customerUuid',
      componentName: '',
      fileName: `${a.slice(0, 96)}.jpg`,
      subject: 'subjectTest',
      uniqueFileUploadReference: expect.any(String),
    });
    expect(spy).toHaveBeenNthCalledWith(4, '', expect.any(String), {
      entityUuid: 'entityUuid',
      customerUuid: 'customerUuid',
      componentName: '',
      fileName: `${a.slice(0, 95)}.jpeg`,
      subject: 'subjectTest',
      uniqueFileUploadReference: expect.any(String),
    });
    spy.mockRestore();
  });

  it('should generate upload urls with no metadata', async () => {
    const url = 'url';
    const s3 = new S3Client();
    (s3 as any).getSignedUrl.mockResolvedValueOnce(url);
    await expect(
      entityService.getEntityDocumentUploadUrls('entityUuid', ['fileName.jpg'], 'customerUuid'),
    ).resolves.toStrictEqual([{ uploadUrl: url, fileName: expect.any(String) }]);
  });

  it('should throw an error when trying to generate the upload url', async () => {
    entityService.getPreSignedUrlForUpload = jest.fn().mockRejectedValueOnce(() => new Error('Error test'));
    await expect(
      entityService.getEntityDocumentUploadUrls('entityUuid', ['fileName.jpg'], 'subjectTest'),
    ).rejects.toEqual(new Error('Error when fetching upload url'));
  });

  it('should throw an error when trying to generate the upload urls based on max limit validation of 30', async () => {
    await expect(
      entityService.getEntityDocumentUploadUrls(
        'entityUuid',
        [
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
          'fileName.jpg',
        ],
        'subjectTest',
      ),
    ).rejects.toEqual(new Error('Error when fetching upload url'));
  });

  describe('checking all the getEntity instances are mapped correctly for BEAUTY', () => {
    const caseList = [
      {
        expectedCategory: 'OTHER',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '000', categoryGroup: '01' },
      },
      {
        expectedCategory: 'BEAUTYSALON',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup: '01' },
      },
      {
        expectedCategory: 'HAIRSALON',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '002', categoryGroup: '01' },
      },
      {
        expectedCategory: 'BARBERSHOP',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '003', categoryGroup: '01' },
      },
      {
        expectedCategory: 'MASSAGETHERAPIST',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '004', categoryGroup: '01' },
      },
      {
        expectedCategory: 'NAILSALON',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '005', categoryGroup: '01' },
      },
      {
        expectedCategory: 'TATTOOPIERCING',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '006', categoryGroup: '01' },
      },
      {
        expectedCategory: 'HEALTHBEAUTYSPA',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '007', categoryGroup: '01' },
      },
      {
        expectedCategory: 'MASSAGEPARLOUR',
        expectedCategoryGroup: 'BEAUTY',
        data: { entityUuid, id: entityUuid, category: '008', categoryGroup: '01' },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(cases.expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for EDUCATION', () => {
    const caseList = [
      {
        expectedCategory: 'OTHER',
        expectedCategoryGroup: 'EDUCATION',
        data: { entityUuid, id: entityUuid, category: '000', categoryGroup: '02' },
      },
      {
        expectedCategory: 'CHILDCARE',
        expectedCategoryGroup: 'EDUCATION',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup: '02' },
      },
      {
        expectedCategory: 'TEACHER',
        expectedCategoryGroup: 'EDUCATION',
        data: { entityUuid, id: entityUuid, category: '002', categoryGroup: '02' },
      },
      {
        expectedCategory: 'TUTOR',
        expectedCategoryGroup: 'EDUCATION',
        data: { entityUuid, id: entityUuid, category: '003', categoryGroup: '02' },
      },
      {
        expectedCategory: 'SCHOOL',
        expectedCategoryGroup: 'EDUCATION',
        data: { entityUuid, id: entityUuid, category: '004', categoryGroup: '02' },
      },
      {
        expectedCategory: 'UNIVERSITY',
        expectedCategoryGroup: 'EDUCATION',
        data: { entityUuid, id: entityUuid, category: '005', categoryGroup: '02' },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(cases.expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for CHARITIES', () => {
    const caseList = [
      {
        expectedCategory: 'OTHER',
        expectedCategoryGroup: 'CHARITIES',
        data: { entityUuid, id: entityUuid, category: '000', categoryGroup: '03' },
      },
      {
        expectedCategory: 'CHARITY',
        expectedCategoryGroup: 'CHARITIES',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup: '03' },
      },
      {
        expectedCategory: 'MEMBERSHIPORG',
        expectedCategoryGroup: 'CHARITIES',
        data: { entityUuid, id: entityUuid, category: '002', categoryGroup: '03' },
      },
      {
        expectedCategory: 'POLITICALORG',
        expectedCategoryGroup: 'CHARITIES',
        data: { entityUuid, id: entityUuid, category: '003', categoryGroup: '03' },
      },
      {
        expectedCategory: 'RELIGIOUSORG',
        expectedCategoryGroup: 'CHARITIES',
        data: { entityUuid, id: entityUuid, category: '004', categoryGroup: '03' },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(cases.expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for FOODDRINK', () => {
    const caseList = [
      {
        expectedCategory: 'OTHER',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '000', categoryGroup: '04' },
      },
      {
        expectedCategory: 'BARCLUB',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup: '04' },
      },
      {
        expectedCategory: 'SPECIALITYSHOP',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '002', categoryGroup: '04' },
      },
      {
        expectedCategory: 'CATERING',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '003', categoryGroup: '04' },
      },
      {
        expectedCategory: 'COFFEE',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '004', categoryGroup: '04' },
      },
      {
        expectedCategory: 'FOODTRUCKCART',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '005', categoryGroup: '04' },
      },
      {
        expectedCategory: 'GROCERY',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '006', categoryGroup: '04' },
      },
      {
        expectedCategory: 'MARKET',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '007', categoryGroup: '04' },
      },
      {
        expectedCategory: 'PRIVATECHEF',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '008', categoryGroup: '04' },
      },
      {
        expectedCategory: 'TAKEAWAYRESTAURANT',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '009', categoryGroup: '04' },
      },
      {
        expectedCategory: 'TABLESERVICERESTAURANT',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '010', categoryGroup: '04' },
      },
      {
        expectedCategory: 'WHOLESALEVENDOR',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '011', categoryGroup: '04' },
      },
      {
        expectedCategory: 'ALCOHOLWHOLESALER',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '012', categoryGroup: '04' },
      },
      {
        expectedCategory: 'BAKERY',
        expectedCategoryGroup: 'FOODDRINK',
        data: { entityUuid, id: entityUuid, category: '013', categoryGroup: '04' },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(cases.expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for HEALTHCAREFITNESS', () => {
    const expectedCategoryGroup = 'HEALTHCAREFITNESS';
    const categoryGroup = '05';
    const caseList = [
      { expectedCategory: 'OTHER', data: { entityUuid, id: entityUuid, category: '000', categoryGroup } },
      {
        expectedCategory: 'ACUPUNCTURE',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup },
      },
      { expectedCategory: 'CAREGIVER', data: { entityUuid, id: entityUuid, category: '002', categoryGroup } },
      {
        expectedCategory: 'CHIROPRACTOR',
        data: { entityUuid, id: entityUuid, category: '003', categoryGroup },
      },
      { expectedCategory: 'DENTIST', data: { entityUuid, id: entityUuid, category: '004', categoryGroup } },
      { expectedCategory: 'GYM', data: { entityUuid, id: entityUuid, category: '005', categoryGroup } },
      {
        expectedCategory: 'MEDICALTPRACTITIONER',
        data: { entityUuid, id: entityUuid, category: '006', categoryGroup },
      },
      {
        expectedCategory: 'OPTOMETRIST',
        data: { entityUuid, id: entityUuid, category: '007', categoryGroup },
      },
      {
        expectedCategory: 'PERSONALTRAINER',
        data: { entityUuid, id: entityUuid, category: '008', categoryGroup },
      },
      {
        expectedCategory: 'PSYCHIATRIST',
        data: { entityUuid, id: entityUuid, category: '009', categoryGroup },
      },
      {
        expectedCategory: 'COUNSELLOR',
        data: { entityUuid, id: entityUuid, category: '010', categoryGroup },
      },
      {
        expectedCategory: 'VETERINARY',
        data: { entityUuid, id: entityUuid, category: '011', categoryGroup },
      },
      {
        expectedCategory: 'PHYSIOTHERAPIST',
        data: { entityUuid, id: entityUuid, category: '012', categoryGroup },
      },
      { expectedCategory: 'DIETITIAN', data: { entityUuid, id: entityUuid, category: '013', categoryGroup } },
      {
        expectedCategory: 'PODIATRIST',
        data: { entityUuid, id: entityUuid, category: '014', categoryGroup },
      },
      {
        expectedCategory: 'OCCUPATIONALTHERAPIST',
        data: { entityUuid, id: entityUuid, category: '015', categoryGroup },
      },
      {
        expectedCategory: 'HYPNOTHERAPIST',
        data: { entityUuid, id: entityUuid, category: '016', categoryGroup },
      },
      {
        expectedCategory: 'PHYSICALTHERAPIST',
        data: { entityUuid, id: entityUuid, category: '017', categoryGroup },
      },
      { expectedCategory: 'DOCTOR', data: { entityUuid, id: entityUuid, category: '018', categoryGroup } },
      {
        expectedCategory: 'ANESTHETIST',
        data: { entityUuid, id: entityUuid, category: '019', categoryGroup },
      },
      { expectedCategory: 'MIDWIFE', data: { entityUuid, id: entityUuid, category: '020', categoryGroup } },
      { expectedCategory: 'NURSE', data: { entityUuid, id: entityUuid, category: '021', categoryGroup } },
      {
        expectedCategory: 'PHARMACIST',
        data: { entityUuid, id: entityUuid, category: '022', categoryGroup },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for HOMEMAINTENANCE', () => {
    const expectedCategoryGroup = 'HOMEMAINTENANCE';
    const categoryGroup = '06';
    const caseList = [
      { expectedCategory: 'OTHER', data: { entityUuid, id: entityUuid, category: '000', categoryGroup } },
      {
        expectedCategory: 'AUTOMOTIVE',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup },
      },
      {
        expectedCategory: 'CARPETCLEANING',
        data: { entityUuid, id: entityUuid, category: '002', categoryGroup },
      },
      { expectedCategory: 'CLEANING', data: { entityUuid, id: entityUuid, category: '003', categoryGroup } },
      {
        expectedCategory: 'CLOTHINGALTERATIONS',
        data: { entityUuid, id: entityUuid, category: '004', categoryGroup },
      },
      {
        expectedCategory: 'DRYCLEANING',
        data: { entityUuid, id: entityUuid, category: '005', categoryGroup },
      },
      {
        expectedCategory: 'ELECTRICAL',
        data: { entityUuid, id: entityUuid, category: '006', categoryGroup },
      },
      { expectedCategory: 'FLOORING', data: { entityUuid, id: entityUuid, category: '007', categoryGroup } },
      {
        expectedCategory: 'GENERALCONTRACTING',
        data: { entityUuid, id: entityUuid, category: '008', categoryGroup },
      },
      {
        expectedCategory: 'HEATINGANDAC',
        data: { entityUuid, id: entityUuid, category: '009', categoryGroup },
      },
      {
        expectedCategory: 'INSTALLATIONSERVICES',
        data: { entityUuid, id: entityUuid, category: '010', categoryGroup },
      },
      {
        expectedCategory: 'RUBBISHREMOVAL',
        data: { entityUuid, id: entityUuid, category: '011', categoryGroup },
      },
      {
        expectedCategory: 'LANDSCAPING',
        data: { entityUuid, id: entityUuid, category: '012', categoryGroup },
      },
      { expectedCategory: 'LOCKSMITH', data: { entityUuid, id: entityUuid, category: '013', categoryGroup } },
      { expectedCategory: 'PAINTING', data: { entityUuid, id: entityUuid, category: '014', categoryGroup } },
      {
        expectedCategory: 'PESTCONTROL',
        data: { entityUuid, id: entityUuid, category: '015', categoryGroup },
      },
      { expectedCategory: 'PLUMBING', data: { entityUuid, id: entityUuid, category: '016', categoryGroup } },
      { expectedCategory: 'CARPENTRY', data: { entityUuid, id: entityUuid, category: '017', categoryGroup } },
      {
        expectedCategory: 'PLASTERINGCEILING',
        data: { entityUuid, id: entityUuid, category: '018', categoryGroup },
      },
      {
        expectedCategory: 'TILINGCARPETING',
        data: { entityUuid, id: entityUuid, category: '019', categoryGroup },
      },
      {
        expectedCategory: 'BRICKLAYING',
        data: { entityUuid, id: entityUuid, category: '020', categoryGroup },
      },
      {
        expectedCategory: 'CONCRETING',
        data: { entityUuid, id: entityUuid, category: '021', categoryGroup },
      },
      { expectedCategory: 'GLAZING', data: { entityUuid, id: entityUuid, category: '022', categoryGroup } },
      {
        expectedCategory: 'CONSTRUCTIONMATERIALS',
        data: { entityUuid, id: entityUuid, category: '023', categoryGroup },
      },
      {
        expectedCategory: 'CONSTRUCTION',
        data: { entityUuid, id: entityUuid, category: '024', categoryGroup },
      },
      {
        expectedCategory: 'ARCHITECTURE',
        data: { entityUuid, id: entityUuid, category: '025', categoryGroup },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for LEISUREENTERTAINMENT', () => {
    const expectedCategoryGroup = 'LEISUREENTERTAINMENT';
    const categoryGroup = '07';
    const caseList = [
      { expectedCategory: 'OTHER', data: { entityUuid, id: entityUuid, category: '000', categoryGroup } },
      { expectedCategory: 'FESTIVALS', data: { entityUuid, id: entityUuid, category: '001', categoryGroup } },
      { expectedCategory: 'CINEMA', data: { entityUuid, id: entityUuid, category: '002', categoryGroup } },
      { expectedCategory: 'MUSEUM', data: { entityUuid, id: entityUuid, category: '003', categoryGroup } },
      { expectedCategory: 'MUSIC', data: { entityUuid, id: entityUuid, category: '004', categoryGroup } },
      {
        expectedCategory: 'PERFORMINGARTS',
        data: { entityUuid, id: entityUuid, category: '005', categoryGroup },
      },
      {
        expectedCategory: 'SPORTINGEVENTS',
        data: { entityUuid, id: entityUuid, category: '006', categoryGroup },
      },
      {
        expectedCategory: 'SPORTSRECREATION',
        data: { entityUuid, id: entityUuid, category: '007', categoryGroup },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for PROFESSIONALSERVICES', () => {
    const expectedCategoryGroup = 'PROFESSIONALSERVICES';
    const categoryGroup = '08';
    const caseList = [
      { expectedCategory: 'OTHER', data: { entityUuid, id: entityUuid, category: '000', categoryGroup } },
      {
        expectedCategory: 'ACCOUNTING',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup },
      },
      {
        expectedCategory: 'CONSULTING',
        data: { entityUuid, id: entityUuid, category: '002', categoryGroup },
      },
      { expectedCategory: 'DESIGN', data: { entityUuid, id: entityUuid, category: '003', categoryGroup } },
      {
        expectedCategory: 'INTERIORDESIGN',
        data: { entityUuid, id: entityUuid, category: '004', categoryGroup },
      },
      {
        expectedCategory: 'LEGALSERVICES',
        data: { entityUuid, id: entityUuid, category: '005', categoryGroup },
      },
      { expectedCategory: 'MARKETING', data: { entityUuid, id: entityUuid, category: '006', categoryGroup } },
      {
        expectedCategory: 'PHOTOGRAPHY',
        data: { entityUuid, id: entityUuid, category: '007', categoryGroup },
      },
      {
        expectedCategory: 'PRINTINGSERVICES',
        data: { entityUuid, id: entityUuid, category: '008', categoryGroup },
      },
      {
        expectedCategory: 'REALESTATE',
        data: { entityUuid, id: entityUuid, category: '009', categoryGroup },
      },
      {
        expectedCategory: 'SOFTWAREDEVELOPMENT',
        data: { entityUuid, id: entityUuid, category: '010', categoryGroup },
      },
      {
        expectedCategory: 'DATINGSERVICES',
        data: { entityUuid, id: entityUuid, category: '011', categoryGroup },
      },
      {
        expectedCategory: 'EMPLOYMENTAGENCIES',
        data: { entityUuid, id: entityUuid, category: '012', categoryGroup },
      },
      {
        expectedCategory: 'MOTIVATIONALSERVICES',
        data: { entityUuid, id: entityUuid, category: '013', categoryGroup },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for RETAIL', () => {
    const expectedCategoryGroup = 'RETAIL';
    const categoryGroup = '09';
    const caseList = [
      { expectedCategory: 'OTHER', data: { entityUuid, id: entityUuid, category: '000', categoryGroup } },
      { expectedCategory: 'FESTIVALS', data: { entityUuid, id: entityUuid, category: '001', categoryGroup } },
      {
        expectedCategory: 'SPECIALITYSHOP',
        data: { entityUuid, id: entityUuid, category: '002', categoryGroup },
      },
      { expectedCategory: 'CLOTHING', data: { entityUuid, id: entityUuid, category: '003', categoryGroup } },
      {
        expectedCategory: 'COMPUTERAPPLICANCES',
        data: { entityUuid, id: entityUuid, category: '004', categoryGroup },
      },
      {
        expectedCategory: 'ELECTRONICS',
        data: { entityUuid, id: entityUuid, category: '005', categoryGroup },
      },
      { expectedCategory: 'EYEWEAR', data: { entityUuid, id: entityUuid, category: '006', categoryGroup } },
      { expectedCategory: 'EVENTS', data: { entityUuid, id: entityUuid, category: '007', categoryGroup } },
      {
        expectedCategory: 'ARTPHOTOFILM',
        data: { entityUuid, id: entityUuid, category: '008', categoryGroup },
      },
      {
        expectedCategory: 'FLOWERSGIFTS',
        data: { entityUuid, id: entityUuid, category: '009', categoryGroup },
      },
      { expectedCategory: 'FURNITURE', data: { entityUuid, id: entityUuid, category: '010', categoryGroup } },
      { expectedCategory: 'HOMEGOODS', data: { entityUuid, id: entityUuid, category: '011', categoryGroup } },
      { expectedCategory: 'HOBBYSHOP', data: { entityUuid, id: entityUuid, category: '012', categoryGroup } },
      {
        expectedCategory: 'JEWELLERYWATCHES',
        data: { entityUuid, id: entityUuid, category: '013', categoryGroup },
      },
      {
        expectedCategory: 'OFFICESUPPLY',
        data: { entityUuid, id: entityUuid, category: '014', categoryGroup },
      },
      { expectedCategory: 'PETSHOP', data: { entityUuid, id: entityUuid, category: '015', categoryGroup } },
      {
        expectedCategory: 'BOOKSMUSICVIDEO',
        data: { entityUuid, id: entityUuid, category: '016', categoryGroup },
      },
      {
        expectedCategory: 'SPORTINGGOODS',
        data: { entityUuid, id: entityUuid, category: '017', categoryGroup },
      },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for TRANSPORTATION', () => {
    const expectedCategoryGroup = 'TRANSPORTATION';
    const categoryGroup = '10';
    const caseList = [
      { expectedCategory: 'OTHER', data: { entityUuid, id: entityUuid, category: '000', categoryGroup } },
      { expectedCategory: 'BUS', data: { entityUuid, id: entityUuid, category: '001', categoryGroup } },
      { expectedCategory: 'DELIVERY', data: { entityUuid, id: entityUuid, category: '002', categoryGroup } },
      {
        expectedCategory: 'REMOVALIST',
        data: { entityUuid, id: entityUuid, category: '003', categoryGroup },
      },
      {
        expectedCategory: 'PRIVATECARHIRE',
        data: { entityUuid, id: entityUuid, category: '004', categoryGroup },
      },
      { expectedCategory: 'TAXI', data: { entityUuid, id: entityUuid, category: '005', categoryGroup } },
      {
        expectedCategory: 'AIRLINEANDAIRCARRIER',
        data: { entityUuid, id: entityUuid, category: '006', categoryGroup },
      },
      {
        expectedCategory: 'COURIERSERVICES',
        data: { entityUuid, id: entityUuid, category: '007', categoryGroup },
      },
      { expectedCategory: 'MOVERS', data: { entityUuid, id: entityUuid, category: '008', categoryGroup } },
      {
        expectedCategory: 'BOATRENTALS',
        data: { entityUuid, id: entityUuid, category: '009', categoryGroup },
      },
      { expectedCategory: 'DEALERS', data: { entityUuid, id: entityUuid, category: '010', categoryGroup } },
      {
        expectedCategory: 'BUSINESSSERVICES',
        data: { entityUuid, id: entityUuid, category: '011', categoryGroup },
      },
      {
        expectedCategory: 'CARTRUCKSERVICES',
        data: { entityUuid, id: entityUuid, category: '012', categoryGroup },
      },
      { expectedCategory: 'CARRENTAL', data: { entityUuid, id: entityUuid, category: '013', categoryGroup } },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for TRAVEL', () => {
    const expectedCategoryGroup = 'TRAVEL';
    const categoryGroup = '11';
    const caseList = [
      { expectedCategory: 'OTHER', data: { entityUuid, id: entityUuid, category: '000', categoryGroup } },
      {
        expectedCategory: 'TRAVELAGENCY',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup },
      },
      { expectedCategory: 'LODGING', data: { entityUuid, id: entityUuid, category: '002', categoryGroup } },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped correctly for GOVERNMENTSERVICES', () => {
    const expectedCategoryGroup = 'GOVERNMENTSERVICES';
    const categoryGroup = '12';
    const caseList = [
      { expectedCategory: 'OTHER', data: { entityUuid, id: entityUuid, category: '000', categoryGroup } },
      {
        expectedCategory: 'LOCALCOUNCIL',
        data: { entityUuid, id: entityUuid, category: '001', categoryGroup },
      },
      { expectedCategory: 'LIBRARY', data: { entityUuid, id: entityUuid, category: '002', categoryGroup } },
      { expectedCategory: 'PRIMARYSCHOOL', data: { entityUuid, id: entityUuid, category: '003', categoryGroup } },
      { expectedCategory: 'PARKSRECREATION', data: { entityUuid, id: entityUuid, category: '004', categoryGroup } },
    ];

    test.each(caseList)('Should handle category parsing when using getEntity', async (cases) => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() => Promise.resolve(cases.data as any));
      const res = await entityService.getEntity('entityUuid');
      expect(res?.category).toEqual(cases.expectedCategory);
      expect(res?.categoryGroup).toEqual(expectedCategoryGroup);
    });
  });

  describe('checking all the getEntity instances are mapped for incorrect category getting passed', () => {
    it('Should handle incorrect category parsing when using getEntity', async () => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({
          entityUuid,
          id: entityUuid,
          category: 'fakeCategory',
          categoryGroup: 'fakeCategoryGroup',
        } as any),
      );
      const res = await entityService.getEntity('customerId');
      expect(res?.category).toEqual('fakeCategory');
      expect(res?.categoryGroup).toEqual('fakeCategoryGroup');
    });
  });

  describe('Update primary account holder', () => {
    it('should update primary account holder', async () => {
      (axios as any).put.mockResolvedValue({
        status: 200,
        data: {},
      });

      expect(await entityService.updatePAH('customerUuid', 'entityUuids')).toBe(true);
    });
  });

  describe('should handle entity primary account holder changed event', () => {
    it('should handle entity primary account holder changed event', async () => {
      resetCalls(mockEntityDb);
      const event = {
        entityUuid: 'entityUuid',
        previousPrimaryAccountHolder: 'customerUuid',
        primaryAccountHolder: 'newPrimary',
      };

      when(mockEntityDb.saveEntityProjection).thenReturn(() => Promise.resolve());

      await entityService.savePrimaryAccountHolderChangedProjection(event);
      verify(mockEntityDb.saveEntityProjection).once();
    });
  });

  describe('should handle entity currenycy and domicile attach event', () => {
    it('should handle entity currenycy and domicile attach event', async () => {
      resetCalls(mockEntityDb);
      const event = {
        entityUuid: 'entityUuid',
        domicile: 'AUS',
        currency: 'AUD',
      };

      when(mockEntityDb.saveEntityProjection).thenReturn(() => Promise.resolve());

      await entityService.attachSitDomicileAndCurrencyProjection(event);
      verify(mockEntityDb.saveEntityProjection).once();
    });
  });

  describe('setAcceptTermsOfService', () => {
    let spy: jest.SpyInstance;

    beforeEach(() => {
      spy = jest.spyOn(modifiedEntityService.amsApiService, 'update');
    });

    it('should call amsApiService.update with the correct parameters', async () => {
      when(mockEntityDb.getEntityOrThrow).thenReturn(() =>
        Promise.resolve({ entityUuid, id: entityUuid, ...entity } as any),
      );
      const input = {
        accepted: true,
        entityUuid: uuidv4(),
        customerUuid: uuidv4(),
      };
      const result = await modifiedEntityService.setAcceptTermsOfService(input);

      expect(result.entity.termsOfService.ezta.accepted).toBe(input.accepted);
      expect(spy).toHaveBeenCalledWith(input.entityUuid, {
        entityUuid: input.entityUuid,
        termsOfService: {
          ezta: {
            accepted: input.accepted,
            customerUuid: input.customerUuid,
            acceptedAt: expect.any(String),
          },
        },
      });
    });
  });
});
