import { BadUserInput, NotFoundError } from '@npco/component-bff-core/dist/error';
import { Domicile } from '@npco/component-bff-core/dist/utils/domicile';
import { debug, error, info } from '@npco/component-bff-core/dist/utils/logger';
import { EnvironmentService } from '@npco/component-dbs-mp-common/dist/config';
import { getRegisteringIndividual } from '@npco/component-dbs-mp-common/dist/customer';
import { DocumentS3BaseService } from '@npco/component-dbs-mp-common/dist/document/baseService';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb';
import { getFeeRateSettings } from '@npco/component-dbs-mp-common/dist/entity/feeUtils';
import { AmsApiService } from '@npco/component-dbs-mp-common/dist/interface/amsApiService';
import { RiskRuleApiService } from '@npco/component-dbs-mp-common/dist/interface/riskRuleApiService';
import { LambdaService } from '@npco/component-dbs-mp-common/dist/lambda';
import type {
  DocumentUploadUrl,
  Entity,
  EntityDetails,
  RegisteredBusinessInput,
  UnregSoleTradBusinessInput,
  UnregBusinessInput,
  EntityOnboardingResult,
  JsonObject,
  PaymentSettings,
  ValidateFinaliseEntityOnboardingResult,
  CohortType,
  CohortCode,
  ConfirmBusinessInitDetailsInput,
} from '@npco/component-dbs-mp-common/dist/types';
import { EntityOnboardingResultStatus, COHORT_CODE } from '@npco/component-dbs-mp-common/dist/types';
import { trimObjectStringValues } from '@npco/component-dbs-mp-common/dist/utils';
import { EntityType, PaymentType } from '@npco/component-dto-core';
import {
  Category,
  EntityFullSearchRequestedEventDto,
  EntityInitialSearchRequestedEventDto,
  EntityUpdatedEventDto,
  OnboardingStatus,
} from '@npco/component-dto-entity';
import type {
  EntityCreateRequestedEventDto,
  EntityCreatedEventDto,
  EntityDomicileAndCurrencyAttachedEventDto,
  EntityFeeRatesEffectiveDto,
  EntityFirstTransactionCreatedDto,
  EntityPaymentSettingsDto,
  EntityPrimaryAccountHolderChangedEventDto,
  EntityReferredEventDto,
  PaymentLimit,
  EntityOnboardingStatusUpdatedEventDto,
  EntityGqlCreateRequestedEventDto,
} from '@npco/component-dto-entity';

import { Injectable } from '@nestjs/common';
import type { AxiosResponse } from 'axios';
import axios from 'axios';
import { v4 } from 'uuid';

import {
  categoryDictionary,
  categoryGroupAndCategoryDictionary,
  categoryGroupDictionary,
  invertDictionary,
} from './categoryUtils';
import { EntityDb } from './entityDb';
import { ReferralService } from './referral';

const EntityOnboardedStatus = [
  OnboardingStatus.ONBOARDED,
  OnboardingStatus.RC_ONBOARDED,
  OnboardingStatus.RC_REJECTED,
  OnboardingStatus.RC_ABANDONED,
  OnboardingStatus.RC_DEPLATFORMED,
];

@Injectable()
export class EntityService extends DocumentS3BaseService {
  entityDb: EntityDb;

  private readonly amsApiService: AmsApiService;

  private readonly riskService: RiskRuleApiService;

  constructor(
    private readonly dbService: DynamodbService,
    private readonly environmentService: EnvironmentService,
    private readonly lambdaService: LambdaService,
    private readonly referralService: ReferralService,
  ) {
    super();
    this.entityDb = new EntityDb(this.dbService, this.environmentService.componentTableName);
    this.amsApiService = new AmsApiService(
      this.environmentService,
      this.environmentService.amsEntityEndpointPath,
      'entity',
    );
    this.riskService = new RiskRuleApiService(this.environmentService);
  }

  readonly saveEntityCreateResponse = async (dto: EntityCreatedEventDto) =>
    this.entityDb.saveEntityProjection({
      ...dto,
      transactionMetaData: {
        yetToMakeTransaction: true,
      },
    });

  /**
   * @deprecated Please use confirmRegisteredBusinessInitialDetails
   * @param entityUuid
   * @param entityDetails
   * @param sourceIp
   */
  confirmEntityDetailsInitial = async (entityUuid: string, entityDetails: EntityDetails, sourceIp: string) => {
    if (!entityDetails.abn && !entityDetails.acn) {
      return Promise.reject(new BadUserInput('ABN or ACN is required.')); // NOSONAR
    }
    return this.createEntity({ entityUuid, sourceIp, ...entityDetails });
  };

  prepareEntityDetails(entityDetails: RegisteredBusinessInput | UnregSoleTradBusinessInput | UnregBusinessInput) {
    info(`calling prepareEntityDetails: ${JSON.stringify(entityDetails)}`);
    const entityObj: any = { ...entityDetails };
    if (entityDetails.category) {
      const categoryDict = categoryDictionary();
      const lookup = categoryDict[entityDetails.category];
      if (lookup) {
        entityObj.category = lookup; // NOSONAR
      }
    }

    if (entityDetails.categoryGroup) {
      const categoryGroupDict = categoryGroupDictionary();
      const lookup = categoryGroupDict[entityDetails.categoryGroup];
      if (lookup) {
        entityObj.categoryGroup = lookup; // NOSONAR
      }
    }
    info(`Final prepared entityObj: ${JSON.stringify(entityObj)}`);
    return entityObj;
  }

  confirmRegisteredBusinessInitialDetails = async (
    input: ConfirmBusinessInitDetailsInput,
    entityDetails: RegisteredBusinessInput,
  ) => {
    info(`calling confirmRegisteredBusinessInitialDetails: ${JSON.stringify(input)}`);
    const { entityUuid, registeringIndividual, sourceIp } = input;
    if (entityDetails.country && entityDetails.country !== 'AUS' && entityDetails.country !== 'GBR') {
      return Promise.reject(new BadUserInput('Only AUS and GBR are supported countries.')); // NOSONAR
    }
    if (
      entityDetails.country === 'AUS' &&
      !(entityDetails?.businessDetails?.ausBusiness?.abn || entityDetails?.businessDetails?.ausBusiness?.acn)
    ) {
      return Promise.reject(new BadUserInput('ABN or ACN is required.')); // NOSONAR
    }
    if (entityDetails.country === 'GBR' && !entityDetails?.businessDetails?.gbrBusiness?.crn) {
      return Promise.reject(new BadUserInput('CRN is required.')); // NOSONAR
    }
    const entityObj = this.prepareEntityDetails(entityDetails);
    info(`calling createEntity from confirmRegisteredBusinessInitialDetails: ${JSON.stringify(entityObj)}`);
    return this.createEntity({
      entityUuid,
      registeringIndividual,
      sourceIp,
      ...entityObj,
    } as EntityCreateRequestedEventDto);
  };

  /**
   * @deprecated Please use confirmUnregisteredSoleTraderInitialDetails
   * @param entityUuid
   * @param name
   * @param sourceIp
   */
  createSoletraderEntity = async (entityUuid: string, name: string, sourceIp: string) => {
    const dto = {
      entityUuid,
      name,
      type: EntityType.INDIVIDUAL,
      manualEntry: true,
      sourceIp,
      country: 'AUS',
    };
    return this.createEntity(dto);
  };

  confirmUnregisteredSoleTraderInitialDetails = async (
    input: ConfirmBusinessInitDetailsInput,
    entityDetails: UnregSoleTradBusinessInput,
  ) => {
    info(`calling confirmUnregisteredSoleTraderInitialDetails: ${JSON.stringify(input)}`);
    const { entityUuid, registeringIndividual, sourceIp } = input;
    const entityObj = this.prepareEntityDetails(entityDetails);
    const dto: any = {
      entityUuid,
      sourceIp,
      name: '',
      type: EntityType.INDIVIDUAL,
      manualEntry: true,
      ...entityObj,
      registeringIndividual,
    };
    info(`calling createEntity from confirmUnregisteredSoleTraderInitialDetails: ${JSON.stringify(dto)}`, entityUuid);
    return this.createEntity(dto);
  };

  confirmUnregisteredBusinessInitialDetails = async (
    input: ConfirmBusinessInitDetailsInput,
    entityDetails: UnregBusinessInput,
  ) => {
    info(`calling confirmUnregisteredBusinessInitialDetails: ${JSON.stringify(input)}`);
    const { entityUuid, registeringIndividual, sourceIp } = input;
    if (entityDetails.country !== 'GBR') {
      throw new BadUserInput('Only GBR country is supported.'); // NOSONAR
    }

    if (entityDetails.type !== EntityType.TRUST) {
      throw new BadUserInput('Only TRUST entity type is supported.'); // NOSONAR
    }
    const entityObj = this.prepareEntityDetails(entityDetails);
    const dto: any = {
      entityUuid,
      sourceIp,
      manualEntry: true,
      ...entityObj,
      registeringIndividual,
    };
    info(`calling createEntity from confirmUnregisteredBusinessInitialDetails: ${JSON.stringify(dto)}`, entityUuid);
    return this.createEntity(dto);
  };

  selectDepositAccount = async (entityUuid: string, accountUuid: string, remitToCard: boolean): Promise<boolean> => {
    if (remitToCard) {
      await this.entityDb.getDebitCardAccount(entityUuid, accountUuid);
    }
    return this.amsApiService.selectDepositAccount(entityUuid, accountUuid, remitToCard);
  };

  validateFinaliseEntityOnboarding = async (entityUuid: string): Promise<ValidateFinaliseEntityOnboardingResult> => {
    const entity = await this.getEntity(entityUuid);
    const { name: entityName, onboardingStatus, businessAddress, registeredAddress } = entity;

    const missingEntityName = !entityName;
    const invalidOnboardingStatus =
      !onboardingStatus || [...EntityOnboardedStatus, OnboardingStatus.REVIEW].some((s) => s === onboardingStatus);
    const missingBusinessAddress = !(
      businessAddress?.street1 &&
      businessAddress?.suburb &&
      businessAddress?.postcode &&
      businessAddress?.state
    );
    const missingRegisteredAddress = !(
      registeredAddress?.street1 &&
      registeredAddress?.suburb &&
      registeredAddress?.postcode &&
      registeredAddress?.state
    );
    const missingEntityAddress = missingBusinessAddress && missingRegisteredAddress;

    const customer = await getRegisteringIndividual(this.dbService, entityUuid);
    if (!customer) {
      throw new NotFoundError('Unable to get registering individual.');
    }

    const { firstname, lastname, dob, address: individualAddress, phone, email } = customer;
    const missingIndividualName = !firstname || !lastname;
    const missingDob = !dob;
    const missingPhone = !phone;
    const missingEmail = !email;
    const missingIndividualAddress = !(
      individualAddress?.street &&
      individualAddress?.suburb &&
      individualAddress?.postcode &&
      individualAddress?.state
    );
    return {
      missingIndividualName,
      missingDob,
      missingIndividualAddress,
      missingPhone,
      missingEmail,
      missingEntityName,
      missingEntityAddress,
      onboardingStatus: entity.onboardingStatus,
      invalidOnboardingStatus,
    };
  };

  finaliseEntityOnboarding = async (entityUuid: string): Promise<EntityOnboardingResult> => {
    let result: OnboardingStatus.ONBOARDED | OnboardingStatus.REVIEW;
    try {
      result = await this.evaluateOnboardingDetails(entityUuid);
      debug(`entityUuid: "${entityUuid}" - evaluateOnboardingDetails result: ${result}`);
    } catch (e) {
      error(e);
      return Promise.reject(new Error('Failed to evaluate onboarding details'));
    }

    if (this.environmentService.componentName === 'mp') {
      await this.saveEntityUpdatedProjection({
        entityUuid,
        onboardingStatus: result,
      });
    }

    return {
      entityUuid,
      result:
        result === OnboardingStatus.ONBOARDED
          ? EntityOnboardingResultStatus.COMPLETED
          : EntityOnboardingResultStatus.IN_REVIEW,
    };
  };

  getEntity = async (entityUuid: string): Promise<Entity> => {
    const entity = await this.entityDb.getEntityOrThrow(entityUuid);

    if (entity.category !== undefined && entity.categoryGroup !== undefined) {
      const categoryGroupAndDict = categoryGroupAndCategoryDictionary();
      const lookup = categoryGroupAndDict[entity.categoryGroup + entity.category];
      if (lookup) {
        entity.category = lookup;
      }
    }
    if (entity.categoryGroup !== undefined) {
      const categoryGroupDict = categoryGroupDictionary();
      const invertedDictionary = invertDictionary(categoryGroupDict);
      const lookup = invertedDictionary[entity.categoryGroup];
      if (lookup) {
        entity.categoryGroup = lookup;
      }
    }

    if (entity.cohort) {
      entity.cohort = entity.cohort.map((cohort) => (cohort as CohortType).code.toString() as any);
    }

    return entity;
  };

  getPaymentLimits = async (entityUuid: string, type: PaymentType) => {
    let limitField: 'cnpPaymentLimits' | 'motoPaymentLimits' | 'cpocPaymentLimits' | 'paymentLimits';
    switch (type) {
      case PaymentType.CNP:
      case PaymentType.PBLINT:
      case PaymentType.PBLLOC:
      case PaymentType.XINVINT:
      case PaymentType.XINVLOC:
      case PaymentType.ZINVINT:
      case PaymentType.ZINVLOC:
        limitField = 'cnpPaymentLimits';
        break;
      case PaymentType.MOTO:
      case PaymentType.VT:
        limitField = 'motoPaymentLimits';
        break;
      case PaymentType.CPOC:
        limitField = 'cpocPaymentLimits';
        break;
      case PaymentType.DEFAULT:
      case PaymentType.CP:
      default:
        limitField = 'paymentLimits';
    }
    const entity = await this.entityDb.getEntity(entityUuid);
    const limits = entity?.paymentSettings?.[limitField];
    if (!limits) {
      error(`Entity ${entityUuid} is missing payment limits ${limitField}`, entityUuid);
      // ZD-17228 temporary support nullable payment limits
      // throw new NotFoundError('Payment limits not found');
      return undefined;
    }

    return limits;
  };

  requestSearchEntityDetailsInitial = async (entityUuid: string, businessIdentifier: string) => {
    const handlerName = this.environmentService.cqrsCmds.Entity.InitialSearchRequested;
    const requestDto = new EntityInitialSearchRequestedEventDto({
      entityUuid,
      businessIdentifier,
    });
    info(`send request dto to command handler: ${JSON.stringify(requestDto)}`, entityUuid);
    return this.lambdaService.invokeCommand(handlerName, requestDto);
  };

  requestFullSearchEntityDetails = async (entityUuid: string, customerUuid: string) => {
    const handlerName = this.environmentService.cqrsCmds.Entity.FullSearchRequested;
    const requestDto = new EntityFullSearchRequestedEventDto({
      entityUuid,
      customerUuid,
    });
    info(`send request dto to command handler: ${JSON.stringify(requestDto)}`, entityUuid);
    return this.lambdaService.invokeCommand(handlerName, requestDto);
  };

  updateEntity = (entity: Entity): Promise<boolean> => {
    info(`calling updateEntity: ${JSON.stringify(entity)}`, entity.id);
    const entityUpdateEventDto = this.prepareUpdateDto(entity);
    if (entity.cohort?.length) {
      entityUpdateEventDto.cohort = entity.cohort
        .filter((cohort) => Object.values(COHORT_CODE).includes(cohort as unknown as CohortCode))
        .map((cohort) => ({ code: cohort as unknown as CohortCode }));
    }
    info(`sending entityUpdateEventDto: ${JSON.stringify(entityUpdateEventDto)}`, entity.id);
    return this.amsApiService.update<EntityUpdatedEventDto>(entity.id, entityUpdateEventDto);
  };

  prepareUpdateDto(entity: Entity): EntityUpdatedEventDto {
    const entityUuid = entity.id;
    const entityObj: any = { ...entity };
    delete entityObj.id;

    if (entity.category) {
      const categoryDict = categoryDictionary();
      const lookup = categoryDict[entity.category];
      if (lookup) {
        entityObj.category = lookup;
      }
    }

    if (entity.categoryGroup) {
      const categoryGroupDict = categoryGroupDictionary();
      const lookup = categoryGroupDict[entity.categoryGroup];
      if (lookup) {
        entityObj.categoryGroup = lookup;
      }
    }
    const accountStatus = {
      canAcquire: entity.canAcquire,
      canAcquireMoto: entity.canAcquireMoto,
      canAcquireCnp: entity.canAcquireCnp,
      canAcquireMobile: entity.canAcquireMobile,
      canRefund: entity.canRefund,
      canStandIn: entity.canStandIn,
      hasChargeback: entity.hasChargeback,
      hadForcedRefund: entity.hadForcedRefund,
      hasDirectDebitRequest: entity.hasDirectDebitRequest,
      hadDirectDebitFailure: entity.hadDirectDebitFailure,
    };
    return new EntityUpdatedEventDto(
      trimObjectStringValues({
        entityUuid,
        accountStatus: JSON.stringify(accountStatus) === '{}' ? undefined : accountStatus,
        ...entityObj,
      }),
    );
  }

  saveEntityUpdatedProjection = async (dto: EntityUpdatedEventDto) => {
    await this.entityDb.saveEntityProjection(dto);
  };

  saveEntityReferredProjection = async (dto: EntityReferredEventDto) => {
    await this.entityDb.saveEntity(dto.referred, { referredBy: dto.entityUuid });
    await this.referralService.saveReferralProjection(dto);
  };

  saveEntityFeeRatesEffectiveProjection = async (dto: EntityFeeRatesEffectiveDto) => {
    const feeRateSettings = getFeeRateSettings(dto);
    info(
      `saveEntityFeeRatesEffectiveProjection: ${JSON.stringify(dto)} ${JSON.stringify(feeRateSettings)}`,
      dto.entityUuid,
    );
    await this.entityDb.saveEntity(dto.entityUuid, { feeRateSettings });
  };

  saveEntityFirstTransactionCreatedProjection = async (dto: EntityFirstTransactionCreatedDto) => {
    debug(`saveEntityFirstTransactionCreatedProjection: ${JSON.stringify(dto)}`);
    await this.entityDb.saveEntityFirstTransactionCreatedProjection(dto);
  };

  savePaymentSettingsProjection = async (dto: EntityPaymentSettingsDto) => {
    info(`save entity payment settings ${JSON.stringify(dto)}`, dto.entityUuid);
    const paymentSettings: PaymentSettings = dto.paymentLimits.reduce((acc: PaymentSettings, limit: PaymentLimit) => {
      const limits = {
        maximum: Number(limit.maximum),
        minimum: Number(limit.minimum),
      };
      if (limit.paymentType === PaymentType.CNP) {
        acc.cnpPaymentLimits = limits;
      }
      if (limit.paymentType === PaymentType.MOTO) {
        acc.motoPaymentLimits = limits;
      }
      if (limit.paymentType === PaymentType.CP) {
        acc.paymentLimits = limits;
      }
      if (limit.paymentType === PaymentType.CPOC) {
        acc.cpocPaymentLimits = limits;
      }
      return acc;
    }, {});
    await this.entityDb.saveEntity(dto.entityUuid, { paymentSettings });
  };

  savePrimaryAccountHolderChangedProjection = async (event: EntityPrimaryAccountHolderChangedEventDto) => {
    const { entityUuid, primaryAccountHolder } = event;

    const dto: EntityUpdatedEventDto = {
      entityUuid,
      primaryAccountHolder,
    };
    await this.entityDb.saveEntityProjection(dto);
  };

  attachSitDomicileAndCurrencyProjection = async (event: EntityDomicileAndCurrencyAttachedEventDto) => {
    const { entityUuid, domicile, currency } = event;
    const dto = {
      entityUuid,
      domicile,
      currency,
    };
    await this.entityDb.saveEntityProjection(dto);
  };

  checkForAdditionalEntityInfo = async (entityUuid: string): Promise<boolean> => {
    const entity = await this.entityDb.getEntityOrThrow(entityUuid);

    const isInRiskCategory = await this.isInRiskCategory(entity);
    const isRiskProhibitedMCC = await this.isOnboardingProhibitedMCC(entity);

    return (
      entity.type !== EntityType.COMPANY ||
      entity.estimatedAnnualRevenue <= this.environmentService.estimatedAnnualRevenueThreshold ||
      entity.category === categoryDictionary()[Category.OTHER] ||
      isInRiskCategory ||
      isRiskProhibitedMCC
    );
  };

  noOps = async (_event: EntityOnboardingStatusUpdatedEventDto) => {
    // No operation
  };

  requiredOnboardingDocumentsUpload = async (entityUuid: string): Promise<boolean> => {
    const entity = await this.entityDb.getEntityOrThrow(entityUuid);
    return this.isInOnboardingRiskCategory(entity);
  };

  regulatedMCCOnboarding = async (entityUuid: string): Promise<boolean> => {
    const entity = await this.entityDb.getEntityOrThrow(entityUuid);
    return this.isOnboardingRegulatedMCC(entity);
  };

  getEntityDocumentUploadUrl = async (entityUuid: string): Promise<string> => {
    const bucket = this.environmentService.entityDocumentUploadBucket;
    const key = `entity/${entityUuid}/${v4()}`;
    const params = { Bucket: bucket, Key: key };
    return this.s3.getSignedUrl(params, 'putObject');
  };

  getEntityDocumentUploadUrls = async (
    entityUuid: string,
    fileNames: Array<string>,
    customerUuid?: string,
    subject?: string,
    metadata?: JsonObject<string>,
  ): Promise<Array<DocumentUploadUrl>> => {
    let metadataInstance = { ...metadata };
    if (!metadataInstance) {
      metadataInstance = {
        entityUuid,
        ...(customerUuid ? { customerUuid } : {}),
        componentName: this.environmentService.componentName,
      };
    } else {
      metadataInstance = {
        ...metadataInstance,
        entityUuid,
        ...(customerUuid ? { customerUuid } : {}),
        componentName: this.environmentService.componentName,
      };
    }

    return this.getDocumentUploadUrls(
      'entity',
      entityUuid,
      fileNames,
      this.environmentService.entityDocumentUploadBucket,
      subject,
      metadataInstance,
    );
  };

  setAcceptTermsOfService = async ({
    accepted,
    entityUuid,
    customerUuid,
    acceptedAt,
  }: {
    accepted: boolean;
    entityUuid: string;
    customerUuid?: string;
    acceptedAt?: Date;
  }) => {
    debug(`EntityService -> setAcceptTermsOfService input is: ${accepted}`, entityUuid);

    const entity = await this.entityDb.getEntityOrThrow(entityUuid);

    const ezta = {
      accepted,
      customerUuid,
      acceptedAt: acceptedAt ?? new Date(),
    };

    await this.acceptTermsOfService({ ...ezta, entityUuid });

    const optimisticResult = {
      entity: {
        ...entity,
        termsOfService: {
          ...entity.termsOfService,
          ezta,
        },
      },
    };

    debug(`EntityService -> setAcceptTermsOfService: ${JSON.stringify(optimisticResult)}`, entityUuid);

    return optimisticResult;
  };

  updatePAH = async (customerUuid: string, entityUuid: string): Promise<boolean> =>
    this.amsApiService.updatePAH(customerUuid, entityUuid);

  private readonly createEntity = async (dto: EntityCreateRequestedEventDto | EntityGqlCreateRequestedEventDto) => {
    info(`calling createEntity: ${JSON.stringify(dto)}`);
    if (dto.country && dto.country !== 'AUS' && dto.country !== 'GBR') {
      return Promise.reject(new BadUserInput('Only AUS and GBR are supported countries.')); // NOSONAR
    }
    const domicile = Object.values(Domicile).includes(dto.country as Domicile) ? (dto.country as Domicile) : undefined;
    if (dto.country && !domicile) {
      return Promise.reject(new BadUserInput('Invalid or unsupported domicile/country.')); // NOSONAR
    }
    const entityCreateDto = {
      ...dto,
      manualEntry: dto.manualEntry ?? false,
      ...(dto.cohort?.length && {
        cohort: dto.cohort
          .map((cohort) =>
            Object.values(COHORT_CODE).includes(cohort as unknown as CohortCode)
              ? { code: cohort as unknown as CohortCode }
              : null,
          )
          .filter(Boolean) as CohortType[],
      }),
    };
    info(`sending entityCreateDto: ${JSON.stringify(entityCreateDto)}`);

    const trimmedDto = trimObjectStringValues(entityCreateDto) as any;
    info(`trimmedDto: ${JSON.stringify(trimmedDto)}`);
    if ('country' in trimmedDto) {
      delete trimmedDto.country;
    }

    info(`calling AMS createEntity: ${JSON.stringify({ trimmedDto, dto, domicile })}`);

    let result;
    const hasEntityUuid = 'entityUuid' in dto && dto.entityUuid;
    info(`hasEntityUuid: ${hasEntityUuid}, domicile: ${domicile}`);
    if (hasEntityUuid) {
      result = domicile
        ? await this.amsApiService.create<EntityCreateRequestedEventDto>(dto.entityUuid, trimmedDto, domicile)
        : await this.amsApiService.create<EntityCreateRequestedEventDto>(dto.entityUuid, trimmedDto);
    } else {
      result = domicile
        ? await this.amsApiService.createEntity<EntityGqlCreateRequestedEventDto>(trimmedDto, domicile)
        : await this.amsApiService.createEntity<EntityGqlCreateRequestedEventDto>(trimmedDto);
    }

    if (result.cohort?.length) {
      result.cohort = result.cohort.map((cohort: any) => (cohort as CohortType).code.toString() as any);
    }

    info(`createEntity result: ${JSON.stringify(result)}`);
    return result;
  };

  private readonly evaluateOnboardingDetails = async (
    entityUuid: string,
  ): Promise<OnboardingStatus.ONBOARDED | OnboardingStatus.REVIEW> => {
    // will need to be replaced with the value retrieved from 'domicile' global lookup table based on 'entityUuid'
    const domicile = 'AUS';
    const path = `${domicile}/entity/${entityUuid}/finaliseEntityOnboarding`;
    const endpoint = `${this.environmentService.amsEndpoint}/v2/${path}`;
    const payload = new EntityUpdatedEventDto({ entityUuid });
    const loggingInfo = {
      aggregateId: entityUuid,
      errorMessage: `Error finalising entity onboarding`,
    };
    try {
      // will need to be replaced with AMS API service wrapper library to perform request
      const response: AxiosResponse<OnboardingStatus.ONBOARDED | OnboardingStatus.REVIEW, undefined> =
        await axios.patch(endpoint, JSON.stringify(payload), {
          headers: { 'Content-Type': 'application/json' },
        });
      info(
        `invoke ams endpoint response: ${JSON.stringify(response.data)} ${response.status}`,
        loggingInfo.aggregateId,
      );
      return response.data;
    } catch (e) {
      error(JSON.stringify(e), entityUuid);
      return Promise.reject(new Error(`PATCH request to ${path} failed to be fulfilled`));
    }
  };

  private readonly isInRiskCategory = async (entity: Entity): Promise<boolean> => {
    let result = true;
    try {
      result = await this.riskService.isInRiskCategory({
        categoryGroup: entity.categoryGroup,
        category: entity.category,
      });
      debug(`EntityService -> isInRiskCategory: ${result}`, entity.id);
    } catch (e) /* istanbul ignore next */ {
      error(e);
    }
    return result;
  };

  private readonly isInOnboardingRiskCategory = async (entity: Entity): Promise<boolean> => {
    let result = true;
    try {
      result = await this.riskService.isInOnboardingRiskCategory({
        categoryGroup: entity.categoryGroup,
        category: entity.category,
      });
      debug(`EntityService -> isInOnboardingRiskCategory: ${result}`, entity.id);
    } catch (e) {
      error(e);
    }
    return result;
  };

  private readonly isOnboardingRegulatedMCC = async (entity: Entity): Promise<boolean> => {
    let result = true;
    try {
      result = await this.riskService.isOnboardingRegulatedMCC({
        categoryGroup: entity.categoryGroup,
        category: entity.category,
        state: entity.businessAddress?.state ?? entity.registeredAddress?.state,
      });
      debug(`EntityService -> isOnboardingRegulatedMCC: ${result}`, entity.id);
    } catch (e) {
      error(e);
    }
    info(`Returning result from isOnboardingRegulatedMCC: ${JSON.stringify(result)}`, entity.id);
    return result;
  };

  private readonly isOnboardingProhibitedMCC = async (entity: Entity): Promise<boolean> => {
    let result = true;
    try {
      result = await this.riskService.isOnboardingProhibitedMCC({
        categoryGroup: entity.categoryGroup,
        category: entity.category,
      });

      debug(`EntityService -> isOnboardingProhibitedMCC: ${result}`, entity.id);
    } catch (e) {
      error(e);
    }
    return result;
  };

  private readonly acceptTermsOfService = async ({
    accepted,
    entityUuid,
    customerUuid,
    acceptedAt,
  }: {
    accepted: boolean;
    entityUuid: string;
    customerUuid?: string;
    acceptedAt?: Date;
  }) => {
    return this.amsApiService.update(entityUuid, {
      entityUuid,
      termsOfService: {
        ezta: {
          accepted,
          customerUuid,
          acceptedAt: acceptedAt?.toISOString() ?? new Date().toISOString(),
        },
      },
    });
  };
}
