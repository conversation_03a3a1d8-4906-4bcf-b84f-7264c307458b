import { ApiAppServerlessStack, lambdasCrossAccountInvocationPolicy, vpcImport } from '@npco/component-bff-serverless';
import { lambdas } from './resources/mp/lambdas';
import {
  resolvers,
  bffResolvers,
  publishEntityFullSearchResultDataSource,
  publishEntityFullSearchResultResolver,
  publishEntityInitialSearchResultDataSource,
  publishEntityInitialSearchResultResolver,
  requiredAdditionalOnboardingInfoDataSource,
  requiredAdditionalOnboardingInfoResolver,
} from './resources/mp/resolvers';
import { MpApiAppEnvConfig, pluginsApp, createEventBridgeProjectionRuleAndPermission } from './resources/common';

export const envConfig = new MpApiAppEnvConfig('resources/mp/config/', true);

const { AWS_REGION: region } = process.env;

const sls = new ApiAppServerlessStack('entity', envConfig, {
  plugins: [...pluginsApp, 'serverless-plugin-lambda-dead-letter'],
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    ...envConfig.getAuth0(),
    ...envConfig.getEsbuild(),
    mpCqrsCommandHandler: envConfig.cqrsCommandHandler,
    sessionCacheTableName: envConfig.sessionCacheTableName,
    entityModelSerialGsi: '${env:ENTITY_MODEL_SERIAL_GSI}',
    iamUserSecret: envConfig.iamUserSecret,
    iamUserKey: envConfig.iamUserKey,
    auth0Audience: envConfig.auth0Audience,
    accessTokenGsi: envConfig.accessTokenGsi,
    modelSerialGsi: envConfig.modelSerialGsi,
    siteGsi: envConfig.siteGsi,
    entityCacheGsi: envConfig.entityCacheGsi,
    entityGsi: envConfig.entityGsi,
    merchantTableName: `${envConfig.dynamodbStackName}-${envConfig.merchantTable}`,
    merchantTableQueryRolePolicyArn: envConfig.merchantTableQueryRolePolicyArn,
    shortIdGsi: envConfig.shortIdGsi,
    siteNameGsi: envConfig.siteNameGsi,
    typeGsi: envConfig.typeGsi,
    cqrsStackName: {
      dev: '${opt:stage}-mp-cqrs',
      staging: '${opt:stage}-mp-cqrs',
      prod: '${opt:stage}-mp-cqrs',
      st: '${opt:stage}-mp-entity-cqrs',
    },
    cqrsEventBus:
      '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionArn}',
    mpCqrsProjectionDLQArn:
      '${cf:${self:custom.cqrsStackName.${opt:stage}, "${self:custom.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionDLQArn}',
    vpcImport,
    cmsStackName: '${env:STATIC_ENV_NAME}-cms-cqrs-apiHandlers',
    selfieVerificationEnabled: '${env:SELFIE_CHECK_VERIFICATION_ENABLED}',
    businessIdentifierSearchEndpoint: "${ssm:${opt:stage}-os-engine-private-api-endpoint, ''}",
    businessIdentifierSearchEndpointVersion: '${env:BUSINESS_IDENTIFIER_SEARCH_API_ENDPOINT_VERSION}',
    dcaTransactionLookupSqsQueueArn:
      'arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.serviceName}-banking-sqs-dcaTransactionLookup',
    dcaTransactionLookupSqsQueueUrl:
      'https://sqs.${self:provider.region}.amazonaws.com/${self:custom.accountId}/${self:custom.serviceName}-banking-sqs-dcaTransactionLookup',
    transactionTotalsUpdateSqsQueueArn:
      'arn:aws:sqs:${self:provider.region}:${self:custom.accountId}:${self:custom.serviceName}-txn-totalsUpdateHandler.fifo',
    transactionTotalsUpdateSqsQueueUrl:
      'https://sqs.${self:provider.region}.amazonaws.com/${self:custom.accountId}/${self:custom.serviceName}-txn-totalsUpdateHandler.fifo',

    estimatedAnnualRevenueThreshold:
      '${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/ESTIMATED_ANNUAL_REVENUE_THRESHOLD}',
    riskRuleApiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5-endpoint}',
    riskRuleEndpointRegulatedMCCEndpoint: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-regulated-mcc-endpoint}',
    riskRuleEndpointCategoryPath: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5-endpoint-path}',
    onboardingRiskRuleApiEndpoint: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule22-endpoint}',
    onboardingRiskRuleEndpointCategoryPath: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule22-endpoint-path}',
    riskRuleEndpointProhibitedMCCPath: '${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5a-endpoint-path}',
    cmsEndpoint: '${ssm:${env:STATIC_ENV_NAME}-bankingwrapper-engine-api-endpoint}/',
    streamArn: '${param:dbStreamArn, "${cf:${self:custom.serviceName}-dynamodb.EntityTableStreamArn, \'\'}"}',
    documentUploadsBucket: '${ssm:${self:custom.serviceName}-assets-document-uploads}',
    entityStreamHandlerFilterPatterns: [
      { prefix: 'deposit.' },
      { prefix: 'transaction.' },
      { prefix: 'device.settings' },
      { prefix: 'device.software' },
      { prefix: 'sim.core' },
      { prefix: 'dca.transaction.' },
      { prefix: 'contact.core' },
      { prefix: 'dcac.core' },
      { prefix: 'dca.core' },
      { prefix: 'issuing.account.savings' },
      { prefix: 'ecommerce.' },
      { prefix: 'paymentinstrument.core' },
      { prefix: 'contact#' },
      { prefix: 'customer.core' },
    ],
    getEntityDailyLimitLambda: '${self:custom.serviceName}-banking-getEntityDailyLimit',
    getSavingsAccountProductByEntityLambda: '${self:custom.serviceName}-banking-getSavingsAccountProductByEntity',
    getEntityAddressTimeZoneLambda: '${self:custom.serviceName}-banking-getEntityAddressTimeZone',
    checkForAdditionalEntityInfoLambda:
      '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-entity-checkForAdditionalEntityInfoHandler',
    requiredOnboardingDocumentsUploadLambda:
      '${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-entity-requiredOnboardingDocumentsUploadHandler',
    domicileLookupTableReadRolePolicyArn: {
      'Fn::ImportValue': '${env:STATIC_ENV_NAME}-ams-engine-domicileLookupTableReadRolePolicyArn',
    },
  },

  functions: lambdas,
  resources: {
    ...createEventBridgeProjectionRuleAndPermission(
      'entityProjectionHandler',
      'EntityProjectionHandlerLambdaFunction',
      [
        'mp.Entity.Created',
        'mp.Entity.CreateFailed',
        'mp.Entity.Updated',
        'mp.Entity.FeeRatesEffective',
        'mp.Entity.FeeRatesEffected',
        'mp.Entity.FeeRatesAssigned',
        'mp.Entity.OnboardingStatusUpdated',
        'mp.Entity.TagAdded',
        'mp.Entity.TagRemoved',
        'mp.Entity.SubcategoryCreated',
        'mp.Entity.SubcategoryUpdated',
        'mp.Entity.SubcategoryDeleted',
        'mp.Entity.Referred',
        'mp.Entity.FirstTransactionCreated',
        'mp.Entity.PaymentSettingsUpdated',
        'mp.Entity.PrimaryAccountHolderChanged',
        'mp.Entity.DomicileCurrencyAttached',
      ],
    ),
    ...resolvers,
    ...bffResolvers,
    publishEntityFullSearchResultDataSource,
    publishEntityFullSearchResultResolver,
    publishEntityInitialSearchResultDataSource,
    publishEntityInitialSearchResultResolver,
    requiredAdditionalOnboardingInfoDataSource,
    requiredAdditionalOnboardingInfoResolver,
    ...lambdasCrossAccountInvocationPolicy(lambdas, region, false),
  },
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
    AUTH0_CLIENT_ID: '${self:custom.auth0ClientId}',
    AUTH0_CLIENT_SECRET: '${self:custom.auth0ClientSecret}',
    CQRS_COMMAND_HANDLER: '${self:custom.mpCqrsCommandHandler}',
    IS_ZELLER_SESSION_ID_ENABLED: '${env:IS_ZELLER_SESSION_ID_ENABLED}',
    SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    SELFIE_CHECK_VERIFICATION_ENABLED: '${self:custom.selfieVerificationEnabled}',
    MULTI_ENTITY_ENABLED: '${env:MULTI_ENTITY_ENABLED}',
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}',
  },
});

module.exports = sls.build();
