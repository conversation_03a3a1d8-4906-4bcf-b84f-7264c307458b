Resources:
  GetEntityHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt GetEntityHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  CreateSoletraderEntityHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt CreateSoletraderEntityHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  UpdateEntityHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt UpdateEntityHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  ConfirmEntityDetailsInitialHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt ConfirmEntityDetailsInitialHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  ResponseInitialSearchCompletedHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt ResponseInitialSearchCompletedHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  SearchEntityDetailsInitialHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt SearchEntityDetailsInitialHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  ResponseFullSearchCompletedHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt ResponseFullSearchCompletedHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  SearchEntityDetailsFullHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt SearchEntityDetailsFullHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  SaveEntityOnboardingDetailsHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt SaveEntityOnboardingDetailsHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  RetrieveOnboardingDetailsInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt RetrieveOnboardingDetailsLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'

  SaveEntityOnboardingErrorsHandlerInvocationPolicy:
    Type: AWS::Lambda::Permission
    Properties:
      Action: 'lambda:InvokeFunction'
      FunctionName: !GetAtt SaveEntityOnboardingErrorsHandlerLambdaFunction.Arn
      Principal: 'arn:aws:iam::${self:custom.remoteAccountId.${self:provider.region}}:root'
