Resources:
  getEntityDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: getEntityDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt GetEntityHandlerLambdaFunction.Arn

  getEntityResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: getEntity
      TypeName: Query
      RequestMappingTemplate: |
        {
         "version" : "2018-05-29",
         "operation": "Invoke",
         "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.to<PERSON><PERSON>($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt getEntityDataSource.Name

  updateEntityDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: updateEntityDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt UpdateEntityHandlerLambdaFunction.Arn

  updateEntityResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: updateEntity
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt updateEntityDataSource.Name

  createSoletraderEntityDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: createSoletraderEntityDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt CreateSoletraderEntityHandlerLambdaFunction.Arn

  createSoletraderEntityResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: createSoletraderEntity
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt createSoletraderEntityDataSource.Name

  confirmEntityDetailsInitialDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: confirmEntityDetailsInitialDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt ConfirmEntityDetailsInitialHandlerLambdaFunction.Arn

  confirmEntityDetailsInitialResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: confirmEntityDetailsInitial
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt confirmEntityDetailsInitialDataSource.Name

  searchEntityDetailsInitialDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: searchEntityDetailsInitialDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt SearchEntityDetailsInitialHandlerLambdaFunction.Arn

  searchEntityDetailsInitialResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: searchEntityDetailsInitial
      TypeName: Subscription
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt searchEntityDetailsInitialDataSource.Name

  publishEntityInitialSearchResultDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: publishEntityInitialSearchResultDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: NONE

  publishEntityInitialSearchResultResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: publishEntityInitialSearchResult
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version": "2017-02-28",
          "payload": $utils.toJson($context.arguments.input)
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt publishEntityInitialSearchResultDataSource.Name

  searchEntityDetailsFullDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: searchEntityDetailsFullDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt SearchEntityDetailsFullHandlerLambdaFunction.Arn

  searchEntityDetailsFullResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: searchEntityDetailsFull
      TypeName: Subscription
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt searchEntityDetailsFullDataSource.Name

  publishEntityFullSearchResultDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: publishEntityFullSearchResultDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: NONE

  publishEntityFullSearchResultResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: publishEntityFullSearchResult
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version": "2017-02-28",
          "payload": $utils.toJson($context.arguments.input)
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt publishEntityFullSearchResultDataSource.Name

  saveEntityOnboardingDetailsDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: saveEntityOnboardingDetailsDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt SaveEntityOnboardingDetailsHandlerLambdaFunction.Arn

  saveEntityOnboardingDetailsResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: saveEntityOnboardingDetails
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt saveEntityOnboardingDetailsDataSource.Name

  retrieveEntityOnboardingDetailsDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: retrieveEntityOnboardingDetailsDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt RetrieveOnboardingDetailsLambdaFunction.Arn

  retrieveEntityOnboardingDetailsResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: retrieveEntityOnboardingDetails
      TypeName: Query
      RequestMappingTemplate: |
        {
         "version" : "2018-05-29",
         "operation": "Invoke",
         "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt retrieveEntityOnboardingDetailsDataSource.Name

  saveEntityOnboardingErrorsDataSource:
    Type: AWS::AppSync::DataSource
    Properties:
      Name: saveEntityOnboardingErrorsDataSource
      ApiId: ${self:provider.appSyncApiId}
      Type: AWS_LAMBDA
      ServiceRoleArn: ${self:custom.appsyncDataSourceRoleArn}
      LambdaConfig:
        LambdaFunctionArn: !GetAtt SaveEntityOnboardingErrorsHandlerLambdaFunction.Arn

  saveEntityOnboardingErrorsResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: saveEntityOnboardingErrors
      TypeName: Mutation
      RequestMappingTemplate: |
        {
          "version" : "2018-05-29",
          "operation": "Invoke",
          "payload": { "args": $util.toJson($context.args), "identity": $util.toJson($context.identity), "request": $util.toJson($context.request), "authType": $util.toJson($util.authType()), "source": $util.toJson($context.source), "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt saveEntityOnboardingErrorsDataSource.Name

  getCustomerEntityResolver:
    Type: AWS::AppSync::Resolver
    Properties:
      ApiId: ${self:provider.appSyncApiId}
      FieldName: entity
      TypeName: CustomerEntityRelation
      RequestMappingTemplate: |
        #if ($util.isNullOrEmpty($context.source.entityUuid))
          #return
        #end
        {
         "version" : "2018-05-29",
         "operation": "Invoke",
         "payload": { 
          "args": { "entityUuid": $util.toJson($context.source.entityUuid) }, 
          "identity": $util.toJson($context.identity), 
          "request": $util.toJson($context.request), 
          "authType": $util.toJson($util.authType()),  
          "source": $util.toJson($context.source), 
          "info": $util.toJson($context.info)}
        }
      ResponseMappingTemplate: |
        #if($context.result && $context.result.errorMessage)
        $utils.error($context.result.errorMessage, $context.result.errorType, $context.result.data, $context.result.errors)
        #elseif($context.error)
        $utils.error($context.error.message, $context.error.type, $context.result)
        #end
        $util.toJson($context.result)
      DataSourceName: !GetAtt getEntityDataSource.Name
