import type { ServerlessFunctions } from '@npco/component-bff-serverless';
import { Action, Arn, ManagedPolicy, loadTemplateFile } from '@npco/component-bff-serverless';
import { deploymentSettings } from '@npco/component-bff-serverless';

import { lambdaCommon } from '../common';

export const lambdas: ServerlessFunctions = {
  onEntityTableStreamHandler: {
    handler: 'src/lambda/crms/publisher/entityTableStreamHandler.onEntityTableStreamHandler',
    name: 'onEntityTableStreamHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      APP_SYNC_ENDPOINT: '${self:custom.appSyncEndpoint}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      MERCHANT_TABLE: '${self:custom.merchantTableName}',
      IAM_USER_KEY: '${self:custom.iamUserKey}',
      IAM_USER_SECRET: '${self:custom.iamUserSecret}',
      TYPE_GSI: '${self:custom.typeGsi}',
      DCA_TRANSACTION_LOOKUP_SQS_URL: '${self:custom.dcaTransactionLookupSqsUrl}',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.allGsiQueryRolePolicyArn,
        '${self:custom.merchantTableQueryRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        onEntityTableUpdateDBStreamPolicy: [
          {
            actions: [
              Action.dynamodb.ListStreams,
              Action.dynamodb.DescribeStream,
              Action.dynamodb.DescribeTable,
              Action.dynamodb.GetItem,
              Action.dynamodb.GetRecords,
              Action.dynamodb.GetShardIterator,
            ],
            resources: ['${self:custom.tableStreamArn}'],
          },
        ],
        onEntityTableUpdateSQSPolicy: [
          {
            actions: [Action.sqs.SendMessage],
            resources: ['${self:custom.dcaTransactionLookupSqsArn}'],
          },
        ],
      },
    },
    timeout: 300,
  },
  getEntityHandler: {
    handler: 'src/lambda/crms/entityLambdas.getEntityHandler',
    name: 'getEntityHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}' },
    appsync: {
      fieldName: 'getEntity',
      typeName: 'Query',
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  updateEntityHandler: {
    handler: 'src/lambda/crms/entityLambdas.updateEntityHandler',
    name: 'updateEntityHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'updateEntity',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  updateEntityRefundOverdraftHandler: {
    handler: 'src/lambda/crms/crmsEntityLambdas.updateEntityRefundOverdraftHandler',
    name: 'updateEntityRefundOverdraft',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      REFUND_OVERDRAFT_ENDPOINT: '${self:custom.refundOverdraftEndpoint}',
      REFUND_OVERDRAFT_ENDPOINT_PATH: '${self:custom.refundOverdraftEndpointPath}',
    },
    appsync: {
      fieldName: 'updateEntityRefundOverdraft',
      typeName: 'Mutation',
    },
    policy: {
      managed: [ManagedPolicy.crossAccountInvocationPolicyArn],
    },
  },
  updateEntityFeeRatePaymentSettingsHandler: {
    handler: 'src/lambda/crms/crmsEntityLambdas.updateEntityFeeRatePaymentSettingsHandler',
    name: 'updateEntityFeeRatePaymentSettings',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      FS_API_ENDPOINT: '${self:custom.fsApiEndpoint}',
      FS_API_ENDPOINT_VERSION: '${self:custom.fsApiEndpointVersion}',
      CQRS_COMMAND_HANDLER: '${self:custom.cqrsCommandHandler}',
    },
    appsync: {
      fieldName: 'updateEntityFeeRatePaymentSettings',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        cqrsCommandHandlerPolicy: [
          {
            actions: [Action.lambda.InvokeFunction],
            resources: [Arn.lambda.function('${self:custom.cqrsCommandHandler}')],
          },
        ],
      },
    },
  },
  getEntityFeeRatePaymentSettingsHandler: {
    handler: 'src/lambda/crms/crmsEntityLambdas.getEntityFeeRatePaymentSettingsHandler',
    name: 'getEntityFeeRatePaymentSettings',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'getEntityFeeRatePaymentSettings',
      typeName: 'Query',
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  getEntityDocumentUrlsHandler: {
    handler: 'src/lambda/crms/crmsEntityLambdas.getEntityDocumentUrlsHandler',
    name: 'getEntityDocumentUrlsHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      S3_ENTITY_DOCUMENT_UPLOADS: '${self:custom.documentUploadsBucket}',
    },
    appsync: {
      fieldName: 'getEntityDocumentUrls',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        entityGetDocumentUrlS3Policy: [
          {
            actions: [Action.s3.GetObject, 's3:HeadObject'],
            resources: [Arn.s3('${self:custom.documentUploadsBucket}/*')],
          },
        ],
        entityListBucketS3Policy: [
          {
            actions: [Action.s3.ListBucket],
            resources: [Arn.s3('${self:custom.documentUploadsBucket}')],
          },
        ],
      },
    },
  },
  getEntityDocumentUploadUrlHandler: {
    handler: 'src/lambda/crms/entityLambdas.getEntityDocumentUploadUrlHandler',
    name: 'getEntityDocumentUploadUrlHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      S3_ENTITY_DOCUMENT_UPLOADS: '${self:custom.documentUploadsBucket}',
    },
    appsync: {
      fieldName: 'getEntityDocumentUploadUrl',
      typeName: 'Query',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getEntityDocumentUploadUrlS3Policy: [
          {
            actions: [Action.s3.PutObject],
            resources: [Arn.s3('${self:custom.documentUploadsBucket}/*')],
          },
        ],
      },
    },
  },
  updateEntityStandInRulesHandler: {
    handler: 'src/lambda/crms/crmsEntityLambdas.updateEntityStandInRulesHandler',
    name: 'updateEntityStandInRulesHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'updateEntityStandInRules',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  finaliseEntityOnboardingHandler: {
    handler: 'src/lambda/crms/entityLambdas.finaliseEntityOnboardingHandler',
    name: 'finaliseEntityOnboardingHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      CMS_API_ENDPOINT: '${self:custom.cmsEndpoint}',
      RISK_RULE_API_ENDPOINT: '${self:custom.riskRuleApiEndpoint}',
      RISK_RULE_ENDPOINT_CATEGORY_PATH: '${self:custom.riskRuleEndpointCategoryPath}',
      ONBOARDING_RISK_RULE_API_ENDPOINT: '${self:custom.onboardingRiskRuleApiEndpoint}',
      ONBOARDING_RISK_RULE_ENDPOINT_CATEGORY_PATH: '${self:custom.onboardingRiskRuleEndpointCategoryPath}',
      RISK_RULE_ENDPOINT_PROHIBITED_MCC_PATH: '${self:custom.riskRuleEndpointProhibitedMCCPath}',
      SELFIE_CHECK_VERIFICATION_ENABLED: '${self:custom.selfieVerificationEnabled}',
    },
    appsync: {
      fieldName: 'finaliseEntityOnboarding',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  validateFinaliseEntityOnboarding: {
    handler: 'src/lambda/crms/entityLambdas.validateFinaliseEntityOnboarding',
    name: 'validateFinaliseEntityOnboarding',
    ...lambdaCommon,
    deploymentSettings,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}' },
    appsync: {
      fieldName: 'validateFinaliseEntityOnboarding',
      typeName: 'Query',
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  getEntityTagsHandler: {
    handler: 'src/lambda/crms/tagLambdas.getEntityTagsHandler',
    name: 'getTagsHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}' },
    appsync: {
      fieldName: 'getEntityTags',
      typeName: 'Query',
      dataResolverName: {
        namePrefix: 'getEntityTags',
      },
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  addEntityTagHandler: {
    handler: 'src/lambda/crms/tagLambdas.addEntityTagHandler',
    name: 'addTagHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'addEntityTag',
      typeName: 'Mutation',
      dataResolverName: {
        namePrefix: 'addEntityTag',
      },
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  removeEntityTagHandler: {
    handler: 'src/lambda/crms/tagLambdas.removeEntityTagHandler',
    name: 'removeTagHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'removeEntityTag',
      typeName: 'Mutation',
      dataResolverName: {
        namePrefix: 'removeEntityTag',
      },
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  getEntitySubcategoriesHandler: {
    handler: 'src/lambda/crms/subcategoryLambdas.getEntitySubcategoriesHandler',
    name: 'getSubcategoriesHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}' },
    appsync: {
      fieldName: 'getEntitySubcategories',
      typeName: 'Query',
      dataResolverName: {
        namePrefix: 'getEntitySubcategories',
      },
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  getAllSubcategoriesHandler: {
    handler: 'src/lambda/crms/subcategoryLambdas.getAllSubcategoriesHandler',
    name: 'getAllSubcategoriesHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}' },
    appsync: {
      fieldName: 'getAllSubcategories',
      typeName: 'Query',
      dataResolverName: {
        namePrefix: 'getAllSubcategories',
      },
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  addEntitySubcategoryHandler: {
    handler: 'src/lambda/crms/subcategoryLambdas.addEntitySubcategoryHandler',
    name: 'addSubcategoryHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'addEntitySubcategory',
      typeName: 'Mutation',
      dataResolverName: {
        namePrefix: 'addEntitySubcategory',
      },
    },
    policy: { managed: [ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  updateEntitySubcategoryHandler: {
    handler: 'src/lambda/crms/subcategoryLambdas.updateEntitySubcategoryHandler',
    name: 'updateSubcategoryHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'updateEntitySubcategory',
      typeName: 'Mutation',
      dataResolverName: {
        namePrefix: 'updateEntitySubcategory',
      },
    },
    policy: { managed: [ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  removeEntitySubcategoryHandler: {
    handler: 'src/lambda/crms/subcategoryLambdas.removeEntitySubcategoryHandler',
    name: 'removeSubcategoryHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'removeEntitySubcategory',
      typeName: 'Mutation',
      dataResolverName: {
        namePrefix: 'removeEntitySubcategory',
      },
    },
    policy: { managed: [ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  getReferralsHandler: {
    handler: 'src/lambda/crms/referralLambdas.getReferralsHandler',
    name: 'getReferralsHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}' },
    appsync: {
      fieldName: 'referrals',
      typeName: 'Entity',
    },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
  },
  getEntitySubcategoryResolverHandler: {
    handler: 'src/lambda/crms/subcategoryLambdas.getEntitySubcategoryResolverHandler',
    name: 'getEntitySubcategoryResolverHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}' },
    policy: { managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn] },
    appsync: {
      typeName: 'DebitCardAccountTransaction',
      fieldName: 'subcategoryDetails',
      template: {
        request: loadTemplateFile('resources/crms/template/debitCardTransactionsSubcategoryRequest.vtl'),
        response: loadTemplateFile('resources/crms/template/response.vtl'),
      },
    },
  },
  getDocumentUploadUrlsHandler: {
    handler: 'src/lambda/crms/documentLambda.getDocumentUploadUrlsHandler',
    name: 'getDocumentUploadUrlsHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      S3_DOCUMENT_UPLOADS: '${self:custom.merchantPortalDocumentUploadBucketName}',
      S3_ENTITY_DOCUMENT_UPLOADS: '${self:custom.documentUploadsBucket}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
      inline: {
        getSecDocumentUploadUrlsS3Policy: [
          {
            actions: [Action.s3.PutObject],
            resources: [Arn.s3('${self:custom.documentUploadsBucket}/*')],
          },
        ],
        getDocumentUploadUrlsS3Policy: [
          {
            actions: [Action.s3.PutObject],
            resources: [Arn.s3('${self:custom.merchantPortalDocumentUploadBucketName}/*')],
          },
        ],
      },
    },
  },
  selectDepositAccountHandler: {
    handler: 'src/lambda/crms/entityLambdas.selectDepositAccountHandler',
    name: 'selectDepositAccountHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'selectDepositAccount',
      typeName: 'Mutation',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, ManagedPolicy.crossAccountInvocationPolicyArn],
    },
  },
  updatePAHHandler: {
    handler: 'src/lambda/crms/entityLambdas.updatePrimaryAccountHolderHandler',
    name: 'updatePAH',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      COMPONENT_TABLE: '${self:custom.componentTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'updatePAH',
      typeName: 'Mutation',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
};
