import { ManagedPolicy, ServerlessFunctions, deploymentSettings } from '@npco/component-bff-serverless';

import { lambdaCommon } from '../common';

export const lambdas: ServerlessFunctions = {
  getEntityHandler: {
    handler: 'src/lambda/sdk/entityLambdas.getEntityHandler',
    name: 'getEntityHandler',
    ...lambdaCommon,
    deploymentSettings,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}' },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
};
