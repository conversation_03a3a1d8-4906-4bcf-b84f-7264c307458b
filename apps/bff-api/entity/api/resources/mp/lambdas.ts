import type { ServerlessFunctions } from '@npco/component-bff-serverless';
import { Action, Arn, loadTemplateFile, ManagedPolicy } from '@npco/component-bff-serverless';

export const lambdas: ServerlessFunctions = {
  getEntityHandler: {
    handler: 'src/lambda/mp/entityLambda.getEntityHandler',
    name: 'getEntityHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    appsync: {
      fieldName: 'getEntity',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  checkForAdditionalEntityInfoHandler: {
    handler: 'src/lambda/mp/entityLambda.checkForAdditionalEntityInfoHandler',
    name: 'checkForAdditionalEntityInfoHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      ESTIMATED_ANNUAL_REVENUE_THRESHOLD: '${self:custom.estimatedAnnualRevenueThreshold}',
      RISK_RULE_API_ENDPOINT: '${self:custom.riskRuleApiEndpoint}',
      RISK_RULE_ENDPOINT_CATEGORY_PATH: '${self:custom.riskRuleEndpointCategoryPath}',
      RISK_RULE_ENDPOINT_PROHIBITED_MCC_PATH: '${self:custom.riskRuleEndpointProhibitedMCCPath}',
    },
    appsync: {
      fieldName: 'checkForAdditionalEntityInfo',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  requiredOnboardingDocumentsUploadHandler: {
    handler: 'src/lambda/mp/entityLambda.requiredOnboardingDocumentsUploadHandler',
    name: 'requiredOnboardingDocumentsUploadHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      ONBOARDING_RISK_RULE_API_ENDPOINT: '${self:custom.onboardingRiskRuleApiEndpoint}',
      ONBOARDING_RISK_RULE_ENDPOINT_CATEGORY_PATH: '${self:custom.onboardingRiskRuleEndpointCategoryPath}',
      RISK_RULE_API_ENDPOINT: '${self:custom.riskRuleApiEndpoint}',
      RISK_RULE_ENDPOINT_PROHIBITED_MCC_PATH: '${self:custom.riskRuleEndpointProhibitedMCCPath}',
    },
    appsync: {
      fieldName: 'requiredOnboardingDocumentsUpload',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  regulatedMCCOnboardingHandler: {
    handler: 'src/lambda/mp/entityLambda.regulatedMCCOnboardingHandler',
    name: 'regulatedMCCOnboardingHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      RISK_RULE_ENDPOINT_REGULATED_MCC_ENDPOINT: '${self:custom.riskRuleEndpointRegulatedMCCEndpoint}',
    },
    appsync: {
      fieldName: 'regulatedBusiness',
      typeName: 'AdditionalOnboardingInfoRequired',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  createSoletraderEntityHandler: {
    handler: 'src/lambda/mp/entityLambda.createSoletraderEntityHandler',
    name: 'createSoletraderEntityHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'createSoletraderEntity',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  confirmUnregSoleTradInitDetailsHandler: {
    handler: 'src/lambda/mp/entityLambda.confirmUnregSoleTradInitDetailsHandler',
    name: 'confirmUnregSoleTradInitDetailsHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'confirmUnregisteredSoleTraderInitialDetails',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  confirmUnregInitDetailsHandler: {
    handler: 'src/lambda/mp/entityLambda.confirmUnregInitDetailsHandler',
    name: 'confirmUnregInitDetailsHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'confirmUnregisteredBusinessInitialDetails',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  updateEntityHandler: {
    handler: 'src/lambda/mp/entityLambda.updateEntityHandler',
    name: 'updateEntityHandler',
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'updateEntity',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  confirmEntityDetailsInitialHandler: {
    handler: 'src/lambda/mp/entityLambda.confirmEntityDetailsInitialHandler',
    name: 'confirmEntityDetailsInitialHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'confirmEntityDetailsInitial',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  confirmRegBusInitDetailsHandler: {
    handler: 'src/lambda/mp/entityLambda.confirmRegBusInitDetailsHandler',
    name: 'confirmRegBusInitDetailsHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'confirmRegisteredBusinessInitialDetails',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  selectDepositAccountHandler: {
    handler: 'src/lambda/mp/entityMfaLambda.selectDepositAccountHandler',
    name: 'selectDepositAccountHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      MFA_ENROLMENT_ENABLED: '${env:MFA_ENROLMENT_ENABLED}',
    },
    appsync: {
      fieldName: 'selectDepositAccount',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  responseInitialSearchCompletedHandler: {
    handler: 'src/lambda/mp/publisher/entityLambda.responseInitialSearchCompletedHandler',
    name: 'responseInitialSearch',
    tracing: true,
    environment: {
      IAM_USER_KEY: '${self:custom.iamUserKey}',
      IAM_USER_SECRET: '${self:custom.iamUserSecret}',
      APP_SYNC_ENDPOINT: '${self:custom.appSyncEndpoint}',
    },
  },
  searchEntityDetailsInitialHandler: {
    handler: 'src/lambda/mp/entityLambda.searchEntityDetailsInitialHandler',
    name: 'searchEntityDetailsInitialHandler',
    tracing: true,
    environment: {
      CQRS_COMMAND_HANDLER: '${self:custom.mpCqrsCommandHandler}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    appsync: {
      fieldName: 'searchEntityDetailsInitial',
      typeName: 'Subscription',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        initialSearchInvokeLambdaPolicy: [
          {
            effect: 'Allow',
            actions: ['lambda:InvokeFunction'],
            resources: ['arn:aws:lambda:${self:provider.region}:*:function:${self:custom.mpCqrsCommandHandler}*'],
          },
        ],
      },
    },
  },
  responseFullSearchCompletedHandler: {
    handler: 'src/lambda/mp/publisher/entityLambda.responseFullSearchCompletedHandler',
    name: 'responseFullSearch',
    tracing: true,
    environment: {
      IAM_USER_KEY: '${self:custom.iamUserKey}',
      IAM_USER_SECRET: '${self:custom.iamUserSecret}',
      APP_SYNC_ENDPOINT: '${self:custom.appSyncEndpoint}',
    },
    policy: {
      managed: [ManagedPolicy.crossAccountInvocationPolicyArn],
    },
  },
  searchEntityDetailsFullHandler: {
    handler: 'src/lambda/mp/entityLambda.searchEntityDetailsFullHandler',
    name: 'searchEntityDetailsFullHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      CQRS_COMMAND_HANDLER: '${self:custom.mpCqrsCommandHandler}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    appsync: {
      fieldName: 'searchEntityDetailsFull',
      typeName: 'Subscription',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        fullSearchInvokeLambdaPolicy: [
          {
            effect: 'Allow',
            actions: ['lambda:InvokeFunction'],
            resources: ['arn:aws:lambda:${self:provider.region}:*:function:${self:custom.mpCqrsCommandHandler}*'],
          },
        ],
      },
    },
  },
  entityProjectionHandler: {
    handler: 'src/lambda/mp/entityLambda.entityProjectionHandler',
    name: 'entityProjectionHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      ONBOARDING_RISK_RULE_API_ENDPOINT: '${self:custom.onboardingRiskRuleApiEndpoint}',
      ONBOARDING_RISK_RULE_ENDPOINT_CATEGORY_PATH: '${self:custom.onboardingRiskRuleEndpointCategoryPath}',
      RISK_RULE_API_ENDPOINT: '${self:custom.riskRuleApiEndpoint}',
      RISK_RULE_ENDPOINT_PROHIBITED_MCC_PATH: '${self:custom.riskRuleEndpointProhibitedMCCPath}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        entityProjectionSqsPolicy: [
          {
            effect: 'Allow',
            actions: ['sqs:SendMessage'],
            resources: ['${self:custom.mpCqrsProjectionDLQArn}'],
          },
        ],
      },
    },
    deadLetter: { targetArn: '${self:custom.mpCqrsProjectionDLQArn}' },
  } as any,
  saveEntityOnboardingDetailsHandler: {
    handler: 'src/lambda/mp/entityLambda.saveEntityOnboardingDetailsHandler',
    name: 'saveEntityOnboardingDetails',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'saveEntityOnboardingDetails',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  retrieveEntityOnboardingDetailsHandler: {
    handler: 'src/lambda/mp/entityLambda.retrieveEntityOnboardingDetailsHandler',
    name: 'retrieveEntityOnboardingDetails',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'retrieveEntityOnboardingDetails',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  saveEntityOnboardingErrorsHandler: {
    handler: 'src/lambda/mp/entityLambda.saveEntityOnboardingErrorsHandler',
    name: 'saveEntityOnboardingErrors',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'saveEntityOnboardingErrors',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  finaliseEntityOnboardingHandler: {
    handler: 'src/lambda/mp/entityLambda.finaliseEntityOnboardingHandler',
    name: 'finaliseEntityOnboarding',
    tracing: true,
    environment: {
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      CMS_API_ENDPOINT: '${self:custom.cmsEndpoint}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      ONBOARDING_RISK_RULE_API_ENDPOINT: '${self:custom.onboardingRiskRuleApiEndpoint}',
      ONBOARDING_RISK_RULE_ENDPOINT_CATEGORY_PATH: '${self:custom.onboardingRiskRuleEndpointCategoryPath}',
      RISK_RULE_API_ENDPOINT: '${self:custom.riskRuleApiEndpoint}',
      RISK_RULE_ENDPOINT_PROHIBITED_MCC_PATH: '${self:custom.riskRuleEndpointProhibitedMCCPath}',
      SELFIE_CHECK_VERIFICATION_ENABLED: '${self:custom.selfieVerificationEnabled}',
    },
    appsync: {
      fieldName: 'finaliseEntityOnboarding',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  getEntityDocumentUploadUrlHandler: {
    handler: 'src/lambda/mp/entityLambda.getEntityDocumentUploadUrlHandler',
    name: 'getEntityDocumentUploadUrlHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      S3_ENTITY_DOCUMENT_UPLOADS: '${self:custom.documentUploadsBucket}',
    },
    appsync: {
      fieldName: 'getEntityDocumentUploadUrl',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        getEntityDocumentUploadUrlS3Policy: [
          {
            effect: 'Allow',
            actions: ['s3:PutObject'],
            resources: ['arn:aws:s3:::${self:custom.documentUploadsBucket}/*'],
          },
        ],
      },
    },
  },
  getEntityDocumentUploadUrlsHandler: {
    handler: 'src/lambda/mp/entityLambda.getEntityDocumentUploadUrlsHandler',
    name: 'getEntityDocumentUploadUrlsHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      S3_ENTITY_DOCUMENT_UPLOADS: '${self:custom.documentUploadsBucket}',
    },
    appsync: {
      fieldName: 'getEntityDocumentUploadUrls',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
      inline: {
        getEntityDocumentUploadUrlsS3Policy: [
          {
            effect: 'Allow',
            actions: ['s3:PutObject'],
            resources: ['arn:aws:s3:::${self:custom.documentUploadsBucket}/*'],
          },
        ],
      },
    },
  },
  getEntityTagsHandler: {
    handler: 'src/lambda/mp/tagLambda.getEntityTagsHandler',
    name: 'getTagsHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    appsync: {
      fieldName: 'getEntityTags',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  addEntityTagHandler: {
    handler: 'src/lambda/mp/tagLambda.addEntityTagHandler',
    name: 'addTagHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'addEntityTag',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  removeEntityTagHandler: {
    handler: 'src/lambda/mp/tagLambda.removeEntityTagHandler',
    name: 'removeTagHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'removeEntityTag',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  onEntityTableStreamHandler: {
    handler: 'src/lambda/mp/publisher/entityTableStreamHandler.onEntityTableStreamHandler',
    name: 'onEntityTableStreamHandler',
    tracing: true,
    environment: {
      IAM_USER_KEY: '${self:custom.iamUserKey}',
      IAM_USER_SECRET: '${self:custom.iamUserSecret}',
      APP_SYNC_ENDPOINT: '${self:custom.appSyncEndpoint}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      MERCHANT_TABLE: '${self:custom.merchantTableName}',
      DCA_TRANSACTION_LOOKUP_SQS_URL: '${self:custom.dcaTransactionLookupSqsQueueUrl}',
      TRANSACTION_TOTALS_UPDATE_SQS_URL: '${self:custom.transactionTotalsUpdateSqsQueueUrl}',
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.merchantTableQueryRolePolicyArn}',
        ManagedPolicy.entityTableWriteItemRolePolicy,
      ],
      inline: {
        onEntityTableStreamUpdateSqsPolicy: [
          {
            effect: 'Allow',
            actions: ['sqs:SendMessage'],
            resources: [
              '${self:custom.dcaTransactionLookupSqsQueueArn}',
              '${self:custom.transactionTotalsUpdateSqsQueueArn}',
            ],
          },
        ],

        onEntityTableUpdateDBStreamPolicy: [
          {
            effect: 'Allow',
            actions: [
              Action.dynamodb.ListStreams,
              Action.dynamodb.DescribeStream,
              Action.dynamodb.DescribeTable,
              Action.dynamodb.GetItem,
              Action.dynamodb.GetRecords,
              Action.dynamodb.GetShardIterator,
            ],
            resources: ['${self:custom.streamArn}'],
          },
          {
            effect: 'Allow',
            actions: [Action.dynamodb.Query],
            resources: [
              Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.siteGsi}'),
              Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.typeGsi}'),
            ],
          },
          {
            effect: 'Allow',
            actions: [Action.dynamodb.BatchGetItem],
            resources: [Arn.dynamodb.table('${self:custom.entityTableName}')],
          },
        ],
      },
    },
    timeout: 300,
    events: [
      {
        stream: {
          type: 'dynamodb',
          arn: '${self:custom.streamArn}',
          startingPosition: 'LATEST',
          batchSize: 100,
          maximumRetryAttempts: 30,
          maximumRecordAgeInSeconds: 120,
          filterPatterns: [
            {
              eventName: ['INSERT', 'MODIFY'],
              dynamodb: { NewImage: { type: { S: '${self:custom.entityStreamHandlerFilterPatterns}' } } },
            },
            { eventName: ['REMOVE'], dynamodb: { OldImage: { type: { S: [{ prefix: 'contact#' }] } } } },
            {
              eventName: ['MODIFY'],
              dynamodb: {
                NewImage: { type: { S: [{ prefix: 'site.core' }] }, siteType: { S: ['CNP_ZELLER_INVOICE'] } },
              },
            },
          ],
        },
      } as any,
    ],
  },
  getEntitySubcategoriesHandler: {
    handler: 'src/lambda/mp/subcategoryLambda.getEntitySubcategoriesHandler',
    name: 'getSubcategoriesHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
    },
    appsync: {
      fieldName: 'getEntitySubcategories',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  getAllSubcategoriesHandler: {
    handler: 'src/lambda/mp/subcategoryLambda.getAllSubcategoriesHandler',
    name: 'getAllSubcategoriesHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
    },
    appsync: {
      fieldName: 'getAllSubcategories',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  addEntitySubcategoryHandler: {
    handler: 'src/lambda/mp/subcategoryLambda.addEntitySubcategoryHandler',
    name: 'addSubcategoryHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'addEntitySubcategory',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  updateEntitySubcategoryHandler: {
    handler: 'src/lambda/mp/subcategoryLambda.updateEntitySubcategoryHandler',
    name: 'updateSubcategoryHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'updateEntitySubcategory',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  removeEntitySubcategoryHandler: {
    handler: 'src/lambda/mp/subcategoryLambda.removeEntitySubcategoryHandler',
    name: 'removeSubcategoryHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'removeEntitySubcategory',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  getEntitySubcategoryResolverHandler: {
    handler: 'src/lambda/mp/subcategoryLambda.getEntitySubcategoryResolverHandler',
    name: 'getEntitySubcategoryResolverHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    appsync: {
      fieldName: 'subcategoryDetails',
      typeName: 'DebitCardTransactionV2',
      template: {
        request: loadTemplateFile('resources/mp/template/getSubcategoryAdditionalResolverRequest.vtl'),
        response: loadTemplateFile('resources/mp/template/response.vtl'),
      },
      dataResolverName: {
        namePrefix: 'getEntitySubcategoryResolver',
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableWriteItemRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
    timeout: 30,
  },
  getReferralsHandler: {
    handler: 'src/lambda/mp/referralLambda.getReferralsHandler',
    name: 'getReferralsHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    appsync: {
      fieldName: 'referrals',
      typeName: 'Entity',
      template: {
        request: `{
          "version":"2018-05-29",
          "operation":"Invoke",
          "payload":{
            "identity": $util.toJson($context.identity),
            "request": $util.toJson($context.request),
            "authType": $util.toJson($util.authType()),
            "source": $util.toJson($context.source),
            "info": $util.toJson($context.info),
          }
        }`,
      },
      dataResolverName: {
        namePrefix: 'getReferrals',
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  getReferrerNameHandler: {
    handler: 'src/lambda/mp/entityLambda.getReferrerNameHandler',
    name: 'getReferrerName',
    tracing: true,
    environment: { COMPONENT_TABLE: '${self:custom.entityTableName}', SHORT_ID_GSI: '${self:custom.shortIdGsi}' },
    appsync: {
      fieldName: 'getReferrerName',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  getEntityBankingMigrationStateHandler: {
    handler: 'src/lambda/mp/entityLambda.getBankingMigrationStateHandler',
    name: 'getBankingMigrationStateHandler',
    tracing: true,
    environment: {
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    appsync: {
      fieldName: 'getBankingMigrationState',
      typeName: 'Query',
      dataResolverName: {
        namePrefix: 'getEntityBankingMigrationState',
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  getPaymentLimitsHandler: {
    handler: 'src/lambda/mp/getPaymentLimitsHandler.getPaymentLimitsHandler',
    name: 'getPaymentLimitsHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
    },
    appsync: {
      fieldName: 'paymentLimits',
      typeName: 'ZellerInvoiceSite',
      template: {
        request: `{ 
            "version":"2018-05-29",
            "operation":"Invoke",
            "payload":{
              "args":{"type":"CNP"},
              "identity": $util.toJson($context.identity),
              "request": $util.toJson($context.request),
              "authType": $util.toJson($util.authType()),
              "source": $util.toJson($context.source),
              "info": $util.toJson($context.info)
            }
          }`,
      },
      dataResolverName: {
        customResolverName: 'getCnpPaymentLimitsResolver',
        customDataSourceName: 'getPaymentLimitsDataSource',
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  searchBusinessIdentifierHandler: {
    handler: 'src/lambda/mp/businessIdentifierLambda.searchBusinessIdentifierHandler',
    name: 'searchBusinessIdentifierHandler',
    tracing: true,
    environment: {
      BUSINESS_IDENTIFIER_SEARCH_ENDPOINT: '${self:custom.businessIdentifierSearchEndpoint}',
      BUSINESS_IDENTIFIER_SEARCH_ENDPOINT_VERSION: '${self:custom.businessIdentifierSearchEndpointVersion}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    appsync: {
      fieldName: 'searchBusinessIdentifier',
      typeName: 'Query',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
  setEztaTermsOfServiceHandler: {
    handler: 'src/lambda/mp/entityLambda.setEztaTermsOfServiceHandler',
    name: 'setEztaTermsOfServiceHandler',
    tracing: true,
    environment: {
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      SESSION_CACHE_TABLE: '${self:custom.sessionCacheTableName}',
      AUTH0_TENANT: '${self:custom.auth0Tenant}',

      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
    },
    appsync: {
      fieldName: 'setEztaTermsOfService',
      typeName: 'Mutation',
      dataResolverName: {
        useFieldNamePrefix: true,
      },
    },
    policy: {
      managed: [
        ManagedPolicy.sessionCacheTableDBPolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        ManagedPolicy.permissionsTableReadRolePolicyArn,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
        ManagedPolicy.crossAccountInvocationPolicyArn,
      ],
    },
  },
};
