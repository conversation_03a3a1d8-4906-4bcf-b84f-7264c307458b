service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}-entity

plugins:
  - serverless-esbuild
  - serverless-dotenv-plugin
  - serverless-plugin-tracing
  - serverless-pseudo-parameters
  - serverless-plugin-resource-tagging
  - serverless-plugin-lambda-dead-letter
  - serverless-prune-plugin

useDotenv: true
variablesResolutionMode: ********
frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ${opt:region}
  accountId: '#{AWS::AccountId}'
  vpc:
    securityGroupIds:
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-sg'
    subnetIds:
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet01'
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet02'
      - 'Fn::ImportValue': '${env:VPC_ENV_NAME}-${env:COMPONENT_NAME}-lambda-subnet03'
  stackName: ${self:service}
  serviceName: ${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}
  service: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  timeout: ${env:LAMBDA_TIMEOUT_IN_SECONDS}
  bucketStackName: ${env:COMPONENT_NAME}-${env:PART_NAME}-iac-s3
  deploymentBucket:
    name: ${cf:${self:provider.bucketStackName}.deploymentBucket}
    maxPreviousDeploymentArtifacts: 100
    blockPublicAccess: true
  dynamodbStackName: ${self:provider.serviceName}-dynamodb
  appsyncStackName: ${self:provider.service}-appsync
  deviceTableName: ${self:provider.dynamodbStackName}-${env:COMPONENT_TABLE}
  cacheTableName: ${self:provider.dynamodbStackName}-${env:SESSION_CACHE_TABLE}
  accessTokenGsi: ${env:ACCESS_TOKEN_GSI}
  auth0Tenant: ${env:IDENTITY_AUTH0_TENANT}
  entityGsi: ${env:ENTITY_GSI}
  entityCacheGsi: ${env:ENTITY_CACHE_GSI}
  appSyncApiId:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiId'
  appSyncEndpoint: ${self:custom.appSyncAutoGeneratedEndpoint}
  iamUserKey: ${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_KEY}
  iamUserSecret: ${ssm:/aws/reference/secretsmanager/${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}/IAM_USER_SECRET}
  amsApiEndpoint: ${ssm:${opt:stage}-ams-engine-api-endpoint, ''}
  amsApiEndpointVersion: ${env:AMS_API_ENDPOINT_VERSION}
  auth0ClientId: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_ID}
  auth0ClientSecret: ${ssm:/${env:STATIC_ENV_NAME}-${env:COMPONENT_NAME}-${env:PART_NAME}/AUTH0_CLIENT_SECRET}
  dbsCqrsStackName: ${opt:stage}-dbs-cqrs
  cqrsStackName:
    dev: '${opt:stage}-dbs-cqrs'
    staging: '${opt:stage}-dbs-cqrs'
    prod: '${opt:stage}-dbs-cqrs'
    st: '${opt:stage}-dbs-entity-cqrs'
  dbsCqrsEventBus: '${cf:${self:provider.cqrsStackName.${opt:stage}, "${self:provider.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionArn}'
  dbsCqrsCommandHandler: ${self:provider.dbsCqrsStackName}-commandHandlers-handler
  environment:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    AUTH0_CLIENT_ID: ${self:provider.auth0ClientId}
    AUTH0_CLIENT_SECRET: ${self:provider.auth0ClientSecret}
    AWS_NODEJS_CONNECTION_REUSE_ENABLED: 1
    IS_RBAC_ENFORCED: ${env:IS_RBAC_ENFORCED}
    IS_RBAC_ENFORCE_ROLE: ${env:IS_RBAC_ENFORCE_ROLE}
    MULTI_ENTITY_ENABLED: ${env:MULTI_ENTITY_ENABLED}
    GLOBAL_ACCOUNT_ID: ${env:SYDNEY_ACCOUNT_ID}

  tags:
    COMPONENT_NAME: ${env:COMPONENT_NAME}
    PART_NAME: ${env:PART_NAME}
    STAGE: ${opt:stage}
    service: ${env:COMPONENT_NAME}-${env:PART_NAME}
    env: ${opt:stage}
  stackTags: ${self:provider.tags}

package:
  individually: true
  exclude:
    - node_modules/**

custom:
  esbuild:
    bundle: true
    keepNames: true
    plugins: esbuild_plugin.js
    exclude:
      - 'cache-manager'
      - 'class-transformer'
      - 'class-validator'
      - '@nestjs/microservices'
      - '@nestjs/websockets/socket-module'
      - '@nestjs/platform-express'
  prune:
    automatic: true
    includeLayers: true
    number: 5
  serviceName: ${opt:stage}-${env:COMPONENT_NAME}-${env:PART_NAME}
  dbsCqrsProjectionDLQArn: '${cf:${self:provider.cqrsStackName.${opt:stage}, "${self:provider.cqrsStackName.st}"}-iac-eventBridge.EventBusProjectionDLQArn}'
  appsyncDataSourceRoleArn: ${cf:${self:provider.appsyncStackName}.DataSourceLambdaRole}
  env:
    riskRuleApiEndpoint: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5-endpoint}
    riskRuleEndpointCategoryPath: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5-endpoint-path}
    onboardingRiskRuleApiEndpoint: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule22-endpoint}
    onboardingRiskRuleEndpointCategoryPath: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule22-endpoint-path}
    riskRuleEndpointProhibitedMCCPath: ${ssm:${env:STATIC_ENV_NAME}-risk-engine-rule5a-endpoint-path}

  appSyncEndpoints:
    dev: https://devices.myzeller.${opt:stage}/graphql
    staging: https://devices.myzeller.show/graphql
    prod: https://devices.myzeller.com/graphql
    st: ${self:custom.appSyncAutoGeneratedEndpoint}

  appSyncAutoGeneratedEndpoint:
    Fn::ImportValue: !Sub '${self:provider.appsyncStackName}-graphQlApiUrl'

  remoteAccountId:
    ap-southeast-2: ${env:LONDON_ACCOUNT_ID}
    eu-west-2: ${env:SYDNEY_ACCOUNT_ID}

functions:
  - ${file(resources/dbs/lambdas.yml)}

resources:
  - ${file(resources/dbs/resolvers.yml)}
  - ${file(resources/dbs/iam.yml)}
  - ${file(resources/dbs/eventBridgeRule.yml)}
  - ${file(resources/dbs/crossAccountPolicy.yml)}
