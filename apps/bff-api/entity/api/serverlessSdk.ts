import {
  ApiAppServerlessStack,
  lambdasCrossAccountInvocationPolicy,
  ssmSharedVpcImport as vpcImport,
} from '@npco/component-bff-serverless';
import { SdkApiAppEnvConfig, pluginsApp } from './resources/common';
import { resolvers, getEntityDataSource } from './resources/sdk/resolvers';
import { lambdas } from './resources/sdk/lambdas';

const envConfig = new SdkApiAppEnvConfig('./resources/sdk/config', true);

const { AWS_REGION: region } = process.env;

const sls = new ApiAppServerlessStack('entity', envConfig, {
  plugins: [...pluginsApp],
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getEsbuild(),
    vpcImport,
    dependsOn: {
      // Optional. Defaults to true, set to false to disable the plugin
      enabled: true,
      // Optional. Sets amount of lambda deployment parallelization plugin will attempt to create. Defaults to 1
      chains: 5,
    },
  },
  functions: lambdas,
  resources: {
    ...resolvers,
    getEntityDataSource,
    ...lambdasCrossAccountInvocationPolicy(lambdas, region, false),
  },
  environment: {
    ...envConfig.dotenvConfig,
    COMPONENT_TABLE: envConfig.componentTableName,
    SESSION_CACHE_TABLE: envConfig.sessionCacheTableName,
  },
});

module.exports = sls.build();
