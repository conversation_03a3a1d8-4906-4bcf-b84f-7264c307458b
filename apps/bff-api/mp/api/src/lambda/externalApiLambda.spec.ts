import { SsmClient } from '@npco/component-bff-core/dist/aws/ssmClient';
import * as Logger from '@npco/component-bff-core/dist/utils/logger';

import type { Context } from 'aws-lambda';
import nock from 'nock';

import { getCurrentSignUpLocationHandler, isSupportedLocationHandler } from './externalApiLambda';

jest.mock('@npco/component-bff-core/dist/aws/ssmClient');
jest.mock('@npco/component-bff-core/dist/utils/logger');

const HOST_NAME = 'https://api.bigdatacloud.net';
const PATH = '/data/ip-geolocation-full';
const IP = '127.0.0.1';
const API_KEY = 'test-api-key';
const AwsRegion = [
  `ap-east-1`,
  'ap-northeast-1',
  'ap-northeast-2',
  'ap-northeast-3',
  'ap-south-1',
  'ap-south-2',
  'ap-southeast-1',
  'ap-southeast-2',
  'ap-southeast-3',
  'ap-southeast-4',
  'ap-southheast-5',
  'ap-southheast-6', // Not yet open
  'ap-southheast-7',
  'ca-central-1',
  'ca-west-1',
  'cn-north-1',
  'cn-northwest-1',
  'eu-central-1',
  'eu-central-2',
  'eu-north-1',
  'eu-south-1',
  'eu-south-2',
  'eu-west-1',
  'eu-west-2',
  'eu-west-3',
  'il-central-1',
  'me-central-1',
  'me-south-1',
  'mx-central-1',
  'sa-east-1',
  'us-east-1',
  'us-east-2',
  'us-gov-east-1',
  'us-gov-west-1',
  'us-west-1',
  'us-west-2',
];

describe('isSupportLocationHandler test suite', () => {
  beforeEach(() => {
    SsmClient.prototype.getParameter = jest
      .fn()
      .mockImplementationOnce(() =>
        Promise.resolve({
          Parameter: {
            Value: API_KEY,
          },
        }),
      )
      .mockImplementationOnce(() =>
        Promise.resolve({
          Parameter: {
            Value: 'AUS,NZL',
          },
        }),
      );
  });

  afterEach(() => {
    nock.cleanAll();
    jest.resetAllMocks();
  });

  afterAll(nock.restore);

  it('return false if there is no request header', async () => {
    const result = await isSupportedLocationHandler({}, {} as Context, () => {});
    expect(result).toBe(false);
  });

  it('return false if there is no header', async () => {
    const result = await isSupportedLocationHandler({ request: {} }, {} as Context, () => {});
    expect(result).toBe(false);
  });

  it('return false if there is no x-forward-for header', async () => {
    const result = await isSupportedLocationHandler({ request: { headers: {} } }, {} as Context, () => {});
    expect(result).toBe(false);
  });

  it('return false if there is no x-forward-for is an empty string', async () => {
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': '' } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(false);
  });

  it('return true if Parameter store throw error in reading the API key', async () => {
    SsmClient.prototype.getParameter = jest.fn().mockImplementation(() => {
      throw new Error('AWS Parameter Store Error');
    });

    const spy = jest.spyOn(Logger, 'error');
    expect(spy).not.toBeCalled();
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': IP } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(true);
    expect(spy).toBeCalledWith('Error: AWS Parameter Store Error');
  });

  it('return true if API Key is not set', async () => {
    SsmClient.prototype.getParameter = jest.fn().mockImplementation(() => ({
      promise: () => Promise.resolve({}),
    }));
    const spy = jest.spyOn(Logger, 'error');
    expect(spy).not.toBeCalled();
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': IP } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(true);
    expect(spy).toBeCalledWith('Error: Big Data Cloud API did not set up in the secret manager correctly');
  });

  it(`return true if Parameter Store throw error in reading the country white list`, async () => {
    SsmClient.prototype.getParameter = jest
      .fn()
      .mockImplementationOnce(() =>
        Promise.resolve({
          Parameter: {
            Value: API_KEY,
          },
        }),
      )
      .mockImplementationOnce(() => {
        throw new Error('AWS Parameter Store Error - Country White List');
      });
    const spy = jest.spyOn(Logger, 'error');
    expect(spy).not.toBeCalled();
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': IP } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(true);
    expect(spy).toBeCalledWith('Error: AWS Parameter Store Error - Country White List');
  });

  it(`return true if Parameter Store did not return the parameter value`, async () => {
    SsmClient.prototype.getParameter = jest
      .fn()
      .mockImplementationOnce(() =>
        Promise.resolve({
          Parameter: {
            Value: API_KEY,
          },
        }),
      )
      .mockImplementation(() => Promise.resolve({}));

    const spy = jest.spyOn(Logger, 'error');
    expect(spy).not.toBeCalled();
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': IP } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(true);
    expect(spy).toBeCalledWith('Error: Allow Sign Up Country Whitelist is not set up in the parameter store correctly');
  });

  it('return false if the country code is not in the list', async () => {
    nock(HOST_NAME)
      .get(PATH)
      .query({ ip: IP, key: API_KEY })
      .reply(200, {
        country: {
          isoAlpha3: 'AFG',
        },
      });
    const spy = jest.spyOn(Logger, 'error');
    expect(spy).not.toBeCalled();
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': IP } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(false);
    expect(spy).not.toBeCalled();
  });

  it('return true if the country code is not in the list', async () => {
    nock(HOST_NAME)
      .get(PATH)
      .query({ ip: IP, key: API_KEY })
      .reply(200, {
        country: {
          isoAlpha3: 'AUS',
        },
      });
    const spy = jest.spyOn(Logger, 'error');
    expect(spy).not.toBeCalled();
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': IP } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(true);
    expect(spy).not.toBeCalled();
  });

  it('return true if BIG DATA CLOUD API is not returning 2xx', async () => {
    nock(HOST_NAME).get(PATH).query({ ip: IP, key: API_KEY }).replyWithError('BIG DATA CLOUD API Error');
    const spy = jest.spyOn(Logger, 'error');
    expect(spy).not.toBeCalled();
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': IP } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(true);
    expect(spy).toBeCalledWith('Error: BIG DATA CLOUD API Error');
  });

  it('returns true if the BIG DATA CLOUD API call times out', async () => {
    nock(HOST_NAME)
      .get(PATH)
      .query({ ip: IP, key: API_KEY })
      .delay(6000)
      .reply(200, {
        country: {
          isoAlpha3: 'AUS',
        },
      });

    const spy = jest.spyOn(Logger, 'error');
    expect(spy).not.toBeCalled();
    const result = await isSupportedLocationHandler(
      { request: { headers: { 'x-forwarded-for': IP } } },
      {} as Context,
      () => {},
    );
    expect(result).toBe(true);
    expect(spy).toBeCalledWith(expect.stringContaining('timeout'));
  });
});

describe('getCurrentSignupLocationHandler test suite', () => {
  describe('Sydney Region', () => {
    beforeAll(() => {
      process.env.AWS_REGION = 'ap-southeast-2';
    });

    it('return default signup country if cloudfront-viewer-country is not set', async () => {
      const result = await getCurrentSignUpLocationHandler({}, {} as Context, () => {});
      expect(result).toStrictEqual({
        countryCode: 'AUS',
        isSignupCountry: true,
        defaultSignupCountry: 'AUS',
      });
    });

    it('return isSignupCountry as true if for Australia', async () => {
      const result = await getCurrentSignUpLocationHandler(
        { request: { headers: { 'cloudfront-viewer-country': 'AU' } } },
        {} as Context,
        () => {},
      );
      expect(result).toStrictEqual({
        countryCode: 'AUS',
        isSignupCountry: true,
        defaultSignupCountry: 'AUS',
      });
    });

    it('return isSignupCountry as true if for Cocos (Keeling) Islands', async () => {
      const result = await getCurrentSignUpLocationHandler(
        { request: { headers: { 'cloudfront-viewer-country': 'CC' } } },
        {} as Context,
        () => {},
      );
      expect(result).toStrictEqual({
        countryCode: 'CCK',
        isSignupCountry: true,
        defaultSignupCountry: 'AUS',
      });
    });

    it('return isSignupCountry as true if for Christmas Island', async () => {
      const result = await getCurrentSignUpLocationHandler(
        { request: { headers: { 'cloudfront-viewer-country': 'CX' } } },
        {} as Context,
        () => {},
      );
      expect(result).toStrictEqual({
        countryCode: 'CXR',
        isSignupCountry: true,
        defaultSignupCountry: 'AUS',
      });
    });

    it('return isSignupCountry as true if for Norfolk Island', async () => {
      const result = await getCurrentSignUpLocationHandler(
        { request: { headers: { 'cloudfront-viewer-country': 'NF' } } },
        {} as Context,
        () => {},
      );
      expect(result).toStrictEqual({
        countryCode: 'NFK',
        isSignupCountry: true,
        defaultSignupCountry: 'AUS',
      });
    });

    it('return isSignupCountry as false if for New Zealand', async () => {
      const result = await getCurrentSignUpLocationHandler(
        { request: { headers: { 'cloudfront-viewer-country': 'NZ' } } },
        {} as Context,
        () => {},
      );
      expect(result).toStrictEqual({
        countryCode: 'NZL',
        isSignupCountry: false,
        defaultSignupCountry: 'AUS',
      });
    });
  });
  describe('London Region', () => {
    beforeAll(() => {
      process.env.AWS_REGION = 'eu-west-2';
    });

    it('return default signup country if cloudfront-viewer-country is not set', async () => {
      const result = await getCurrentSignUpLocationHandler({}, {} as Context, () => {});
      expect(result).toStrictEqual({
        countryCode: 'GBR',
        isSignupCountry: true,
        defaultSignupCountry: 'GBR',
      });
    });

    it('return isSignupCountry as true if for UK', async () => {
      const result = await getCurrentSignUpLocationHandler(
        { request: { headers: { 'cloudfront-viewer-country': 'GB' } } },
        {} as Context,
        () => {},
      );
      expect(result).toStrictEqual({
        countryCode: 'GBR',
        isSignupCountry: true,
        defaultSignupCountry: 'GBR',
      });
    });

    it('return isSignupCountry as false if for Ireland', async () => {
      const result = await getCurrentSignUpLocationHandler(
        { request: { headers: { 'cloudfront-viewer-country': 'IE' } } },
        {} as Context,
        () => {},
      );
      expect(result).toStrictEqual({
        countryCode: 'IRL',
        isSignupCountry: false,
        defaultSignupCountry: 'GBR',
      });
    });
  });
  describe('Other Regions', () => {
    describe.each(AwsRegion.filter((region) => !['ap-southeast-2', 'eu-west-2'].includes(region)))(`%s`, (region) => {
      it('will throw', async () => {
        process.env.AWS_REGION = region;
        await expect(getCurrentSignUpLocationHandler({}, {} as Context, () => {})).rejects.toThrowError(
          `No supported domicile found in the region: ${region}`,
        );
      });
    });
  });
});
