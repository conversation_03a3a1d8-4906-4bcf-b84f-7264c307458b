import { SessionService } from '@npco/component-dbs-mp-common/dist/session/sessionService';
import { OnboardingStatus } from '@npco/component-dto-entity';

import { v4 } from 'uuid';

import { blackListStatus, entityCacheMiddleware, entityOnboardingStatusCheckMiddleware } from './middleware';

const service = {
  checkOnboardingSession: jest.fn(() => ({})),
  checkIdentitySession: jest.fn(() => ({})),
  updateEntityStatus: jest.fn(() => ({})),
  getEntityStatuses: jest.fn(() => ({})),
};

jest.mock('@npco/component-dbs-mp-common/dist/session/sessionService', () => {
  return { SessionService: jest.fn(() => service) };
});

jest.mock('@npco/component-dbs-mp-common/dist/authzero/auth0Service', () => {
  return { Auth0Service: jest.fn(() => service) };
});
const mockGetEntityDbItemOrThrow = jest.fn().mockResolvedValue({});
jest.mock('@npco/component-dbs-mp-common/dist/entity/getEntityDbItem', () => ({
  getEntityDbItemOrThrow: () => mockGetEntityDbItemOrThrow(),
}));

describe('lambda middleware test suite', () => {
  let sessionService: SessionService;

  let context: any;
  const next = jest.fn();
  const getApp = jest.fn();

  beforeAll(() => {
    const mock = {} as any;
    sessionService = new SessionService(mock, mock);

    context = {
      app: { get: getApp },
      accessToken: 'accessToken',
    };
  });

  beforeEach(() => {
    next.mockReset();
    getApp.mockReturnValue(sessionService);
  });

  describe('entityCacheMiddleware test suite', () => {
    it('should call next when accountStatus does not exist', async () => {
      const updateEntityStatus = jest.fn();
      getApp.mockReturnValue(sessionService).mockReturnValue({
        updateEntityStatus,
      });

      await entityCacheMiddleware({ detail: { entityUuid: 'id' } }, context, next);
      expect(updateEntityStatus).toHaveBeenCalledTimes(0);
    });

    it('should update cache when accountStatus exists', async () => {
      const updateEntityStatus = jest.fn();
      getApp.mockReturnValue(sessionService).mockReturnValue({
        updateEntityStatus,
      });
      await entityCacheMiddleware({ detail: { entityUuid: 'id', status: 'BLOCKED' } } as any, context, next);
      expect(updateEntityStatus).toHaveBeenCalledTimes(1);
    });
  });

  describe('entityOnboardingStatusCheckMiddleware test suite', () => {
    const entityUuid = v4();

    beforeEach(next.mockReset);

    it('should be able to handle the entityUpdateDto with no onboardingStatus', async () => {
      mockGetEntityDbItemOrThrow.mockResolvedValue({
        entityUuid,
      });

      context = {
        entityUuid,
        app: {
          get: jest.fn().mockImplementation(() => {
            return {
              DynamodbService: jest.fn(),
            };
          }),
        },
      };

      const event = {
        args: {
          entity: {
            id: entityUuid,
          },
        },
      } as any;
      expect(next).not.toBeCalled();
      await entityOnboardingStatusCheckMiddleware(event, context, next);
      expect(next).toBeCalledWith(event, context);
    });

    test.each(Object.keys(OnboardingStatus).filter((status) => !blackListStatus.includes(status as OnboardingStatus)))(
      'should be able to handle if the entity is not in materialise view with onboardingStatus %s',
      async (onboardingStatus) => {
        mockGetEntityDbItemOrThrow.mockRejectedValue('Entity is not existed');
        context = {
          entityUuid,
          app: {
            get: jest.fn().mockImplementation(() => {
              return {
                DynamodbService: jest.fn(),
              };
            }),
          },
        };

        const event = {
          args: {
            entity: {
              id: entityUuid,
              onboardingStatus,
            },
          },
        } as any;
        expect(next).not.toBeCalled();
        await entityOnboardingStatusCheckMiddleware(event, context, next);
        expect(next).toBeCalledWith(event, context);
      },
    );

    test.each(blackListStatus)(
      'should be thrown if the entity is not in materialise view with onboardingStatus %s',
      async (onboardingStatus) => {
        mockGetEntityDbItemOrThrow.mockRejectedValue('Entity is not existed');
        context = {
          entityUuid,
          app: {
            get: jest.fn().mockImplementation(() => {
              return {
                DynamodbService: jest.fn(),
              };
            }),
          },
        };

        const event = {
          args: {
            entity: {
              id: entityUuid,
              onboardingStatus,
            },
          },
        } as any;
        expect(next).not.toBeCalled();
        await expect(entityOnboardingStatusCheckMiddleware(event, context, next)).rejects.toThrowError(
          'Action is not allowed',
        );
        expect(next).not.toBeCalled();
      },
    );

    describe('Handler the incoming onboarding status, if the current onboarding status is empty', () => {
      mockGetEntityDbItemOrThrow.mockResolvedValue({
        entityUuid,
      });

      beforeAll(() => {
        context = {
          entityUuid,
          app: {
            get: jest.fn().mockImplementation(() => {
              return {
                DynamodbService: jest.fn(),
              };
            }),
          },
        };
      });

      beforeEach(() => {
        mockGetEntityDbItemOrThrow.mockClear();
      });

      test.each(blackListStatus)('should be reject if set the onboarding status to %s', async (onboardingStatus) => {
        const event = {
          args: {
            entity: {
              id: entityUuid,
              onboardingStatus,
            },
          },
        } as any;
        expect(next).not.toBeCalled();
        await expect(entityOnboardingStatusCheckMiddleware(event, context, next)).rejects.toThrowError(
          'Action is not allowed',
        );
        expect(next).not.toBeCalled();
      });

      test.each(
        Object.keys(OnboardingStatus).filter((status) => !blackListStatus.includes(status as OnboardingStatus)),
      )('should be allow if set the onboarding status to %s', async (onboardingStatus) => {
        const event = {
          args: {
            entity: {
              id: entityUuid,
              onboardingStatus,
            },
          },
        } as any;
        expect(next).not.toBeCalled();
        await entityOnboardingStatusCheckMiddleware(event, context, next);
        expect(next).toBeCalledWith(event, context);
      });
    });

    describe('Handler if the current onboarding status is set', () => {
      test.each(Object.keys(OnboardingStatus))(
        'should be allow if current onboarding status - %s with input parameter without onboardingStatus',
        async (onboardingStatus) => {
          mockGetEntityDbItemOrThrow.mockResolvedValue({
            entityUuid,
            onboardingStatus,
          });

          context = {
            entityUuid,
            app: {
              get: jest.fn().mockImplementation(() => {
                return {
                  DynamodbService: jest.fn(),
                };
              }),
            },
          };

          const event = {
            args: {
              entity: {
                id: entityUuid,
              },
            },
          } as any;
          expect(next).not.toBeCalled();
          await entityOnboardingStatusCheckMiddleware(event, context, next);
          expect(next).toBeCalledWith(event, context);
        },
      );

      test.each(blackListStatus)(
        'should be allow if current onboarding status - %s to set back to current status',
        async (onboardingStatus) => {
          mockGetEntityDbItemOrThrow.mockResolvedValue({
            entityUuid,
            onboardingStatus,
          });

          context = {
            entityUuid,
            app: {
              get: jest.fn().mockImplementation(() => {
                return {
                  DynamodbService: jest.fn(),
                };
              }),
            },
          };

          const event = {
            args: {
              entity: {
                id: entityUuid,
                onboardingStatus,
              },
            },
          } as any;
          expect(next).not.toBeCalled();
          await entityOnboardingStatusCheckMiddleware(event, context, next);
          expect(next).toBeCalledWith(event, context);
        },
      );
    });

    test.each(blackListStatus)(
      'should not allow if current onboarding status - %s to set back to other status',
      async (onboardingStatus) => {
        mockGetEntityDbItemOrThrow.mockResolvedValue({
          entityUuid,
          onboardingStatus,
        });
        context = {
          entityUuid,
          app: {
            get: jest.fn().mockImplementation(() => {
              return {
                DynamodbService: jest.fn(),
              };
            }),
          },
        };

        for (const status of Object.keys(OnboardingStatus).filter(
          (s) => OnboardingStatus[s as keyof typeof OnboardingStatus] !== onboardingStatus,
        )) {
          const event = {
            args: {
              entity: {
                id: entityUuid,
                onboardingStatus: status,
              },
            },
          } as any;
          expect(next).not.toBeCalled();
          await expect(entityOnboardingStatusCheckMiddleware(event, context, next)).rejects.toThrowError(
            'Action is not allowed',
          );
          expect(next).not.toBeCalled();
        }
      },
    );
  });
});
