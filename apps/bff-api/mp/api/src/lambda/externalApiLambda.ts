import { SsmClient } from '@npco/component-bff-core/dist/aws/ssmClient';
import {
  getDomicileFromISOAlpha2CountryCode,
  getSupportedDomiciles,
} from '@npco/component-bff-core/dist/utils/domicile';
import { error, info } from '@npco/component-bff-core/dist/utils/logger';

import type { Handler } from 'aws-lambda';
import axios from 'axios';
import countries from 'i18n-iso-countries';

const BIGDATA_CLOUD_URL = 'https://api.bigdatacloud.net/data/ip-geolocation-full';

const ssmClient = new SsmClient();

export const isSupportedLocationHandler: Handler = async (event: any) => {
  info(event);
  const sourceIpAddress = event.request?.headers?.['x-forwarded-for']?.split(', ')[0];
  if (!sourceIpAddress) {
    return false;
  }

  try {
    const bigDataCloudApiKeySecretValue = await ssmClient.getParameter(`${process.env.BIGDATACLOUD_API_KEY}`, true);

    if (!bigDataCloudApiKeySecretValue.Parameter?.Value) {
      throw new Error('Big Data Cloud API did not set up in the secret manager correctly');
    }

    const bigDataCloudApiKey = bigDataCloudApiKeySecretValue.Parameter.Value;

    const allowSignUpCountryParameterStore = await ssmClient.getParameter(`${process.env.ALLOW_SIGN_UP_COUNTRIES}`);

    if (!allowSignUpCountryParameterStore.Parameter?.Value) {
      throw new Error('Allow Sign Up Country Whitelist is not set up in the parameter store correctly');
    }

    const countryList = allowSignUpCountryParameterStore.Parameter.Value.split(',');
    info(`Checking if ${sourceIpAddress} is in the country list: ${countryList}`);
    const apiResponse = await axios.get(BIGDATA_CLOUD_URL, {
      params: {
        ip: sourceIpAddress,
        key: bigDataCloudApiKey,
      },
      headers: {
        Accept: 'application/json',
      },
      timeout: 5000,
    });

    const ipCountry = apiResponse.data.country.isoAlpha3 as string;
    info(`IP Country: ${ipCountry}`);
    return countryList.includes(ipCountry);
  } catch (
    // Following disable comment can be removed when upgrade to node16
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    err: any
  ) {
    error(err.toString());
    return true;
  }
};

export const getCurrentSignUpLocationHandler: Handler = async (event: any) => {
  info(event);
  const defaultSignupCountries = getSupportedDomiciles(process.env.AWS_REGION as string);

  // This should never happen
  if (!defaultSignupCountries.length) {
    throw new Error(`No supported domicile found in the region: ${process.env.AWS_REGION}`);
  }

  const cloudfrontViewerCountry = event.request?.headers?.['cloudfront-viewer-country'];
  const domicile = cloudfrontViewerCountry
    ? getDomicileFromISOAlpha2CountryCode(cloudfrontViewerCountry)
    : defaultSignupCountries[0];

  return {
    countryCode: cloudfrontViewerCountry
      ? countries.alpha2ToAlpha3(cloudfrontViewerCountry)
      : defaultSignupCountries[0],
    isSignupCountry: domicile ? defaultSignupCountries.includes(domicile) : false,
    defaultSignupCountry: domicile ?? defaultSignupCountries[0],
  };
};
