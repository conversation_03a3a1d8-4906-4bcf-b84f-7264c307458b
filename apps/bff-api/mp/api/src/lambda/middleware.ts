import { BadUserInput } from '@npco/component-bff-core/dist/error';
import { info } from '@npco/component-bff-core/dist/utils/logger';
import { DynamodbService } from '@npco/component-dbs-mp-common/dist/dynamodb';
import { convertDbItemToEntity } from '@npco/component-dbs-mp-common/dist/entity/convertDbItemToEntity';
import { getEntityDbItemOrThrow } from '@npco/component-dbs-mp-common/dist/entity/getEntityDbItem';
import { SessionService } from '@npco/component-dbs-mp-common/dist/session/sessionService';
import type { Entity, NestAppEntityContext } from '@npco/component-dbs-mp-common/dist/types';
import type { EntityUpdatedEventDto } from '@npco/component-dto-entity';
import { OnboardingStatus } from '@npco/component-dto-entity';

import type { Context } from 'aws-lambda';

export const entityCacheMiddleware = async (
  event: { detail: EntityUpdatedEventDto },
  context: Context | NestAppEntityContext,
  next: any,
) => {
  const { entityUuid, status } = event.detail;
  if (entityUuid && status) {
    await (context as NestAppEntityContext).app.get(SessionService).updateEntityStatus(entityUuid, status);
    info(`entity data saved: ${JSON.stringify(event.detail)}`, event.detail.entityUuid);
  }
  return next(event, context);
};

export const blackListStatus = [
  OnboardingStatus.ONBOARDED,
  OnboardingStatus.RC_ONBOARDED,
  OnboardingStatus.REVIEW,
  OnboardingStatus.RC_REVIEW,
  OnboardingStatus.RC_DEPLATFORMED,
  OnboardingStatus.RC_REJECTED,
  OnboardingStatus.RC_ABANDONED,
];

const ACTION_IS_NOT_ALLOW = 'Action is not allowed';

export const entityOnboardingStatusCheckMiddleware = async (
  event: { args: { entity: Entity } },
  context: Context | NestAppEntityContext,
  // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
  next: any,
) => {
  if (!event.args.entity.onboardingStatus) {
    return next(event, context);
  }
  const { app, entityUuid } = context as NestAppEntityContext;
  const newOnboardingStatus = OnboardingStatus[event.args.entity.onboardingStatus as keyof typeof OnboardingStatus];
  let entity: Entity;

  try {
    const db = app.get(DynamodbService);
    const item = await getEntityDbItemOrThrow(db, entityUuid);
    entity = convertDbItemToEntity(item);
  } catch {
    if (blackListStatus.includes(newOnboardingStatus)) {
      return Promise.reject(new BadUserInput(ACTION_IS_NOT_ALLOW)); // NOSONAR
    }
    return next(event, context);
  }

  if (entity.onboardingStatus === newOnboardingStatus) {
    return next(event, context);
  }

  if (
    blackListStatus.includes(OnboardingStatus[entity.onboardingStatus as keyof typeof OnboardingStatus]) ||
    blackListStatus.includes(newOnboardingStatus)
  ) {
    return Promise.reject(new BadUserInput(ACTION_IS_NOT_ALLOW)); // NOSONAR
  }

  return next(event, context);
};
