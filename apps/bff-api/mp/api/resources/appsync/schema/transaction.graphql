type Mutation {
  publishTransactionUpdateEvent(transaction: TransactionInput!): Transaction! @aws_iam
  sendReceipt(input: SendReceiptInput!, entityUuid: ID): Boolean!
  # Complete the cardholder opt out receipts request
  optOutReceipts(code: String!, id: String!): CompleteOptOutReceiptsResult! @aws_api_key
  publishTransactionExport(transactionDownload: TransactionDownloadInput!): TransactionDownload! @aws_iam
  createTransactionNotes(transactionUuid: ID!, notes: String!, entityUuid: ID): Boolean!
  updateTransactionNotes(transactionUuid: ID!, notes: String!, entityUuid: ID): Boolean!
  deleteTransactionNotes(transactionUuid: ID!, entityUuid: ID): Boolean!
  removeTransactionImage(transactionUuid: ID!, fileUuid: String!, entityUuid: ID): Boolean!
  refundTransaction(input: RefundTransactionInput!, entityUuid: ID): RefundTransactionResponse!
  requestCpocTransaction(transaction: CpocTransactionInput!, entityUuid: ID): CpocTransactionResponse!
  reverseCpocTransaction(transaction: CpocReverseTransactionInput!, entityUuid: ID): CpocReverseTransactionResponse!
  reverseCpocTransactionV2(transaction: CpocReverseTransactionV2Input!, entityUuid: ID): CpocReverseTransactionResponse!
  cancelCpocTransaction(
    reason: TransactionCancelReason!
    transactionDetails: CpocTransactionInput!
    entityUuid: ID
  ): Boolean!
  generateCpocSessionToken(deviceUuid: ID, entityUuid: ID): String!
  transferRemittanceNotification(
    input: TransferRemittanceNotificationInput!
    entityUuid: ID
  ): TransferRemittanceNotificationResponse!
}

type Query {
  getTransactions(
    filter: TransactionFilterInput
    limit: Int!
    nextToken: TransactionNextTokenInput
    entityUuid: ID
  ): TransactionConnection!
  getTransaction(transactionUuid: ID!, entityUuid: ID): Transaction!
  getTransactionTotals(
    filter: TransactionFilterInput!
    totalsType: TransactionTotalsType!
    entityUuid: ID
  ): [TransactionTotals!] @deprecated(reason: "use getTransactionBigIntTotals")
  getTransactionTotalsBigInt(
    filter: TransactionFilterInput!
    totalsType: TransactionTotalsType!
    entityUuid: ID
  ): [TransactionTotalsBigInt!]
  getTransactionImageUploadUrls(
    transactionUuid: ID!
    fileNames: [String!]!
    entityUuid: ID
  ): [TransactionImageUploadUrls!]!
  getReceiptPdfPresignedUrl(transactionUuid: ID!, rrn: String!, entityUuid: ID): S3PresignedUrlDownload!
  getTransferRemittancePdf(dcaTransactionUuid: String!, entityUuid: ID): TransferRemittancePdfDownload!
  getTransactionTotalsV2(
    totalsType: TransactionTotalsTypeV2!
    entityUuid: String!
    timestampRange: BetweenFilter!
    filter: TransactionFilterInputV2
  ): TransactionTotalsV2
  getTransactionTotalsMultiEntityV2(
    totalsType: TransactionTotalsTypeV2!
    entityUuids: [String!]!
    timestampRange: BetweenFilter!
    filter: TransactionFilterInputMultiEntity
  ): MultiEntityTransactionTotalsV2
  getTransactionTotalsMultiEntityExport(
    totalsType: TransactionTotalsTypeV2!
    format: TransactionTotalsMultiEntityExportFormat!
    entityUuids: [String!]!
    timestampRange: BetweenFilter!
  ): TransactionTotalsMultiEntityExport
}

type Subscription {
  onTransactionUpdate(entityUuid: ID!, siteUuids: [ID!], deviceUuid: ID): Transaction
    @aws_oidc
    @aws_subscribe(mutations: ["publishTransactionUpdateEvent"])

  # Get a download link for exporting transactions. The maximum time to get transaction download link is 15 mniutes but Subscription doesn't terminate from server side, that means FE needs to handle the timeout.
  # filter: Due to appsync subscription limtation, we can't use customer object as the input parameter. The filter is a type of string which is JSON string from TransactionFilterInput
  exportTransaction(
    entityUuid: ID!
    customerUuid: ID!
    # filter is a string value which is JSON string from TransactionFilterInput
    filter: String!
    format: TransactionExportFormat!
  ): TransactionDownload @aws_oidc @aws_subscribe(mutations: ["publishTransactionExport"])
}

input TransactionNextTokenInput {
  type: String!
  id: ID
  entityUuid: ID
}

type TransactionNextToken {
  type: String!
  id: ID
  entityUuid: ID
}

input RefundTransactionInput {
  transactionUuid: ID!
  originalTransactionUuid: ID!
  amount: Int!
  timestamp: AWSDateTime!
  timestampLocal: AWSDateTime!
  currency: String!
  timezone: String
  # required for Zeller App to populate channel
  deviceUuid: ID
}

type RefundTransactionResponse {
  id: ID!
  responseCode: String
  approvalCode: String
  rrn: String
  status: RefundTransactionStatus
}

enum RefundTransactionStatus {
  APPROVED
  DECLINED
}

enum CardScheme {
  VISA
  MC
  AMEX
  JCB
  EFTPOS
  CUP
  DINERS
  OTHER
}

enum CardMedia {
  MANUAL
  MSR
  ICC
  PICC
  NFC
  CNP
}

enum TransactionType {
  PURCHASE
  REFUND
  PURCHASEADVICE
  REFUNDADVICE
  PREAUTH
  ADJUSTMENT
  CASHOUT
  DEPOSIT
  ACCOUNTVERIFY
  REVERSAL
}

enum TransactionStatus {
  APPROVED
  DECLINED
  PROCESSING
  DELETED
}

enum SourceFilter {
  TERMINAL
  POINT_OF_SALE
  ZELLER_APP
  INVOICE
  DASHBOARD
  ZELLER_ADMIN
}

enum Source {
  STANDALONE
  LINKLY
  QUICKPOS @deprecated(reason: "to be removed")
  ZELLER_INVOICE
  XERO_INVOICE
  DASHBOARD @deprecated(reason: "use SourceFilter")
  HL_POS
  ORACLE_POS
  MOBILE_PHONE @deprecated(reason: "use SourceFilter")
  VIRTUAL_TERMINAL
  PAY_BY_LINK
  ZELLER_POS
  IMPOS
  SDK
  TEVALIS_POS
}

enum Channel {
  MOBILE_ANDROID
  MOBILE_IOS
  TERMINAL
  PAY_MYZELLER
  DASHBOARD
  ZELLER_ADMIN
}

enum TransactionCancelReason {
  USER_CANCELLED
  RESPONSE_TIMEOUT
  CARD_DECLINED
  SIGNATURE_MISMATCH
  COMMUNICATIONS_FAILURE
  INVALID_RESPONSE
  PENDING_REVERSAL_FAILURE
  SYSTEM_ERROR
}

enum ThreeDSOutcome {
  PASSED
  FAILED
}

type TaxAmount @aws_iam @aws_oidc {
  name: String!
  amount: Int!
}

input TaxAmountInput {
  name: String!
  amount: Int!
}

enum SplitPaymentType {
  PORTION
  AMOUNT
  ITEMS
}

type SplitPayment @aws_iam @aws_oidc {
  id: ID!
  type: SplitPaymentType!
  targetAmount: Int!
  portions: Int
  transactions: [Transaction!]!
}

type Transaction @aws_iam @aws_oidc {
  id: ID!
  entityUuid: ID!
  deviceUuid: ID
  deviceName: String
  deviceModel: String
  siteUuid: ID!
  siteName: String
  timestamp: AWSDateTime!
  amount: Int!
  currency: String
  saleAmount: Int!
  tipAmount: Int!
  taxAmounts: [TaxAmount!]
  feeAmount: Int
  feeCharged: Boolean
  surchargeAmount: Int!
  refundedAmount: Int
  scheme: CardScheme!
  type: TransactionType!
  status: TransactionStatus!
  depositUuid: ID
  # getDepositShortId resolver
  depositShortId: String @deprecated(reason: "Use deposit field")
  depositDate: AWSDateTime @deprecated(reason: "Use deposit field")
  maskedPan: String
  reference: String
  review: Boolean
  refunded: Boolean
  deposited: Boolean @deprecated(reason: "Use deposit field")
  reversed: Boolean
  cardholderUuid: ID
  cardholderEmail: String
  cardholderPhone: String
  stan: String
  caid: String
  catid: String
  cardMedia: CardMedia
  emvAid: String
  emvTagsPrint: String
  emvAppName: String
  isoProcessingCode: String
  responseCode: String
  responseDescription: String
  source: Source
  channel: Channel
  sourceFilter: SourceFilter
  posName: String
  externalReference: String
  externalReferenceUrl: String
  # getContactByCardholder resolver
  contact: Contact
  notes: String
  issuer: TransactionIssuer
  adjustAmount: Int
  customerUuid: ID
  customerName: String
  cardholderName: String
  location: Location
  refundedTransactionUuid: ID
  refundedTransaction: Transaction
  refundTransactions: [Transaction!]
  images: [TransactionImageDownloadUrls!]
  splitPaymentUuid: ID
  splitPayment: SplitPayment
  threeDSOutcome: ThreeDSOutcome
  # getDeposit resolver
  deposit: Deposit
}

type Location @aws_iam @aws_oidc {
  accuracy: Int
  location: String
  timestampLocal: AWSDateTime
}

input LocationInput {
  accuracy: Int
  location: String
  timestampLocal: AWSDateTime
}

input SplitPaymentInput {
  id: ID!
  type: SplitPaymentType!
  targetAmount: Int!
  portions: Int
}

input TransactionInput {
  id: ID!
  entityUuid: ID!
  deviceUuid: ID
  deviceName: String
  deviceModel: String
  siteUuid: ID!
  siteName: String
  timestamp: AWSDateTime!
  amount: Int!
  currency: String
  saleAmount: Int!
  tipAmount: Int!
  taxAmounts: [TaxAmountInput!]
  feeAmount: Int
  feeCharged: Boolean
  surchargeAmount: Int!
  refundedAmount: Int
  scheme: CardScheme!
  type: TransactionType!
  status: TransactionStatus!
  deposited: Boolean
  depositUuid: ID
  depositDate: AWSDateTime
  maskedPan: String
  reference: String
  responseCode: String
  responseDescription: String
  issuer: TransactionIssuer
  channel: Channel
  source: Source
  sourceFilter: SourceFilter
  posName: String
  externalReference: String
  externalReferenceUrl: String
  refunded: Boolean
  stan: String
  caid: String
  catid: String
  isoProcessingCode: String
  emvAid: String
  customerUuid: ID
  location: LocationInput
  review: Boolean
  reversed: Boolean
  adjustAmount: Int
  cardholderEmail: String
  cardholderPhone: String
  cardholderName: String
  cardholderUuid: ID
  cardMedia: CardMedia
  notes: String
  refundedTransactionUuid: ID
  splitPaymentUuid: ID
  splitPayment: SplitPaymentInput
  threeDSOutcome: ThreeDSOutcome
}

type TransactionConnection {
  transactions: [Transaction]!
  nextToken: TransactionNextToken
}

input TransactionFilterExcludeInput {
  cardholderUuid: StringFilterInput
}

input TransactionFilterInput {
  timestamp: StringFilterInput
  amount: IntFilterInput
  scheme: StringFilterInput
  type: StringFilterInput
  status: StringFilterInput
  maskedPan: StringFilterInput
  reference: StringFilterInput
  deviceUuid: StringFilterInput
  siteUuid: StringFilterInput
  depositUuid: StringFilterInput
  panToken: StringFilterInput
  par: StringFilterInput
  review: BoolFilterInput
  refunded: BoolFilterInput
  deposited: BoolFilterInput @deprecated(reason: "Not Supported")
  depositShortId: StringEqualInput
  splitPaymentUuid: StringFilterInput
  reversed: BoolFilterInput
  source: StringFilterInput
  channel: StringFilterInput
  posName: StringFilterInput
  contactUuid: ListFilterInput
  sourceFilter: ListFilterInput
  or: [TransactionFilterInput]
  #for cardholderGsi
  not: TransactionFilterExcludeInput
  and: [TransactionFilterInput]
}

input StringEqualInput {
  eq: String
}

input StringFilterInput {
  ne: String
  eq: String
  le: String
  lt: String
  ge: String
  gt: String
  in: [String]
  contains: String
  notContains: String
  between: [String]
  beginsWith: String
}

input IntFilterInput {
  ne: Int
  eq: Int
  le: Int
  lt: Int
  ge: Int
  gt: Int
  contains: Int
  notContains: Int
  between: [Int]
}

input ListFilterInput {
  in: [String!]!
}

input BoolFilterInput {
  ne: Boolean
  eq: Boolean
}

enum SendReceiptMode {
  EMAIL
  SMS
}

input SendReceiptInput {
  transactionUuid: ID!
  mode: SendReceiptMode!
  newEmail: String
  newPhone: String
}

enum CompleteOptOutReceiptsStatus {
  CODE_EXPIRED_OR_INVALID
  COMPLETED
  FAILED
}

type CompleteOptOutReceiptsResult @aws_api_key {
  status: CompleteOptOutReceiptsStatus!
  message: String
}

type TransactionTotals {
  countPurchases: Int!
  countRefunds: Int!
  totalAmount: Int!
  purchaseAmount: Int!
  refundAmount: Int!
  surchargeAmount: Int!
  tipAmount: Int!
  taxAmounts: [TaxAmount!]
  declinedAmount: Int
  noResponseAmount: Int
  cancelledAmount: Int
  totalsType: TransactionTotalsType!
  period: AWSDateTime
  periodLabel: String
}

type TaxAmountBigInt @aws_iam @aws_oidc {
  name: String!
  amount: String!
}

type TransactionTotalsBigInt {
  countPurchases: String!
  countRefunds: String!
  totalAmount: String!
  totalAmountMinusFees: String!
  purchaseAmount: String!
  refundAmount: String!
  saleAmount: String!
  surchargeAmount: String!
  tipAmount: String!
  taxAmounts: [TaxAmountBigInt!]
  feeAmount: String!
  declinedAmount: String
  noResponseAmount: String
  cancelledAmount: String
  totalsType: TransactionTotalsType!
  period: AWSDateTime
  periodLabel: String
}

enum TransactionTotalsTypeV2 {
  DAILY
  WEEKLY
  MONTHLY
}

type TransactionTotalsV2ReportRow {
  countPurchases: String!
  countRefunds: String!
  totalAmount: String!
  totalAmountMinusFees: String!
  purchaseAmount: String!
  refundAmount: String!
  saleAmount: String!
  surchargeAmount: String!
  tipAmount: String!
  taxAmounts: [TaxAmountBigInt!]
  feeAmount: String!
  declinedAmount: String
  totalsType: TransactionTotalsTypeV2!
  period: AWSDateTime
}

type TransactionTotalsHourlySummaryRow {
  countPurchases: String!
  countRefunds: String!
  totalAmount: String!
  totalAmountMinusFees: String!
  purchaseAmount: String!
  refundAmount: String!
  saleAmount: String!
  surchargeAmount: String!
  tipAmount: String!
  taxAmounts: [TaxAmountBigInt!]
  feeAmount: String!
  declinedAmount: String!
  totalsType: TransactionTotalsTypeV2!
  hourOfDay24HourZeroPadded: String!
}

type TransactionTotalsDailySummaryRow {
  countPurchases: String!
  countRefunds: String!
  totalAmount: String!
  totalAmountMinusFees: String!
  purchaseAmount: String!
  refundAmount: String!
  saleAmount: String!
  surchargeAmount: String!
  tipAmount: String!
  taxAmounts: [TaxAmountBigInt!]
  feeAmount: String!
  declinedAmount: String!
  totalsType: TransactionTotalsTypeV2!
  weekdayNumberISO8601: String!
}

type TransactionTotalsV2 {
  totals: [TransactionTotalsV2ReportRow!]!
  currency: String!
  hourlySummary: [TransactionTotalsHourlySummaryRow!]!
  dailySummary: [TransactionTotalsDailySummaryRow!]!
  entityUuid: String!
  regionalOptions: RegionalOptions
}

type MultiEntityTransactionTotalsV2 {
  entities: [TransactionTotalsV2!]!
}

input BetweenFilter {
  between: [String]
}

input EqualInFilter {
  eq: String
  in: [String]
}

input SiteAndSubDeviceFilterInput {
  siteUuid: String!
  deviceUuidIn: [String]
}

input TransactionFilterInputV2 {
  sitesAndDevicesIn: [SiteAndSubDeviceFilterInput]
  typesIn: [String]
  statusIn: [String]
  sourceIn: [String]
}

input TransactionFilterInputMultiEntity {
  typesIn: [String]
  statusIn: [String]
  sourceIn: [String]
}

enum TransactionTotalsType {
  HOURLY
  DAILY
  WEEKLY
  MONTHLY
  HOURLY_SUMMARY
  DAILY_SUMMARY
}

type S3PresignedUrlDownload {
  downloadLink: String!
  expire: AWSDateTime!
}

# The response parameter of exportTransaction subscription
# downloadLink is a pre-signed url points to s3 bucket file
# expire is the expire time of the downloadLink, it lasts 1 hour
type TransactionDownload @aws_iam @aws_oidc {
  # The download link is a presigned URL from s3 object. The file name is a uuid plus some random number, FE better to rename the file.
  downloadLink: String
  # The expire date time of the download link
  expire: AWSDateTime
  # The requested entityUuid which is used by subscription. FE doesn't need to subscribe this value.
  entityUuid: ID!
  # The requested customerUuid which is used by subscription. FE doesn't need to subscribe this value.
  customerUuid: ID!
  # The requested filter which is used by subscription. FE doesn't need to subscribe this value.
  filter: String!
  # The requested format which is used by subscription. FE doesn't need to subscribe this value.
  format: TransactionExportFormat!
  # The error includes error message if something wrong happens. Unlike `Query`, subscription doesn't response the error in `errors` field of the payload.
  error: String
}

input TransactionDownloadInput {
  downloadLink: String
  expire: AWSDateTime
  entityUuid: ID!
  customerUuid: ID!
  filter: String!
  format: TransactionExportFormat!
  error: String
}

enum TransactionExportFormat {
  CSV
  XLSX
  PDF
}

enum TransactionIssuer {
  AFTERPAY
}

type TransactionImageUploadUrls {
  uploadUrl: String!
  fileName: String!
  fileUuid: String!
  expireDate: AWSDateTime!
}

type TransactionImageDownloadUrls {
  downloadUrl: String!
  fileName: String!
  fileUuid: String!
  expireDate: AWSDateTime!
}

input CpocTransactionInput {
  id: ID!
  timestamp: AWSDateTime!
  timestampUtc: AWSDateTime!
  timezone: String
  message: String!
  messageLength: Int!
  ksn: String!
  stan: String @deprecated(reason: "Provide by backend")
  caid: String!
  deviceUuid: ID!
  type: TransactionType!
  source: Source!
  scheme: CardScheme
  bin: String
  panMasked: String
  cardMedia: CardMedia
  isoMessageType: String
  isoProcessingCode: String
  isoPosEntryMode: String!
  isoPosConditionCode: String
  isoCurrencyCode: String
  amount: Int!
  tipAmount: Int
  taxAmounts: [TaxAmountInput!]
  cashAmount: Int
  adjustAmount: Int
  surchargeAmount: Int
  lineItems: [Int!]
  location: String
  locationTimestamp: AWSDateTime
  locationAccuracy: Int
  altitude: String
  bearing: String
  speed: String
  speedAccuracy: String
  temperatureDevice: String
  temperatureAmbient: String
  humidity: String
  pressure: String
  light: String
  originalTransactionUuid: ID
  commsFallback: Boolean
  pinBypassed: Boolean
  posName: String
  externalReference: String
  lcr: Boolean
  attestationDetails: AttestationDetailsInput
}

input AttestationDetailsInput {
  token: String!
  signature: String!
  deviceId: String!
  instanceId: String!
}

type CpocReverseTransactionResponse {
  id: ID!
  timestampUtc: AWSDateTime!
  responseCode: String!
  approvalCode: String
  rrn: String
}

type CpocTransactionResponse {
  id: ID!
  timestampUtc: AWSDateTime!
  responseCode: String!
  approvalCode: String
  rrn: String
  panMasked: String
  scheme: CardScheme
  cardMedia: CardMedia
  cardholderUuid: ID
  cardholderEmail: String
  cardholderMobile: String
  # Expected on Android but no Tweed
  response: String
  responseLength: Int
}

input CpocReverseTransactionInput {
  id: ID!
  deviceUuid: ID!
  siteUuid: ID!
  originalTransactionUuid: ID!
  caid: String!
  stan: String @deprecated(reason: "Provide by backend")
  source: Source!
  amount: Int!
  timestamp: AWSDateTime!
  timestampUtc: AWSDateTime!
  timezone: String
  reason: TransactionCancelReason!
  isRepeat: Boolean
  location: String
  locationTimestamp: AWSDateTime
  locationAccuracy: Int
  altitude: String
  bearing: String
  speed: String
  speedAccuracy: String
  temperatureDevice: String
  temperatureAmbient: String
  humidity: String
  pressure: String
  light: String
  isoCurrencyCode: String
}

input CpocReverseTransactionV2Input {
  id: ID!
  deviceUuid: ID!
  timestampUtc: AWSDateTime!
  timestamp: AWSDateTime!
  timezone: String
  reason: TransactionCancelReason!
  siteUuid: ID!
  transactionDetails: CpocTransactionInput!
  isRetry: Boolean
}

input TransferRemittanceNotificationInput {
  dcaTransactionUuid: String!
  type: ReceiptNotificationType!
  recipientEmail: AWSEmail
  recipientMobile: AWSPhone
  isSenderNotified: Boolean!
}

type TransferRemittanceNotificationResponse {
  isTransferRemittanceQueued: Boolean!
  error: TransferRemittanceErrorEnum
}

enum TransferRemittanceErrorEnum {
  RECIPIENT_EMAIL_NOT_FOUND
  RECIPIENT_MOBILE_NOT_FOUND
  TOO_MANY_REQUESTS
  TRANSACTION_NOTIFICATION_PERIOD_EXPIRED
}

enum ReceiptNotificationType {
  SMS
  EMAIL
}

type TransferRemittancePdfDownload {
  downloadLink: String
  expire: AWSDateTime
  error: TransferRemittancePdfErrorEnum
}

enum TransferRemittancePdfErrorEnum {
  TRANSACTION_NOT_FOUND
}

type TransactionTotalsMultiEntityExport {
  url: String!
}

enum TransactionTotalsMultiEntityExportFormat {
  CSV
}
