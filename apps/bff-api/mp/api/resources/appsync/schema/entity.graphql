type Query {
    getEntity(entityUuid: ID): Entity!
    retrieveEntityOnboardingDetails(entityUuid: ID): EntityOnboardingDetails!
    checkForAdditionalEntityInfo(entityUuid: ID): Boolean!
    getEntityDocumentUploadUrl(entityUuid: ID!): String!
    getEntityDocumentUploadUrls(fileNames: [String]!, subject: String, entityUuid: ID): [EntityDocumentUploadUrls]!
    getEntitySubcategories(category: EntityCategories!, entityUuid: ID): [Subcategory]! @aws_oidc
    getAllSubcategories: [CategorySubcategories!]!
    getEntityTags(entityUuid: ID): [String!]!
    getReferrerName(referralCode: ID!, entityUuid: ID): String @aws_api_key
    getBankingMigrationState(entityUuid: ID): EntityBankingMigrationState! @deprecated(reason: "This query is deprecated and will always return COMPLETED")
    getEntityDailyLimit(entityUuid: ID): EntityDailyLimit!
    requiredOnboardingDocumentsUpload(entityUuid: ID): Boolean!
    getEntitySavingsAccountProduct(entityUuid: ID): EntitySavingsAccountProduct
    getEntityAddressTimeZone(entityUuid: ID): String!
    searchBusinessIdentifier(businessIdentifier: String!, country: String): EntitySearchResultsInitial @aws_oidc
    requiredAdditionalOnboardingInfo(entityUuid: ID!): AdditionalOnboardingInfoRequired!
}

type Mutation {
    createSoletraderEntity(name: String!, entityUuid: ID): Entity! @deprecated(reason: "use confirmUnregisteredSoleTraderInitialDetails")
    updateEntity(entity: EntityInput!): Boolean!
    confirmEntityDetailsInitial(input: ConfirmEntityDetailsInitialInput!, entityUuid: ID): Entity! @deprecated(reason: "use confirmRegisteredBusinessInitialDetails")
    confirmRegisteredBusinessInitialDetails(input: ConfirmRegisteredBusinessInitialDetailsInput!): Entity!
    confirmUnregisteredSoleTraderInitialDetails(input: ConfirmUnregisteredSoleTraderInitialDetailsInput!): Entity!
    confirmUnregisteredBusinessInitialDetails(input: ConfirmUnregisteredBusinessInitialDetailsInput!): Entity!
    saveEntityOnboardingDetails(input: EntityOnboardingDetailsInput!): Boolean!
    saveEntityOnboardingErrors(input: [EntityOnboardingErrorsInput!]!, entityUuid: ID): Boolean! @aws_oidc
    # Select the deposit account for the entity or remit to Zeller card account
    # ID is thirdPartyBankAccountUuid or debitCardAccountUuid if remitToCard is true
    selectDepositAccount(id: ID!, remitToCard: Boolean!, entityUuid: ID): Boolean!
    finaliseEntityOnboarding(entityUuid: ID): EntityOnboardingResults!
    publishEntityInitialSearchResult(input: EntitySearchResultsInitialInput!): EntitySearchResultsInitial! @aws_iam
    publishEntityFullSearchResult(input: EntitySearchResultsFullInput!): EntitySearchResultsFull! @aws_iam
    addEntitySubcategory(input: EntitySubcategoryInput!, entityUuid: ID): ID! @aws_oidc
    updateEntitySubcategory(subcategoryUuid: ID!, subcategory: String!, entityUuid: ID): Boolean! @aws_oidc
    removeEntitySubcategory(subcategoryUuid: ID!, entityUuid: ID): Boolean! @aws_oidc
    addEntityTag(tag: String!, entityUuid: ID): Boolean!
    removeEntityTag(tag: String!, entityUuid: ID): Boolean!
}

type Subscription {
    searchEntityDetailsInitial(businessIdentifier: String!): EntitySearchResultsInitial
    @aws_oidc
    @aws_subscribe(mutations: ["publishEntityInitialSearchResult"])
    @deprecated(reason: "use Query.searchBusinessIdentifier instead")

    searchEntityDetailsFull(entityUuid: ID!): EntitySearchResultsFull
    @aws_subscribe(mutations: ["publishEntityFullSearchResult"])
}

enum EntityBankingMigrationState {
    NOT_REQUIRED
    REQUIRED
    STARTED
    REQUEST_STARTED
    ERROR
    COMPLETED
    COMPLETED_WITH_ERROR
}

enum EntityOnboardingResult {
    COMPLETED
    IN_REVIEW
    MANUAL_ACTIVATION @deprecated(reason: "no longer in use")
    MORE_INFO_REQUIRED @deprecated(reason: "no longer in use")
}

type EntityOnboardingResults {
    entityUuid: ID!
    bsb: String @deprecated(reason: "no return value")
    account: String @deprecated(reason: "no return value")
    result: EntityOnboardingResult
}

type EntityDocumentUploadUrls {
    uploadUrl: String!
    fileName: String!
}

type EntityAddress @aws_oidc @aws_iam {
    street1: String
    street2: String
    suburb: String
    state: String
    postcode: String
    country: String
}

type FeeRateSettings @aws_oidc @aws_iam {
    feePercent: Int
    feeFixed: Int
    feePercentMoto: Int
    feeFixedMoto: Int
    feePercentVt: Int
    feeFixedCnp: Int @deprecated(reason: "Use subtypes instead")
    feeFixedCpoc: Int
    feeFixedIntlXinv: Int
    feeFixedIntlZinv: Int
    feeFixedIntlPbl: Int
    feeFixedXinv: Int
    feeFixedZinv: Int
    feeFixedVt: Int
    feeFixedPbl: Int
    feePercentCnp: Int @deprecated(reason: "Use subtypes instead")
    feePercentCpoc: Int
    feePercentIntlXinv: Int
    feePercentIntlZinv: Int
    feePercentIntlPbl: Int
    feePercentXinv: Int
    feePercentZinv: Int
    feePercentPbl: Int
}

enum CategoryGroup {
    BEAUTY
    EDUCATION
    CHARITIES
    FOODDRINK
    HEALTHCAREFITNESS
    HOMEMAINTENANCE
    LEISUREENTERTAINMENT
    PROFESSIONALSERVICES
    RETAIL
    TRANSPORTATION
    TRAVEL
    GOVERNMENTSERVICES
    FINANCIALSERVICES
}

enum Category {
    OTHER
    BEAUTYSALON
    HAIRSALON
    BARBERSHOP
    MASSAGETHERAPIST
    NAILSALON
    TATTOOPIERCING
    HEALTHBEAUTYSPA
    MASSAGEPARLOUR
    CHILDCARE
    TEACHER
    TUTOR
    SCHOOL
    UNIVERSITY
    CHARITY
    MEMBERSHIPORG
    POLITICALORG
    RELIGIOUSORG
    BAKERY
    BARCLUB
    CATERING
    COFFEE
    FOODTRUCKCART
    GROCERY
    MARKET
    PRIVATECHEF
    TAKEAWAYRESTAURANT
    TABLESERVICERESTAURANT
    WHOLESALEVENDOR
    ALCOHOLWHOLESALER
    ACUPUNCTURE
    CAREGIVER
    CHIROPRACTOR
    DENTIST
    GYM
    MEDICALTPRACTITIONER
    OPTOMETRIST
    PERSONALTRAINER
    PSYCHIATRIST
    COUNSELLOR
    VETERINARY
    PHYSIOTHERAPIST
    DIETITIAN
    PODIATRIST
    OCCUPATIONALTHERAPIST
    HYPNOTHERAPIST
    PHYSICALTHERAPIST
    DOCTOR
    ANESTHETIST
    MIDWIFE
    NURSE
    PHARMACIST
    AUTOMOTIVE
    CARPETCLEANING
    CLEANING
    CLOTHINGALTERATIONS
    DRYCLEANING
    ELECTRICAL
    FLOORING
    GENERALCONTRACTING
    HEATINGANDAC
    INSTALLATIONSERVICES
    RUBBISHREMOVAL
    LANDSCAPING
    LOCKSMITH
    PAINTING
    PESTCONTROL
    PLUMBING
    CARPENTRY
    PLASTERINGCEILING
    TILINGCARPETING
    BRICKLAYING
    CONCRETING
    GLAZING
    CONSTRUCTIONMATERIALS
    CONSTRUCTION
    ARCHITECTURE
    FESTIVALS
    CINEMA
    MUSEUM
    MUSIC
    PERFORMINGARTS
    SPORTINGEVENTS
    SPORTSRECREATION
    ACCOUNTING
    CONSULTING
    DESIGN
    INTERIORDESIGN
    LEGALSERVICES
    MARKETING
    PHOTOGRAPHY
    PRINTINGSERVICES
    REALESTATE
    SOFTWAREDEVELOPMENT
    DATINGSERVICES
    EMPLOYMENTAGENCIES
    MOTIVATIONALSERVICES
    ARTPHOTOFILM
    BOOKSMUSICVIDEO
    CLOTHING
    COMPUTERAPPLICANCES
    ELECTRONICS
    EYEWEAR
    EVENTS
    FLOWERSGIFTS
    FURNITURE
    HOMEGOODS
    HOBBYSHOP
    JEWELLERYWATCHES
    OFFICESUPPLY
    PETSHOP
    SPECIALITYSHOP
    SPORTINGGOODS
    BUS
    DELIVERY
    REMOVALIST
    PRIVATECARHIRE
    TAXI
    AIRLINEANDAIRCARRIER
    COURIERSERVICES
    MOVERS
    BOATRENTALS
    DEALERS
    BUSINESSSERVICES
    CARTRUCKSERVICES
    CARRENTAL
    TRAVELAGENCY
    LODGING
    LOCALCOUNCIL
    LIBRARY
    PRIMARYSCHOOL
    PARKSRECREATION
    GAMBLINGESTABLISHMENT
    FOREIGNEXCHANGESEVICES
    CRYPTOCURRENCY
    PAYMENTPROCESSORS
    INSURANCEPROVIDERS
    ADULTSERVICES
    ONLINETOBACCOVAPERETAILERS
    WEAPONSAMMUNITIONS
}

enum EntityCategories {
    PURCHASES
    COST_OF_GOODS_SOLD
    ADVERTISING
    BANK_FEES
    CLEANING
    CONSULTING_ACCOUNTING
    ENTERTAINMENT
    FREIGHT_COURIER
    GENERAL_EXPENSES
    INSURANCE
    INTEREST_EXPENSE
    LEGAL_EXPENSES
    LIGHT_POWER_HEATING
    MOTOR_VEHICLE_EXPENSES
    OFFICE_EXPENSES
    PRINTING_STATIONERY
    RENT
    WAGES_SALARIES
    SUPERANNUATION
    COMMISSION
    SUBSCRIPTIONS
    TELEPHONE_INTERNET
    TRAVEL_NATIONAL
    TRAVEL_INTERNATIONAL
    INCOME_TAX_EXPENSE
    OFFICE_EQUIPMENT
    COMPUTER_EQUIPMENT
}

enum TaxRate {
    BAS_EXCLUDED
    GST_FREE_EXPENSE
    GST_FREE_INCOME
    GST_ON_EXPENSE
    GST_ON_IMPORTS
    GST_ON_INCOME
}

enum EntityType {
    INDIVIDUAL
    COMPANY
    PARTNERSHIP
    TRUST
    ASSOCIATION
    ASSOCIATION_UNINCORPORATED
    BENEFICIARY_CLASS
    OTHER
    GOVERNMENT
}

enum OnboardingStatus {
    NONE
    PHONE_COMPLETE
    ENTITY_ESTABLISHED
    ENTITY_ADDRESS1
    ENTITY_ADDRESS2
    ENTITY_REVENUE
    ENTITY_CATEGORY
    TRADING_NAME_ESTABLISHED
    IDV_REQUIRED
    IDV_COMPLETE
    DIRECTORS_ESTABLISHED
    BOS_ESTABLISHED
    ALT_BOS_ESTABLISHED
    MORE_INFO_COLLECTED
    BUSINESS_REG_COLLECTED
    PARTNERS_ESTABLISHED
    DOC_UPLOADED
    SETTLORS_ESTABLISHED
    BEN_ESTABLISHED
    TRUSTEES_ESTABLISHED
    CHAIR_ESTABLISHED
    SECRETARY_ESTABLISHED
    TREASURE_ESTABLISHED
    GOVERNMENT_ROLE_ESTABLISHED
    FINALISING_ONBOARDING
    REVIEW
    ONBOARDED
    RC_ONBOARDED
    RC_REJECTED
    RC_ABANDONED
    RC_DEPLATFORMED
    RC_REVIEW
    BV_COMPLETE
    BV_ERROR
}

enum OnboardingFlowType {
    COMPANY
    COMPANY_NOT_FOUND
    INDIVIDUAL
    INDIVIDUAL_NO_ABN
    TRUST
    PARTNERSHIP
    ASSOCIATION
    GOVERNMENT
}

enum SettledSumValue {
    LessThan10k
    MoreOrEqual10k
}

enum TrusteeType {
    COMPANY
    INDIVIDUAL
}

type RegionalOptions {
    surchargeAllowed: Boolean!
}

type Entity {
    id: ID!
    shortId: String
    name: String
    acn: String
    abn: String
    type: EntityType
    tradingName: String
    registeredAddress: EntityAddress
    businessAddress: EntityAddress
    categoryGroup: CategoryGroup
    category: Category
    remitToCard: Boolean
    estimatedAnnualRevenue: Int
    debitCardAccountUuid: ID
    depositAccountUuid: ID
    goodsServicesProvided: String
    customerDiscovery: String
    feeRateSettings: FeeRateSettings
    website: String
    instagram: String
    facebook: String
    twitter: String
    regulatorBody: RegulatorBody
    onboardingStatus: OnboardingStatus
    canAcquireCnp: Boolean
    canAcquireVt: Boolean
    canAcquireMoto: Boolean
    canAcquire: Boolean
    canAcquireMobile: Boolean
    canCreateAccount: Boolean
    canCreateCard: Boolean
    canPayByCard: Boolean
    canRefund: Boolean
    canSettle: Boolean
    canStandIn: Boolean
    canTransferIn: Boolean
    canTransferOut: Boolean
    hadForcedRefund: Boolean
    hasChargeback: Boolean
    hadDirectDebitFailure: Boolean
    hasDirectDebitRequest: Boolean
    establishingBusiness: Boolean
    referralCode: String
    promotionBalance: Int
    @deprecated(reason: "bff promotions have been retired")
    referralPromotion: EntityPromotion
    @deprecated(reason: "bff promotions have been retired")
    referrals: [EntityReferral!]!
    termsOfService: EntityTermsOfService
    transactionMetaData: EntityTransactionMetaData
    outstandingTransactionRequirementConfig: OutstandingTransactionRequirementConfig
    # additional resolver
    savingsAccountProduct: EntitySavingsAccountProduct
    paymentSettings: PaymentSettings!
    primaryAccountHolder: String
    currency: String
    domicile: String
    cohort: [String!]
    crn: String
    regionalOptions: RegionalOptions
}

type PaymentSettings {
    motoPaymentLimits: PaymentLimits!
    paymentLimits: PaymentLimits!
    cpocPaymentLimits: PaymentLimits!
    cnpPaymentLimits: PaymentLimits!
}

type PaymentLimits {
    maximum: String!
    minimum: String!
}

type EntitySavingsAccountProduct {
    id: ID
    productType: SavingsAccountProductType
    effectiveInterestRate: String
    baseInterestRate: String
    bonusInterestRate: String
    bonusLengthInDays: String
    interestThreshold: Money
}

type OutstandingTransactionRequirementConfig {
    note: Boolean!
    attachments: Boolean!
    category: Boolean!
    accountingCategory: Boolean!
}

input EntityAddressInput {
    street1: String
    street2: String
    suburb: String
    state: String
    postcode: String
    country: String
}

input EntityInput {
    id: ID!
    name: String
    acn: String
    abn: String
    type: EntityType
    tradingName: String
    registeredAddress: EntityAddressInput
    businessAddress: EntityAddressInput
    categoryGroup: CategoryGroup
    category: Category
    remitToCard: Boolean
    estimatedAnnualRevenue: Int
    debitCardAccountUuid: ID
    goodsServicesProvided: String
    customerDiscovery: String
    website: String
    instagram: String
    facebook: String
    twitter: String
    regulatorBody: RegulatorBodyInput
    onboardingStatus: OnboardingStatus
    establishingBusiness: Boolean
    outstandingTransactionRequirementConfig: OutstandingTransactionRequirementConfigInput
}

input OutstandingTransactionRequirementConfigInput {
    note: Boolean
    attachments: Boolean
    category: Boolean
    accountingCategory: Boolean
}

input ConfirmEntityDetailsInitialInput {
    name: String!
    acn: String
    abn: String
    type: EntityType!
    manualEntry: Boolean
}

input AusBusinessInput {
    acn: String
    abn: String
}

input GbrBusinessInput {
    crn: String!
}

input BusinessDetailsInput {
    ausBusiness: AusBusinessInput
    gbrBusiness: GbrBusinessInput
}

type AusBusiness @aws_oidc {
  acn: String
  abn: String
}

type GbrBusiness @aws_oidc {
  crn: String!
}

type BusinessDetails @aws_oidc {
  ausBusiness: AusBusiness
  gbrBusiness: GbrBusiness
}


input ConfirmRegisteredBusinessInitialDetailsInput {
    name: String!
    type: EntityType!
    tradingName:String
    businessDetails: BusinessDetailsInput!
    categoryGroup: CategoryGroup!
    category: Category!
    estimatedAnnualRevenue: Int!
    cohort: [String!]
    manualEntry: Boolean
    country: String! #ISO 3166-1 alpha-3
}

input ConfirmUnregisteredSoleTraderInitialDetailsInput {
    tradingName: String
    categoryGroup: CategoryGroup!
    category: Category!
    estimatedAnnualRevenue: Int!
    establishingBusiness: Boolean!
    cohort: [String!]
    country: String! #ISO 3166-1 alpha-3
}

input ConfirmUnregisteredBusinessInitialDetailsInput {
    name: String!
    type: EntityType!
    tradingName: String
    categoryGroup: CategoryGroup!
    category: Category!
    estimatedAnnualRevenue: Int!
    establishingBusiness: Boolean!
    cohort: [String!]
    country: String! #ISO 3166-1 alpha-3
}

type EntitySearchResultsInitial @aws_iam @aws_oidc {
    businessIdentifier: String!
    found: Boolean!
    country: String #ISO 3166-1 alpha-3
    name: String
    businessDetails: BusinessDetails
    acn: String @deprecated(reason: "use businessDetails.ausBusiness.acn")
    abn: String @deprecated(reason: "use businessDetails.ausBusiness.abn")
    type: EntityType
    error: String
}

input EntitySearchResultsInitialInput {
    businessIdentifier: String!
    found: Boolean!
    name: String
    acn: String
    abn: String
    type: EntityType
    error: String
}

type EntitySearchMemberAddress @aws_oidc @aws_iam {
    street: String
    suburb: String
    state: String
    postcode: String
    country: String
}

type EntitySearchMember @aws_oidc @aws_iam {
    type: EntityType
    firstname: String
    middlename: String
    lastname: String
    companyTrustName: String
    abn: String
    address: EntitySearchMemberAddress
    dob: AWSDate
    director: Boolean
    secretary: Boolean
    ceo: Boolean
    beneficialOwner: Boolean
    beneficialOwnerAlt: Boolean
    shareholder: Boolean
    beneficiary: Boolean
    partner: Boolean
    trustee: Boolean
    settlor: Boolean
    chair: Boolean
    treasurer: Boolean
}
type EntitySearchMemberOnboarding @aws_oidc @aws_iam {
    type: EntityType
    firstname: String
    middlename: String
    lastname: String
    companyTrustName: String
    abn: String
    acn: String
    address: EntitySearchMemberAddress
    dob: AWSDate
    director: Boolean
    secretary: Boolean
    ceo: Boolean
    beneficialOwner: Boolean
    beneficialOwnerAlt: Boolean
    shareholder: Boolean
    beneficiary: Boolean
    partner: Boolean
    trustee: Boolean
    settlor: Boolean
    chair: Boolean
    treasurer: Boolean
    temporaryId: String
    isCurrentUser: Boolean
    companyProfileData: CompanyProfileData
}
input EntitySearchMemberAddressInput {
    street: String
    suburb: String
    state: String
    postcode: String
    country: String
}

input EntitySearchMemberInput {
    type: EntityType
    firstname: String
    middlename: String
    lastname: String
    companyTrustName: String
    abn: String
    address: EntitySearchMemberAddressInput
    dob: AWSDate
    director: Boolean
    secretary: Boolean
    ceo: Boolean
    beneficialOwner: Boolean
    beneficialOwnerAlt: Boolean
    shareholder: Boolean
    beneficiary: Boolean
    partner: Boolean
    trustee: Boolean
    settlor: Boolean
    chair: Boolean
    treasurer: Boolean
}
input EntitySearchMemberOnboardingInput {
    type: EntityType
    firstname: String
    middlename: String
    lastname: String
    companyTrustName: String
    abn: String
    acn: String
    address: EntitySearchMemberAddressInput
    dob: AWSDate
    director: Boolean
    secretary: Boolean
    ceo: Boolean
    beneficialOwner: Boolean
    beneficialOwnerAlt: Boolean
    shareholder: Boolean
    beneficiary: Boolean
    partner: Boolean
    trustee: Boolean
    settlor: Boolean
    chair: Boolean
    treasurer: Boolean
    temporaryId: String
    isCurrentUser: Boolean
    companyProfileData: CompanyProfileData
}
type EntitySearchResultsFull @aws_oidc @aws_iam {
    entityUuid: ID!
    found: Boolean
    acn: String
    registeredAddress: EntityAddress
    businessAddress: EntityAddress
    members: [EntitySearchMember!]
    error: String
}
input EntitySearchResultsFullInput {
    entityUuid: ID!
    found: Boolean
    acn: String
    registeredAddress: EntityAddressInput
    businessAddress: EntityAddressInput
    members: [EntitySearchMemberInput!]
    error: String
}

enum RegulatorBodyType {
    COMMONWEALTH
    VIC
    ACT
    NSW
    QLD
    TAS
    NT
    SA
    WA
}

type RegulatorBody {
    name: String
    referenceNumber: String
    type: RegulatorBodyType
}

input RegulatorBodyInput {
    name: String
    referenceNumber: String
    type: RegulatorBodyType
}

type EntityOnboardingDetails {
    entityUuid: ID!
    name: String
    acn: String
    abn: String
    type: EntityType
    registeredAddress: EntityAddress
    businessAddress: EntityAddress
    members: [EntitySearchMemberOnboarding]
    categoryGroup: CategoryGroup
    category: Category
    estimatedAnnualRevenue: Int
    goodsServicesProvided: String
    customerDiscovery: String
    website: String
    instagram: String
    facebook: String
    twitter: String
    tradingName: String
    hasNoTradingName: Boolean
    onboardingFlowType: OnboardingFlowType
    lastCheckPoint: String
    lastRoute: String
    isAfterFullSearch: Boolean
    helperFields: HelperFields
    membersFilters: MembersFilters
    uploadedFileNames: [String]
    kycInitialData: KYCInitialData
    regulatorBody: RegulatorBody
    initialCustomerData: InitialCustomerData
    governmentRole: String
    establishingBusiness: Boolean
    flowFlags: String
    cohort: [String!]
    interestProducts: String
    crn: String
}

input EntityOnboardingDetailsInput {
    entityUuid: ID!
    name: String
    acn: String
    abn: String
    type: EntityType
    registeredAddress: EntityAddressInput
    businessAddress: EntityAddressInput
    members: [EntitySearchMemberOnboardingInput]
    categoryGroup: CategoryGroup
    category: Category
    estimatedAnnualRevenue: Int
    goodsServicesProvided: String
    customerDiscovery: String
    website: String
    instagram: String
    facebook: String
    twitter: String
    tradingName: String
    hasNoTradingName: Boolean
    onboardingFlowType: OnboardingFlowType
    lastCheckPoint: String
    lastRoute: String
    isAfterFullSearch: Boolean
    helperFields: HelperFieldsInput
    membersFilters: MembersFiltersInput
    uploadedFileNames: [String]
    kycInitialData: KYCInitialDataInput
    regulatorBody: RegulatorBodyInput
    initialCustomerData: InitialCustomerDataInput
    governmentRole: String
    establishingBusiness: Boolean
    flowFlags: String
    cohort: [String!]
    interestProducts: String
    crn: String
}

type HelperFields {
    isNotRegulated: Boolean
    hasNoPlaceOfBusiness: Boolean
    settledSum: SettledSumValue
    trustee: TrusteeType
}

input HelperFieldsInput {
    isNotRegulated: Boolean
    hasNoPlaceOfBusiness: Boolean
    settledSum: SettledSumValue
    trustee: TrusteeType
}

type MembersFilters {
    director: Boolean
    beneficialOwner: Boolean
    settlor: Boolean
    trustee: Boolean
    beneficiary: Boolean
    partner: Boolean
    chair: Boolean
    secretary: Boolean
    treasurer: Boolean
}

input MembersFiltersInput {
    director: Boolean
    beneficialOwner: Boolean
    settlor: Boolean
    trustee: Boolean
    beneficiary: Boolean
    partner: Boolean
    chair: Boolean
    secretary: Boolean
    treasurer: Boolean
}

type KYCInitialDataAttemptsType {
    DRIVING_LICENCE: Int
    PASSPORT: Int
    MEDICARE_CARD: Int
}

input KYCInitialDataAttemptsInput {
    DRIVING_LICENCE: Int
    PASSPORT: Int
    MEDICARE_CARD: Int
}

type KYCInitialData {
    documents: [DocumentType]
    attempts: KYCInitialDataAttemptsType
    isAgreed: Boolean
    personalData: PersonalData
}

input KYCInitialDataInput {
    documents: [DocumentType]
    attempts: KYCInitialDataAttemptsInput
    isAgreed: Boolean
    personalData: PersonalDataInput
}

enum DocumentType {
    DRIVING_LICENCE
    PASSPORT
    MEDICARE_CARD
    NO_SECOND_ID
}

type PersonalData {
    street: String
    suburb: String
    state: String
    postcode: String
    firstName: String
    middleName: String
    lastName: String
    dob: String
}

input PersonalDataInput {
    street: String
    suburb: String
    state: String
    postcode: String
    firstName: String
    middleName: String
    lastName: String
    dob: String
}

type InitialCustomerData {
    street: String
    suburb: String
    state: String
    postcode: String
    country: String
    roles: [String!]
    companyTrustName: String
    abn: String
    acn: String
    firstName: String
    lastName: String
    middleName: String
    dob: String
    temporaryId: String
    companyProfileData: CompanyProfileData
}

input InitialCustomerDataInput {
    street: String
    suburb: String
    state: String
    postcode: String
    country: String
    roles: [String!]
    companyTrustName: String
    abn: String
    acn: String
    firstName: String
    lastName: String
    middleName: String
    dob: String
    temporaryId: String
    companyProfileData: CompanyProfileData
}

input EntityOnboardingErrorsInput {
    onboardingStatus: OnboardingStatus!
    errorType: String!
    errorMessage: String!
    data: String!
    timestamp: AWSDateTime!
}

input EntitySubcategoryInput {
    category: EntityCategories!
    subcategory: String!
}

type Subcategory @aws_iam @aws_oidc {
    id: ID!
    name: String!
    predefined: Boolean!
}

type CategorySubcategories {
  category: EntityCategories!
  subcategories: [Subcategory!]!
}

type EntityPromotion {
    balance: Int!
    expiry: AWSDateTime
}

type EntityReferral {
    referredUuid: ID!
    createdAt: AWSDateTime!
    credit: Int!
    creditAppliedAt: AWSDateTime
}

type EntityTermsOfService @aws_iam @aws_oidc {
    ezta: EntityTermsOfServiceAcceptance
}

type EntityTermsOfServiceAcceptance @aws_iam @aws_oidc {
    accepted: Boolean!
}

type EntityTransactionMetaData {
    yetToMakeTransaction: Boolean!
    firstTransactionUuid: String
    firstTransactionTimestamp: AWSDateTime
}

type EntityDailyLimit {
    id: String!
    maximumLimit: Money!
}

type AdditionalOnboardingInfoRequired {
  additionalEntityInfo: Boolean!
  onboardingDocumentsUpload: Boolean!
  regulatedBusiness: Boolean!
}
