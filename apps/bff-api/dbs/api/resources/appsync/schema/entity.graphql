type Query {
  getEntity(entityUuid: ID): Entity!
  retrieveEntityOnboardingDetails(entityUuid: ID): EntityOnboardingDetails!
}

type Mutation {
  createSoletraderEntity(name: String!, entityUuid: ID): Entity!
  updateEntity(entity: EntityInput!): Boolean!
  # Does not implement
  finaliseEntityOnboarding(entityUuid: ID): EntityOnboardingResults!
  confirmEntityDetailsInitial(input: ConfirmEntityDetailsInitialInput!, entityUuid: ID): Entity!
  saveEntityOnboardingDetails(input: EntityOnboardingDetailsInput!, entityUuid: ID): Boolean!
  saveEntityOnboardingErrors(input: [EntityOnboardingErrorsInput!]!, entityUuid: ID): Boolean!
  publishEntityInitialSearchResult(input: EntitySearchResultsInitialInput!): EntitySearchResultsInitial! @aws_iam
  publishEntityFullSearchResult(input: EntitySearchResultsFullInput!): EntitySearchResultsFull! @aws_iam
}

type Subscription {
  searchEntityDetailsInitial(businessIdentifier: String!, entityUuid: ID): EntitySearchResultsInitial
  @aws_oidc
  @aws_subscribe(mutations: ["publishEntityInitialSearchResult"])

  searchEntityDetailsFull(entityUuid: ID!): EntitySearchResultsFull
  @aws_subscribe(mutations: ["publishEntityFullSearchResult"])
}

enum EntityOnboardingResult {
  COMPLETED
  IN_REVIEW
  MANUAL_ACTIVATION
  MORE_INFO_REQUIRED
}

type EntityOnboardingResults {
  entityUuid: ID!
  bsb: String
  account: String
  result: EntityOnboardingResult
}

type EntityAddress @aws_oidc @aws_iam {
  street1: String
  street2: String
  suburb: String
  state: String
  postcode: String
  country: String
}

type FeeRateSettings @aws_oidc @aws_iam {
  feePercent: Int
  feeFixed: Int
  feePercentMoto: Int
  feeFixedMoto: Int
  feePercentVt: Int
  feeFixedCnp: Int @deprecated(reason: "Use subtypes instead")
  feeFixedCpoc: Int
  feeFixedIntlXinv: Int
  feeFixedIntlZinv: Int
  feeFixedIntlPbl: Int
  feeFixedXinv: Int
  feeFixedZinv: Int
  feeFixedVt: Int
  feeFixedPbl: Int
  feePercentCnp: Int @deprecated(reason: "Use subtypes instead")
  feePercentCpoc: Int
  feePercentIntlXinv: Int
  feePercentIntlZinv: Int
  feePercentIntlPbl: Int
  feePercentXinv: Int
  feePercentZinv: Int
  feePercentPbl: Int
}

enum EntityType {
  INDIVIDUAL
  COMPANY
  PARTNERSHIP
  TRUST
  ASSOCIATION
  ASSOCIATION_UNINCORPORATED
  BENEFICIARY_CLASS
  OTHER
  GOVERNMENT
}

enum OnboardingStatus {
  NONE
  PHONE_COMPLETE
  ENTITY_ESTABLISHED
  ENTITY_ADDRESS1
  ENTITY_ADDRESS2
  ENTITY_REVENUE
  ENTITY_CATEGORY
  TRADING_NAME_ESTABLISHED
  IDV_REQUIRED
  IDV_COMPLETE
  DIRECTORS_ESTABLISHED
  BOS_ESTABLISHED
  ALT_BOS_ESTABLISHED
  MORE_INFO_COLLECTED
  BUSINESS_REG_COLLECTED
  PARTNERS_ESTABLISHED
  DOC_UPLOADED
  SETTLORS_ESTABLISHED
  BEN_ESTABLISHED
  TRUSTEES_ESTABLISHED
  CHAIR_ESTABLISHED
  SECRETARY_ESTABLISHED
  TREASURE_ESTABLISHED
  GOVERNMENT_ROLE_ESTABLISHED
  FINALISING_ONBOARDING
  REVIEW
  ONBOARDED
  RC_ONBOARDED
  RC_REJECTED
  RC_ABANDONED
  RC_DEPLATFORMED
  RC_REVIEW
  BV_ERROR
}

enum OnboardingFlowType {
  COMPANY
  COMPANY_NOT_FOUND
  INDIVIDUAL
  INDIVIDUAL_NO_ABN
  TRUST
  PARTNERSHIP
  ASSOCIATION
  GOVERNMENT
}

enum SettledSumValue {
  LessThan10k
  MoreOrEqual10k
}

enum TrusteeType {
  COMPANY
  INDIVIDUAL
}

type RegionalOptions {
  surchargeAllowed: Boolean!
}

type Entity {
  id: ID!
  shortId: String
  name: String
  acn: String
  abn: String
  type: EntityType!
  tradingName: String
  registeredAddress: EntityAddress
  businessAddress: EntityAddress
  categoryGroup: CategoryGroup
  category: Category
  estimatedAnnualRevenue: Int
  remitToCard: Boolean
  debitCardAccountUuid: ID
  depositAccountUuid: ID
  goodsServicesProvided: String
  customerDiscovery: String
  website: String
  instagram: String
  facebook: String
  twitter: String
  regulatorBody: RegulatorBody
  onboardingStatus: OnboardingStatus
  feeRateSettings: FeeRateSettings
  canAcquireCnp: Boolean
  canAcquireVt: Boolean
  canAcquireMoto: Boolean
  canAcquire: Boolean
  canAcquireMobile: Boolean
  canRefund: Boolean
  canStandIn: Boolean
  hadForcedRefund: Boolean
  hasChargeback: Boolean
  hadDirectDebitFailure: Boolean
  hasDirectDebitRequest: Boolean
  establishingBusiness: Boolean
  referralCode: String
  transactionMetaData: EntityTransactionMetaData
  currency: String
  domicile: String
  regionalOptions: RegionalOptions
}

input EntityAddressInput {
  street1: String
  street2: String
  suburb: String
  state: String
  postcode: String
  country: String
}

input EntityInput {
  id: ID!
  name: String
  acn: String
  abn: String
  type: EntityType
  tradingName: String
  registeredAddress: EntityAddressInput
  businessAddress: EntityAddressInput
  categoryGroup: CategoryGroup
  category: Category
  estimatedAnnualRevenue: Int
  goodsServicesProvided: String
  customerDiscovery: String
  website: String
  instagram: String
  facebook: String
  twitter: String
  regulatorBody: RegulatorBodyInput
  onboardingStatus: OnboardingStatus
  establishingBusiness: Boolean
}

input ConfirmEntityDetailsInitialInput {
  name: String!
  acn: String
  abn: String
  type: EntityType!
  manualEntry: Boolean
}

type EntitySearchResultsInitial @aws_iam @aws_oidc {
  businessIdentifier: String!
  found: Boolean!
  name: String
  acn: String
  abn: String
  type: EntityType
  error: String
}

input EntitySearchResultsInitialInput {
  businessIdentifier: String!
  found: Boolean!
  name: String
  acn: String
  abn: String
  type: EntityType
  error: String
}

type EntitySearchMemberAddress @aws_oidc @aws_iam {
  street: String
  suburb: String
  state: String
  postcode: String
  country: String
}

type EntitySearchMember @aws_oidc @aws_iam {
  type: EntityType
  firstname: String
  middlename: String
  lastname: String
  companyTrustName: String
  abn: String
  address: EntitySearchMemberAddress
  dob: AWSDate
  director: Boolean
  secretary: Boolean
  ceo: Boolean
  beneficialOwner: Boolean
  beneficialOwnerAlt: Boolean
  shareholder: Boolean
  beneficiary: Boolean
  partner: Boolean
  trustee: Boolean
  settlor: Boolean
}

type EntitySearchMemberOnboarding @aws_oidc @aws_iam {
  type: EntityType
  firstname: String
  middlename: String
  lastname: String
  companyTrustName: String
  abn: String
  address: EntitySearchMemberAddress
  dob: AWSDate
  director: Boolean
  secretary: Boolean
  ceo: Boolean
  beneficialOwner: Boolean
  beneficialOwnerAlt: Boolean
  shareholder: Boolean
  beneficiary: Boolean
  partner: Boolean
  trustee: Boolean
  settlor: Boolean
  temporaryId: String
  isCurrentUser: Boolean
}
input EntitySearchMemberAddressInput {
  street: String
  suburb: String
  state: String
  postcode: String
  country: String
}

input EntitySearchMemberInput {
  type: EntityType
  firstname: String
  middlename: String
  lastname: String
  companyTrustName: String
  abn: String
  address: EntitySearchMemberAddressInput
  dob: AWSDate
  director: Boolean
  secretary: Boolean
  ceo: Boolean
  beneficialOwner: Boolean
  beneficialOwnerAlt: Boolean
  shareholder: Boolean
  beneficiary: Boolean
  partner: Boolean
  trustee: Boolean
  settlor: Boolean
}
input EntitySearchMemberOnboardingInput {
  type: EntityType
  firstname: String
  middlename: String
  lastname: String
  companyTrustName: String
  abn: String
  address: EntitySearchMemberAddressInput
  dob: AWSDate
  director: Boolean
  secretary: Boolean
  ceo: Boolean
  beneficialOwner: Boolean
  beneficialOwnerAlt: Boolean
  shareholder: Boolean
  beneficiary: Boolean
  partner: Boolean
  trustee: Boolean
  settlor: Boolean
  temporaryId: String
  isCurrentUser: Boolean
}

type EntitySearchResultsFull @aws_oidc @aws_iam {
  entityUuid: ID!
  found: Boolean
  acn: String
  registeredAddress: EntityAddress
  businessAddress: EntityAddress
  members: [EntitySearchMember!]
  estimatedAnnualRevenue: Int
  error: String
}
input EntitySearchResultsFullInput {
  entityUuid: ID!
  found: Boolean
  acn: String
  registeredAddress: EntityAddressInput
  businessAddress: EntityAddressInput
  members: [EntitySearchMemberInput!]
  estimatedAnnualRevenue: Int
  error: String
}

type EntityOnboardingDetails {
  entityUuid: ID!
  name: String
  acn: String
  abn: String
  type: EntityType
  registeredAddress: EntityAddress
  businessAddress: EntityAddress
  members: [EntitySearchMemberOnboarding]
  categoryGroup: CategoryGroup
  category: Category
  estimatedAnnualRevenue: Int
  goodsServicesProvided: String
  customerDiscovery: String
  website: String
  instagram: String
  facebook: String
  twitter: String
  tradingName: String
  hasNoTradingName: Boolean
  onboardingFlowType: OnboardingFlowType
  lastCheckPoint: String
  lastRoute: String
  isAfterFullSearch: Boolean
  helperFields: HelperFields
  membersFilters: MembersFilters
  uploadedFileNames: [String]
  kycInitialData: KYCInitialData
  regulatorBody: RegulatorBody
  initialCustomerData: InitialCustomerData
  governmentRole: String
  establishingBusiness: Boolean
}

input EntityOnboardingDetailsInput {
  entityUuid: ID!
  name: String
  acn: String
  abn: String
  type: EntityType
  registeredAddress: EntityAddressInput
  businessAddress: EntityAddressInput
  members: [EntitySearchMemberOnboardingInput]
  categoryGroup: CategoryGroup
  category: Category
  estimatedAnnualRevenue: Int
  goodsServicesProvided: String
  customerDiscovery: String
  website: String
  instagram: String
  facebook: String
  twitter: String
  tradingName: String
  hasNoTradingName: Boolean
  onboardingFlowType: OnboardingFlowType
  lastCheckPoint: String
  lastRoute: String
  isAfterFullSearch: Boolean
  helperFields: HelperFieldsInput
  membersFilters: MembersFiltersInput
  uploadedFileNames: [String]
  kycInitialData: KYCInitialDataInput
  regulatorBody: RegulatorBodyInput
  initialCustomerData: InitialCustomerDataInput
  governmentRole: String
  establishingBusiness: Boolean
}

enum Category {
	OTHER
	BEAUTYSALON
	HAIRSALON
	BARBERSHOP
	MASSAGETHERAPIST
	NAILSALON
	TATTOOPIERCING
	HEALTHBEAUTYSPA
	MASSAGEPARLOUR
	CHILDCARE
	TEACHER
	TUTOR
	SCHOOL
	UNIVERSITY
	CHARITY
	MEMBERSHIPORG
	POLITICALORG
	RELIGIOUSORG
	BAKERY
	BARCLUB
	CATERING
	COFFEE
	FOODTRUCKCART
	GROCERY
	MARKET
	PRIVATECHEF
	TAKEAWAYRESTAURANT
	TABLESERVICERESTAURANT
	WHOLESALEVENDOR
	ALCOHOLWHOLESALER
	ACUPUNCTURE
	CAREGIVER
	CHIROPRACTOR
	DENTIST
	GYM
	MEDICALTPRACTITIONER
	OPTOMETRIST
	PERSONALTRAINER
	PSYCHIATRIST
	COUNSELLOR
	VETERINARY
	PHYSIOTHERAPIST
	DIETITIAN
	PODIATRIST
	OCCUPATIONALTHERAPIST
	HYPNOTHERAPIST
	PHYSICALTHERAPIST
	DOCTOR
	ANESTHETIST
	MIDWIFE
	NURSE
	PHARMACIST
	AUTOMOTIVE
	CARPETCLEANING
	CLEANING
	CLOTHINGALTERATIONS
	DRYCLEANING
	ELECTRICAL
	FLOORING
	GENERALCONTRACTING
	HEATINGANDAC
	INSTALLATIONSERVICES
	RUBBISHREMOVAL
	LANDSCAPING
	LOCKSMITH
	PAINTING
	PESTCONTROL
	PLUMBING
	CARPENTRY
	PLASTERINGCEILING
	TILINGCARPETING
	BRICKLAYING
	CONCRETING
	GLAZING
	CONSTRUCTIONMATERIALS
	CONSTRUCTION
	ARCHITECTURE
	FESTIVALS
	CINEMA
	MUSEUM
	MUSIC
	PERFORMINGARTS
	SPORTINGEVENTS
	SPORTSRECREATION
	ACCOUNTING
	CONSULTING
	DESIGN
	INTERIORDESIGN
	LEGALSERVICES
	MARKETING
	PHOTOGRAPHY
	PRINTINGSERVICES
	REALESTATE
	SOFTWAREDEVELOPMENT
	DATINGSERVICES
	EMPLOYMENTAGENCIES
	MOTIVATIONALSERVICES
	ARTPHOTOFILM
	BOOKSMUSICVIDEO
	CLOTHING
	COMPUTERAPPLICANCES
	ELECTRONICS
	EYEWEAR
	EVENTS
	FLOWERSGIFTS
	FURNITURE
	HOMEGOODS
	HOBBYSHOP
	JEWELLERYWATCHES
	OFFICESUPPLY
	PETSHOP
	SPECIALITYSHOP
	SPORTINGGOODS
	BUS
	DELIVERY
	REMOVALIST
	PRIVATECARHIRE
	TAXI
	AIRLINEANDAIRCARRIER
	COURIERSERVICES
	MOVERS
	BOATRENTALS
	DEALERS
	BUSINESSSERVICES
	CARTRUCKSERVICES
	CARRENTAL
	TRAVELAGENCY
	LODGING
}

enum CategoryGroup {
	BEAUTY
	EDUCATION
	CHARITIES
	FOODDRINK
	HEALTHCAREFITNESS
	HOMEMAINTENANCE
	LEISUREENTERTAINMENT
	PROFESSIONALSERVICES
	RETAIL
	TRANSPORTATION
	TRAVEL
}

enum EntityCategories {
  PURCHASES
  COST_OF_GOODS_SOLD
  ADVERTISING
  BANK_FEES
  CLEANING
  CONSULTING_ACCOUNTING
  ENTERTAINMENT
  FREIGHT_COURIER
  GENERAL_EXPENSES
  INSURANCE
  INTEREST_EXPENSE
  LEGAL_EXPENSES
  LIGHT_POWER_HEATING
  MOTOR_VEHICLE_EXPENSES
  OFFICE_EXPENSES
  PRINTING_STATIONERY
  RENT
  WAGES_SALARIES
  SUPERANNUATION
  COMMISSION
  SUBSCRIPTIONS
  TELEPHONE_INTERNET
  TRAVEL_NATIONAL
  TRAVEL_INTERNATIONAL
  INCOME_TAX_EXPENSE
  OFFICE_EQUIPMENT
  COMPUTER_EQUIPMENT
}

type HelperFields {
  isNotRegulated: Boolean
  hasNoPlaceOfBusiness: Boolean
  settledSum: SettledSumValue
  trustee: TrusteeType
}

input HelperFieldsInput {
  isNotRegulated: Boolean
  hasNoPlaceOfBusiness: Boolean
  settledSum: SettledSumValue
  trustee: TrusteeType
}

type MembersFilters {
  director: Boolean
  beneficialOwner: Boolean
  settlor: Boolean
  trustee: Boolean
  beneficiary: Boolean
  partner: Boolean
  chair: Boolean
  secretary: Boolean
  treasurer: Boolean
}

input MembersFiltersInput {
  director: Boolean
  beneficialOwner: Boolean
  settlor: Boolean
  trustee: Boolean
  beneficiary: Boolean
  partner: Boolean
  chair: Boolean
  secretary: Boolean
  treasurer: Boolean
}

enum RegulatorBodyType {
  COMMONWEALTH
  VIC
  ACT
  NSW
  QLD
  TAS
  NT
  SA
  WA
}

type RegulatorBody {
	name: String
	referenceNumber: String
  type: RegulatorBodyType
}

input RegulatorBodyInput {
  name: String
  referenceNumber: String
  type: RegulatorBodyType
}

type KYCInitialData {
  documents: [DocumentType]
  isAgreed: Boolean
  personalData: PersonalData
}

input KYCInitialDataInput {
  documents: [DocumentType]
  isAgreed: Boolean
  personalData: PersonalDataInput
}

enum DocumentType {
  DRIVING_LICENCE
  PASSPORT
  MEDICARE_CARD
  NO_SECOND_ID
}

type PersonalData {
  street: String
  suburb: String
  state: String
  postcode: String
  firstName: String
  middleName: String
  lastName: String
  dob: String
}

input PersonalDataInput {
  street: String
  suburb: String
  state: String
  postcode: String
  firstName: String
  middleName: String
  lastName: String
  dob: String
}

type InitialCustomerData {
  street: String
  suburb: String
  state: String
  postcode: String
  country: String
  roles: [String!]
  companyTrustName: String
  abn: String
  acn: String
  firstName: String
  lastName: String
  middleName: String
  dob: String
  temporaryId: String
  companyProfileData: CompanyProfileData
}

input InitialCustomerDataInput {
  street: String
  suburb: String
  state: String
  postcode: String
  country: String
  roles: [String!]
  companyTrustName: String
  abn: String
  acn: String
  firstName: String
  lastName: String
  middleName: String
  dob: String
  temporaryId: String
  companyProfileData: CompanyProfileData
}

input EntityOnboardingErrorsInput {
  onboardingStatus: OnboardingStatus!
  errorType: String!
  errorMessage: String!
  data: String!
  timestamp: AWSDateTime!
}

type EntityTransactionMetaData {
  yetToMakeTransaction: Boolean!
  firstTransactionUuid: String
  firstTransactionTimestamp: AWSDateTime
}
