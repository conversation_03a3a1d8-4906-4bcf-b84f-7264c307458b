import { ComponentClients, retry } from '@npco/bff-systemtest-utils';
import { CustomerRole, DbRecordType, EntityType, Status } from '@npco/component-dto-core';
import type { CustomerCreatedEventDto, CustomerUpdatedEventDto } from '@npco/component-dto-customer';
import { CustomerDeletedEventDto } from '@npco/component-dto-customer';
import { OnboardingStatus } from '@npco/component-dto-entity';

import gql from 'graphql-tag';
import { v4 as uuidv4 } from 'uuid';

import { CustomerApiTestHelper } from './customerApiTestHelper';
import { testIf } from './util';

const { STAGE: stage } = process.env;

describe('customer api test suite', () => {
  const apiTestHelper = new CustomerApiTestHelper(ComponentClients.DeviceBackend);
  let customer: any;

  beforeAll(async () => {
    await apiTestHelper.beforeAll();
  });

  const createCustomerUpdatedDto = (entityUuid: string, customerUuid: string): CustomerUpdatedEventDto => ({
    customerUuid,
    entityUuid,
    firstname: uuidv4(),
    middlename: uuidv4(),
    lastname: uuidv4(),
    nickname: uuidv4(),
  });

  const createCustomerCreatedDto = (entityUuid: string) => {
    const dto: CustomerCreatedEventDto = {
      entityUuid,
      customerUuid: uuidv4(),
      createdTime: `${new Date().getTime()}`,
      firstname: uuidv4(),
      middlename: uuidv4(),
      lastname: uuidv4(),
      nickname: uuidv4(),
      registeringIndividual: false,
      address: {
        country: uuidv4(),
        postcode: uuidv4(),
        state: uuidv4(),
        street: uuidv4(),
        suburb: uuidv4(),
      },
      dob: `2020-01-01`,
      email: `${uuidv4()}@myzeller.com`,
      emailVerified: true,
      phone: uuidv4(),
      phoneVerified: false,
      role: CustomerRole.ADMIN,
      director: false,
      secretary: false,
      ceo: false,
      shareholder: false,
      beneficialOwner: false,
      beneficialOwnerAlt: true,
      beneficiary: true,
      partner: true,
      trustee: true,
      settlor: true,
      generalContact: true,
      financialContact: true,
      companyTrustName: uuidv4(),
      abn: uuidv4(),
      // type: EntityType.INDIVIDUAL,
    };
    return dto;
  };
  it('should not find a customer', async () => {
    try {
      await (
        await apiTestHelper.getOpenIdClient()
      ).query({
        query: gql`
          query getCustomer($customerId: ID) {
            getCustomer(customerUuid: $customerId) {
              id
            }
          }
        `,
        variables: {
          customerId: 'customerId',
        },
      });
    } catch (e: any) {
      console.log('e', e);
      expect(e.graphQLErrors[0].message).toEqual('Customer not found customerId');
      expect(e.graphQLErrors[0].errorType).toEqual('NOT_FOUND');
    }
  });

  it('should be able to create a customer', async () => {
    customer = createCustomerCreatedDto(apiTestHelper.getEntityUuid());
    customer.customerUuid = apiTestHelper.testData.customerUuid;
    await apiTestHelper.sendDtoProjectionHandler('dbs.Customer.Created', customer);
    await retry(async () => {
      const data = await apiTestHelper.dbsApi.getCustomer(customer.customerUuid);
      console.log('create customer getCustomer response: ', JSON.stringify(data));
      expect(data.getCustomer.id).toBe(customer.customerUuid);
      expect(data.getCustomer.entityUuid).toBe(customer.entityUuid);
      expect(data.getCustomer).toBeDefined();
    }, 30);
  });

  it('should be able to query a customer with no arguments', async () => {
    await retry(async () => {
      const { data }: any = await (
        await apiTestHelper.getOpenIdClient()
      ).query({
        query: gql`
          query getCustomer {
            getCustomer {
              id
            }
          }
        `,
        variables: {},
      });

      console.log(
        'query a customer with no arguments, getCustomer response: ',
        data.getCustomer.id,
        JSON.stringify(data),
      );
      expect(data.getCustomer.id).toBe(apiTestHelper.getCustomerUuid());
    }, 30);
  });

  it('should be able to project customer updated event', async () => {
    const updated = createCustomerUpdatedDto(apiTestHelper.getEntityUuid(), customer);
    await apiTestHelper.sendDtoProjectionHandler('dbs.Customer.Updated', updated);
    await retry(async () => {
      const data = await apiTestHelper.dbsApi.getCustomer(customer.customerUuid);
      console.log('project customer, getCustomer response: ', JSON.stringify(data));
      expect(data.getCustomer.id).toBe(customer.customerUuid);
      expect(data.getCustomer.entityUuid).toBe(customer.entityUuid);
      expect(data.getCustomer).toBeDefined();
    }, 30);
  });

  it('should successfully call getCustomerEntityMapping', async () => {
    const entityUuid = apiTestHelper.getEntityUuid();
    const createEntityDto = {
      manualEntry: true,
      entityUuid,
      name: uuidv4(),
      type: EntityType.INDIVIDUAL,
      sourceIp: '127.0.0.1',
      onboardingStatus: OnboardingStatus.ONBOARDED,
      accountStatus: {
        canAcquireCnp: true,
        canAcquireVt: true,
        canAcquireMoto: true,
        canAcquire: true,
        canAcquireMobile: true,
        canRefund: true,
        canStandIn: true,
        hasChargeback: true,
        hadForcedRefund: true,
        hadDirectDebitFailure: true,
        hasDirectDebitRequest: true,
        canAcquireAmex: true,
        canCreateAccount: true,
        canCreateCard: true,
        canPayByCard: true,
        canSettle: true,
        canUpdateSettlementAccount: true,
        canTransferIn: true,
        canTransferOut: true,
      },
      abn: '**************',
      tradingName: 'Umbrella Corp',
      registeredAddress: {
        street1: '93 Swanston Street',
        suburb: 'Sydney',
        state: 'NSW',
        postcode: '2000',
      },
    };
    await apiTestHelper.createEntity(createEntityDto);

    await apiTestHelper.sleep(3000);

    const client = await apiTestHelper.getOpenIdClient();
    const result: any = await client.query({
      query: gql`
        query getCustomerEntityMapping {
          getCustomerEntityMapping {
            entityRelations {
              entityUuid
              role
              entity {
                id
              }
            }
          }
        }
      `,
    });
    console.log(result);
    if (stage === 'dev') {
      // additional resolver
      expect(result.data.getCustomerEntityMapping.entityRelations).toEqual([
        {
          entity: {
            id: entityUuid,
          },
          entityUuid,
          role: 'ADMIN',
        },
      ]);
    } else {
      expect(result.data.getCustomerEntityMapping.entityRelations).toEqual([
        {
          entityUuid,
          role: 'ADMIN',
        },
      ]);
    }
  });

  it('should be able to getMyPersonalInfo', async () => {
    expect(await apiTestHelper.getMyPersonalInfo()).toEqual({
      getMyPersonalInfo: {
        address: {
          country: expect.any(String),
          postcode: expect.any(String),
          state: expect.any(String),
          street: expect.any(String),
          suburb: expect.any(String),
        },
        email: expect.any(String),
        emailVerified: true,
        firstname: expect.any(String),
        id: expect.any(String),
        idvAttempts: null,
        kycCheckpoints: null,
        kycStatus: null,
        lastname: expect.any(String),
        middlename: expect.any(String),
        phone: expect.any(String),
        phoneVerified: false,
      },
    });
  });

  it('should be able to process delete customer projection in db', async () => {
    const customerProjection = new CustomerDeletedEventDto({
      customerUuid: customer.customerUuid,
    });
    await apiTestHelper.sendDtoProjectionHandler('dbs.Customer.Deleted', customerProjection);
    await retry(async () => {
      const customerRecord = await apiTestHelper.dbClient.queryById(
        apiTestHelper.getComponentTableName(),
        customer.customerUuid,
        DbRecordType.CUSTOMER,
      );
      expect(customerRecord.Items?.[0].id).toEqual(customer.customerUuid);
      expect(customerRecord.Items?.[0].status).toEqual(Status.DELETED);
      try {
        await apiTestHelper.dbsApi.getCustomer(customer.customerUuid);
      } catch (e: any) {
        expect(e.graphQLErrors[0].message).toEqual(`Customer not found ${customer.customerUuid}`);
        expect(e.graphQLErrors[0].errorType).toEqual('NOT_FOUND');
      }
    }, 60);
  });
});
