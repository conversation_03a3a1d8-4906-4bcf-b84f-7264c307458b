import { InvalidRequest } from '@npco/component-bff-core/dist/error';
import { appEntityMiddleware, xrayAggregateMiddleware } from '@npco/component-bff-core/dist/middleware';
import { PermissionRoot } from '@npco/component-bff-core/dist/middleware/lambdaMetadata';
import { withMiddlewaresV2 } from '@npco/component-bff-core/dist/middleware/withMiddlewaresV2';
import { LambdaEventSource } from '@npco/component-bff-core/dist/types/lambdaEventSource';
import { ZellerComponent } from '@npco/component-bff-core/dist/types/zellerComponent';
import { createProjectionHandler } from '@npco/component-dbs-mp-common/dist/lambda/projectionHandlerLambda';
import { bootstrapNestJSMiddleware } from '@npco/component-dbs-mp-common/dist/middleware';
import type { NestAppEntityContext } from '@npco/component-dbs-mp-common/dist/types';
import type { CreateCustomerInput, CustomerUpdateInput } from '@npco/component-dto-customer/dist/types';

import type { Context, Handler } from 'aws-lambda';

import { CustomerEntityService } from '../../services/customerEntityService';
import { CustomerModule } from '../../services/customerModule';
import { customerEntityProjectionEvents, customerProjectionEvents } from '../../services/customerProjection';
import { CustomerService } from '../../services/customerService';
import { getCustomerSites } from '../common/commonLambda';

export const getArgId = (event: any, context: any) => event.args.customerUuid ?? context.customerUuid;
export const getEventId = (event: any) => event.detail.customerUuid;

export const getMyPersonalInfoHandler: Handler = withMiddlewaresV2(
  {
    component: ZellerComponent.DBS,
    eventType: LambdaEventSource.APPSYNC,
    permissionsDerivedFrom: PermissionRoot.CUSTOMER_OWN_DATA,
  },
  async (_, context: Context) => {
    const { app, customerUuid } = context as NestAppEntityContext;
    return app.get(CustomerService).getMyPersonalInfo(customerUuid);
  },
  [bootstrapNestJSMiddleware(CustomerModule), appEntityMiddleware(true)],
);

export const getCustomerHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { customerUuid?: string } }, context: Context) => {
    const { app, entityUuid, customerUuid } = context as NestAppEntityContext;
    return app.get(CustomerService).getCustomer(entityUuid, event.args.customerUuid ?? customerUuid);
  },
  [bootstrapNestJSMiddleware(CustomerModule), appEntityMiddleware(false), xrayAggregateMiddleware(getArgId)],
);

export const getCustomersHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (_event: any, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(CustomerService).getCustomers(entityUuid);
  },
  [bootstrapNestJSMiddleware(CustomerModule), appEntityMiddleware(false)],
);

export const deleteCustomerHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { customerUuid: string } }, context: Context) => {
    const { app, customerUuid, entityUuid } = context as NestAppEntityContext;
    if (customerUuid === event.args.customerUuid) {
      return Promise.reject(new InvalidRequest('Cannot delete current customer')); // NOSONAR
    }
    return app.get(CustomerService).deleteCustomer(event.args.customerUuid, entityUuid);
  },
  [bootstrapNestJSMiddleware(CustomerModule), appEntityMiddleware(false), xrayAggregateMiddleware(getArgId)],
);

export const updateCustomerHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { customer: CustomerUpdateInput; entityUuid: string } }, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(CustomerService).updateCustomer(event.args.customer, entityUuid);
  },
  [
    bootstrapNestJSMiddleware(CustomerModule),
    appEntityMiddleware(false),
    xrayAggregateMiddleware((event) => event.args.customer.id),
  ],
);

export const customerProjectionHandler: Handler = createProjectionHandler(
  {
    ...customerProjectionEvents,
    ...customerEntityProjectionEvents,
  },
  CustomerModule,
);

export const createCustomerHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC },
  async (event: { args: { input: [CreateCustomerInput] } }, context: Context) => {
    const { app, entityUuid } = context as NestAppEntityContext;
    return app.get(CustomerService).createCustomer(entityUuid, event.args.input);
  },
  [bootstrapNestJSMiddleware(CustomerModule), appEntityMiddleware(false)],
);

export const getCustomerSitesHandler: Handler = withMiddlewaresV2(
  { component: ZellerComponent.DBS, eventType: LambdaEventSource.APPSYNC_BATCH },
  getCustomerSites,
  [bootstrapNestJSMiddleware(CustomerModule)],
);

export const getCustomerEntityMappingHandler: Handler = withMiddlewaresV2(
  {
    component: ZellerComponent.DBS,
    eventType: LambdaEventSource.APPSYNC,
    permissionsDerivedFrom: PermissionRoot.CUSTOMER_OWN_DATA,
  },
  async (_: any, context: Context) => {
    const { app, customerUuid } = context as NestAppEntityContext;
    return app.get(CustomerEntityService).getCustomerEntityMapping(customerUuid);
  },
  [bootstrapNestJSMiddleware(CustomerModule), appEntityMiddleware(true)],
);
