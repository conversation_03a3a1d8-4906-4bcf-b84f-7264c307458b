import { ApiAppServerlessStack, lambdasCrossAccountInvocationPolicy, ssmSharedVpcImport as vpcImport } from '@npco/component-bff-serverless';

import { CrmsApiAppEnvConfig, pluginsApp } from './resources/common';
import { lambdas } from './resources/crms/customer/lambdas';

const { AWS_REGION: region } = process.env;
const envConfig = new CrmsApiAppEnvConfig('./resources/crms/config', true);

// lambda env size limits reached
const env = Object.keys(envConfig.dotenvConfig).reduce((acc: { [env: string]: string }, key) => {
  if (
    ![
      'SCREENSAVER_LOGO_BASE_URL',
      'WAS_OFFLINE_APPROVED_GSI',
      'METRIC_OUTSTANDING_AMOUNT_GSI',
      'METRIC_AMOUNT_GSI',
      'METRIC_LIFETIME_OUTSTANDING_AMOUNT_GSI',
      'METRIC_LIFETIME_AMOUNT_GSI',
      'METRIC_LIFETIME_GPV_GSI',
      'METRIC_AVERAGE_VALUE_GSI',
      'DEPOSITS_PENDING_GSI',
      'DEPOSITS_PENDING_SCHEDULER',
      'STAND_IN_METRIC_30_DAY_SCHEDULER',
      'EXPORT_BUCKET_ALLOWED_ORIGINS',
      'UPLOAD_BUCKET_ALLOWED_ORIGINS',
      'GROWSURF_ENABLE',
      'GROWSURF_API_ENDPOINT',
      'FORTIRO_ENABLE',
      'BILLING_API_ENDPOINT_VERSION',
      'BANKING_WRAPPER_ENABLED',
      'MATERIALISE_INCOMING_EXTERNAL_NON_APPROVED_TRANSACTION',
    ].includes(key)
  ) {
    acc[key] = envConfig.dotenvConfig[key];
  }
  return acc;
}, {});

envConfig.appsyncStackName = `${envConfig.service}-appsync` as const;

const sls = new ApiAppServerlessStack('customer', envConfig, {
  plugins: pluginsApp,
  custom: {
    ...envConfig.getDefaults(),
    ...envConfig.getAppsync(),
    ...envConfig.getDynamoDb(),
    ...envConfig.getAmsApi(),
    ...envConfig.getComponentCqrs(),
    ...envConfig.getAuth0(),
    vpcImport,
    growSurfApiEndpoint: 'https://api.growsurf.com/',
    growSurfApiKey: envConfig.ssmParam('GROWSURF_API_KEY'),
    growSurfCampaignId: envConfig.ssmParam('GROWSURF_CAMPAIGN_ID'),
    documentUploadsBucket: '${ssm:${env:STATIC_ENV_NAME}-mp-api-assets-document-uploads}',
    documentEncryptionKeyArn: '${ssm:document-id-tokenisation-key-arn}',
    siteGsi: envConfig.siteGsi,
    entityGsi: envConfig.entityGsi,
    hubspotApiGatewayEndpoint: 'https://${env:CRMS_API_ENDPOINT}',
    dependsOn: {
      // Optional. Defaults to true, set to false to disable the plugin
      enabled: true,
      // Optional. Sets amount of lambda deployment parallelization plugin will attempt to create. Defaults to 1
      chains: 5,
    },
    multiEntityEnabled: '${env:MULTI_ENTITY_ENABLED}',
  },
  functions: lambdas,
  environment: {
    ...env,
    HUBSPOT_API_ENDPOINT: '${env:HUBSPOT_API_ENDPOINT}',
    HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
    GROWSURF_ENABLE: '${env:GROWSURF_ENABLE}',
    COMPONENT_TABLE: envConfig.componentTableName,
    GLOBAL_ACCOUNT_ID: '${env:SYDNEY_ACCOUNT_ID}',
  },
  resources: {
    ...lambdasCrossAccountInvocationPolicy(lambdas, region, false),
  },
});

module.exports = sls.build();
