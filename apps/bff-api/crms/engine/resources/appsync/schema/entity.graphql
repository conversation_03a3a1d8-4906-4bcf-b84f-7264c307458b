type Mutation {
  updateEntity(entity: EntityInput!): Boolean!
  updateEntityStandInRules(entityUuid: ID!, standInRules: [StandInRuleInput]!): Boolean!
  addEntityTag(entityUuid: ID!, tag: String!): Boolean!
  removeEntityTag(entityUuid: ID!, tag: String!): Boolean!
  addEntitySubcategory(input: AddEntitySubcategoryInput!): ID!
  updateEntitySubcategory(entityUuid: ID!, subcategoryUuid: ID!, subcategory: String!): Boolean!
  removeEntitySubcategory(entityUuid: ID!, subcategoryUuid: ID!): Boolean!
  finaliseEntityOnboarding(entityUuid: ID!): EntityOnboardingResults!
  updateEntityFeeRatePaymentSettings(entityUuid: ID!, input: UpdateEntityFeeRatePaymentSettingsInput!): Boolean!
  updateEntityRefundOverdraft(entityUuid: ID!, input: UpdateRefundOverdraftInput!): Boolean!
  updateEntityDailyLimit(entityUuid: ID!, maximumLimit: MoneyInput!, reason: String!): EntityDailyLimitResponse!
  # Select the deposit account for the entity or remit to Zeller card account
  # ID is thirdPartyBankAccountUuid or debitCardAccountUuid if remitToCard is true
  selectDepositAccount(entityUuid: ID!, id: ID!, remitToCard: Boolean!, reason: String!): Boolean
  updatePAH(customerUuid: ID!, entityUuid: ID!): Boolean!
}

type Query {
  getEntity(entityUuid: ID!): Entity!
  getEntityDocumentUrls(entityUuid: ID!): EntityDocumentUrls
  getEntityDocumentUploadUrl(entityUuid: ID!): String!
  getEntitySubcategories(entityUuid: ID!, category: EntityCategories!): [Subcategory]!
  getAllSubcategories: [CategorySubcategories!]!
  getEntityTags(entityUuid: String!): [String!]!
  validateFinaliseEntityOnboarding(entityUuid: String!): ValidateFinaliseEntityOnboardingResult!
  getEntityFeeRatePaymentSettings(entityUuid: ID!): EntityFeeRatePaymentSettings
  getEntityDailyLimit(entityUuid: ID!): EntityDailyLimit!
  getEntityDailyLimitConfig: EntityDailyLimitConfig!
  # not yet implemented
  getEntitySavingsAccountProduct(entityUuid: ID!): EntitySavingsAccountProduct
}

enum BillingCycle {
  CURRENT
  NEXT
}

input FeeRatesInput {
  feePercent: Float
  feeFixed: Int
  feePercentMoto: Float
  feeFixedMoto: Int
  feePercentCnp: Float @deprecated(reason: "Use subtypes instead")
  feeFixedCnp: Int @deprecated(reason: "Use subtypes instead")
  feePercentZinv: Float
  feeFixedZinv: Int
  feePercentIntlZinv: Float
  feeFixedIntlZinv: Int
  feePercentXinv: Float
  feeFixedXinv: Int
  feePercentIntlXinv: Float
  feeFixedIntlXinv: Int
  feePercentCpoc: Float
  feeFixedCpoc: Int
  feePercentVt: Float
  feeFixedVt: Int
  feePercentPbl: Float
  feeFixedPbl: Int
  feePercentIntlPbl: Float
  feeFixedIntlPbl: Int
}

input FeeRateSettingsInput {
  feeRates: FeeRatesInput!
  effectiveFrom: BillingCycle!
}

input PaymentLimitsInput {
  minimum: Int
  maximum: Int
}

input PaymentSettingsInput {
  paymentLimits: PaymentLimitsInput
  cnpPaymentLimits: PaymentLimitsInput
  motoPaymentLimits: PaymentLimitsInput
  cpocPaymentLimits: PaymentLimitsInput
}

input UpdateEntityFeeRatePaymentSettingsInput {
  feeRateSettings: FeeRateSettingsInput
  paymentSettings: PaymentSettingsInput
}

input UpdateRefundOverdraftInput {
  limit: Int
  reason: String!
}

type FeeRates {
  feePercent: Int
  feeFixed: Int
  feePercentMoto: Int
  feeFixedMoto: Int
  feePercentCnp: Int @deprecated(reason: "Use subtypes instead")
  feePercentCpoc: Int
  feePercentXinv: Int
  feePercentIntlXinv: Int
  feePercentZinv: Int
  feePercentIntlZinv: Int
  feeFixedCnp: Int @deprecated(reason: "Use subtypes instead")
  feeFixedCpoc: Int
  feeFixedXinv: Int
  feeFixedIntlXinv: Int
  feeFixedZinv: Int
  feeFixedIntlZinv: Int
  feePercentVt: Int
  feeFixedVt: Int
  feePercentPbl: Int
  feeFixedPbl: Int
  feePercentIntlPbl: Int
  feeFixedIntlPbl: Int
}

type PaymentSettingsLimits {
  minimum: Int
  maximum: Int
}

type PaymentSettings {
  motoPaymentLimits: PaymentSettingsLimits
  cnpPaymentLimits: PaymentSettingsLimits
  paymentLimits: PaymentSettingsLimits
  cpocPaymentLimits: PaymentSettingsLimits
}

type FeeRateSettings {
  feeRates: FeeRates!
}

enum PaymentType {
  CP
  CNP
  MOTO
}

type PaymentSettingsLastUpdated {
  updatedTime: String!
  updatedBy: String!
  effectiveFrom: String
}

type EntityFeeRatePaymentSettings {
  feeRateSettings: FeeRateSettings
  paymentSettings: PaymentSettings
  lastUpdated: PaymentSettingsLastUpdated
}

type Subcategory {
  id: ID!
  name: String!
  predefined: Boolean!
}

type CategorySubcategories {
  category: EntityCategories!
  subcategories: [Subcategory!]!
}

input AddEntitySubcategoryInput {
  entityUuid: ID!
  category: EntityCategories!
  subcategory: String!
}

enum EntityCategories {
  PURCHASES
  COST_OF_GOODS_SOLD
  ADVERTISING
  BANK_FEES
  CLEANING
  CONSULTING_ACCOUNTING
  ENTERTAINMENT
  FREIGHT_COURIER
  GENERAL_EXPENSES
  INSURANCE
  INTEREST_EXPENSE
  LEGAL_EXPENSES
  LIGHT_POWER_HEATING
  MOTOR_VEHICLE_EXPENSES
  OFFICE_EXPENSES
  PRINTING_STATIONERY
  RENT
  WAGES_SALARIES
  SUPERANNUATION
  COMMISSION
  SUBSCRIPTIONS
  TELEPHONE_INTERNET
  TRAVEL_NATIONAL
  TRAVEL_INTERNATIONAL
  INCOME_TAX_EXPENSE
  OFFICE_EQUIPMENT
  COMPUTER_EQUIPMENT
}

type StandInRule {
  operation: String
  field: String
  value: String
}

input StandInRuleInput {
  operation: String
  field: String
  value: String
}

input EntityAddressInput {
  street1: String
  street2: String
  suburb: String
  state: String
  postcode: String
  country: String
}

input RegulatorBodyInput {
  name: String
  referenceNumber: String
}

enum OnboardingStatus {
  NONE
  PHONE_COMPLETE
  ENTITY_ESTABLISHED
  ENTITY_ADDRESS1
  ENTITY_ADDRESS2
  ENTITY_REVENUE
  ENTITY_CATEGORY
  TRADING_NAME_ESTABLISHED
  IDV_REQUIRED
  IDV_COMPLETE
  DIRECTORS_ESTABLISHED
  BOS_ESTABLISHED
  ALT_BOS_ESTABLISHED
  MORE_INFO_COLLECTED
  BUSINESS_REG_COLLECTED
  PARTNERS_ESTABLISHED
  DOC_UPLOADED
  SETTLORS_ESTABLISHED
  BEN_ESTABLISHED
  TRUSTEES_ESTABLISHED
  CHAIR_ESTABLISHED
  SECRETARY_ESTABLISHED
  TREASURE_ESTABLISHED
  GOVERNMENT_ROLE_ESTABLISHED
  FINALISING_ONBOARDING
  ONBOARDED
  REVIEW
  RC_ONBOARDED
  RC_REJECTED
  RC_ABANDONED
  RC_DEPLATFORMED
  RC_REVIEW
  BV_COMPLETE
  BV_ERROR
}

input EntityInput {
  id: ID!
  shortId: String
  name: String
  acn: String
  abn: String
  type: EntityType
  tradingName: String
  registeredAddress: EntityAddressInput
  businessAddress: EntityAddressInput
  categoryGroup: CategoryGroup
  category: Category
  estimatedAnnualRevenue: Int
  goodsServicesProvided: String
  customerDiscovery: String
  website: String
  instagram: String
  facebook: String
  twitter: String
  regulatorBody: RegulatorBodyInput
  onboardingStatus: OnboardingStatus
  canAcquireCnp: Boolean
  canAcquireVt: Boolean
  canAcquireMoto: Boolean
  canAcquire: Boolean
  canAcquireMobile: Boolean
  canCreateAccount: Boolean
  canCreateCard: Boolean
  canPayByCard: Boolean
  canRefund: Boolean
  canSettle: Boolean
  canStandIn: Boolean
  canTransferIn: Boolean
  canTransferOut: Boolean
  hadForcedRefund: Boolean
  hasChargeback: Boolean
  hadDirectDebitFailure: Boolean
  hasDirectDebitRequest: Boolean
  riskRating: RiskRating
}

enum EntityType {
  INDIVIDUAL
  COMPANY
  PARTNERSHIP
  TRUST
  ASSOCIATION
  ASSOCIATION_UNINCORPORATED
  BENEFICIARY_CLASS
  OTHER
  GOVERNMENT
}

enum CategoryGroup {
  BEAUTY
  EDUCATION
  CHARITIES
  FOODDRINK
  HEALTHCAREFITNESS
  HOMEMAINTENANCE
  LEISUREENTERTAINMENT
  PROFESSIONALSERVICES
  RETAIL
  TRANSPORTATION
  TRAVEL
  GOVERNMENTSERVICES
  FINANCIALSERVICES
}

enum Category {
  OTHER
  BEAUTYSALON
  HAIRSALON
  BARBERSHOP
  MASSAGETHERAPIST
  NAILSALON
  TATTOOPIERCING
  HEALTHBEAUTYSPA
  MASSAGEPARLOUR
  CHILDCARE
  TEACHER
  TUTOR
  SCHOOL
  UNIVERSITY
  CHARITY
  MEMBERSHIPORG
  POLITICALORG
  RELIGIOUSORG
  BAKERY
  BARCLUB
  CATERING
  COFFEE
  FOODTRUCKCART
  GROCERY
  MARKET
  PRIVATECHEF
  TAKEAWAYRESTAURANT
  TABLESERVICERESTAURANT
  WHOLESALEVENDOR
  ALCOHOLWHOLESALER
  ACUPUNCTURE
  CAREGIVER
  CHIROPRACTOR
  DENTIST
  GYM
  MEDICALTPRACTITIONER
  OPTOMETRIST
  PERSONALTRAINER
  PSYCHIATRIST
  COUNSELLOR
  VETERINARY
  PHYSIOTHERAPIST
  DIETITIAN
  PODIATRIST
  OCCUPATIONALTHERAPIST
  HYPNOTHERAPIST
  PHYSICALTHERAPIST
  DOCTOR
  ANESTHETIST
  MIDWIFE
  NURSE
  PHARMACIST
  AUTOMOTIVE
  CARPETCLEANING
  CLEANING
  CLOTHINGALTERATIONS
  DRYCLEANING
  ELECTRICAL
  FLOORING
  GENERALCONTRACTING
  HEATINGANDAC
  INSTALLATIONSERVICES
  RUBBISHREMOVAL
  LANDSCAPING
  LOCKSMITH
  PAINTING
  PESTCONTROL
  PLUMBING
  CARPENTRY
  PLASTERINGCEILING
  TILINGCARPETING
  BRICKLAYING
  CONCRETING
  GLAZING
  CONSTRUCTIONMATERIALS
  CONSTRUCTION
  ARCHITECTURE
  FESTIVALS
  CINEMA
  MUSEUM
  MUSIC
  PERFORMINGARTS
  SPORTINGEVENTS
  SPORTSRECREATION
  ACCOUNTING
  CONSULTING
  DESIGN
  INTERIORDESIGN
  LEGALSERVICES
  MARKETING
  PHOTOGRAPHY
  PRINTINGSERVICES
  REALESTATE
  SOFTWAREDEVELOPMENT
  DATINGSERVICES
  EMPLOYMENTAGENCIES
  MOTIVATIONALSERVICES
  ARTPHOTOFILM
  BOOKSMUSICVIDEO
  CLOTHING
  COMPUTERAPPLICANCES
  ELECTRONICS
  EYEWEAR
  EVENTS
  FLOWERSGIFTS
  FURNITURE
  HOMEGOODS
  HOBBYSHOP
  JEWELLERYWATCHES
  OFFICESUPPLY
  PETSHOP
  SPECIALITYSHOP
  SPORTINGGOODS
  BUS
  DELIVERY
  REMOVALIST
  PRIVATECARHIRE
  TAXI
  AIRLINEANDAIRCARRIER
  COURIERSERVICES
  MOVERS
  BOATRENTALS
  DEALERS
  BUSINESSSERVICES
  CARTRUCKSERVICES
  CARRENTAL
  TRAVELAGENCY
  LODGING
  LOCALCOUNCIL
  LIBRARY
  PRIMARYSCHOOL
  PARKSRECREATION
  GAMBLINGESTABLISHMENT
  FOREIGNEXCHANGESEVICES
  CRYPTOCURRENCY
  PAYMENTPROCESSORS
  INSURANCEPROVIDERS
  ADULTSERVICES
  ONLINETOBACCOVAPERETAILERS
  WEAPONSAMMUNITIONS
}

enum RegulatorBodyType {
  COMMONWEALTH
  VIC
  ACT
  NSW
  QLD
  TAS
  NT
  SA
  WA
}

type RegulatorBody {
  name: String
  referenceNumber: String
  type: RegulatorBodyType
}

type EntityFeeRateSettings {
  feePercent: Int
  feeFixed: Int
  feePercentMoto: Int
  feeFixedMoto: Int
  feePercentCnp: Int @deprecated(reason: "Use subtypes instead")
  feeFixedCnp: Int @deprecated(reason: "Use subtypes instead")
  feePercentZinv: Int
  feeFixedZinv: Int
  feePercentIntlZinv: Int
  feeFixedIntlZinv: Int
  feePercentXinv: Int
  feeFixedXinv: Int
  feePercentIntlXinv: Int
  feeFixedIntlXinv: Int
  feePercentCpoc: Int
  feeFixedCpoc: Int
  feePercentVt: Int
  feeFixedVt: Int
  feePercentPbl: Int
  feeFixedPbl: Int
  feePercentIntlPbl: Int
  feeFixedIntlPbl: Int
}

enum RiskRating {
  LOW_NEW
  LOW
  MEDIUM
  HIGH
}

type RefundOverdraftSettings {
  limit: Int
  reason: String!
}

type RegionalOptions {
  surchargeAllowed: Boolean!
}

type Entity {
  id: ID!
  name: String
  acn: String
  abn: String
  countryOfOrigin: String
  type: EntityType
  tradingName: String
  registeredAddress: EntityAddress
  businessAddress: EntityAddress
  categoryGroup: CategoryGroup
  category: Category
  remitToCard: Boolean
  feeRateSettings: EntityFeeRateSettings
  estimatedAnnualRevenue: Int
  debitCardAccountUuid: ID
  depositAccountUuid: ID
  goodsServicesProvided: String
  customerDiscovery: String
  website: String
  instagram: String
  facebook: String
  twitter: String
  regulatorBody: RegulatorBody
  onboardingStatus: OnboardingStatus
  canAcquireCnp: Boolean
  canAcquireVt: Boolean
  canAcquireMoto: Boolean
  canAcquire: Boolean
  canAcquireMobile: Boolean
  canRefund: Boolean
  canStandIn: Boolean
  hadForcedRefund: Boolean
  hasChargeback: Boolean
  hadDirectDebitFailure: Boolean
  hasDirectDebitRequest: Boolean
  standInRules: [StandInRule]
  referrals: [EntityReferral!]!
  referralCode: String
  riskRating: RiskRating!
  refundOverdraftSettings: RefundOverdraftSettings
  transactionMetaData: EntityTransactionMetaData
  # not yet implemented, will have additional resolver
  savingsAccountProduct: EntitySavingsAccountProduct
  primaryAccountHolder: String
  hubspotCompanyId: String
  pahCustomer: Customer
  currency: String
  domicile: String
  cohort: [String!]
  regionalOptions: RegionalOptions
}

type EntitySavingsAccountProduct {
  id: ID
  productType: SavingsAccountProductType
  effectiveInterestRate: String
  baseInterestRate: String
  bonusInterestRate: String
  bonusLengthInDays: String
  interestThreshold: Money
}

type EntityAddress {
  street1: String
  street2: String
  suburb: String
  state: String
  postcode: String
  country: String
}

type EntityDocumentUrls {
  documents: [EntityDocumentUrlContent]
}

enum DocumentUploadedType {
  ID_DOCUMENT
  BANK_STATEMENT_DOCUMENT
  OTHER_DOCUMENT
  ASSOCIATION_DOCUMENT
  CUSTOMER_SUPPORT_DOCUMENT
  INVOICE_DOCUMENT
  PARTNERSHIP_DOCUMENT
  TRUST_DOCUMENT
}

type EntityDocumentUrlContent {
  url: String
  lastModified: String
  documentUuid: String
  contentType: String
  documentName: String
  documentType: DocumentUploadedType
  consentToViewDocument: Boolean
}

type ValidateFinaliseEntityOnboardingResult {
  missingIndividualName: Boolean!
  missingDob: Boolean!
  missingIndividualAddress: Boolean!
  missingPhone: Boolean!
  missingEmail: Boolean!
  missingEntityName: Boolean!
  missingEntityAddress: Boolean!
  invalidOnboardingStatus: Boolean!
  onboardingStatus: OnboardingStatus
}

enum EntityOnboardingResult {
  COMPLETED
  IN_REVIEW
  MANUAL_ACTIVATION @deprecated(reason: "no longer in use")
  MORE_INFO_REQUIRED @deprecated(reason: "no longer in use")
}
type EntityOnboardingResults {
  entityUuid: ID!
  bsb: String @deprecated(reason: "no return value")
  account: String @deprecated(reason: "no return value")
  result: EntityOnboardingResult
}

type EntityReferral {
  referredUuid: ID!
  createdAt: AWSDateTime!
  credit: Int!
  creditAppliedAt: AWSDateTime
}

type EntityTransactionMetaData {
  yetToMakeTransaction: Boolean!
  firstTransactionUuid: String
  firstTransactionTimestamp: AWSDateTime
}

type EntityDailyLimitLastUpdated {
  updatedBy: String!
  reason: String!
  updatedTime: String!
}

type EntityDailyLimit {
  id: String!
  maximumLimit: Money!
  lastUpdated: EntityDailyLimitLastUpdated!
}

enum EntityDailyLimitErrorType {
  VALIDATION_ERROR
}

type EntityDailyLimitError {
  errorType: EntityDailyLimitErrorType!
  errorMessage: String!
}

type EntityDailyLimitResponse {
  result: EntityDailyLimit
  error: EntityDailyLimitError
}

type EntityDailyLimitConfig {
  defaultLimit: Money!
  maximumAllowedLimit: Money!
}
