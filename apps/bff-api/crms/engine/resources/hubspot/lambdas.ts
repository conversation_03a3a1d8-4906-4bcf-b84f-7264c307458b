import type { ServerlessFunctions } from '@npco/component-bff-serverless';
import { Action, Arn, ManagedPolicy, deploymentSettings } from '@npco/component-bff-serverless';

import { lambdaCommon } from '../common';

export const lambdas: ServerlessFunctions = {
  oauthCallback: {
    handler: 'src/lambda/hubspotLambdas.oauthCallback',
    name: 'oauthCallback',
    ...lambdaCommon,
    environment: {
      HUBSPOT_APP_CLIENT_ID: '${self:custom.hubspotAppClientId}',
      HUBSPOT_APP_CLIENT_SECRET: '${self:custom.hubspotAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [ManagedPolicy.crmsEntityTableWriteRolePolicy, '${self:custom.domicileLookupTableReadRolePolicyArn}'],
    },
    events: [
      {
        http: {
          path: '${self:custom.apiVersion}/hubspot/oauth',
          method: 'get',
          integration: 'lambda',
          request: { parameters: { querystrings: { code: true } } },
        },
      },
    ],
  },
  customObjectOauthCallback: {
    handler: 'src/lambda/hubspotLambdas.customObjectOauthCallback',
    name: 'customObjectOauthCallback',
    ...lambdaCommon,
    environment: {
      HUBSPOT_CUSTOM_OBJECT_APP_CLIENT_ID: '${self:custom.hubspotCustomObjectAppClientId}',
      HUBSPOT_CUSTOM_OBJECT_APP_CLIENT_SECRET: '${self:custom.hubspotCustomObjectAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [ManagedPolicy.crmsEntityTableWriteRolePolicy, '${self:custom.domicileLookupTableReadRolePolicyArn}'],
    },
    events: [
      {
        http: {
          path: '${self:custom.apiVersion}/hubspot/customobject/oauth',
          method: 'get',
          integration: 'lambda',
          request: { parameters: { querystrings: { code: true } } },
        },
      },
    ],
  },
  bulkUpdateOauthCallback: {
    handler: 'src/lambda/hubspotLambdas.bulkUpdateOauthCallback',
    name: 'bulkUpdateOauthCallback',
    ...lambdaCommon,
    environment: {
      HUBSPOT_BULK_UPDATE_APP_CLIENT_ID: '${self:custom.hubspotBulkUpdateAppClientId}',
      HUBSPOT_BULK_UPDATE_APP_CLIENT_SECRET: '${self:custom.hubspotBulkUpdateAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [ManagedPolicy.crmsEntityTableWriteRolePolicy, '${self:custom.domicileLookupTableReadRolePolicyArn}'],
    },
    events: [
      {
        http: {
          path: '${self:custom.apiVersion}/hubspot/bulkupdate/oauth',
          method: 'get',
          integration: 'lambda',
          request: { parameters: { querystrings: { code: true } } },
        },
      },
    ],
  },
  privateApiOauthCallback: {
    handler: 'src/lambda/hubspotLambdas.privateApiOauthCallback',
    name: 'privateApiOauthCallback',
    ...lambdaCommon,
    environment: {
      HUBSPOT_PRIVATE_API_APP_CLIENT_ID: '${self:custom.hubspotPrivateApiAppClientId}',
      HUBSPOT_PRIVATE_API_APP_CLIENT_SECRET: '${self:custom.hubspotPrivateApiAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [ManagedPolicy.crmsEntityTableWriteRolePolicy, '${self:custom.domicileLookupTableReadRolePolicyArn}'],
    },
    events: [
      {
        http: {
          path: '${self:custom.apiVersion}/hubspot/privateapi/oauth',
          method: 'get',
          integration: 'lambda',
          request: { parameters: { querystrings: { code: true } } },
        },
      },
    ],
  },
  docScanningOauthCallback: {
    handler: 'src/lambda/hubspotLambdas.docScanningOauthCallback',
    name: 'docScanningOauthCallback',
    ...lambdaCommon,
    environment: {
      HUBSPOT_DOC_SCANNING_APP_CLIENT_ID: '${self:custom.hubspotDocScanningAppClientId}',
      HUBSPOT_DOC_SCANNING_APP_CLIENT_SECRET: '${self:custom.hubspotDocScanningAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [ManagedPolicy.crmsEntityTableWriteRolePolicy, '${self:custom.domicileLookupTableReadRolePolicyArn}'],
    },
    events: [
      {
        http: {
          path: '${self:custom.apiVersion}/hubspot/docscanning/oauth',
          method: 'get',
          integration: 'lambda',
          request: { parameters: { querystrings: { code: true } } },
        },
      },
    ],
  },
  hubspotTokenRefresh: {
    handler: 'src/lambda/hubspotLambdas.tokenRefresh',
    name: 'tokenRefresh',
    ...lambdaCommon,
    environment: {
      HUBSPOT_APP_CLIENT_ID: '${self:custom.hubspotAppClientId}',
      HUBSPOT_APP_CLIENT_SECRET: '${self:custom.hubspotAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        refreshTokenDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotTokenGsi}')],
          },
        ],
      },
    },
  },
  hubspotCustomObjectTokenRefresh: {
    handler: 'src/lambda/hubspotLambdas.customObjectTokenRefresh',
    name: 'customObjectTokenRefresh',
    ...lambdaCommon,
    environment: {
      HUBSPOT_CUSTOM_OBJECT_APP_CLIENT_ID: '${self:custom.hubspotCustomObjectAppClientId}',
      HUBSPOT_CUSTOM_OBJECT_APP_CLIENT_SECRET: '${self:custom.hubspotCustomObjectAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        refreshTokenDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotTokenGsi}')],
          },
        ],
      },
    },
  },
  hubspotBulkUpdateTokenRefresh: {
    handler: 'src/lambda/hubspotLambdas.bulkUpdateTokenRefresh',
    name: 'bulkUpdateTokenRefresh',
    ...lambdaCommon,
    environment: {
      HUBSPOT_BULK_UPDATE_APP_CLIENT_ID: '${self:custom.hubspotBulkUpdateAppClientId}',
      HUBSPOT_BULK_UPDATE_APP_CLIENT_SECRET: '${self:custom.hubspotBulkUpdateAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        refreshTokenDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotTokenGsi}')],
          },
        ],
      },
    },
  },
  hubspotPrivateApiTokenRefresh: {
    handler: 'src/lambda/hubspotLambdas.privateApiTokenRefresh',
    name: 'privateApiTokenRefresh',
    ...lambdaCommon,
    environment: {
      HUBSPOT_PRIVATE_API_APP_CLIENT_ID: '${self:custom.hubspotPrivateApiAppClientId}',
      HUBSPOT_PRIVATE_API_APP_CLIENT_SECRET: '${self:custom.hubspotPrivateApiAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        refreshTokenDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotTokenGsi}')],
          },
        ],
      },
    },
  },
  hubspotDocScanningTokenRefresh: {
    handler: 'src/lambda/hubspotLambdas.docScanningTokenRefresh',
    name: 'docScanningTokenRefresh',
    ...lambdaCommon,
    environment: {
      HUBSPOT_DOC_SCANNING_APP_CLIENT_ID: '${self:custom.hubspotDocScanningAppClientId}',
      HUBSPOT_DOC_SCANNING_APP_CLIENT_SECRET: '${self:custom.hubspotDocScanningAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        refreshTokenDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotTokenGsi}')],
          },
        ],
      },
    },
  },
  hubspotNotificationsTokenRefresh: {
    handler: 'src/lambda/hubspotLambdas.notificationsTokenRefresh',
    name: 'notificationsTokenRefresh',
    ...lambdaCommon,
    environment: {
      HUBSPOT_NOTIFICATIONS_APP_CLIENT_ID: '${self:custom.hubspotNotificationsAppClientId}',
      HUBSPOT_NOTIFICATIONS_APP_CLIENT_SECRET: '${self:custom.hubspotNotificationsAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_TOKEN_GSI: '${self:custom.hubspotTokenGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        refreshTokenDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotTokenGsi}')],
          },
        ],
      },
    },
  },
  cardRecordType: {
    handler: 'src/lambda/hubspotLambdas.cardRecordType',
    name: 'cardRecordType',
    ...lambdaCommon,
    deploymentSettings,
    environment: {
      HUBSPOT_APP_CLIENT_ID: '${self:custom.hubspotAppClientId}',
      HUBSPOT_APP_CLIENT_SECRET: '${self:custom.hubspotAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_COMPANY_GSI: '${self:custom.hubspotCompanyGsi}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      HUBSPOT_SPA_URI: '${self:custom.hubspotSpaUrl}',
    },
    policy: {
      managed: [ManagedPolicy.allGsiQueryRolePolicyArn, '${self:custom.domicileLookupTableReadRolePolicyArn}'],
    },
    events: [
      {
        http: {
          path: '${self:custom.apiVersion}/hubspot/cardrecord',
          method: 'get',
          integration: 'lambda',
          request: {
            parameters: {
              querystrings: {
                associatedObjectId: true,
                associatedObjectType: true,
                name: false,
                portalId: false,
                userEmail: false,
                userId: false,
              },
            },
          },
        },
      },
    ],
  },
  docCardRecordType: {
    handler: 'src/lambda/hubspotLambdas.docCardRecordType',
    name: 'docCardRecordType',
    ...lambdaCommon,
    environment: {
      HUBSPOT_APP_CLIENT_ID: '${self:custom.hubspotAppClientId}',
      HUBSPOT_APP_CLIENT_SECRET: '${self:custom.hubspotAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_COMPANY_GSI: '${self:custom.hubspotCompanyGsi}',
      ENTITY_TABLE_NAME: '${self:custom.entityTableName}',
      HUBSPOT_SPA_URI: '${self:custom.hubspotSpaUrl}',
    },
    policy: {
      managed: [ManagedPolicy.allGsiQueryRolePolicyArn, '${self:custom.domicileLookupTableReadRolePolicyArn}'],
    },
    events: [
      {
        http: {
          path: '${self:custom.apiVersion}/hubspot/doccardrecord',
          method: 'get',
          integration: 'lambda',
          request: {
            parameters: {
              querystrings: {
                associatedObjectId: true,
                associatedObjectType: true,
                name: false,
                portalId: false,
                userEmail: false,
                userId: false,
              },
            },
          },
        },
      },
    ],
  },
  webhook: {
    handler: 'src/lambda/hubspotLambdas.webhook',
    name: 'webhook',
    ...lambdaCommon,
    environment: {
      HUBSPOT_APP_CLIENT_ID: '${self:custom.hubspotAppClientId}',
      HUBSPOT_APP_CLIENT_SECRET: '${self:custom.hubspotAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      HUBSPOT_COMPANY_GSI: '${self:custom.hubspotCompanyGsi}',
      HUBSPOT_CONTACT_GSI: '${self:custom.hubspotContactGsi}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      HUBSPOT_API_KEY: '${self:custom.hubspotApiKey}',
      HUBSPOT_WEBHOOK_REQUEST_PATH: '${self:custom.apiWebhookRequestPath}',
      HUBSPOT_WEBHOOK_SQS_URL: '${self:custom.hubspotWebhookSqsUrl}',
      TMS_ENTITY_THREEDS_API_ENDPOINT: '${self:custom.tmsEntityThreeDSApiEndpoint}',
      CQRS_COMMAND_HANDLER: '${self:custom.cqrsCommandHandler}',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        webhookDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [
              Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotCompanyGsi}'),
              Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotContactGsi}'),
            ],
          },
          {
            actions: [Action.dynamodb.BatchWriteItem],
            resources: [Arn.dynamodb.table('${self:custom.entityTableName}')],
          },
        ],
        webhookSqsPolicy: [
          {
            actions: [
              Action.sqs.ReceiveMessage,
              Action.sqs.SendMessage,
              Action.sqs.DeleteMessage,
              Action.sqs.ChangeMessageVisibility,
              Action.sqs.GetQueueAttributes,
              Action.sqs.GetQueueUrl,
            ],
            resources: ['${self:custom.hubspotWebhookSqsArn}'],
          },
        ],
        webhookLambdaPolicy: [
          {
            actions: [Action.lambda.InvokeFunction],
            resources: [Arn.lambda.function('${self:custom.cqrsCommandHandler}')],
          },
        ],
      },
    },
    reservedConcurrency: 3,
    events: [{ sqs: { arn: '${self:custom.hubspotWebhookSqsArn}', batchSize: 2, maximumConcurrency: 2 } as any }],
  },
  webhookLowPriority: {
    handler: 'src/lambda/hubspotLambdas.webhookLowPriority',
    name: 'webhookLowPriority',
    ...lambdaCommon,
    timeout: 300,
    memorySize: 1536,
    environment: {
      HUBSPOT_APP_CLIENT_ID: '${self:custom.hubspotAppClientId}',
      HUBSPOT_APP_CLIENT_SECRET: '${self:custom.hubspotLowPriorityWebhookAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      COMPONENT_TABLE: '${self:custom.entityTableName}',
      HUBSPOT_COMPANY_GSI: '${self:custom.hubspotCompanyGsi}',
      HUBSPOT_CONTACT_GSI: '${self:custom.hubspotContactGsi}',
      AMS_API_ENDPOINT: '${self:custom.amsApiEndpoint}',
      AMS_API_ENDPOINT_VERSION: '${self:custom.amsApiEndpointVersion}',
      HUBSPOT_API_KEY: '${self:custom.hubspotApiKey}',
      CQRS_COMMAND_HANDLER: '${self:custom.cqrsCommandHandler}',
      HUBSPOT_WEBHOOK_LOW_PRIORITY_SQS_URL: '${self:custom.hubspotWebhookLowPrioritySqsUrl}',
      HUBSPOT_WEBHOOK_LOW_PRIORITY_REQUEST_PATH: '${self:custom.apiWebhookLowPriorityRequestPath}',
    },
    policy: {
      managed: [
        ManagedPolicy.crmsEntityTableWriteRolePolicy,
        ManagedPolicy.entityTableQueryRolePolicy,
        '${self:custom.domicileLookupTableReadRolePolicyArn}',
      ],
      inline: {
        webhookLowPriorityDbPolicy: [
          {
            actions: [Action.dynamodb.Query],
            resources: [
              Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotCompanyGsi}'),
              Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.hubspotContactGsi}'),
              Arn.dynamodb.gsi('${self:custom.entityTableName}', '${self:custom.typeGsi}'),
            ],
          },
          {
            actions: [Action.dynamodb.BatchWriteItem],
            resources: [Arn.dynamodb.table('${self:custom.entityTableName}')],
          },
        ],
        webhookLowPriorityLambdaPolicy: [
          {
            actions: [Action.lambda.InvokeFunction],
            resources: [Arn.lambda.function('${self:custom.cqrsCommandHandler}')],
          },
        ],
        webhookLowPrioritySqsPolicy: [
          {
            actions: [
              Action.sqs.ReceiveMessage,
              Action.sqs.SendMessage,
              Action.sqs.DeleteMessage,
              Action.sqs.ChangeMessageVisibility,
              Action.sqs.GetQueueAttributes,
              Action.sqs.GetQueueUrl,
            ],
            resources: ['${self:custom.hubspotWebhookLowPrioritySqsArn}'],
          },
        ],
      },
    },
    reservedConcurrency: 3,
    events: [
      {
        sqs: {
          arn: '${self:custom.hubspotWebhookLowPrioritySqsArn}',
          batchSize: 10,
          maximumConcurrency: 2,
        } as any,
      },
    ],
  },
  getReferralCodeHandler: {
    handler: 'src/lambda/crmsCustomerLambda.getReferralCodeHandler',
    name: 'getReferralCodeHandler',
    ...lambdaCommon,
    environment: {
      HUBSPOT_APP_CLIENT_ID: '${self:custom.hubspotAppClientId}',
      HUBSPOT_APP_CLIENT_SECRET: '${self:custom.hubspotAppClientSecret}',
      HUBSPOT_APIGATEWAY_ENDPOINT: '${self:custom.hubspotApiGatewayEndpoint}',
      HUBSPOT_COMPANY_GSI: '${self:custom.hubspotCompanyGsi}',
      ENTITY_TABLE_NAME: '${self:custom.entityTableName}',
    },
    policy: {
      managed: [ManagedPolicy.entityTableQueryRolePolicy, '${self:custom.domicileLookupTableReadRolePolicyArn}'],
    },
    events: [
      {
        http: {
          path: '${self:custom.apiVersion}/customer/{id}/referralcode',
          method: 'get',
          integration: 'lambda',
          request: {
            parameters: { paths: { id: true } },
            template: {
              'application/json': '{ "detail": { "customerUuid": "$input.params("id")" } }',
            },
          },
        },
      },
      // There is an issue with the types/serverless that is missing template options
    ] as any,
  },
};
