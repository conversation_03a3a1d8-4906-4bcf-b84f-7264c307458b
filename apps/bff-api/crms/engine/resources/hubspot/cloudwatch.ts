import { Arn } from '@npco/component-bff-serverless';

export const cloudwatch = {
  hubspotTokenSchedulerRule: {
    Type: 'AWS::Events::Rule',
    Properties: {
      Description: 'hubspot oauth token refresh',
      Name: '${self:provider.stackName}-hubspot-token-refresh',
      RoleArn: {
        'Fn::GetAtt': ['hubspotTokenSchedulerRole', 'Arn'],
      },
      ScheduleExpression: '${self:custom.hubspotTokenCronJob}',
      State: 'ENABLED',
      Targets: [
        {
          Arn: Arn.lambda.function('${self:provider.stackName}-tokenRefresh'),
          Id: 'integration-test-maintenance-lambda',
        },
      ],
    },
  },
  hubspotCustomObjectTokenSchedulerRule: {
    Type: 'AWS::Events::Rule',
    Properties: {
      Description: 'hubspot custom object oauth token refresh',
      Name: '${self:provider.stackName}-hubspot-custom-object-token-refresh',
      RoleArn: {
        'Fn::GetAtt': ['hubspotCustomObjectTokenSchedulerRole', 'Arn'],
      },
      ScheduleExpression: '${self:custom.hubspotTokenCronJob}',
      State: 'ENABLED',
      Targets: [
        {
          Arn: Arn.lambda.function('${self:provider.stackName}-customObjectTokenRefresh'),
          Id: 'integration-test-maintenance-customObject-lambda',
        },
      ],
    },
  },
  hubspotBulkUpdateTokenSchedulerRule: {
    Type: 'AWS::Events::Rule',
    Properties: {
      Description: 'hubspot bulk update oauth token refresh',
      Name: '${self:provider.stackName}-hubspot-bulk-update-token-refresh',
      RoleArn: {
        'Fn::GetAtt': ['hubspotBulkUpdateTokenSchedulerRole', 'Arn'],
      },
      ScheduleExpression: '${self:custom.hubspotTokenCronJob}',
      State: 'ENABLED',
      Targets: [
        {
          Arn: Arn.lambda.function('${self:provider.stackName}-bulkUpdateTokenRefresh'),
          Id: 'integration-test-maintenance-bulkUpdate-lambda',
        },
      ],
    },
  },
  hubspotPrivateApiTokenSchedulerRule: {
    Type: 'AWS::Events::Rule',
    Properties: {
      Description: 'hubspot private api oauth token refresh',
      Name: '${self:provider.stackName}-hubspot-private-api-token-refresh',
      RoleArn: {
        'Fn::GetAtt': ['hubspotPrivateApiTokenSchedulerRole', 'Arn'],
      },
      ScheduleExpression: '${self:custom.hubspotTokenCronJob}',
      State: 'ENABLED',
      Targets: [
        {
          Arn: Arn.lambda.function('${self:provider.stackName}-privateApiTokenRefresh'),
          Id: 'integration-test-maintenance-privateApi-lambda',
        },
      ],
    },
  },
  hubspotDocScanningTokenSchedulerRule: {
    Type: 'AWS::Events::Rule',
    Properties: {
      Description: 'hubspot doc scanning oauth token refresh',
      Name: '${self:provider.stackName}-hubspot-doc-scanning-token-refresh',
      RoleArn: {
        'Fn::GetAtt': ['hubspotDocScanningTokenSchedulerRole', 'Arn'],
      },
      ScheduleExpression: '${self:custom.hubspotTokenCronJob}',
      State: 'ENABLED',
      Targets: [
        {
          Arn: Arn.lambda.function('${self:provider.stackName}-docScanningTokenRefresh'),
          Id: 'integration-test-maintenance-docScanning-lambda',
        },
      ],
    },
  },
  hubspotNotificationsTokenSchedulerRule: {
    Type: 'AWS::Events::Rule',
    Properties: {
      Description: 'hubspot oauth token refresh',
      Name: '${self:provider.stackName}-hubspot-notifications-token-refresh',
      RoleArn: {
        'Fn::GetAtt': ['hubspotNotificationTokenSchedulerRole', 'Arn'],
      },
      ScheduleExpression: '${self:custom.hubspotTokenCronJob}',
      State: 'ENABLED',
      Targets: [
        {
          Arn: Arn.lambda.function('${self:provider.stackName}-notificationsTokenRefresh'),
          Id: 'integration-test-maintenance-notifications-lambda',
        },
      ],
    },
  },
  maintenanceSchedulerPermission: {
    Type: 'AWS::Lambda::Permission',
    DependsOn: 'HubspotTokenRefreshLambdaFunction',
    Properties: {
      FunctionName: '${self:provider.stackName}-tokenRefresh',
      Action: 'lambda:InvokeFunction',
      Principal: 'events.amazonaws.com',
      SourceArn: {
        'Fn::GetAtt': ['hubspotTokenSchedulerRule', 'Arn'],
      },
    },
  },
  maintenanceCustomObjectSchedulerPermission: {
    Type: 'AWS::Lambda::Permission',
    DependsOn: 'HubspotCustomObjectTokenRefreshLambdaFunction',
    Properties: {
      FunctionName: '${self:provider.stackName}-customObjectTokenRefresh',
      Action: 'lambda:InvokeFunction',
      Principal: 'events.amazonaws.com',
      SourceArn: {
        'Fn::GetAtt': ['hubspotCustomObjectTokenSchedulerRule', 'Arn'],
      },
    },
  },
  maintenanceBulkUpdateSchedulerPermission: {
    Type: 'AWS::Lambda::Permission',
    DependsOn: 'HubspotBulkUpdateTokenRefreshLambdaFunction',
    Properties: {
      FunctionName: '${self:provider.stackName}-bulkUpdateTokenRefresh',
      Action: 'lambda:InvokeFunction',
      Principal: 'events.amazonaws.com',
      SourceArn: {
        'Fn::GetAtt': ['hubspotBulkUpdateTokenSchedulerRule', 'Arn'],
      },
    },
  },
  maintenancePrivateApiSchedulerPermission: {
    Type: 'AWS::Lambda::Permission',
    DependsOn: 'HubspotPrivateApiTokenRefreshLambdaFunction',
    Properties: {
      FunctionName: '${self:provider.stackName}-privateApiTokenRefresh',
      Action: 'lambda:InvokeFunction',
      Principal: 'events.amazonaws.com',
      SourceArn: {
        'Fn::GetAtt': ['hubspotPrivateApiTokenSchedulerRule', 'Arn'],
      },
    },
  },
  maintenanceDocScanningSchedulerPermission: {
    Type: 'AWS::Lambda::Permission',
    DependsOn: 'HubspotDocScanningTokenRefreshLambdaFunction',
    Properties: {
      FunctionName: '${self:provider.stackName}-docScanningTokenRefresh',
      Action: 'lambda:InvokeFunction',
      Principal: 'events.amazonaws.com',
      SourceArn: {
        'Fn::GetAtt': ['hubspotDocScanningTokenSchedulerRule', 'Arn'],
      },
    },
  },
  maintenanceNotificationSchedulerPermission: {
    Type: 'AWS::Lambda::Permission',
    DependsOn: 'HubspotNotificationsTokenRefreshLambdaFunction',
    Properties: {
      FunctionName: '${self:provider.stackName}-notificationsTokenRefresh',
      Action: 'lambda:InvokeFunction',
      Principal: 'events.amazonaws.com',
      SourceArn: {
        'Fn::GetAtt': ['hubspotNotificationsTokenSchedulerRule', 'Arn'],
      },
    },
  },
};
